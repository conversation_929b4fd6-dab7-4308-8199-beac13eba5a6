{"skeleton": {"hash": "CbaPlbGZsqfY6u8HK58oIN0pP3E", "spine": "3.8.75", "x": -409.29, "y": -759.22, "width": 813.11, "height": 1528.81, "images": "./images/", "audio": "X:/中转/圣殿云朵动画"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 6.65, "y": 28.66}, {"name": "yunc1", "parent": "bone", "x": 4.53, "y": -788.07, "scaleX": 1.0813, "scaleY": 1.0813}, {"name": "yun2", "parent": "bone", "x": 200.1, "y": -373.24}, {"name": "bone5", "parent": "yun2", "x": -82.86, "y": -52.24, "color": "5bff00ff"}, {"name": "bone6", "parent": "yun2", "x": -34.31, "y": -32.16, "color": "5bff00ff"}, {"name": "bone7", "parent": "yun2", "x": 33.21, "y": 20.3, "color": "5bff00ff"}, {"name": "bone8", "parent": "yun2", "x": 80.64, "y": 44.29, "color": "5bff00ff"}, {"name": "yun3", "parent": "bone", "x": 200.1, "y": -373.24}, {"name": "bone9", "parent": "yun3", "x": -82.86, "y": -52.24, "color": "5bff00ff"}, {"name": "bone10", "parent": "yun3", "x": -34.31, "y": -32.16, "color": "5bff00ff"}, {"name": "bone11", "parent": "yun3", "x": 33.21, "y": 20.3, "color": "5bff00ff"}, {"name": "bone12", "parent": "yun3", "x": 80.64, "y": 44.29, "color": "5bff00ff"}, {"name": "yun4", "parent": "bone", "x": 200.1, "y": -373.24}, {"name": "bone13", "parent": "yun4", "x": -82.86, "y": -52.24, "color": "5bff00ff"}, {"name": "bone14", "parent": "yun4", "x": -34.31, "y": -32.16, "color": "5bff00ff"}, {"name": "bone15", "parent": "yun4", "x": 33.21, "y": 20.3, "color": "5bff00ff"}, {"name": "bone16", "parent": "yun4", "x": 80.64, "y": 44.29, "color": "5bff00ff"}, {"name": "bone17", "parent": "yun2", "x": -82.86, "y": -52.24, "color": "5bff00ff"}, {"name": "bone18", "parent": "yun2", "x": -34.31, "y": -32.16, "color": "5bff00ff"}, {"name": "bone19", "parent": "yun2", "x": 33.21, "y": 20.3, "color": "5bff00ff"}, {"name": "bone20", "parent": "yun2", "x": 80.64, "y": 44.29, "color": "5bff00ff"}, {"name": "bone2", "parent": "yunc1", "x": -329.83, "y": 73.52, "color": "5bff00ff"}, {"name": "bone3", "parent": "yunc1", "x": -338.06, "y": 152.12, "color": "5bff00ff"}, {"name": "bone4", "parent": "yunc1", "x": -225.64, "y": 61.64, "color": "5bff00ff"}, {"name": "bone21", "parent": "yunc1", "x": -239.35, "y": 134.76, "color": "5bff00ff"}, {"name": "bone22", "parent": "yunc1", "x": -134.15, "y": 50.67, "color": "5bff00ff"}, {"name": "bone23", "parent": "yunc1", "x": 130.91, "y": 26.9, "color": "5bff00ff"}, {"name": "bone24", "parent": "yunc1", "x": 139.14, "y": 85.4, "color": "5bff00ff"}, {"name": "bone25", "parent": "yunc1", "x": 220.48, "y": 30.56, "color": "5bff00ff"}, {"name": "bone26", "parent": "yunc1", "x": 231.45, "y": 103.68, "color": "5bff00ff"}, {"name": "bone27", "parent": "yunc1", "x": 313.71, "y": 36.96, "color": "5bff00ff"}, {"name": "bone28", "parent": "yunc1", "x": 327.42, "y": 146.64, "color": "5bff00ff"}, {"name": "yunc2", "parent": "bone", "x": 4.53, "y": -788.07, "scaleX": 1.0813, "scaleY": 1.0813}, {"name": "bone29", "parent": "yunc2", "x": -329.83, "y": 73.52, "color": "5bff00ff"}, {"name": "bone30", "parent": "yunc2", "x": -338.06, "y": 152.12, "color": "5bff00ff"}, {"name": "bone31", "parent": "yunc2", "x": -225.64, "y": 61.64, "color": "5bff00ff"}, {"name": "bone32", "parent": "yunc2", "x": -239.35, "y": 134.76, "color": "5bff00ff"}, {"name": "bone33", "parent": "yunc2", "x": -134.15, "y": 50.67, "color": "5bff00ff"}, {"name": "bone34", "parent": "yunc2", "x": 130.91, "y": 26.9, "color": "5bff00ff"}, {"name": "bone35", "parent": "yunc2", "x": 139.14, "y": 85.4, "color": "5bff00ff"}, {"name": "bone36", "parent": "yunc2", "x": 220.48, "y": 30.56, "color": "5bff00ff"}, {"name": "bone37", "parent": "yunc2", "x": 231.45, "y": 103.68, "color": "5bff00ff"}, {"name": "bone38", "parent": "yunc2", "x": 313.71, "y": 36.96, "color": "5bff00ff"}, {"name": "bone39", "parent": "yunc2", "x": 327.42, "y": 146.64, "color": "5bff00ff"}, {"name": "yunc3", "parent": "bone", "x": 4.53, "y": -788.07, "scaleX": 1.0813, "scaleY": 1.0813}, {"name": "bone40", "parent": "yunc3", "x": -329.83, "y": 73.52, "color": "5bff00ff"}, {"name": "bone41", "parent": "yunc3", "x": -338.06, "y": 152.12, "color": "5bff00ff"}, {"name": "bone42", "parent": "yunc3", "x": -225.64, "y": 61.64, "color": "5bff00ff"}, {"name": "bone43", "parent": "yunc3", "x": -239.35, "y": 134.76, "color": "5bff00ff"}, {"name": "bone44", "parent": "yunc3", "x": -134.15, "y": 50.67, "color": "5bff00ff"}, {"name": "bone45", "parent": "yunc3", "x": 130.91, "y": 26.9, "color": "5bff00ff"}, {"name": "bone46", "parent": "yunc3", "x": 139.14, "y": 85.4, "color": "5bff00ff"}, {"name": "bone47", "parent": "yunc3", "x": 220.48, "y": 30.56, "color": "5bff00ff"}, {"name": "bone48", "parent": "yunc3", "x": 231.45, "y": 103.68, "color": "5bff00ff"}, {"name": "bone49", "parent": "yunc3", "x": 313.71, "y": 36.96, "color": "5bff00ff"}, {"name": "bone50", "parent": "yunc3", "x": 327.42, "y": 146.64, "color": "5bff00ff"}, {"name": "y<PERSON><PERSON><PERSON>", "parent": "bone", "x": -288.27, "y": -24.87, "scaleX": 1.0535, "scaleY": 1.0535}, {"name": "bone51", "parent": "y<PERSON><PERSON><PERSON>", "x": 173.05, "y": 12.13}, {"name": "bone52", "parent": "y<PERSON><PERSON><PERSON>", "x": 115.4, "y": 54.31}, {"name": "bone53", "parent": "y<PERSON><PERSON><PERSON>", "x": 38.77, "y": 107.74}, {"name": "bone54", "parent": "y<PERSON><PERSON><PERSON>", "x": -39.96, "y": 162.58}, {"name": "bone55", "parent": "y<PERSON><PERSON><PERSON>", "x": -86.36, "y": 192.8}, {"name": "yunwuwu2", "parent": "bone", "x": -288.27, "y": -24.87, "scaleX": 1.0535, "scaleY": 1.0535}, {"name": "bone56", "parent": "yunwuwu2", "x": 173.05, "y": 12.13}, {"name": "bone57", "parent": "yunwuwu2", "x": 115.4, "y": 54.31}, {"name": "bone58", "parent": "yunwuwu2", "x": 38.77, "y": 107.74}, {"name": "bone59", "parent": "yunwuwu2", "x": -39.96, "y": 162.58}, {"name": "bone60", "parent": "yunwuwu2", "x": -86.36, "y": 192.8}, {"name": "yuwyuw1", "parent": "bone", "x": -6.2, "y": 779.92, "scaleX": 1.0553, "scaleY": 1.0553}, {"name": "bone61", "parent": "yuwyuw1", "x": -330.09, "y": -70.76, "color": "2bff00ff"}, {"name": "bone62", "parent": "yuwyuw1", "x": -331.87, "y": -140.45, "color": "2bff00ff"}, {"name": "bone63", "parent": "yuwyuw1", "x": -335.45, "y": -206.57, "color": "2bff00ff"}, {"name": "bone64", "parent": "yuwyuw1", "x": -224.65, "y": -63.61, "color": "2bff00ff"}, {"name": "bone65", "parent": "yuwyuw1", "x": -226.44, "y": -142.24, "color": "2bff00ff"}, {"name": "bone66", "parent": "yuwyuw1", "x": -230.02, "y": -199.42, "color": "2bff00ff"}, {"name": "bone67", "parent": "yuwyuw1", "x": -85.27, "y": -60.04, "color": "2bff00ff"}, {"name": "bone68", "parent": "yuwyuw1", "x": -85.27, "y": -145.81, "color": "2bff00ff"}, {"name": "bone69", "parent": "yuwyuw1", "x": -81.69, "y": -203, "color": "2bff00ff"}, {"name": "bone70", "parent": "yuwyuw1", "x": 38.03, "y": -61.82, "color": "2bff00ff"}, {"name": "bone71", "parent": "yuwyuw1", "x": 48.76, "y": -129.73, "color": "2bff00ff"}, {"name": "bone72", "parent": "yuwyuw1", "x": 50.54, "y": -195.85, "color": "2bff00ff"}, {"name": "bone73", "parent": "yuwyuw1", "x": 155.98, "y": -61.82, "color": "2bff00ff"}, {"name": "bone74", "parent": "yuwyuw1", "x": 161.34, "y": -126.16, "color": "2bff00ff"}, {"name": "bone75", "parent": "yuwyuw1", "x": 168.49, "y": -195.85, "color": "2bff00ff"}, {"name": "bone76", "parent": "yuwyuw1", "x": 284.64, "y": -68.97, "color": "2bff00ff"}, {"name": "bone77", "parent": "yuwyuw1", "x": 288.21, "y": -135.09, "color": "2bff00ff"}, {"name": "bone78", "parent": "yuwyuw1", "x": 286.43, "y": -188.7, "color": "2bff00ff"}, {"name": "yuwyuw2", "parent": "bone", "x": -6.2, "y": 779.92, "scaleX": 1.0553, "scaleY": 1.0553}, {"name": "bone79", "parent": "yuwyuw2", "x": -330.09, "y": -70.76, "color": "2bff00ff"}, {"name": "bone80", "parent": "yuwyuw2", "x": -331.87, "y": -140.45, "color": "2bff00ff"}, {"name": "bone81", "parent": "yuwyuw2", "x": -335.45, "y": -206.57, "color": "2bff00ff"}, {"name": "bone82", "parent": "yuwyuw2", "x": -224.65, "y": -63.61, "color": "2bff00ff"}, {"name": "bone83", "parent": "yuwyuw2", "x": -226.44, "y": -142.24, "color": "2bff00ff"}, {"name": "bone84", "parent": "yuwyuw2", "x": -230.02, "y": -199.42, "color": "2bff00ff"}, {"name": "bone85", "parent": "yuwyuw2", "x": -85.27, "y": -60.04, "color": "2bff00ff"}, {"name": "bone86", "parent": "yuwyuw2", "x": -85.27, "y": -145.81, "color": "2bff00ff"}, {"name": "bone87", "parent": "yuwyuw2", "x": -81.69, "y": -203, "color": "2bff00ff"}, {"name": "bone88", "parent": "yuwyuw2", "x": 38.03, "y": -61.82, "color": "2bff00ff"}, {"name": "bone89", "parent": "yuwyuw2", "x": 48.76, "y": -129.73, "color": "2bff00ff"}, {"name": "bone90", "parent": "yuwyuw2", "x": 50.54, "y": -195.85, "color": "2bff00ff"}, {"name": "bone91", "parent": "yuwyuw2", "x": 155.98, "y": -61.82, "color": "2bff00ff"}, {"name": "bone92", "parent": "yuwyuw2", "x": 161.34, "y": -126.16, "color": "2bff00ff"}, {"name": "bone93", "parent": "yuwyuw2", "x": 168.49, "y": -195.85, "color": "2bff00ff"}, {"name": "bone94", "parent": "yuwyuw2", "x": 284.64, "y": -68.97, "color": "2bff00ff"}, {"name": "bone95", "parent": "yuwyuw2", "x": 288.21, "y": -135.09, "color": "2bff00ff"}, {"name": "bone96", "parent": "yuwyuw2", "x": 286.43, "y": -188.7, "color": "2bff00ff"}, {"name": "yuwyuw3", "parent": "bone", "x": -6.2, "y": 779.92, "scaleX": 1.0553, "scaleY": 1.0553}, {"name": "bone97", "parent": "yuwyuw3", "x": -330.09, "y": -70.76, "color": "2bff00ff"}, {"name": "bone98", "parent": "yuwyuw3", "x": -331.87, "y": -140.45, "color": "2bff00ff"}, {"name": "bone99", "parent": "yuwyuw3", "x": -335.45, "y": -206.57, "color": "2bff00ff"}, {"name": "bone100", "parent": "yuwyuw3", "x": -224.65, "y": -63.61, "color": "2bff00ff"}, {"name": "bone101", "parent": "yuwyuw3", "x": -226.44, "y": -142.24, "color": "2bff00ff"}, {"name": "bone102", "parent": "yuwyuw3", "x": -230.02, "y": -199.42, "color": "2bff00ff"}, {"name": "bone103", "parent": "yuwyuw3", "x": -85.27, "y": -60.04, "color": "2bff00ff"}, {"name": "bone104", "parent": "yuwyuw3", "x": -85.27, "y": -145.81, "color": "2bff00ff"}, {"name": "bone105", "parent": "yuwyuw3", "x": -81.69, "y": -203, "color": "2bff00ff"}, {"name": "bone106", "parent": "yuwyuw3", "x": 38.03, "y": -61.82, "color": "2bff00ff"}, {"name": "bone107", "parent": "yuwyuw3", "x": 48.76, "y": -129.73, "color": "2bff00ff"}, {"name": "bone108", "parent": "yuwyuw3", "x": 50.54, "y": -195.85, "color": "2bff00ff"}, {"name": "bone109", "parent": "yuwyuw3", "x": 155.98, "y": -61.82, "color": "2bff00ff"}, {"name": "bone110", "parent": "yuwyuw3", "x": 161.34, "y": -126.16, "color": "2bff00ff"}, {"name": "bone111", "parent": "yuwyuw3", "x": 168.49, "y": -195.85, "color": "2bff00ff"}, {"name": "bone112", "parent": "yuwyuw3", "x": 284.64, "y": -68.97, "color": "2bff00ff"}, {"name": "bone113", "parent": "yuwyuw3", "x": 288.21, "y": -135.09, "color": "2bff00ff"}, {"name": "bone114", "parent": "yuwyuw3", "x": 286.43, "y": -188.7, "color": "2bff00ff"}], "slots": [{"name": "y4", "bone": "yunc1", "attachment": "y4"}, {"name": "y6", "bone": "yunc3", "attachment": "y4"}, {"name": "y5", "bone": "yunc2", "attachment": "y4"}, {"name": "y3", "bone": "yuwyuw1", "attachment": "y3"}, {"name": "y12", "bone": "yuwyuw3", "attachment": "y3"}, {"name": "y11", "bone": "yuwyuw2", "attachment": "y3"}, {"name": "y2", "bone": "yun2", "attachment": "y3"}, {"name": "y9", "bone": "yun2", "attachment": "y2"}, {"name": "y8", "bone": "yun4", "attachment": "y2"}, {"name": "y7", "bone": "yun3", "attachment": "y2"}, {"name": "y1", "bone": "y<PERSON><PERSON><PERSON>", "attachment": "y1"}, {"name": "y10", "bone": "yunwuwu2", "attachment": "y1"}], "skins": [{"name": "default", "attachments": {"y10": {"y1": {"type": "mesh", "uvs": [0.94772, 0.96069, 0.76623, 0.73402, 0.67898, 0.49288, 0.50796, 0.21799, 0.37534, 0, 0.13103, 0, 0, 0, 0, 0.26622, 0, 0.48806, 0.15197, 0.70508, 0.36138, 0.86906, 0.64059, 0.94622, 0.76972, 1, 0.89218, 1, 1, 1, 0.11749, 0.21573, 0.25393, 0.43302, 0.45743, 0.61197, 0.63318, 0.77813, 0.76499, 0.88038], "triangles": [15, 6, 5, 7, 6, 15, 4, 15, 5, 16, 4, 3, 16, 15, 4, 8, 7, 15, 8, 15, 16, 9, 8, 16, 17, 16, 3, 17, 3, 2, 9, 16, 17, 18, 17, 2, 10, 9, 17, 10, 17, 18, 18, 2, 1, 19, 18, 1, 11, 18, 19, 10, 18, 11, 19, 1, 0, 11, 19, 12, 13, 12, 19, 0, 13, 19, 13, 0, 14], "vertices": [1, 64, 18.99, 2.72, 1, 3, 64, -36.18, 52.59, 0.09592, 65, 21.47, 10.41, 0.89179, 66, 98.09, -43.02, 0.01229, 3, 65, -5.06, 63.46, 0.47435, 66, 71.57, 10.03, 0.52347, 67, 150.31, -44.8, 0.00218, 4, 65, -57.05, 123.93, 0.00951, 66, 19.58, 70.51, 0.61198, 67, 98.32, 15.67, 0.37843, 68, 144.71, -14.56, 7e-05, 3, 66, -20.74, 118.46, 0.223, 67, 58, 63.63, 0.72411, 68, 104.4, 33.4, 0.0529, 3, 66, -95.01, 118.46, 0.00611, 67, -16.27, 63.63, 0.32489, 68, 30.13, 33.4, 0.669, 2, 67, -56.1, 63.63, 0.0001, 68, -9.71, 33.4, 0.9999, 3, 66, -134.84, 59.9, 0.00052, 67, -56.1, 5.06, 0.3007, 68, -9.71, -25.17, 0.69878, 3, 66, -134.84, 11.09, 0.07459, 67, -56.1, -43.74, 0.73666, 68, -9.71, -73.97, 0.18874, 4, 65, -165.27, 16.77, 0.00389, 66, -88.64, -36.65, 0.45373, 67, -9.91, -91.49, 0.53732, 68, 36.49, -121.72, 0.00506, 3, 65, -101.61, -19.3, 0.21218, 66, -24.98, -72.73, 0.7124, 67, 53.75, -127.56, 0.07542, 3, 64, -74.37, 5.9, 0.0782, 65, -16.73, -36.28, 0.85269, 66, 59.9, -89.7, 0.06911, 2, 64, -35.12, -5.93, 0.54412, 65, 22.53, -48.11, 0.45588, 1, 64, 2.11, -5.93, 1, 1, 64, 34.89, -5.93, 1, 2, 67, -20.39, 16.17, 0.53168, 68, 26.01, -14.06, 0.46832, 2, 66, -57.65, 23.2, 0.3329, 67, 21.09, -31.64, 0.6671, 2, 65, -72.41, 37.26, 0.12339, 66, 4.22, -16.17, 0.87661, 2, 65, -18.98, 0.7, 0.85484, 66, 57.65, -52.72, 0.14516, 2, 64, -36.56, 20.39, 0.3704, 65, 21.09, -21.79, 0.6296], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 28, 12, 30, 30, 32, 32, 34, 34, 36, 36, 38, 24, 26, 26, 28, 38, 26], "width": 152, "height": 110}}, "y1": {"y1": {"type": "mesh", "uvs": [0.94772, 0.96069, 0.76623, 0.73402, 0.67898, 0.49288, 0.50796, 0.21799, 0.37534, 0, 0.13103, 0, 0, 0, 0, 0.26622, 0, 0.48806, 0.15197, 0.70508, 0.36138, 0.86906, 0.64059, 0.94622, 0.76972, 1, 0.89218, 1, 1, 1, 0.11749, 0.21573, 0.25393, 0.43302, 0.45743, 0.61197, 0.63318, 0.77813, 0.76499, 0.88038], "triangles": [15, 6, 5, 7, 6, 15, 4, 15, 5, 16, 4, 3, 16, 15, 4, 8, 7, 15, 8, 15, 16, 9, 8, 16, 17, 16, 3, 17, 3, 2, 9, 16, 17, 18, 17, 2, 10, 9, 17, 10, 17, 18, 18, 2, 1, 19, 18, 1, 11, 18, 19, 10, 18, 11, 19, 1, 0, 11, 19, 12, 13, 12, 19, 0, 13, 19, 13, 0, 14], "vertices": [1, 58, 18.99, 2.72, 1, 3, 58, -36.18, 52.59, 0.09592, 59, 21.47, 10.41, 0.89179, 60, 98.09, -43.02, 0.01229, 3, 59, -5.06, 63.46, 0.47435, 60, 71.57, 10.03, 0.52347, 61, 150.31, -44.8, 0.00218, 4, 59, -57.05, 123.93, 0.00951, 60, 19.58, 70.51, 0.61198, 61, 98.32, 15.67, 0.37843, 62, 144.71, -14.56, 7e-05, 3, 60, -20.74, 118.46, 0.223, 61, 58, 63.63, 0.72411, 62, 104.4, 33.4, 0.0529, 3, 60, -95.01, 118.46, 0.00611, 61, -16.27, 63.63, 0.32489, 62, 30.13, 33.4, 0.669, 2, 61, -56.1, 63.63, 0.0001, 62, -9.71, 33.4, 0.9999, 3, 60, -134.84, 59.9, 0.00052, 61, -56.1, 5.06, 0.3007, 62, -9.71, -25.17, 0.69878, 3, 60, -134.84, 11.09, 0.07459, 61, -56.1, -43.74, 0.73666, 62, -9.71, -73.97, 0.18874, 4, 59, -165.27, 16.77, 0.00389, 60, -88.64, -36.65, 0.45373, 61, -9.91, -91.49, 0.53732, 62, 36.49, -121.72, 0.00506, 3, 59, -101.61, -19.3, 0.21218, 60, -24.98, -72.73, 0.7124, 61, 53.75, -127.56, 0.07542, 3, 58, -74.37, 5.9, 0.0782, 59, -16.73, -36.28, 0.85269, 60, 59.9, -89.7, 0.06911, 2, 58, -35.12, -5.93, 0.54412, 59, 22.53, -48.11, 0.45588, 1, 58, 2.11, -5.93, 1, 1, 58, 34.89, -5.93, 1, 2, 61, -20.39, 16.17, 0.53168, 62, 26.01, -14.06, 0.46832, 2, 60, -57.65, 23.2, 0.3329, 61, 21.09, -31.64, 0.6671, 2, 59, -72.41, 37.26, 0.12339, 60, 4.22, -16.17, 0.87661, 2, 59, -18.98, 0.7, 0.85484, 60, 57.65, -52.72, 0.14516, 2, 58, -36.56, 20.39, 0.3704, 59, 21.09, -21.79, 0.6296], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 28, 12, 30, 30, 32, 32, 34, 34, 36, 36, 38, 24, 26, 26, 28, 38, 26], "width": 152, "height": 110}}, "y11": {"y3": {"type": "mesh", "uvs": [1, 1, 0.91667, 1, 0.83333, 1, 0.75, 1, 0.66667, 1, 0.58333, 1, 0.5, 1, 0.41667, 1, 0.33333, 1, 0.25, 1, 0.16667, 1, 0.08333, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.08333, 0, 0.16667, 0, 0.25, 0, 0.33333, 0, 0.41667, 0, 0.5, 0, 0.58333, 0, 0.66667, 0, 0.75, 0, 0.83333, 0, 0.91667, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.91667, 0.66667, 0.83333, 0.66667, 0.75, 0.66667, 0.66667, 0.66667, 0.58333, 0.66667, 0.5, 0.66667, 0.41667, 0.66667, 0.33333, 0.66667, 0.25, 0.66667, 0.16667, 0.66667, 0.08333, 0.66667, 0.91667, 0.33333, 0.83333, 0.33333, 0.75, 0.33333, 0.66667, 0.33333, 0.58333, 0.33333, 0.5, 0.33333, 0.41667, 0.33333, 0.33333, 0.33333, 0.25, 0.33333, 0.16667, 0.33333, 0.08333, 0.33333], "triangles": [51, 16, 17, 51, 14, 16, 14, 15, 16, 40, 51, 50, 13, 14, 51, 11, 40, 39, 39, 40, 50, 11, 12, 40, 12, 13, 40, 40, 13, 51, 49, 18, 19, 49, 50, 18, 50, 17, 18, 50, 51, 17, 38, 49, 48, 39, 50, 49, 9, 38, 37, 37, 38, 48, 9, 10, 38, 10, 39, 38, 38, 39, 49, 10, 11, 39, 47, 20, 21, 47, 48, 20, 48, 19, 20, 48, 49, 19, 36, 47, 46, 37, 48, 47, 6, 7, 35, 7, 36, 35, 35, 36, 46, 7, 8, 36, 8, 37, 36, 36, 37, 47, 8, 9, 37, 45, 22, 23, 46, 21, 22, 46, 47, 21, 34, 45, 44, 44, 45, 23, 35, 46, 45, 45, 46, 22, 4, 5, 33, 5, 34, 33, 33, 34, 44, 5, 6, 34, 6, 35, 34, 34, 35, 45, 43, 24, 25, 44, 23, 24, 32, 43, 42, 33, 44, 43, 43, 44, 24, 3, 32, 31, 31, 32, 42, 3, 4, 32, 4, 33, 32, 32, 33, 43, 41, 26, 27, 41, 42, 26, 42, 25, 26, 42, 43, 25, 30, 41, 28, 28, 41, 27, 31, 42, 41, 0, 1, 29, 1, 30, 29, 29, 30, 28, 1, 2, 30, 2, 31, 30, 30, 31, 41, 2, 3, 31], "vertices": [3, 106, 87.43, -90.24, 0.99055, 105, 85.65, -143.85, 0.00495, 103, 205.37, -83.09, 0.0045, 2, 106, 24.77, -90.24, 0.90152, 103, 142.71, -83.09, 0.09848, 3, 106, -37.9, -90.24, 0.59751, 103, 80.04, -83.09, 0.4002, 100, 197.98, -83.09, 0.00229, 3, 106, -100.57, -90.24, 0.20364, 103, 17.37, -83.09, 0.71465, 100, 135.32, -83.09, 0.08171, 4, 106, -163.23, -90.24, 0.01825, 103, -45.29, -83.09, 0.57725, 100, 72.65, -83.09, 0.40152, 97, 204.89, -75.94, 0.00298, 3, 103, -107.96, -83.09, 0.18889, 100, 9.98, -83.09, 0.72196, 97, 142.22, -75.94, 0.08915, 4, 103, -170.63, -83.09, 0.01502, 100, -52.68, -83.09, 0.57173, 97, 79.55, -75.94, 0.41309, 94, 227.88, -79.52, 0.00016, 3, 100, -115.35, -83.09, 0.18795, 97, 16.89, -75.94, 0.76285, 94, 165.21, -79.52, 0.0492, 5, 100, -178.02, -83.09, 0.01131, 97, -45.78, -75.94, 0.66279, 94, 102.54, -79.52, 0.32531, 93, 98.97, -136.7, 0.00058, 91, 207.98, -72.37, 1e-05, 4, 97, -108.45, -75.94, 0.27628, 96, -104.87, -133.13, 0.0003, 94, 39.88, -79.52, 0.69238, 91, 145.31, -72.37, 0.03103, 3, 97, -171.11, -75.94, 0.04036, 94, -22.79, -79.52, 0.68847, 91, 82.64, -72.37, 0.27116, 3, 97, -233.78, -75.94, 0.00021, 94, -85.46, -79.52, 0.26387, 91, 19.97, -72.37, 0.73593, 2, 94, -148.12, -79.52, 0.02487, 91, -42.69, -72.37, 0.97513, 2, 91, -42.69, 8.3, 0.79894, 90, -46.27, -57.82, 0.20106, 3, 91, -42.69, 88.96, 0.01613, 90, -46.27, 22.85, 0.6658, 89, -48.05, -46.85, 0.31806, 2, 90, -46.27, 103.51, 0.01371, 89, -48.05, 33.82, 0.98629, 3, 93, -89.03, 105.3, 0.00227, 92, -90.82, 26.67, 0.12708, 89, 14.61, 33.82, 0.87065, 4, 93, -26.37, 105.3, 0.00477, 92, -28.15, 26.67, 0.78013, 90, 79.07, 103.51, 0.00115, 89, 77.28, 33.82, 0.21395, 4, 96, -104.87, 108.87, 0.02823, 95, -104.87, 23.1, 0.17538, 93, 36.3, 105.3, 0.02824, 92, 34.51, 26.67, 0.76815, 4, 96, -42.21, 108.87, 0.04033, 95, -42.21, 23.1, 0.69284, 93, 98.97, 105.3, 0.03685, 92, 97.18, 26.67, 0.22999, 4, 99, -113.56, 92.79, 0.00745, 98, -102.84, 24.88, 0.11688, 96, 20.46, 108.87, 0.00757, 95, 20.46, 23.1, 0.8681, 4, 99, -50.9, 92.79, 0.02139, 98, -40.17, 24.88, 0.69311, 96, 83.13, 108.87, 0.02828, 95, 83.13, 23.1, 0.25723, 4, 102, -100.81, 89.22, 0.00623, 101, -95.45, 24.88, 0.13484, 99, 11.77, 92.79, 0.01829, 98, 22.49, 24.88, 0.84064, 4, 102, -38.14, 89.22, 0.01036, 101, -32.78, 24.88, 0.73626, 99, 74.44, 92.79, 0.04119, 98, 85.16, 24.88, 0.21219, 4, 105, -102.35, 98.15, 0.00417, 104, -98.78, 32.03, 0.16705, 102, 24.52, 89.22, 0.04221, 101, 29.88, 24.88, 0.78657, 4, 105, -39.69, 98.15, 0.00395, 104, -36.11, 32.03, 0.73012, 102, 87.19, 89.22, 0.05328, 101, 92.55, 24.88, 0.21265, 3, 105, 22.98, 98.15, 0.06539, 104, 26.55, 32.03, 0.93453, 101, 155.22, 24.88, 8e-05, 2, 105, 85.65, 98.15, 0.24366, 104, 89.22, 32.03, 0.75634, 3, 106, 87.43, 71.09, 0.12137, 105, 85.65, 17.48, 0.55376, 104, 89.22, -48.63, 0.32487, 3, 106, 87.43, -9.57, 0.74013, 105, 85.65, -63.18, 0.2474, 104, 89.22, -129.3, 0.01247, 4, 106, 24.77, -9.57, 0.89847, 105, 22.98, -63.18, 0.09332, 104, 26.55, -129.3, 0.00042, 103, 142.71, -2.42, 0.00779, 4, 106, -37.9, -9.57, 0.62712, 105, -39.69, -63.18, 0.05321, 103, 80.04, -2.42, 0.27875, 102, 87.19, -72.12, 0.04092, 4, 106, -100.57, -9.57, 0.10416, 105, -102.35, -63.18, 0.01569, 103, 17.37, -2.42, 0.83811, 102, 24.52, -72.12, 0.04204, 4, 103, -45.29, -2.42, 0.53741, 102, -38.14, -72.12, 0.09946, 100, 72.65, -2.42, 0.30923, 99, 74.44, -68.54, 0.0539, 4, 103, -107.96, -2.42, 0.04949, 102, -100.81, -72.12, 0.01097, 100, 9.98, -2.42, 0.92644, 99, 11.77, -68.54, 0.0131, 6, 100, -52.68, -2.42, 0.52296, 99, -50.9, -68.54, 0.10298, 98, -40.17, -136.45, 0.00089, 97, 79.55, 4.72, 0.28906, 96, 83.13, -52.46, 0.08335, 95, 83.13, -138.24, 0.00076, 5, 100, -115.35, -2.42, 0.08092, 99, -113.56, -68.54, 0.02197, 98, -102.84, -136.45, 8e-05, 97, 16.89, 4.72, 0.78509, 96, 20.46, -52.46, 0.11194, 6, 97, -45.78, 4.72, 0.53418, 96, -42.21, -52.46, 0.17902, 95, -42.21, -138.24, 0.0007, 94, 102.54, 1.15, 0.20414, 93, 98.97, -56.03, 0.08036, 92, 97.18, -134.66, 0.00159, 6, 97, -108.45, 4.72, 0.16235, 96, -104.87, -52.46, 0.06688, 95, -104.87, -138.24, 0.00116, 94, 39.88, 1.15, 0.60409, 93, 36.3, -56.03, 0.1654, 92, 34.51, -134.66, 0.00012, 4, 94, -22.79, 1.15, 0.73163, 93, -26.37, -56.03, 0.08998, 91, 82.64, 8.3, 0.13812, 90, 79.07, -57.82, 0.04027, 4, 94, -85.46, 1.15, 0.12269, 93, -89.03, -56.03, 0.03, 91, 19.97, 8.3, 0.74075, 90, 16.4, -57.82, 0.10656, 3, 106, 24.77, 71.09, 0.02885, 105, 22.98, 17.48, 0.71537, 104, 26.55, -48.63, 0.25578, 6, 106, -37.9, 71.09, 0.03794, 105, -39.69, 17.48, 0.46075, 104, -36.11, -48.63, 0.21866, 103, 80.04, 78.24, 0.03586, 102, 87.19, 8.55, 0.18898, 101, 92.55, -55.78, 0.05781, 6, 106, -100.57, 71.09, 0.01311, 105, -102.35, 17.48, 0.06823, 104, -98.78, -48.63, 0.05549, 103, 17.37, 78.24, 0.03431, 102, 24.52, 8.55, 0.71618, 101, 29.88, -55.78, 0.11269, 6, 103, -45.29, 78.24, 0.02485, 102, -38.14, 8.55, 0.52318, 101, -32.78, -55.78, 0.15607, 100, 72.65, 78.24, 0.02917, 99, 74.44, 12.12, 0.21159, 98, 85.16, -55.78, 0.05514, 7, 103, -107.96, 78.24, 0.00336, 102, -100.81, 8.55, 0.05231, 101, -95.45, -55.78, 0.03371, 100, 9.98, 78.24, 0.00376, 99, 11.77, 12.12, 0.7784, 98, 22.49, -55.78, 0.12835, 95, 145.79, -57.57, 0.00011, 6, 100, -52.68, 78.24, 0.04757, 99, -50.9, 12.12, 0.42134, 98, -40.17, -55.78, 0.20566, 97, 79.55, 85.39, 0.02278, 96, 83.13, 28.21, 0.19758, 95, 83.13, -57.57, 0.10507, 8, 100, -115.35, 78.24, 0.00928, 99, -113.56, 12.12, 0.06842, 98, -102.84, -55.78, 0.05395, 97, 16.89, 85.39, 0.00332, 96, 20.46, 28.21, 0.61146, 95, 20.46, -57.57, 0.25028, 93, 161.63, 24.63, 0.00171, 92, 159.85, -54, 0.00159, 6, 97, -45.78, 85.39, 0.016, 96, -42.21, 28.21, 0.47738, 95, -42.21, -57.57, 0.23475, 94, 102.54, 81.82, 0.01798, 93, 98.97, 24.63, 0.15348, 92, 97.18, -54, 0.1004, 6, 97, -108.45, 85.39, 0.01194, 96, -104.87, 28.21, 0.12264, 95, -104.87, -57.57, 0.07683, 94, 39.88, 81.82, 0.01742, 93, 36.3, 24.63, 0.5273, 92, 34.51, -54, 0.24387, 8, 96, -167.54, 28.21, 3e-05, 95, -167.54, -57.57, 0.00012, 94, -22.79, 81.82, 0.00499, 93, -26.37, 24.63, 0.57177, 92, -28.15, -54, 0.21741, 91, 82.64, 88.96, 0.00364, 90, 79.07, 22.85, 0.12451, 89, 77.28, -46.85, 0.07754, 6, 94, -85.46, 81.82, 0.00373, 93, -89.03, 24.63, 0.08701, 92, -90.82, -54, 0.04224, 91, 19.97, 88.96, 0.00126, 90, 16.4, 22.85, 0.61412, 89, 14.61, -46.85, 0.25165], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 376, "height": 121}}, "y3": {"y3": {"type": "mesh", "uvs": [1, 1, 0.91667, 1, 0.83333, 1, 0.75, 1, 0.66667, 1, 0.58333, 1, 0.5, 1, 0.41667, 1, 0.33333, 1, 0.25, 1, 0.16667, 1, 0.08333, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.08333, 0, 0.16667, 0, 0.25, 0, 0.33333, 0, 0.41667, 0, 0.5, 0, 0.58333, 0, 0.66667, 0, 0.75, 0, 0.83333, 0, 0.91667, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.91667, 0.66667, 0.83333, 0.66667, 0.75, 0.66667, 0.66667, 0.66667, 0.58333, 0.66667, 0.5, 0.66667, 0.41667, 0.66667, 0.33333, 0.66667, 0.25, 0.66667, 0.16667, 0.66667, 0.08333, 0.66667, 0.91667, 0.33333, 0.83333, 0.33333, 0.75, 0.33333, 0.66667, 0.33333, 0.58333, 0.33333, 0.5, 0.33333, 0.41667, 0.33333, 0.33333, 0.33333, 0.25, 0.33333, 0.16667, 0.33333, 0.08333, 0.33333], "triangles": [51, 16, 17, 51, 14, 16, 14, 15, 16, 40, 51, 50, 13, 14, 51, 11, 40, 39, 39, 40, 50, 11, 12, 40, 12, 13, 40, 40, 13, 51, 49, 18, 19, 49, 50, 18, 50, 17, 18, 50, 51, 17, 38, 49, 48, 39, 50, 49, 9, 38, 37, 37, 38, 48, 9, 10, 38, 10, 39, 38, 38, 39, 49, 10, 11, 39, 47, 20, 21, 47, 48, 20, 48, 19, 20, 48, 49, 19, 36, 47, 46, 37, 48, 47, 6, 7, 35, 7, 36, 35, 35, 36, 46, 7, 8, 36, 8, 37, 36, 36, 37, 47, 8, 9, 37, 45, 22, 23, 46, 21, 22, 46, 47, 21, 34, 45, 44, 44, 45, 23, 35, 46, 45, 45, 46, 22, 4, 5, 33, 5, 34, 33, 33, 34, 44, 5, 6, 34, 6, 35, 34, 34, 35, 45, 43, 24, 25, 44, 23, 24, 32, 43, 42, 33, 44, 43, 43, 44, 24, 3, 32, 31, 31, 32, 42, 3, 4, 32, 4, 33, 32, 32, 33, 43, 41, 26, 27, 41, 42, 26, 42, 25, 26, 42, 43, 25, 30, 41, 28, 28, 41, 27, 31, 42, 41, 0, 1, 29, 1, 30, 29, 29, 30, 28, 1, 2, 30, 2, 31, 30, 30, 31, 41, 2, 3, 31], "vertices": [3, 87, 87.43, -90.24, 0.99055, 86, 85.65, -143.85, 0.00495, 84, 205.37, -83.09, 0.0045, 2, 87, 24.77, -90.24, 0.90152, 84, 142.71, -83.09, 0.09848, 3, 87, -37.9, -90.24, 0.59751, 84, 80.04, -83.09, 0.4002, 81, 197.98, -83.09, 0.00229, 3, 87, -100.57, -90.24, 0.20364, 84, 17.37, -83.09, 0.71465, 81, 135.32, -83.09, 0.08171, 4, 87, -163.23, -90.24, 0.01825, 84, -45.29, -83.09, 0.57725, 81, 72.65, -83.09, 0.40152, 78, 204.89, -75.94, 0.00298, 3, 84, -107.96, -83.09, 0.18889, 81, 9.98, -83.09, 0.72196, 78, 142.22, -75.94, 0.08915, 4, 84, -170.63, -83.09, 0.01502, 81, -52.68, -83.09, 0.57173, 78, 79.55, -75.94, 0.41309, 75, 227.88, -79.52, 0.00016, 3, 81, -115.35, -83.09, 0.18795, 78, 16.89, -75.94, 0.76285, 75, 165.21, -79.52, 0.0492, 5, 81, -178.02, -83.09, 0.01131, 78, -45.78, -75.94, 0.66279, 75, 102.54, -79.52, 0.32531, 74, 98.97, -136.7, 0.00058, 72, 207.98, -72.37, 1e-05, 4, 78, -108.45, -75.94, 0.27628, 77, -104.87, -133.13, 0.0003, 75, 39.88, -79.52, 0.69238, 72, 145.31, -72.37, 0.03103, 3, 78, -171.11, -75.94, 0.04036, 75, -22.79, -79.52, 0.68847, 72, 82.64, -72.37, 0.27116, 3, 78, -233.78, -75.94, 0.00021, 75, -85.46, -79.52, 0.26387, 72, 19.97, -72.37, 0.73593, 2, 75, -148.12, -79.52, 0.02487, 72, -42.69, -72.37, 0.97513, 2, 72, -42.69, 8.3, 0.79894, 71, -46.27, -57.82, 0.20106, 3, 72, -42.69, 88.96, 0.01613, 71, -46.27, 22.85, 0.6658, 70, -48.05, -46.85, 0.31806, 2, 71, -46.27, 103.51, 0.01371, 70, -48.05, 33.82, 0.98629, 3, 74, -89.03, 105.3, 0.00227, 73, -90.82, 26.67, 0.12708, 70, 14.61, 33.82, 0.87065, 4, 74, -26.37, 105.3, 0.00477, 73, -28.15, 26.67, 0.78013, 71, 79.07, 103.51, 0.00115, 70, 77.28, 33.82, 0.21395, 4, 77, -104.87, 108.87, 0.02823, 76, -104.87, 23.1, 0.17538, 74, 36.3, 105.3, 0.02824, 73, 34.51, 26.67, 0.76815, 4, 77, -42.21, 108.87, 0.04033, 76, -42.21, 23.1, 0.69284, 74, 98.97, 105.3, 0.03685, 73, 97.18, 26.67, 0.22999, 4, 80, -113.56, 92.79, 0.00745, 79, -102.84, 24.88, 0.11688, 77, 20.46, 108.87, 0.00757, 76, 20.46, 23.1, 0.8681, 4, 80, -50.9, 92.79, 0.02139, 79, -40.17, 24.88, 0.69311, 77, 83.13, 108.87, 0.02828, 76, 83.13, 23.1, 0.25723, 4, 83, -100.81, 89.22, 0.00623, 82, -95.45, 24.88, 0.13484, 80, 11.77, 92.79, 0.01829, 79, 22.49, 24.88, 0.84064, 4, 83, -38.14, 89.22, 0.01036, 82, -32.78, 24.88, 0.73626, 80, 74.44, 92.79, 0.04119, 79, 85.16, 24.88, 0.21219, 4, 86, -102.35, 98.15, 0.00417, 85, -98.78, 32.03, 0.16705, 83, 24.52, 89.22, 0.04221, 82, 29.88, 24.88, 0.78657, 4, 86, -39.69, 98.15, 0.00395, 85, -36.11, 32.03, 0.73012, 83, 87.19, 89.22, 0.05328, 82, 92.55, 24.88, 0.21265, 3, 86, 22.98, 98.15, 0.06539, 85, 26.55, 32.03, 0.93453, 82, 155.22, 24.88, 8e-05, 2, 86, 85.65, 98.15, 0.24366, 85, 89.22, 32.03, 0.75634, 3, 87, 87.43, 71.09, 0.12137, 86, 85.65, 17.48, 0.55376, 85, 89.22, -48.63, 0.32487, 3, 87, 87.43, -9.57, 0.74013, 86, 85.65, -63.18, 0.2474, 85, 89.22, -129.3, 0.01247, 4, 87, 24.77, -9.57, 0.89847, 86, 22.98, -63.18, 0.09332, 85, 26.55, -129.3, 0.00042, 84, 142.71, -2.42, 0.00779, 4, 87, -37.9, -9.57, 0.62712, 86, -39.69, -63.18, 0.05321, 84, 80.04, -2.42, 0.27875, 83, 87.19, -72.12, 0.04092, 4, 87, -100.57, -9.57, 0.10416, 86, -102.35, -63.18, 0.01569, 84, 17.37, -2.42, 0.83811, 83, 24.52, -72.12, 0.04204, 4, 84, -45.29, -2.42, 0.53741, 83, -38.14, -72.12, 0.09946, 81, 72.65, -2.42, 0.30923, 80, 74.44, -68.54, 0.0539, 4, 84, -107.96, -2.42, 0.04949, 83, -100.81, -72.12, 0.01097, 81, 9.98, -2.42, 0.92644, 80, 11.77, -68.54, 0.0131, 6, 81, -52.68, -2.42, 0.52296, 80, -50.9, -68.54, 0.10298, 79, -40.17, -136.45, 0.00089, 78, 79.55, 4.72, 0.28906, 77, 83.13, -52.46, 0.08335, 76, 83.13, -138.24, 0.00076, 5, 81, -115.35, -2.42, 0.08092, 80, -113.56, -68.54, 0.02197, 79, -102.84, -136.45, 8e-05, 78, 16.89, 4.72, 0.78509, 77, 20.46, -52.46, 0.11194, 6, 78, -45.78, 4.72, 0.53418, 77, -42.21, -52.46, 0.17902, 76, -42.21, -138.24, 0.0007, 75, 102.54, 1.15, 0.20414, 74, 98.97, -56.03, 0.08036, 73, 97.18, -134.66, 0.00159, 6, 78, -108.45, 4.72, 0.16235, 77, -104.87, -52.46, 0.06688, 76, -104.87, -138.24, 0.00116, 75, 39.88, 1.15, 0.60409, 74, 36.3, -56.03, 0.1654, 73, 34.51, -134.66, 0.00012, 4, 75, -22.79, 1.15, 0.73163, 74, -26.37, -56.03, 0.08998, 72, 82.64, 8.3, 0.13812, 71, 79.07, -57.82, 0.04027, 4, 75, -85.46, 1.15, 0.12269, 74, -89.03, -56.03, 0.03, 72, 19.97, 8.3, 0.74075, 71, 16.4, -57.82, 0.10656, 3, 87, 24.77, 71.09, 0.02885, 86, 22.98, 17.48, 0.71537, 85, 26.55, -48.63, 0.25578, 6, 87, -37.9, 71.09, 0.03794, 86, -39.69, 17.48, 0.46075, 85, -36.11, -48.63, 0.21866, 84, 80.04, 78.24, 0.03586, 83, 87.19, 8.55, 0.18898, 82, 92.55, -55.78, 0.05781, 6, 87, -100.57, 71.09, 0.01311, 86, -102.35, 17.48, 0.06823, 85, -98.78, -48.63, 0.05549, 84, 17.37, 78.24, 0.03431, 83, 24.52, 8.55, 0.71618, 82, 29.88, -55.78, 0.11269, 6, 84, -45.29, 78.24, 0.02485, 83, -38.14, 8.55, 0.52318, 82, -32.78, -55.78, 0.15607, 81, 72.65, 78.24, 0.02917, 80, 74.44, 12.12, 0.21159, 79, 85.16, -55.78, 0.05514, 7, 84, -107.96, 78.24, 0.00336, 83, -100.81, 8.55, 0.05231, 82, -95.45, -55.78, 0.03371, 81, 9.98, 78.24, 0.00376, 80, 11.77, 12.12, 0.7784, 79, 22.49, -55.78, 0.12835, 76, 145.79, -57.57, 0.00011, 6, 81, -52.68, 78.24, 0.04757, 80, -50.9, 12.12, 0.42134, 79, -40.17, -55.78, 0.20566, 78, 79.55, 85.39, 0.02278, 77, 83.13, 28.21, 0.19758, 76, 83.13, -57.57, 0.10507, 8, 81, -115.35, 78.24, 0.00928, 80, -113.56, 12.12, 0.06842, 79, -102.84, -55.78, 0.05395, 78, 16.89, 85.39, 0.00332, 77, 20.46, 28.21, 0.61146, 76, 20.46, -57.57, 0.25028, 74, 161.63, 24.63, 0.00171, 73, 159.85, -54, 0.00159, 6, 78, -45.78, 85.39, 0.016, 77, -42.21, 28.21, 0.47738, 76, -42.21, -57.57, 0.23475, 75, 102.54, 81.82, 0.01798, 74, 98.97, 24.63, 0.15348, 73, 97.18, -54, 0.1004, 6, 78, -108.45, 85.39, 0.01194, 77, -104.87, 28.21, 0.12264, 76, -104.87, -57.57, 0.07683, 75, 39.88, 81.82, 0.01742, 74, 36.3, 24.63, 0.5273, 73, 34.51, -54, 0.24387, 8, 77, -167.54, 28.21, 3e-05, 76, -167.54, -57.57, 0.00012, 75, -22.79, 81.82, 0.00499, 74, -26.37, 24.63, 0.57177, 73, -28.15, -54, 0.21741, 72, 82.64, 88.96, 0.00364, 71, 79.07, 22.85, 0.12451, 70, 77.28, -46.85, 0.07754, 6, 75, -85.46, 81.82, 0.00373, 74, -89.03, 24.63, 0.08701, 73, -90.82, -54, 0.04224, 72, 19.97, 88.96, 0.00126, 71, 16.4, 22.85, 0.61412, 70, 14.61, -46.85, 0.25165], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 376, "height": 121}}, "y4": {"y4": {"type": "mesh", "uvs": [0, 1, 0.12195, 1, 0.26894, 1, 0.41833, 1, 0.50787, 1, 0.59943, 1, 0.70184, 1, 0.82352, 1, 0.91147, 1, 1, 1, 1, 0.80913, 1, 0.50954, 1, 0.08047, 0.93827, 0.12275, 0.86868, 0.21487, 0.83146, 0.36518, 0.75378, 0.45731, 0.66639, 0.43791, 0.59356, 0.50094, 0.59033, 0.67064, 0.62269, 0.77731, 0.5968, 0.84519, 0.49808, 0.80156, 0.42525, 0.66095, 0.34595, 0.56882, 0.43496, 0.56882, 0.40907, 0.41852, 0.2974, 0.35548, 0.22296, 0.22457, 0.16308, 0.07426, 0.09673, 0, 0, 0, 0, 0.28276, 0, 0.60276, 0.04846, 0.16916, 0.07496, 0.34242, 0.09786, 0.56621, 0.1099, 0.80083, 0.27978, 0.79, 0.22436, 0.54094, 0.15328, 0.29188, 0.39664, 0.85497, 0.70666, 0.77304, 0.66931, 0.58895, 0.8175, 0.75138, 0.93195, 0.49871, 0.91147, 0.81635], "triangles": [45, 11, 10, 45, 13, 11, 13, 12, 11, 15, 14, 45, 45, 14, 13, 8, 46, 9, 46, 10, 9, 8, 7, 46, 10, 46, 45, 46, 44, 45, 44, 15, 45, 44, 16, 15, 42, 44, 7, 7, 44, 46, 20, 43, 42, 20, 19, 43, 42, 16, 44, 42, 43, 16, 19, 18, 43, 18, 17, 43, 43, 17, 16, 6, 42, 7, 5, 20, 6, 6, 20, 42, 4, 21, 5, 5, 21, 20, 4, 22, 21, 3, 22, 4, 2, 41, 3, 3, 41, 22, 2, 38, 41, 38, 24, 41, 41, 23, 22, 41, 24, 23, 38, 39, 24, 24, 26, 25, 24, 27, 26, 39, 27, 24, 36, 40, 39, 40, 28, 39, 39, 28, 27, 40, 29, 28, 2, 1, 38, 37, 39, 38, 33, 35, 36, 33, 32, 35, 36, 35, 40, 32, 34, 35, 35, 34, 40, 34, 30, 40, 40, 30, 29, 32, 31, 34, 34, 31, 30, 0, 37, 1, 1, 37, 38, 37, 33, 36, 37, 0, 33, 37, 36, 39], "vertices": [2, 22, -59.03, -73.35, 0.98384, 24, -163.23, -61.46, 0.01616, 2, 22, 32.68, -73.35, 0.58961, 24, -71.52, -61.46, 0.41039, 3, 22, 143.21, -73.35, 0.00626, 24, 39.01, -61.46, 0.58565, 26, -52.47, -50.5, 0.40808, 2, 26, 59.87, -50.5, 0.82323, 27, -205.19, -26.73, 0.17677, 2, 26, 127.2, -50.5, 0.53891, 27, -137.86, -26.73, 0.46109, 3, 26, 196.06, -50.5, 0.21603, 27, -69, -26.73, 0.76941, 28, -77.23, -85.23, 0.01456, 3, 26, 273.07, -50.5, 0.00138, 27, 8.01, -26.73, 0.9103, 29, -81.56, -30.39, 0.08831, 3, 27, 99.51, -26.73, 0.01178, 29, 9.94, -30.39, 0.90648, 31, -83.29, -36.79, 0.08173, 3, 29, 76.08, -30.39, 0.19869, 30, 65.11, -103.51, 0.00071, 31, -17.15, -36.79, 0.80059, 2, 31, 49.43, -36.79, 0.99982, 32, 35.72, -146.47, 0.00018, 2, 31, 49.43, 11.12, 0.87381, 32, 35.72, -98.56, 0.12619, 2, 31, 49.43, 86.32, 0.17094, 32, 35.72, -23.36, 0.82906, 1, 32, 35.72, 84.34, 1, 2, 30, 85.27, 116.68, 0.05811, 32, -10.7, 73.72, 0.94189, 2, 30, 32.93, 93.56, 0.33255, 32, -63.04, 50.6, 0.66745, 4, 28, 97.26, 74.11, 0.01478, 30, 4.94, 55.83, 0.69782, 31, -77.32, 122.55, 2e-05, 32, -91.03, 12.87, 0.28738, 4, 28, 38.84, 50.99, 0.48597, 29, -42.5, 105.83, 0.00192, 30, -53.47, 32.71, 0.50945, 32, -149.44, -10.25, 0.00266, 3, 27, -18.65, 114.35, 0.00209, 28, -26.88, 55.86, 0.96891, 30, -119.19, 37.58, 0.029, 3, 26, 191.64, 74.77, 0.00013, 27, -73.42, 98.53, 0.06948, 28, -81.64, 40.04, 0.93038, 3, 26, 189.21, 32.17, 0.00732, 27, -75.85, 55.94, 0.18794, 28, -84.08, -2.56, 0.80474, 3, 26, 213.55, 5.4, 0.05527, 27, -51.51, 29.16, 0.53281, 28, -59.74, -29.33, 0.41192, 3, 26, 194.08, -11.64, 0.20089, 27, -70.98, 12.13, 0.72052, 28, -79.21, -46.37, 0.0786, 2, 26, 119.84, -0.69, 0.61141, 27, -145.22, 23.08, 0.38859, 3, 25, 170.27, -49.48, 0.01159, 26, 65.08, 34.61, 0.86596, 27, -199.98, 58.37, 0.12245, 4, 24, 96.93, 46.76, 0.03365, 25, 110.64, -26.36, 0.32661, 26, 5.44, 57.73, 0.63642, 27, -259.62, 81.49, 0.00331, 2, 25, 177.57, -26.36, 0.42617, 26, 72.38, 57.73, 0.57383, 2, 25, 158.1, 11.37, 0.43223, 26, 52.91, 95.46, 0.56777, 4, 23, 172.84, 9.82, 0.00064, 24, 60.42, 100.31, 0.0347, 25, 74.13, 27.19, 0.66993, 26, -31.07, 111.28, 0.29473, 3, 23, 116.86, 42.68, 0.16227, 25, 18.15, 60.05, 0.81567, 26, -87.05, 144.14, 0.02206, 2, 23, 71.83, 80.41, 0.53714, 25, -26.88, 97.78, 0.46286, 2, 23, 21.93, 99.05, 0.79861, 25, -76.78, 116.42, 0.20139, 2, 23, -50.81, 99.05, 0.96944, 25, -149.52, 116.42, 0.03056, 3, 22, -59.03, 106.68, 0.00793, 23, -50.81, 28.08, 0.99046, 25, -149.52, 45.45, 0.00161, 2, 22, -59.03, 26.36, 0.61729, 23, -50.81, -52.24, 0.38271, 2, 23, -14.36, 56.59, 0.93658, 25, -113.08, 73.96, 0.06342, 2, 23, 5.57, 13.1, 0.9449, 25, -93.14, 30.47, 0.0551, 4, 22, 14.56, 35.54, 0.49021, 23, 22.78, -43.07, 0.3424, 24, -89.64, 47.42, 0.0385, 25, -75.93, -25.7, 0.12889, 3, 22, 23.62, -23.35, 0.73457, 24, -80.58, -11.47, 0.24962, 25, -66.87, -84.59, 0.0158, 3, 24, 47.17, -8.75, 0.48187, 25, 60.88, -81.87, 0.0428, 26, -44.32, 2.21, 0.47534, 4, 22, 109.69, 41.88, 0.0044, 24, 5.49, 53.76, 0.23017, 25, 19.2, -19.36, 0.69934, 26, -86, 64.73, 0.06609, 3, 22, 56.23, 104.39, 0.00303, 23, 64.46, 25.79, 0.42788, 25, -34.25, 43.15, 0.56909, 3, 25, 148.76, -98.18, 0.00036, 26, 43.56, -14.09, 0.8911, 27, -221.5, 9.67, 0.10854, 5, 26, 276.69, 6.47, 0.00033, 27, 11.63, 30.24, 0.42589, 28, 3.41, -28.26, 0.48905, 29, -77.94, 26.58, 0.0717, 30, -88.91, -46.54, 0.01302, 4, 26, 248.61, 52.68, 0.00077, 27, -16.45, 76.44, 0.03694, 28, -24.68, 17.95, 0.95671, 30, -116.99, -0.33, 0.00558, 5, 27, 94.98, 35.67, 0.00578, 28, 86.76, -22.82, 0.04423, 29, 5.41, 32.02, 0.52157, 30, -5.56, -41.1, 0.38647, 31, -87.82, 25.62, 0.04195, 4, 29, 91.48, 95.44, 0.00049, 30, 80.51, 22.32, 0.15313, 31, -1.75, 89.04, 0.11368, 32, -15.46, -20.64, 0.73271, 4, 29, 76.08, 15.71, 0.12127, 30, 65.11, -57.41, 0.08282, 31, -17.15, 9.31, 0.76299, 32, -30.86, -100.37, 0.03292], "hull": 34, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 376, "height": 125}}, "y5": {"y4": {"type": "mesh", "uvs": [0, 1, 0.12195, 1, 0.26894, 1, 0.41833, 1, 0.50787, 1, 0.59943, 1, 0.70184, 1, 0.82352, 1, 0.91147, 1, 1, 1, 1, 0.80913, 1, 0.50954, 1, 0.08047, 0.93827, 0.12275, 0.86868, 0.21487, 0.83146, 0.36518, 0.75378, 0.45731, 0.66639, 0.43791, 0.59356, 0.50094, 0.59033, 0.67064, 0.62269, 0.77731, 0.5968, 0.84519, 0.49808, 0.80156, 0.42525, 0.66095, 0.34595, 0.56882, 0.43496, 0.56882, 0.40907, 0.41852, 0.2974, 0.35548, 0.22296, 0.22457, 0.16308, 0.07426, 0.09673, 0, 0, 0, 0, 0.28276, 0, 0.60276, 0.04846, 0.16916, 0.07496, 0.34242, 0.09786, 0.56621, 0.1099, 0.80083, 0.27978, 0.79, 0.22436, 0.54094, 0.15328, 0.29188, 0.39664, 0.85497, 0.70666, 0.77304, 0.66931, 0.58895, 0.8175, 0.75138, 0.93195, 0.49871, 0.91147, 0.81635], "triangles": [45, 11, 10, 45, 13, 11, 13, 12, 11, 15, 14, 45, 45, 14, 13, 8, 46, 9, 46, 10, 9, 8, 7, 46, 10, 46, 45, 46, 44, 45, 44, 15, 45, 44, 16, 15, 42, 44, 7, 7, 44, 46, 20, 43, 42, 20, 19, 43, 42, 16, 44, 42, 43, 16, 19, 18, 43, 18, 17, 43, 43, 17, 16, 6, 42, 7, 5, 20, 6, 6, 20, 42, 4, 21, 5, 5, 21, 20, 4, 22, 21, 3, 22, 4, 2, 41, 3, 3, 41, 22, 2, 38, 41, 38, 24, 41, 41, 23, 22, 41, 24, 23, 38, 39, 24, 24, 26, 25, 24, 27, 26, 39, 27, 24, 36, 40, 39, 40, 28, 39, 39, 28, 27, 40, 29, 28, 2, 1, 38, 37, 39, 38, 33, 35, 36, 33, 32, 35, 36, 35, 40, 32, 34, 35, 35, 34, 40, 34, 30, 40, 40, 30, 29, 32, 31, 34, 34, 31, 30, 0, 37, 1, 1, 37, 38, 37, 33, 36, 37, 0, 33, 37, 36, 39], "vertices": [2, 34, -59.03, -73.35, 0.98384, 36, -163.23, -61.46, 0.01616, 2, 34, 32.68, -73.35, 0.58961, 36, -71.52, -61.46, 0.41039, 3, 34, 143.21, -73.35, 0.00626, 36, 39.01, -61.46, 0.58565, 38, -52.47, -50.5, 0.40808, 2, 38, 59.87, -50.5, 0.82323, 39, -205.19, -26.73, 0.17677, 2, 38, 127.2, -50.5, 0.53891, 39, -137.86, -26.73, 0.46109, 3, 38, 196.06, -50.5, 0.21603, 39, -69, -26.73, 0.76941, 40, -77.23, -85.23, 0.01456, 3, 38, 273.07, -50.5, 0.00138, 39, 8.01, -26.73, 0.9103, 41, -81.56, -30.39, 0.08831, 3, 39, 99.51, -26.73, 0.01178, 41, 9.94, -30.39, 0.90648, 43, -83.29, -36.79, 0.08173, 3, 41, 76.08, -30.39, 0.19869, 42, 65.11, -103.51, 0.00071, 43, -17.15, -36.79, 0.80059, 2, 43, 49.43, -36.79, 0.99982, 44, 35.72, -146.47, 0.00018, 2, 43, 49.43, 11.12, 0.87381, 44, 35.72, -98.56, 0.12619, 2, 43, 49.43, 86.32, 0.17094, 44, 35.72, -23.36, 0.82906, 1, 44, 35.72, 84.34, 1, 2, 42, 85.27, 116.68, 0.05811, 44, -10.7, 73.72, 0.94189, 2, 42, 32.93, 93.56, 0.33255, 44, -63.04, 50.6, 0.66745, 4, 40, 97.26, 74.11, 0.01478, 42, 4.94, 55.83, 0.69782, 43, -77.32, 122.55, 2e-05, 44, -91.03, 12.87, 0.28738, 4, 40, 38.84, 50.99, 0.48597, 41, -42.5, 105.83, 0.00192, 42, -53.47, 32.71, 0.50945, 44, -149.44, -10.25, 0.00266, 3, 39, -18.65, 114.35, 0.00209, 40, -26.88, 55.86, 0.96891, 42, -119.19, 37.58, 0.029, 3, 38, 191.64, 74.77, 0.00013, 39, -73.42, 98.53, 0.06948, 40, -81.64, 40.04, 0.93038, 3, 38, 189.21, 32.17, 0.00732, 39, -75.85, 55.94, 0.18794, 40, -84.08, -2.56, 0.80474, 3, 38, 213.55, 5.4, 0.05527, 39, -51.51, 29.16, 0.53281, 40, -59.74, -29.33, 0.41192, 3, 38, 194.08, -11.64, 0.20089, 39, -70.98, 12.13, 0.72052, 40, -79.21, -46.37, 0.0786, 2, 38, 119.84, -0.69, 0.61141, 39, -145.22, 23.08, 0.38859, 3, 37, 170.27, -49.48, 0.01159, 38, 65.08, 34.61, 0.86596, 39, -199.98, 58.37, 0.12245, 4, 36, 96.93, 46.76, 0.03365, 37, 110.64, -26.36, 0.32661, 38, 5.44, 57.73, 0.63642, 39, -259.62, 81.49, 0.00331, 2, 37, 177.57, -26.36, 0.42617, 38, 72.38, 57.73, 0.57383, 2, 37, 158.1, 11.37, 0.43223, 38, 52.91, 95.46, 0.56777, 4, 35, 172.84, 9.82, 0.00064, 36, 60.42, 100.31, 0.0347, 37, 74.13, 27.19, 0.66993, 38, -31.07, 111.28, 0.29473, 3, 35, 116.86, 42.68, 0.16227, 37, 18.15, 60.05, 0.81567, 38, -87.05, 144.14, 0.02206, 2, 35, 71.83, 80.41, 0.53714, 37, -26.88, 97.78, 0.46286, 2, 35, 21.93, 99.05, 0.79861, 37, -76.78, 116.42, 0.20139, 2, 35, -50.81, 99.05, 0.96944, 37, -149.52, 116.42, 0.03056, 3, 34, -59.03, 106.68, 0.00793, 35, -50.81, 28.08, 0.99046, 37, -149.52, 45.45, 0.00161, 2, 34, -59.03, 26.36, 0.61729, 35, -50.81, -52.24, 0.38271, 2, 35, -14.36, 56.59, 0.93658, 37, -113.08, 73.96, 0.06342, 2, 35, 5.57, 13.1, 0.9449, 37, -93.14, 30.47, 0.0551, 4, 34, 14.56, 35.54, 0.49021, 35, 22.78, -43.07, 0.3424, 36, -89.64, 47.42, 0.0385, 37, -75.93, -25.7, 0.12889, 3, 34, 23.62, -23.35, 0.73457, 36, -80.58, -11.47, 0.24962, 37, -66.87, -84.59, 0.0158, 3, 36, 47.17, -8.75, 0.48187, 37, 60.88, -81.87, 0.0428, 38, -44.32, 2.21, 0.47534, 4, 34, 109.69, 41.88, 0.0044, 36, 5.49, 53.76, 0.23017, 37, 19.2, -19.36, 0.69934, 38, -86, 64.73, 0.06609, 3, 34, 56.23, 104.39, 0.00303, 35, 64.46, 25.79, 0.42788, 37, -34.25, 43.15, 0.56909, 3, 37, 148.76, -98.18, 0.00036, 38, 43.56, -14.09, 0.8911, 39, -221.5, 9.67, 0.10854, 5, 38, 276.69, 6.47, 0.00033, 39, 11.63, 30.24, 0.42589, 40, 3.41, -28.26, 0.48905, 41, -77.94, 26.58, 0.0717, 42, -88.91, -46.54, 0.01302, 4, 38, 248.61, 52.68, 0.00077, 39, -16.45, 76.44, 0.03694, 40, -24.68, 17.95, 0.95671, 42, -116.99, -0.33, 0.00558, 5, 39, 94.98, 35.67, 0.00578, 40, 86.76, -22.82, 0.04423, 41, 5.41, 32.02, 0.52157, 42, -5.56, -41.1, 0.38647, 43, -87.82, 25.62, 0.04195, 4, 41, 91.48, 95.44, 0.00049, 42, 80.51, 22.32, 0.15313, 43, -1.75, 89.04, 0.11368, 44, -15.46, -20.64, 0.73271, 4, 41, 76.08, 15.71, 0.12127, 42, 65.11, -57.41, 0.08282, 43, -17.15, 9.31, 0.76299, 44, -30.86, -100.37, 0.03292], "hull": 34, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 376, "height": 125}}, "y6": {"y4": {"type": "mesh", "uvs": [0, 1, 0.12195, 1, 0.26894, 1, 0.41833, 1, 0.50787, 1, 0.59943, 1, 0.70184, 1, 0.82352, 1, 0.91147, 1, 1, 1, 1, 0.80913, 1, 0.50954, 1, 0.08047, 0.93827, 0.12275, 0.86868, 0.21487, 0.83146, 0.36518, 0.75378, 0.45731, 0.66639, 0.43791, 0.59356, 0.50094, 0.59033, 0.67064, 0.62269, 0.77731, 0.5968, 0.84519, 0.49808, 0.80156, 0.42525, 0.66095, 0.34595, 0.56882, 0.43496, 0.56882, 0.40907, 0.41852, 0.2974, 0.35548, 0.22296, 0.22457, 0.16308, 0.07426, 0.09673, 0, 0, 0, 0, 0.28276, 0, 0.60276, 0.04846, 0.16916, 0.07496, 0.34242, 0.09786, 0.56621, 0.1099, 0.80083, 0.27978, 0.79, 0.22436, 0.54094, 0.15328, 0.29188, 0.39664, 0.85497, 0.70666, 0.77304, 0.66931, 0.58895, 0.8175, 0.75138, 0.93195, 0.49871, 0.91147, 0.81635], "triangles": [45, 11, 10, 45, 13, 11, 13, 12, 11, 15, 14, 45, 45, 14, 13, 8, 46, 9, 46, 10, 9, 8, 7, 46, 10, 46, 45, 46, 44, 45, 44, 15, 45, 44, 16, 15, 42, 44, 7, 7, 44, 46, 20, 43, 42, 20, 19, 43, 42, 16, 44, 42, 43, 16, 19, 18, 43, 18, 17, 43, 43, 17, 16, 6, 42, 7, 5, 20, 6, 6, 20, 42, 4, 21, 5, 5, 21, 20, 4, 22, 21, 3, 22, 4, 2, 41, 3, 3, 41, 22, 2, 38, 41, 38, 24, 41, 41, 23, 22, 41, 24, 23, 38, 39, 24, 24, 26, 25, 24, 27, 26, 39, 27, 24, 36, 40, 39, 40, 28, 39, 39, 28, 27, 40, 29, 28, 2, 1, 38, 37, 39, 38, 33, 35, 36, 33, 32, 35, 36, 35, 40, 32, 34, 35, 35, 34, 40, 34, 30, 40, 40, 30, 29, 32, 31, 34, 34, 31, 30, 0, 37, 1, 1, 37, 38, 37, 33, 36, 37, 0, 33, 37, 36, 39], "vertices": [2, 46, -59.03, -73.35, 0.98384, 48, -163.23, -61.46, 0.01616, 2, 46, 32.68, -73.35, 0.58961, 48, -71.52, -61.46, 0.41039, 3, 46, 143.21, -73.35, 0.00626, 48, 39.01, -61.46, 0.58565, 50, -52.47, -50.5, 0.40808, 2, 50, 59.87, -50.5, 0.82323, 51, -205.19, -26.73, 0.17677, 2, 50, 127.2, -50.5, 0.53891, 51, -137.86, -26.73, 0.46109, 3, 50, 196.06, -50.5, 0.21603, 51, -69, -26.73, 0.76941, 52, -77.23, -85.23, 0.01456, 3, 50, 273.07, -50.5, 0.00138, 51, 8.01, -26.73, 0.9103, 53, -81.56, -30.39, 0.08831, 3, 51, 99.51, -26.73, 0.01178, 53, 9.94, -30.39, 0.90648, 55, -83.29, -36.79, 0.08173, 3, 53, 76.08, -30.39, 0.19869, 54, 65.11, -103.51, 0.00071, 55, -17.15, -36.79, 0.80059, 2, 55, 49.43, -36.79, 0.99982, 56, 35.72, -146.47, 0.00018, 2, 55, 49.43, 11.12, 0.87381, 56, 35.72, -98.56, 0.12619, 2, 55, 49.43, 86.32, 0.17094, 56, 35.72, -23.36, 0.82906, 1, 56, 35.72, 84.34, 1, 2, 54, 85.27, 116.68, 0.05811, 56, -10.7, 73.72, 0.94189, 2, 54, 32.93, 93.56, 0.33255, 56, -63.04, 50.6, 0.66745, 4, 52, 97.26, 74.11, 0.01478, 54, 4.94, 55.83, 0.69782, 55, -77.32, 122.55, 2e-05, 56, -91.03, 12.87, 0.28738, 4, 52, 38.84, 50.99, 0.48597, 53, -42.5, 105.83, 0.00192, 54, -53.47, 32.71, 0.50945, 56, -149.44, -10.25, 0.00266, 3, 51, -18.65, 114.35, 0.00209, 52, -26.88, 55.86, 0.96891, 54, -119.19, 37.58, 0.029, 3, 50, 191.64, 74.77, 0.00013, 51, -73.42, 98.53, 0.06948, 52, -81.64, 40.04, 0.93038, 3, 50, 189.21, 32.17, 0.00732, 51, -75.85, 55.94, 0.18794, 52, -84.08, -2.56, 0.80474, 3, 50, 213.55, 5.4, 0.05527, 51, -51.51, 29.16, 0.53281, 52, -59.74, -29.33, 0.41192, 3, 50, 194.08, -11.64, 0.20089, 51, -70.98, 12.13, 0.72052, 52, -79.21, -46.37, 0.0786, 2, 50, 119.84, -0.69, 0.61141, 51, -145.22, 23.08, 0.38859, 3, 49, 170.27, -49.48, 0.01159, 50, 65.08, 34.61, 0.86596, 51, -199.98, 58.37, 0.12245, 4, 48, 96.93, 46.76, 0.03365, 49, 110.64, -26.36, 0.32661, 50, 5.44, 57.73, 0.63642, 51, -259.62, 81.49, 0.00331, 2, 49, 177.57, -26.36, 0.42617, 50, 72.38, 57.73, 0.57383, 2, 49, 158.1, 11.37, 0.43223, 50, 52.91, 95.46, 0.56777, 4, 47, 172.84, 9.82, 0.00064, 48, 60.42, 100.31, 0.0347, 49, 74.13, 27.19, 0.66993, 50, -31.07, 111.28, 0.29473, 3, 47, 116.86, 42.68, 0.16227, 49, 18.15, 60.05, 0.81567, 50, -87.05, 144.14, 0.02206, 2, 47, 71.83, 80.41, 0.53714, 49, -26.88, 97.78, 0.46286, 2, 47, 21.93, 99.05, 0.79861, 49, -76.78, 116.42, 0.20139, 2, 47, -50.81, 99.05, 0.96944, 49, -149.52, 116.42, 0.03056, 3, 46, -59.03, 106.68, 0.00793, 47, -50.81, 28.08, 0.99046, 49, -149.52, 45.45, 0.00161, 2, 46, -59.03, 26.36, 0.61729, 47, -50.81, -52.24, 0.38271, 2, 47, -14.36, 56.59, 0.93658, 49, -113.08, 73.96, 0.06342, 2, 47, 5.57, 13.1, 0.9449, 49, -93.14, 30.47, 0.0551, 4, 46, 14.56, 35.54, 0.49021, 47, 22.78, -43.07, 0.3424, 48, -89.64, 47.42, 0.0385, 49, -75.93, -25.7, 0.12889, 3, 46, 23.62, -23.35, 0.73457, 48, -80.58, -11.47, 0.24962, 49, -66.87, -84.59, 0.0158, 3, 48, 47.17, -8.75, 0.48187, 49, 60.88, -81.87, 0.0428, 50, -44.32, 2.21, 0.47534, 4, 46, 109.69, 41.88, 0.0044, 48, 5.49, 53.76, 0.23017, 49, 19.2, -19.36, 0.69934, 50, -86, 64.73, 0.06609, 3, 46, 56.23, 104.39, 0.00303, 47, 64.46, 25.79, 0.42788, 49, -34.25, 43.15, 0.56909, 3, 49, 148.76, -98.18, 0.00036, 50, 43.56, -14.09, 0.8911, 51, -221.5, 9.67, 0.10854, 5, 50, 276.69, 6.47, 0.00033, 51, 11.63, 30.24, 0.42589, 52, 3.41, -28.26, 0.48905, 53, -77.94, 26.58, 0.0717, 54, -88.91, -46.54, 0.01302, 4, 50, 248.61, 52.68, 0.00077, 51, -16.45, 76.44, 0.03694, 52, -24.68, 17.95, 0.95671, 54, -116.99, -0.33, 0.00558, 5, 51, 94.98, 35.67, 0.00578, 52, 86.76, -22.82, 0.04423, 53, 5.41, 32.02, 0.52157, 54, -5.56, -41.1, 0.38647, 55, -87.82, 25.62, 0.04195, 4, 53, 91.48, 95.44, 0.00049, 54, 80.51, 22.32, 0.15313, 55, -1.75, 89.04, 0.11368, 56, -15.46, -20.64, 0.73271, 4, 53, 76.08, 15.71, 0.12127, 54, 65.11, -57.41, 0.08282, 55, -17.15, 9.31, 0.76299, 56, -30.86, -100.37, 0.03292], "hull": 34, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 376, "height": 125}}, "y7": {"y2": {"type": "mesh", "uvs": [0, 0.87944, 0.10873, 0.69572, 0.13262, 0.42014, 0.32591, 0.19968, 0.52789, 0.14089, 0.71684, 0.00861, 0.86018, 0.0527, 0.99918, 0, 1, 0.12619, 0.88841, 0.35033, 0.75811, 0.54875, 0.62128, 0.56712, 0.61694, 0.75084, 0.45405, 0.92721, 0.21081, 1, 0, 1, 0.14621, 0.87199, 0.24766, 0.70923, 0.35785, 0.57606, 0.49079, 0.43105, 0.60623, 0.32748, 0.78564, 0.19047, 0.89494, 0.12883], "triangles": [22, 6, 7, 8, 22, 7, 21, 5, 6, 21, 6, 22, 20, 4, 5, 20, 5, 21, 9, 21, 22, 9, 22, 8, 10, 20, 21, 10, 21, 9, 11, 20, 10, 19, 3, 4, 19, 4, 20, 19, 20, 11, 18, 3, 19, 12, 19, 11, 12, 18, 19, 2, 3, 18, 17, 1, 2, 18, 17, 2, 12, 13, 18, 17, 18, 13, 14, 17, 13, 16, 1, 17, 0, 1, 16, 15, 0, 16, 14, 16, 17, 15, 16, 14], "vertices": [1, 9, -40.58, -2.44, 1, 3, 9, -8.93, 29.16, 0.73832, 10, -57.48, 9.07, 0.2421, 11, -125, -43.38, 0.01958, 3, 9, -1.98, 76.56, 0.19537, 10, -50.53, 56.47, 0.60036, 11, -118.05, 4.02, 0.20427, 4, 9, 54.27, 114.48, 0.00621, 10, 5.72, 94.39, 0.32663, 11, -61.8, 41.94, 0.66467, 12, -109.23, 17.95, 0.00249, 3, 10, 64.5, 104.5, 0.01771, 11, -3.02, 52.05, 0.62764, 12, -50.45, 28.06, 0.35465, 2, 11, 51.96, 74.8, 0.01644, 12, 4.53, 50.81, 0.98356, 1, 12, 46.24, 43.23, 1, 1, 12, 86.69, 52.29, 1, 1, 12, 86.93, 30.59, 1, 1, 12, 54.46, -7.97, 1, 2, 11, 63.97, -18.1, 0.12273, 12, 16.54, -42.09, 0.87727, 3, 10, 91.67, 31.19, 0.07723, 11, 24.15, -21.26, 0.71116, 12, -23.28, -45.25, 0.21161, 3, 10, 90.41, -0.41, 0.30189, 11, 22.89, -52.86, 0.69389, 12, -24.54, -76.85, 0.00422, 3, 9, 91.55, -10.66, 0.00065, 10, 43.01, -30.74, 0.73678, 11, -24.51, -83.2, 0.26257, 2, 9, 20.77, -23.18, 0.70623, 10, -27.78, -43.26, 0.29377, 1, 9, -40.58, -23.18, 1, 2, 9, 1.97, -1.16, 0.98439, 10, -46.58, -21.25, 0.01561, 3, 9, 31.49, 26.84, 0.22013, 10, -17.05, 6.75, 0.75611, 11, -84.57, -45.7, 0.02376, 3, 9, 63.56, 49.74, 0.00278, 10, 15.01, 29.65, 0.62775, 11, -52.5, -22.8, 0.36946, 3, 10, 53.7, 54.6, 0.09807, 11, -13.82, 2.14, 0.899, 12, -61.25, -21.85, 0.00293, 2, 11, 19.77, 19.96, 0.50465, 12, -27.66, -4.04, 0.49535, 1, 12, 24.55, 19.53, 1, 1, 12, 56.36, 30.13, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 145, "height": 86}}, "y8": {"y2": {"type": "mesh", "uvs": [0, 0.87944, 0.10873, 0.69572, 0.13262, 0.42014, 0.32591, 0.19968, 0.52789, 0.14089, 0.71684, 0.00861, 0.86018, 0.0527, 0.99918, 0, 1, 0.12619, 0.88841, 0.35033, 0.75811, 0.54875, 0.62128, 0.56712, 0.61694, 0.75084, 0.45405, 0.92721, 0.21081, 1, 0, 1, 0.14621, 0.87199, 0.24766, 0.70923, 0.35785, 0.57606, 0.49079, 0.43105, 0.60623, 0.32748, 0.78564, 0.19047, 0.89494, 0.12883], "triangles": [22, 6, 7, 8, 22, 7, 21, 5, 6, 21, 6, 22, 20, 4, 5, 20, 5, 21, 9, 21, 22, 9, 22, 8, 10, 20, 21, 10, 21, 9, 11, 20, 10, 19, 3, 4, 19, 4, 20, 19, 20, 11, 18, 3, 19, 12, 19, 11, 12, 18, 19, 2, 3, 18, 17, 1, 2, 18, 17, 2, 12, 13, 18, 17, 18, 13, 14, 17, 13, 16, 1, 17, 0, 1, 16, 15, 0, 16, 14, 16, 17, 15, 16, 14], "vertices": [1, 14, -40.58, -2.44, 1, 3, 14, -8.93, 29.16, 0.73832, 15, -57.48, 9.07, 0.2421, 16, -125, -43.38, 0.01958, 3, 14, -1.98, 76.56, 0.19537, 15, -50.53, 56.47, 0.60036, 16, -118.05, 4.02, 0.20427, 4, 14, 54.27, 114.48, 0.00621, 15, 5.72, 94.39, 0.32663, 16, -61.8, 41.94, 0.66467, 17, -109.23, 17.95, 0.00249, 3, 15, 64.5, 104.5, 0.01771, 16, -3.02, 52.05, 0.62764, 17, -50.45, 28.06, 0.35465, 2, 16, 51.96, 74.8, 0.01644, 17, 4.53, 50.81, 0.98356, 1, 17, 46.24, 43.23, 1, 1, 17, 86.69, 52.29, 1, 1, 17, 86.93, 30.59, 1, 1, 17, 54.46, -7.97, 1, 2, 16, 63.97, -18.1, 0.12273, 17, 16.54, -42.09, 0.87727, 3, 15, 91.67, 31.19, 0.07723, 16, 24.15, -21.26, 0.71116, 17, -23.28, -45.25, 0.21161, 3, 15, 90.41, -0.41, 0.30189, 16, 22.89, -52.86, 0.69389, 17, -24.54, -76.85, 0.00422, 3, 14, 91.55, -10.66, 0.00065, 15, 43.01, -30.74, 0.73678, 16, -24.51, -83.2, 0.26257, 2, 14, 20.77, -23.18, 0.70623, 15, -27.78, -43.26, 0.29377, 1, 14, -40.58, -23.18, 1, 2, 14, 1.97, -1.16, 0.98439, 15, -46.58, -21.25, 0.01561, 3, 14, 31.49, 26.84, 0.22013, 15, -17.05, 6.75, 0.75611, 16, -84.57, -45.7, 0.02376, 3, 14, 63.56, 49.74, 0.00278, 15, 15.01, 29.65, 0.62775, 16, -52.5, -22.8, 0.36946, 3, 15, 53.7, 54.6, 0.09807, 16, -13.82, 2.14, 0.899, 17, -61.25, -21.85, 0.00293, 2, 16, 19.77, 19.96, 0.50465, 17, -27.66, -4.04, 0.49535, 1, 17, 24.55, 19.53, 1, 1, 17, 56.36, 30.13, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 145, "height": 86}}, "y9": {"y2": {"type": "mesh", "uvs": [0, 0.87944, 0.10873, 0.69572, 0.13262, 0.42014, 0.32591, 0.19968, 0.52789, 0.14089, 0.71684, 0.00861, 0.86018, 0.0527, 0.99918, 0, 1, 0.12619, 0.88841, 0.35033, 0.75811, 0.54875, 0.62128, 0.56712, 0.61694, 0.75084, 0.45405, 0.92721, 0.21081, 1, 0, 1, 0.14621, 0.87199, 0.24766, 0.70923, 0.35785, 0.57606, 0.49079, 0.43105, 0.60623, 0.32748, 0.78564, 0.19047, 0.89494, 0.12883], "triangles": [22, 6, 7, 8, 22, 7, 21, 5, 6, 21, 6, 22, 20, 4, 5, 20, 5, 21, 9, 21, 22, 9, 22, 8, 10, 20, 21, 10, 21, 9, 11, 20, 10, 19, 3, 4, 19, 4, 20, 19, 20, 11, 18, 3, 19, 12, 19, 11, 12, 18, 19, 2, 3, 18, 17, 1, 2, 18, 17, 2, 12, 13, 18, 17, 18, 13, 14, 17, 13, 16, 1, 17, 0, 1, 16, 15, 0, 16, 14, 16, 17, 15, 16, 14], "vertices": [1, 4, -40.58, -2.44, 1, 3, 4, -8.93, 29.16, 0.73832, 5, -57.48, 9.07, 0.2421, 6, -125, -43.38, 0.01958, 3, 4, -1.98, 76.56, 0.19537, 5, -50.53, 56.47, 0.60036, 6, -118.05, 4.02, 0.20427, 4, 4, 54.27, 114.48, 0.00621, 5, 5.72, 94.39, 0.32663, 6, -61.8, 41.94, 0.66467, 7, -109.23, 17.95, 0.00249, 3, 5, 64.5, 104.5, 0.01771, 6, -3.02, 52.05, 0.62764, 7, -50.45, 28.06, 0.35465, 2, 6, 51.96, 74.8, 0.01644, 7, 4.53, 50.81, 0.98356, 1, 7, 46.24, 43.23, 1, 1, 7, 86.69, 52.29, 1, 1, 7, 86.93, 30.59, 1, 1, 7, 54.46, -7.97, 1, 2, 6, 63.97, -18.1, 0.12273, 7, 16.54, -42.09, 0.87727, 3, 5, 91.67, 31.19, 0.07723, 6, 24.15, -21.26, 0.71116, 7, -23.28, -45.25, 0.21161, 3, 5, 90.41, -0.41, 0.30189, 6, 22.89, -52.86, 0.69389, 7, -24.54, -76.85, 0.00422, 3, 4, 91.55, -10.66, 0.00065, 5, 43.01, -30.74, 0.73678, 6, -24.51, -83.2, 0.26257, 2, 4, 20.77, -23.18, 0.70623, 5, -27.78, -43.26, 0.29377, 1, 4, -40.58, -23.18, 1, 2, 4, 1.97, -1.16, 0.98439, 5, -46.58, -21.25, 0.01561, 3, 4, 31.49, 26.84, 0.22013, 5, -17.05, 6.75, 0.75611, 6, -84.57, -45.7, 0.02376, 3, 4, 63.56, 49.74, 0.00278, 5, 15.01, 29.65, 0.62775, 6, -52.5, -22.8, 0.36946, 3, 5, 53.7, 54.6, 0.09807, 6, -13.82, 2.14, 0.899, 7, -61.25, -21.85, 0.00293, 2, 6, 19.77, 19.96, 0.50465, 7, -27.66, -4.04, 0.49535, 1, 7, 24.55, 19.53, 1, 1, 7, 56.36, 30.13, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 145, "height": 86}}, "y2": {"y2": {"type": "mesh", "uvs": [0, 0.87944, 0.10873, 0.69572, 0.13262, 0.42014, 0.32591, 0.19968, 0.52789, 0.14089, 0.71684, 0.00861, 0.86018, 0.0527, 0.99918, 0, 1, 0.12619, 0.88841, 0.35033, 0.75811, 0.54875, 0.62128, 0.56712, 0.61694, 0.75084, 0.45405, 0.92721, 0.21081, 1, 0, 1, 0.14621, 0.87199, 0.24766, 0.70923, 0.35785, 0.57606, 0.49079, 0.43105, 0.60623, 0.32748, 0.78564, 0.19047, 0.89494, 0.12883], "triangles": [22, 6, 7, 8, 22, 7, 21, 5, 6, 21, 6, 22, 20, 4, 5, 20, 5, 21, 9, 21, 22, 9, 22, 8, 10, 20, 21, 10, 21, 9, 11, 20, 10, 19, 3, 4, 19, 4, 20, 19, 20, 11, 18, 3, 19, 12, 19, 11, 12, 18, 19, 2, 3, 18, 17, 1, 2, 18, 17, 2, 12, 13, 18, 17, 18, 13, 14, 17, 13, 16, 1, 17, 0, 1, 16, 15, 0, 16, 14, 16, 17, 15, 16, 14], "vertices": [1, 4, -40.58, -2.44, 1, 3, 4, -8.93, 29.16, 0.73832, 5, -57.48, 9.07, 0.2421, 6, -125, -43.38, 0.01958, 3, 4, -1.98, 76.56, 0.19537, 5, -50.53, 56.47, 0.60036, 6, -118.05, 4.02, 0.20427, 4, 4, 54.27, 114.48, 0.00621, 5, 5.72, 94.39, 0.32663, 6, -61.8, 41.94, 0.66467, 7, -109.23, 17.95, 0.00249, 3, 5, 64.5, 104.5, 0.01771, 6, -3.02, 52.05, 0.62764, 7, -50.45, 28.06, 0.35465, 2, 6, 51.96, 74.8, 0.01644, 7, 4.53, 50.81, 0.98356, 1, 7, 46.24, 43.23, 1, 1, 7, 86.69, 52.29, 1, 1, 7, 86.93, 30.59, 1, 1, 7, 54.46, -7.97, 1, 2, 6, 63.97, -18.1, 0.12273, 7, 16.54, -42.09, 0.87727, 3, 5, 91.67, 31.19, 0.07723, 6, 24.15, -21.26, 0.71116, 7, -23.28, -45.25, 0.21161, 3, 5, 90.41, -0.41, 0.30189, 6, 22.89, -52.86, 0.69389, 7, -24.54, -76.85, 0.00422, 3, 4, 91.55, -10.66, 0.00065, 5, 43.01, -30.74, 0.73678, 6, -24.51, -83.2, 0.26257, 2, 4, 20.77, -23.18, 0.70623, 5, -27.78, -43.26, 0.29377, 1, 4, -40.58, -23.18, 1, 2, 4, 1.97, -1.16, 0.98439, 5, -46.58, -21.25, 0.01561, 3, 4, 31.49, 26.84, 0.22013, 5, -17.05, 6.75, 0.75611, 6, -84.57, -45.7, 0.02376, 3, 4, 63.56, 49.74, 0.00278, 5, 15.01, 29.65, 0.62775, 6, -52.5, -22.8, 0.36946, 3, 5, 53.7, 54.6, 0.09807, 6, -13.82, 2.14, 0.899, 7, -61.25, -21.85, 0.00293, 2, 6, 19.77, 19.96, 0.50465, 7, -27.66, -4.04, 0.49535, 1, 7, 24.55, 19.53, 1, 1, 7, 56.36, 30.13, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 145, "height": 86}, "y3": {"type": "mesh", "path": "y2", "uvs": [0, 0.87944, 0.10873, 0.69572, 0.13262, 0.42014, 0.32591, 0.19968, 0.52789, 0.14089, 0.71684, 0.00861, 0.86018, 0.0527, 0.99918, 0, 1, 0.12619, 0.88841, 0.35033, 0.75811, 0.54875, 0.62128, 0.56712, 0.61694, 0.75084, 0.45405, 0.92721, 0.21081, 1, 0, 1, 0.14621, 0.87199, 0.24766, 0.70923, 0.35785, 0.57606, 0.49079, 0.43105, 0.60623, 0.32748, 0.78564, 0.19047, 0.89494, 0.12883], "triangles": [22, 6, 7, 8, 22, 7, 21, 5, 6, 21, 6, 22, 20, 4, 5, 20, 5, 21, 9, 21, 22, 9, 22, 8, 10, 20, 21, 10, 21, 9, 11, 20, 10, 19, 3, 4, 19, 4, 20, 19, 20, 11, 18, 3, 19, 12, 19, 11, 12, 18, 19, 2, 3, 18, 17, 1, 2, 18, 17, 2, 12, 13, 18, 17, 18, 13, 14, 17, 13, 16, 1, 17, 0, 1, 16, 15, 0, 16, 14, 16, 17, 15, 16, 14], "vertices": [1, 4, -40.58, -2.44, 1, 3, 4, -8.93, 29.16, 0.73832, 5, -57.48, 9.07, 0.2421, 6, -125, -43.38, 0.01958, 3, 4, -1.98, 76.56, 0.19537, 5, -50.53, 56.47, 0.60036, 6, -118.05, 4.02, 0.20427, 4, 4, 54.27, 114.48, 0.00621, 5, 5.72, 94.39, 0.32663, 6, -61.8, 41.94, 0.66467, 7, -109.23, 17.95, 0.00249, 3, 5, 64.5, 104.5, 0.01771, 6, -3.02, 52.05, 0.62764, 7, -50.45, 28.06, 0.35465, 2, 6, 51.96, 74.8, 0.01644, 7, 4.53, 50.81, 0.98356, 1, 7, 46.24, 43.23, 1, 1, 7, 86.69, 52.29, 1, 1, 7, 86.93, 30.59, 1, 1, 7, 54.46, -7.97, 1, 2, 6, 63.97, -18.1, 0.12273, 7, 16.54, -42.09, 0.87727, 3, 5, 91.67, 31.19, 0.07723, 6, 24.15, -21.26, 0.71116, 7, -23.28, -45.25, 0.21161, 3, 5, 90.41, -0.41, 0.30189, 6, 22.89, -52.86, 0.69389, 7, -24.54, -76.85, 0.00422, 3, 4, 91.55, -10.66, 0.00065, 5, 43.01, -30.74, 0.73678, 6, -24.51, -83.2, 0.26257, 2, 4, 20.77, -23.18, 0.70623, 5, -27.78, -43.26, 0.29377, 1, 4, -40.58, -23.18, 1, 2, 4, 1.97, -1.16, 0.98439, 5, -46.58, -21.25, 0.01561, 3, 4, 31.49, 26.84, 0.22013, 5, -17.05, 6.75, 0.75611, 6, -84.57, -45.7, 0.02376, 3, 4, 63.56, 49.74, 0.00278, 5, 15.01, 29.65, 0.62775, 6, -52.5, -22.8, 0.36946, 3, 5, 53.7, 54.6, 0.09807, 6, -13.82, 2.14, 0.899, 7, -61.25, -21.85, 0.00293, 2, 6, 19.77, 19.96, 0.50465, 7, -27.66, -4.04, 0.49535, 1, 7, 24.55, 19.53, 1, 1, 7, 56.36, 30.13, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 145, "height": 86}}, "y12": {"y3": {"type": "mesh", "uvs": [1, 1, 0.91667, 1, 0.83333, 1, 0.75, 1, 0.66667, 1, 0.58333, 1, 0.5, 1, 0.41667, 1, 0.33333, 1, 0.25, 1, 0.16667, 1, 0.08333, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.08333, 0, 0.16667, 0, 0.25, 0, 0.33333, 0, 0.41667, 0, 0.5, 0, 0.58333, 0, 0.66667, 0, 0.75, 0, 0.83333, 0, 0.91667, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.91667, 0.66667, 0.83333, 0.66667, 0.75, 0.66667, 0.66667, 0.66667, 0.58333, 0.66667, 0.5, 0.66667, 0.41667, 0.66667, 0.33333, 0.66667, 0.25, 0.66667, 0.16667, 0.66667, 0.08333, 0.66667, 0.91667, 0.33333, 0.83333, 0.33333, 0.75, 0.33333, 0.66667, 0.33333, 0.58333, 0.33333, 0.5, 0.33333, 0.41667, 0.33333, 0.33333, 0.33333, 0.25, 0.33333, 0.16667, 0.33333, 0.08333, 0.33333], "triangles": [51, 16, 17, 51, 14, 16, 14, 15, 16, 40, 51, 50, 13, 14, 51, 11, 40, 39, 39, 40, 50, 11, 12, 40, 12, 13, 40, 40, 13, 51, 49, 18, 19, 49, 50, 18, 50, 17, 18, 50, 51, 17, 38, 49, 48, 39, 50, 49, 9, 38, 37, 37, 38, 48, 9, 10, 38, 10, 39, 38, 38, 39, 49, 10, 11, 39, 47, 20, 21, 47, 48, 20, 48, 19, 20, 48, 49, 19, 36, 47, 46, 37, 48, 47, 6, 7, 35, 7, 36, 35, 35, 36, 46, 7, 8, 36, 8, 37, 36, 36, 37, 47, 8, 9, 37, 45, 22, 23, 46, 21, 22, 46, 47, 21, 34, 45, 44, 44, 45, 23, 35, 46, 45, 45, 46, 22, 4, 5, 33, 5, 34, 33, 33, 34, 44, 5, 6, 34, 6, 35, 34, 34, 35, 45, 43, 24, 25, 44, 23, 24, 32, 43, 42, 33, 44, 43, 43, 44, 24, 3, 32, 31, 31, 32, 42, 3, 4, 32, 4, 33, 32, 32, 33, 43, 41, 26, 27, 41, 42, 26, 42, 25, 26, 42, 43, 25, 30, 41, 28, 28, 41, 27, 31, 42, 41, 0, 1, 29, 1, 30, 29, 29, 30, 28, 1, 2, 30, 2, 31, 30, 30, 31, 41, 2, 3, 31], "vertices": [3, 125, 87.43, -90.24, 0.99055, 124, 85.65, -143.85, 0.00495, 122, 205.37, -83.09, 0.0045, 2, 125, 24.77, -90.24, 0.90152, 122, 142.71, -83.09, 0.09848, 3, 125, -37.9, -90.24, 0.59751, 122, 80.04, -83.09, 0.4002, 119, 197.98, -83.09, 0.00229, 3, 125, -100.57, -90.24, 0.20364, 122, 17.37, -83.09, 0.71465, 119, 135.32, -83.09, 0.08171, 4, 125, -163.23, -90.24, 0.01825, 122, -45.29, -83.09, 0.57725, 119, 72.65, -83.09, 0.40152, 116, 204.89, -75.94, 0.00298, 3, 122, -107.96, -83.09, 0.18889, 119, 9.98, -83.09, 0.72196, 116, 142.22, -75.94, 0.08915, 4, 122, -170.63, -83.09, 0.01502, 119, -52.68, -83.09, 0.57173, 116, 79.55, -75.94, 0.41309, 113, 227.88, -79.52, 0.00016, 3, 119, -115.35, -83.09, 0.18795, 116, 16.89, -75.94, 0.76285, 113, 165.21, -79.52, 0.0492, 5, 119, -178.02, -83.09, 0.01131, 116, -45.78, -75.94, 0.66279, 113, 102.54, -79.52, 0.32531, 112, 98.97, -136.7, 0.00058, 110, 207.98, -72.37, 1e-05, 4, 116, -108.45, -75.94, 0.27628, 115, -104.87, -133.13, 0.0003, 113, 39.88, -79.52, 0.69238, 110, 145.31, -72.37, 0.03103, 3, 116, -171.11, -75.94, 0.04036, 113, -22.79, -79.52, 0.68847, 110, 82.64, -72.37, 0.27116, 3, 116, -233.78, -75.94, 0.00021, 113, -85.46, -79.52, 0.26387, 110, 19.97, -72.37, 0.73593, 2, 113, -148.12, -79.52, 0.02487, 110, -42.69, -72.37, 0.97513, 2, 110, -42.69, 8.3, 0.79894, 109, -46.27, -57.82, 0.20106, 3, 110, -42.69, 88.96, 0.01613, 109, -46.27, 22.85, 0.6658, 108, -48.05, -46.85, 0.31806, 2, 109, -46.27, 103.51, 0.01371, 108, -48.05, 33.82, 0.98629, 3, 112, -89.03, 105.3, 0.00227, 111, -90.82, 26.67, 0.12708, 108, 14.61, 33.82, 0.87065, 4, 112, -26.37, 105.3, 0.00477, 111, -28.15, 26.67, 0.78013, 109, 79.07, 103.51, 0.00115, 108, 77.28, 33.82, 0.21395, 4, 115, -104.87, 108.87, 0.02823, 114, -104.87, 23.1, 0.17538, 112, 36.3, 105.3, 0.02824, 111, 34.51, 26.67, 0.76815, 4, 115, -42.21, 108.87, 0.04033, 114, -42.21, 23.1, 0.69284, 112, 98.97, 105.3, 0.03685, 111, 97.18, 26.67, 0.22999, 4, 118, -113.56, 92.79, 0.00745, 117, -102.84, 24.88, 0.11688, 115, 20.46, 108.87, 0.00757, 114, 20.46, 23.1, 0.8681, 4, 118, -50.9, 92.79, 0.02139, 117, -40.17, 24.88, 0.69311, 115, 83.13, 108.87, 0.02828, 114, 83.13, 23.1, 0.25723, 4, 121, -100.81, 89.22, 0.00623, 120, -95.45, 24.88, 0.13484, 118, 11.77, 92.79, 0.01829, 117, 22.49, 24.88, 0.84064, 4, 121, -38.14, 89.22, 0.01036, 120, -32.78, 24.88, 0.73626, 118, 74.44, 92.79, 0.04119, 117, 85.16, 24.88, 0.21219, 4, 124, -102.35, 98.15, 0.00417, 123, -98.78, 32.03, 0.16705, 121, 24.52, 89.22, 0.04221, 120, 29.88, 24.88, 0.78657, 4, 124, -39.69, 98.15, 0.00395, 123, -36.11, 32.03, 0.73012, 121, 87.19, 89.22, 0.05328, 120, 92.55, 24.88, 0.21265, 3, 124, 22.98, 98.15, 0.06539, 123, 26.55, 32.03, 0.93453, 120, 155.22, 24.88, 8e-05, 2, 124, 85.65, 98.15, 0.24366, 123, 89.22, 32.03, 0.75634, 3, 125, 87.43, 71.09, 0.12137, 124, 85.65, 17.48, 0.55376, 123, 89.22, -48.63, 0.32487, 3, 125, 87.43, -9.57, 0.74013, 124, 85.65, -63.18, 0.2474, 123, 89.22, -129.3, 0.01247, 4, 125, 24.77, -9.57, 0.89847, 124, 22.98, -63.18, 0.09332, 123, 26.55, -129.3, 0.00042, 122, 142.71, -2.42, 0.00779, 4, 125, -37.9, -9.57, 0.62712, 124, -39.69, -63.18, 0.05321, 122, 80.04, -2.42, 0.27875, 121, 87.19, -72.12, 0.04092, 4, 125, -100.57, -9.57, 0.10416, 124, -102.35, -63.18, 0.01569, 122, 17.37, -2.42, 0.83811, 121, 24.52, -72.12, 0.04204, 4, 122, -45.29, -2.42, 0.53741, 121, -38.14, -72.12, 0.09946, 119, 72.65, -2.42, 0.30923, 118, 74.44, -68.54, 0.0539, 4, 122, -107.96, -2.42, 0.04949, 121, -100.81, -72.12, 0.01097, 119, 9.98, -2.42, 0.92644, 118, 11.77, -68.54, 0.0131, 6, 119, -52.68, -2.42, 0.52296, 118, -50.9, -68.54, 0.10298, 117, -40.17, -136.45, 0.00089, 116, 79.55, 4.72, 0.28906, 115, 83.13, -52.46, 0.08335, 114, 83.13, -138.24, 0.00076, 5, 119, -115.35, -2.42, 0.08092, 118, -113.56, -68.54, 0.02197, 117, -102.84, -136.45, 8e-05, 116, 16.89, 4.72, 0.78509, 115, 20.46, -52.46, 0.11194, 6, 116, -45.78, 4.72, 0.53418, 115, -42.21, -52.46, 0.17902, 114, -42.21, -138.24, 0.0007, 113, 102.54, 1.15, 0.20414, 112, 98.97, -56.03, 0.08036, 111, 97.18, -134.66, 0.00159, 6, 116, -108.45, 4.72, 0.16235, 115, -104.87, -52.46, 0.06688, 114, -104.87, -138.24, 0.00116, 113, 39.88, 1.15, 0.60409, 112, 36.3, -56.03, 0.1654, 111, 34.51, -134.66, 0.00012, 4, 113, -22.79, 1.15, 0.73163, 112, -26.37, -56.03, 0.08998, 110, 82.64, 8.3, 0.13812, 109, 79.07, -57.82, 0.04027, 4, 113, -85.46, 1.15, 0.12269, 112, -89.03, -56.03, 0.03, 110, 19.97, 8.3, 0.74075, 109, 16.4, -57.82, 0.10656, 3, 125, 24.77, 71.09, 0.02885, 124, 22.98, 17.48, 0.71537, 123, 26.55, -48.63, 0.25578, 6, 125, -37.9, 71.09, 0.03794, 124, -39.69, 17.48, 0.46075, 123, -36.11, -48.63, 0.21866, 122, 80.04, 78.24, 0.03586, 121, 87.19, 8.55, 0.18898, 120, 92.55, -55.78, 0.05781, 6, 125, -100.57, 71.09, 0.01311, 124, -102.35, 17.48, 0.06823, 123, -98.78, -48.63, 0.05549, 122, 17.37, 78.24, 0.03431, 121, 24.52, 8.55, 0.71618, 120, 29.88, -55.78, 0.11269, 6, 122, -45.29, 78.24, 0.02485, 121, -38.14, 8.55, 0.52318, 120, -32.78, -55.78, 0.15607, 119, 72.65, 78.24, 0.02917, 118, 74.44, 12.12, 0.21159, 117, 85.16, -55.78, 0.05514, 7, 122, -107.96, 78.24, 0.00336, 121, -100.81, 8.55, 0.05231, 120, -95.45, -55.78, 0.03371, 119, 9.98, 78.24, 0.00376, 118, 11.77, 12.12, 0.7784, 117, 22.49, -55.78, 0.12835, 114, 145.79, -57.57, 0.00011, 6, 119, -52.68, 78.24, 0.04757, 118, -50.9, 12.12, 0.42134, 117, -40.17, -55.78, 0.20566, 116, 79.55, 85.39, 0.02278, 115, 83.13, 28.21, 0.19758, 114, 83.13, -57.57, 0.10507, 8, 119, -115.35, 78.24, 0.00928, 118, -113.56, 12.12, 0.06842, 117, -102.84, -55.78, 0.05395, 116, 16.89, 85.39, 0.00332, 115, 20.46, 28.21, 0.61146, 114, 20.46, -57.57, 0.25028, 112, 161.63, 24.63, 0.00171, 111, 159.85, -54, 0.00159, 6, 116, -45.78, 85.39, 0.016, 115, -42.21, 28.21, 0.47738, 114, -42.21, -57.57, 0.23475, 113, 102.54, 81.82, 0.01798, 112, 98.97, 24.63, 0.15348, 111, 97.18, -54, 0.1004, 6, 116, -108.45, 85.39, 0.01194, 115, -104.87, 28.21, 0.12264, 114, -104.87, -57.57, 0.07683, 113, 39.88, 81.82, 0.01742, 112, 36.3, 24.63, 0.5273, 111, 34.51, -54, 0.24387, 8, 115, -167.54, 28.21, 3e-05, 114, -167.54, -57.57, 0.00012, 113, -22.79, 81.82, 0.00499, 112, -26.37, 24.63, 0.57177, 111, -28.15, -54, 0.21741, 110, 82.64, 88.96, 0.00364, 109, 79.07, 22.85, 0.12451, 108, 77.28, -46.85, 0.07754, 6, 113, -85.46, 81.82, 0.00373, 112, -89.03, 24.63, 0.08701, 111, -90.82, -54, 0.04224, 110, 19.97, 88.96, 0.00126, 109, 16.4, 22.85, 0.61412, 108, 14.61, -46.85, 0.25165], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 376, "height": 121}}}}], "animations": {"animation": {"slots": {"y8": {"color": [{"color": "ffffff00"}]}, "y9": {"color": [{"color": "ffffff00"}]}, "y5": {"color": [{"color": "ffffff00"}]}, "y11": {"color": [{"color": "ffffff92", "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "color": "ffffffff", "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 5.3333, "color": "ffffff92"}]}, "y12": {"color": [{"color": "ffffff92", "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "color": "ffffff00", "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "color": "ffffff92"}]}, "y10": {"color": [{"color": "ffffff00"}]}, "y4": {"color": [{"color": "ffffff00"}]}, "y1": {"color": [{"color": "ffffff00"}]}, "y7": {"color": [{"color": "ffffff00"}]}, "y2": {"color": [{"color": "ffffff00"}]}, "y6": {"color": [{"color": "ffffff00"}]}, "y3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}}, "bones": {"yun2": {"translate": [{"x": 6.19, "y": 3.63}], "scale": [{"x": 1.052, "y": 1.052}]}, "bone5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 3.84, "y": 8.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone6": {"translate": [{"x": 0.59, "y": 1.28, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 3.84, "y": 8.33, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "x": 0.59, "y": 1.28}]}, "bone7": {"translate": [{"x": 1.64, "y": 3.55, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 3.84, "y": 8.33, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": 1.64, "y": 3.55}]}, "bone8": {"translate": [{"x": 2.75, "y": 5.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": 3.84, "y": 8.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": 2.75, "y": 5.96}]}, "yun3": {"translate": [{"x": 21.35, "y": 12.52}], "scale": [{"x": 1.18, "y": 1.18}]}, "bone9": {"translate": [{"x": 2.54, "y": 5.5, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 3.84, "y": 8.33, "curve": 0.26, "c3": 0.618, "c4": 0.45}, {"time": 5.3333, "x": 2.54, "y": 5.5}]}, "bone10": {"translate": [{"x": 3.5, "y": 7.59, "curve": 0.294, "c2": 0.2, "c3": 0.687, "c4": 0.73}, {"time": 1.6667, "x": 0.59, "y": 1.28, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "x": 3.84, "y": 8.33, "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 3.5, "y": 7.59}]}, "bone11": {"translate": [{"x": 3.76, "y": 8.14, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.1667, "x": 3.84, "y": 8.33, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.6667, "x": 1.64, "y": 3.55, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 2.8333, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 5.3333, "x": 3.76, "y": 8.14}]}, "bone12": {"translate": [{"x": 2.97, "y": 6.43, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.7667, "x": 3.84, "y": 8.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.6667, "x": 2.75, "y": 5.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 3.4333, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 5.3333, "x": 2.97, "y": 6.43}]}, "yun4": {"translate": [{"x": 6.19, "y": 3.63}], "scale": [{"x": 1.052, "y": 1.052}]}, "bone13": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 3.84, "y": 8.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone14": {"translate": [{"x": 0.59, "y": 1.28, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 3.84, "y": 8.33, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "x": 0.59, "y": 1.28}]}, "bone15": {"translate": [{"x": 1.64, "y": 3.55, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 3.84, "y": 8.33, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": 1.64, "y": 3.55}]}, "bone16": {"translate": [{"x": 2.75, "y": 5.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": 3.84, "y": 8.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": 2.75, "y": 5.96}]}, "bone17": {"translate": [{"x": 2.2, "y": 4.77, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "x": 3.84, "y": 8.33, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": 2.2, "y": 4.77}]}, "bone18": {"translate": [{"x": 1.09, "y": 2.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "x": 3.84, "y": 8.33, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.8333, "x": 0.59, "y": 1.28, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": 1.09, "y": 2.36}]}, "bone19": {"translate": [{"x": 0.2, "y": 0.43, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "x": 3.84, "y": 8.33, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.8333, "x": 1.64, "y": 3.55, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "x": 0.2, "y": 0.43}]}, "bone20": {"translate": [{"x": 0.19, "y": 0.41, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 3.84, "y": 8.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.8333, "x": 2.75, "y": 5.96, "curve": 0.338, "c2": 0.35, "c3": 0.72, "c4": 0.84}, {"time": 5.3333, "x": 0.19, "y": 0.41}]}, "bone28": {"translate": [{"x": 5.07, "y": 5.91, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 1.2, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.9667, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 3.8, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 5.3333, "x": 5.07, "y": 5.91}]}, "bone27": {"translate": [{"x": 8.06, "y": 9.41, "curve": 0.365, "c2": 0.63, "c3": 0.702}, {"time": 0.3667, "x": 8.64, "y": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": 0.244, "c3": 0.701, "c4": 0.79}, {"time": 5.3333, "x": 8.06, "y": 9.41}]}, "bone26": {"translate": [{"x": 7.36, "y": 8.58, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.6, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.3667, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 3.2, "curve": 0.243, "c3": 0.675, "c4": 0.7}, {"time": 5.3333, "x": 7.36, "y": 8.58}]}, "bone25": {"translate": [{"x": 8.34, "y": 9.73, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "x": 8.64, "y": 10.08, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 5.3333, "x": 8.34, "y": 9.73}]}, "bone24": {"translate": [{"x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 1.7667, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 8.64, "y": 10.08}]}, "bone23": {"translate": [{"x": 6.35, "y": 7.41, "curve": 0.348, "c2": 0.39, "c3": 0.757}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 8.64, "y": 10.08, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 6.35, "y": 7.41}]}, "bone22": {"translate": [{"x": 4.06, "y": 4.74, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 8.64, "y": 10.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5.3333, "x": 4.06, "y": 4.74}]}, "bone21": {"translate": [{"x": 4.83, "y": 5.63, "curve": 0.336, "c2": 0.34, "c3": 0.678, "c4": 0.7}, {"time": 0.6, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 8.64, "y": 10.08, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5.3333, "x": 4.83, "y": 5.63}]}, "bone4": {"translate": [{"x": 1.38, "y": 1.61, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 8.64, "y": 10.08, "curve": 0.242, "c3": 0.672, "c4": 0.69}, {"time": 5.3333, "x": 1.38, "y": 1.61}]}, "bone3": {"translate": [{"x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 5.3333, "x": 2.29, "y": 2.67}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 8.64, "y": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "yunc2": {"scale": [{"x": 1.086, "y": 1.086}]}, "bone29": {"translate": [{"x": 5.36, "y": 6.25, "curve": 0.364, "c2": 0.45, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": 8.64, "y": 10.08, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 5.3333, "x": 5.36, "y": 6.25}]}, "bone30": {"translate": [{"x": 8.33, "y": 9.72, "curve": 0.274, "c2": 0.13, "c3": 0.662, "c4": 0.65}, {"time": 1.5333, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "x": 8.64, "y": 10.08, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 5.3333, "x": 8.33, "y": 9.72}]}, "bone31": {"translate": [{"x": 7.67, "y": 8.94, "curve": 0.3, "c2": 0.22, "c3": 0.687, "c4": 0.73}, {"time": 1.5333, "x": 1.38, "y": 1.61, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "x": 8.64, "y": 10.08, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 5.3333, "x": 7.67, "y": 8.94}]}, "bone32": {"translate": [{"x": 8.06, "y": 9.41, "curve": 0.365, "c2": 0.63, "c3": 0.702}, {"time": 0.3667, "x": 8.64, "y": 10.08, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.5333, "x": 4.83, "y": 5.63, "curve": 0.336, "c2": 0.34, "c3": 0.678, "c4": 0.7}, {"time": 2.1333, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.9667, "curve": 0.244, "c3": 0.701, "c4": 0.79}, {"time": 5.3333, "x": 8.06, "y": 9.41}]}, "bone33": {"translate": [{"x": 8.45, "y": 9.86, "curve": 0.352, "c2": 0.65, "c3": 0.686}, {"time": 0.1667, "x": 8.64, "y": 10.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.5333, "x": 4.06, "y": 4.74, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 2.7667, "curve": 0.247, "c3": 0.723, "c4": 0.88}, {"time": 5.3333, "x": 8.45, "y": 9.86}]}, "bone34": {"translate": [{"x": 6.95, "y": 8.1, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 0.7, "x": 8.64, "y": 10.08, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.5333, "x": 6.35, "y": 7.41, "curve": 0.348, "c2": 0.39, "c3": 0.757}, {"time": 3.3, "curve": 0.242, "c3": 0.664, "c4": 0.66}, {"time": 5.3333, "x": 6.95, "y": 8.1}]}, "bone35": {"translate": [{"x": 3.58, "y": 4.17, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 1.5333, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 3.3, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 4.1333, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "x": 3.58, "y": 4.17}]}, "bone36": {"translate": [{"x": 4.57, "y": 5.33, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "x": 8.64, "y": 10.08, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 1.5333, "x": 8.34, "y": 9.73, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 3.9, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5.3333, "x": 4.57, "y": 5.33}]}, "bone37": {"translate": [{"x": 1.29, "y": 1.51, "curve": 0.309, "c2": 0.25, "c3": 0.691, "c4": 0.75}, {"time": 1.5333, "x": 7.36, "y": 8.58, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 2.1333, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 3.9, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 4.7333, "curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "x": 1.29, "y": 1.51}]}, "bone38": {"translate": [{"x": 2.17, "y": 2.53, "curve": 0.331, "c2": 0.33, "c3": 0.714, "c4": 0.82}, {"time": 1.5333, "x": 8.06, "y": 9.41, "curve": 0.365, "c2": 0.63, "c3": 0.702}, {"time": 1.9, "x": 8.64, "y": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 2.17, "y": 2.53}]}, "bone39": {"translate": [{"curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 1.5333, "x": 5.07, "y": 5.91, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.7333, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4.5, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 5.3333}]}, "yunc3": {"scale": [{"x": 1.021, "y": 1.021}]}, "bone40": {"translate": [{"x": 4.32, "y": 5.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3667, "x": 8.64, "y": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 4.32, "y": 5.04}]}, "bone41": {"translate": [{"x": 1.08, "y": 1.26, "curve": 0.319, "c2": 0.28, "c3": 0.757}, {"time": 2.2, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 3.9667, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 4.8, "curve": 0.285, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "x": 1.08, "y": 1.26}]}, "bone42": {"translate": [{"x": 1.91, "y": 2.22, "curve": 0.341, "c2": 0.36, "c3": 0.757}, {"time": 1.9667, "x": 8.64, "y": 10.08, "curve": 0.242, "c3": 0.672, "c4": 0.69}, {"time": 3.9667, "x": 1.38, "y": 1.61, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 4.5667, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 1.91, "y": 2.22}]}, "bone43": {"translate": [{"x": 0.02, "y": 0.03, "curve": 0.341, "c2": 0.66, "c3": 0.674}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "x": 8.64, "y": 10.08, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 3.9667, "x": 4.83, "y": 5.63, "curve": 0.336, "c2": 0.34, "c3": 0.678, "c4": 0.7}, {"time": 4.5667, "x": 2.29, "y": 2.67, "curve": 0.377, "c2": 0.54, "c3": 0.725, "c4": 0.94}, {"time": 5.3333, "x": 0.02, "y": 0.03}]}, "bone44": {"translate": [{"x": 0.13, "y": 0.15, "curve": 0.268, "c2": 0.08, "c3": 0.752}, {"time": 2.6, "x": 8.64, "y": 10.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 3.9667, "x": 4.06, "y": 4.74, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 5.2, "curve": 0.32, "c3": 0.654, "c4": 0.34}, {"time": 5.3333, "x": 0.13, "y": 0.15}]}, "bone45": {"translate": [{"x": 0.78, "y": 0.91, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 8.64, "y": 10.08, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 3.9667, "x": 6.35, "y": 7.41, "curve": 0.332, "c2": 0.33, "c3": 0.707, "c4": 0.8}, {"time": 5.3333, "x": 0.78, "y": 0.91}]}, "bone46": {"translate": [{"x": 4.05, "y": 4.72, "curve": 0.339, "c2": 0.35, "c3": 0.676, "c4": 0.7}, {"time": 0.4, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 8.64, "y": 10.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5.3333, "x": 4.05, "y": 4.72}]}, "bone47": {"translate": [{"x": 3.07, "y": 3.59, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 8.64, "y": 10.08, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 3.9667, "x": 8.34, "y": 9.73, "curve": 0.275, "c2": 0.13, "c3": 0.652, "c4": 0.62}, {"time": 5.3333, "x": 3.07, "y": 3.59}]}, "bone48": {"translate": [{"x": 6.58, "y": 7.68, "curve": 0.32, "c2": 0.29, "c3": 0.676, "c4": 0.7}, {"time": 1, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 1.8333, "curve": 0.243, "c3": 0.675, "c4": 0.7}, {"time": 3.9667, "x": 7.36, "y": 8.58, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 4.5667, "x": 8.64, "y": 10.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 6.58, "y": 7.68}]}, "bone49": {"translate": [{"x": 5.62, "y": 6.55, "curve": 0.361, "c2": 0.44, "c3": 0.755}, {"time": 1.6, "curve": 0.244, "c3": 0.701, "c4": 0.79}, {"time": 3.9667, "x": 8.06, "y": 9.41, "curve": 0.365, "c2": 0.63, "c3": 0.702}, {"time": 4.3333, "x": 8.64, "y": 10.08, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 5.3333, "x": 5.62, "y": 6.55}]}, "bone50": {"translate": [{"x": 8.45, "y": 9.86, "curve": 0.267, "c2": 0.1, "c3": 0.66, "c4": 0.64}, {"time": 1.6, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.4333, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 3.9667, "x": 5.07, "y": 5.91, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 5.1667, "x": 8.64, "y": 10.08, "curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 5.3333, "x": 8.45, "y": 9.86}]}, "bone51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -9.1, "y": 16.55, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone52": {"translate": [{"x": -1.4, "y": 2.55, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": -9.1, "y": 16.55, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "x": -1.4, "y": 2.55}]}, "bone53": {"translate": [{"x": -3.88, "y": 7.06, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -9.1, "y": 16.55, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": -3.88, "y": 7.06}]}, "bone54": {"translate": [{"x": -6.52, "y": 11.86, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": -9.1, "y": 16.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": -6.52, "y": 11.86}]}, "bone55": {"translate": [{"x": -8.64, "y": 15.7, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": -9.1, "y": 16.55, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "x": -8.64, "y": 15.7}]}, "bone56": {"translate": [{"x": -9.1, "y": 16.55, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -9.1, "y": 16.55}]}, "bone57": {"translate": [{"x": -7.7, "y": 14, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "x": -9.1, "y": 16.55, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2.6667, "x": -1.4, "y": 2.55, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.2667, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "x": -7.7, "y": 14}]}, "bone58": {"translate": [{"x": -5.22, "y": 9.49, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "x": -9.1, "y": 16.55, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 2.6667, "x": -3.88, "y": 7.06, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 3.8333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": -5.22, "y": 9.49}]}, "bone59": {"translate": [{"x": -2.58, "y": 4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "x": -9.1, "y": 16.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": -6.52, "y": 11.86, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": -2.58, "y": 4.7}]}, "bone60": {"translate": [{"x": -0.47, "y": 0.85, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "x": -9.1, "y": 16.55, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2.6667, "x": -8.64, "y": 15.7, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "x": -0.47, "y": 0.85}]}, "bone61": {"translate": [{"y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "y": -1.13}]}, "bone62": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone63": {"translate": [{"y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5.3333, "y": -3.35}]}, "bone64": {"translate": [{"y": -3.11, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.6, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "y": -3.11}]}, "bone65": {"translate": [{"y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "y": -1.13}]}, "bone66": {"translate": [{"y": -5.47, "curve": 0.322, "c2": 0.3, "c3": 0.663, "c4": 0.65}, {"time": 0.6, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "y": -5.47}]}, "bone67": {"translate": [{"y": -5.24, "curve": 0.33, "c2": 0.32, "c3": 0.693, "c4": 0.75}, {"time": 1.2, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "y": -7.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -5.24}]}, "bone68": {"translate": [{"y": -3.13, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "y": -3.13}]}, "bone69": {"translate": [{"y": -7.11, "curve": 0.278, "c2": 0.14, "c3": 0.642, "c4": 0.58}, {"time": 1.2, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 5.3333, "y": -7.11}]}, "bone70": {"translate": [{"y": -7.08, "curve": 0.274, "c2": 0.13, "c3": 0.682, "c4": 0.72}, {"time": 1.8333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 5.3333, "y": -7.08}]}, "bone71": {"translate": [{"y": -5.46, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "y": -5.46}]}, "bone72": {"translate": [{"y": -6.7, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 0.4, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.8333, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.0667, "curve": 0.243, "c3": 0.693, "c4": 0.76}, {"time": 5.3333, "y": -6.7}]}, "bone73": {"translate": [{"y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.3, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2.3667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.9667, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 5.3333, "y": -6.97}]}, "bone74": {"translate": [{"y": -6.97, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "y": -7.35, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "y": -6.97}]}, "bone75": {"translate": [{"y": -5.06, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.9333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2.3667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.6, "curve": 0.244, "c3": 0.645, "c4": 0.58}, {"time": 5.3333, "y": -5.06}]}, "bone76": {"translate": [{"y": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2.9667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 5.3333, "y": -5.26}]}, "bone77": {"translate": [{"y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.3, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 5.3333, "y": -6.97}]}, "bone78": {"translate": [{"y": -2.92, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.5333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2.9667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.2, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 5.3333, "y": -2.92}]}, "yuwyuw1": {"translate": [{"y": 7.72, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "y": 7.72}], "scale": [{"x": 0.978, "y": 0.978, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 1.062, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 0.978, "y": 0.978}]}, "yuwyuw2": {"translate": [{"y": -0.86, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.4333, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "y": 7.72, "curve": 0.242, "c3": 0.662, "c4": 0.65}, {"time": 5.3333, "y": -0.86}], "scale": [{"x": 1.045, "y": 1.045, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.4333, "x": 1.062, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 0.978, "y": 0.978, "curve": 0.242, "c3": 0.662, "c4": 0.65}, {"time": 5.3333, "x": 1.045, "y": 1.045}]}, "bone79": {"translate": [{"y": -6.21, "curve": 0.31, "c2": 0.26, "c3": 0.69, "c4": 0.74}, {"time": 1.4667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "y": -7.35, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "y": -6.21}]}, "bone80": {"translate": [{"y": -4.21, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "y": -7.35, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 5.3333, "y": -4.21}]}, "bone81": {"translate": [{"y": -7.29, "curve": 0.34, "c2": 0.66, "c3": 0.674}, {"time": 0.0667, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.4667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.7333, "curve": 0.249, "c3": 0.741, "c4": 0.96}, {"time": 5.3333, "y": -7.29}]}, "bone82": {"translate": [{"y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.4667, "y": -3.11, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 2.0667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "y": -7.35}]}, "bone83": {"translate": [{"y": -6.21, "curve": 0.31, "c2": 0.26, "c3": 0.69, "c4": 0.74}, {"time": 1.4667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "y": -7.35, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "y": -6.21}]}, "bone84": {"translate": [{"y": -6.04, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.6667, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.4667, "y": -5.47, "curve": 0.322, "c2": 0.3, "c3": 0.663, "c4": 0.65}, {"time": 2.0667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.3, "curve": 0.242, "c3": 0.668, "c4": 0.67}, {"time": 5.3333, "y": -6.04}]}, "bone85": {"translate": [{"y": -6.21, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "y": -7.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "y": -5.24, "curve": 0.33, "c2": 0.32, "c3": 0.693, "c4": 0.75}, {"time": 2.6667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.2667, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "y": -6.21}]}, "bone86": {"translate": [{"y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.4667, "y": -3.13, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "y": -7.35}]}, "bone87": {"translate": [{"y": -4, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.2333, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 1.4667, "y": -7.11, "curve": 0.278, "c2": 0.14, "c3": 0.642, "c4": 0.58}, {"time": 2.6667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.9, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5.3333, "y": -4}]}, "bone88": {"translate": [{"y": -4, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.2333, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 1.4667, "y": -7.08, "curve": 0.274, "c2": 0.13, "c3": 0.682, "c4": 0.72}, {"time": 3.3, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.9, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5.3333, "y": -4}]}, "bone89": {"translate": [{"y": -6.04, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.6667, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.4667, "y": -5.46, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 3.3, "curve": 0.242, "c3": 0.668, "c4": 0.67}, {"time": 5.3333, "y": -6.04}]}, "bone90": {"translate": [{"y": -1.71, "curve": 0.327, "c2": 0.31, "c3": 0.708, "c4": 0.8}, {"time": 1.4667, "y": -6.7, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 1.9, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.3, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.5667, "curve": 0.27, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "y": -1.71}]}, "bone91": {"translate": [{"y": -2.1, "curve": 0.338, "c2": 0.35, "c3": 0.72, "c4": 0.84}, {"time": 1.4667, "y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 1.7667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.8333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -2.1}]}, "bone92": {"translate": [{"y": -4.21, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "y": -7.35, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 1.4667, "y": -6.97, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 3.8333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "y": -4.21}]}, "bone93": {"translate": [{"y": -0.25, "curve": 0.274, "c2": 0.13, "c3": 0.656, "c4": 0.63}, {"time": 1.4667, "y": -5.06, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 2.4333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.8333, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 5.1, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 5.3333, "y": -0.25}]}, "bone94": {"translate": [{"y": -0.36, "curve": 0.28, "c2": 0.16, "c3": 0.662, "c4": 0.65}, {"time": 1.4667, "y": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 4.4333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "y": -0.36}]}, "bone95": {"translate": [{"y": -2.1, "curve": 0.338, "c2": 0.35, "c3": 0.72, "c4": 0.84}, {"time": 1.4667, "y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 1.7667, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -2.1}]}, "bone96": {"translate": [{"y": -0.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3667, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 1.4667, "y": -2.92, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 3.0333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.4333, "y": -3.35, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 5.3333, "y": -0.48}]}, "yuwyuw3": {"translate": [{"y": 5.36, "curve": 0.341, "c2": 0.36, "c3": 0.757}, {"time": 3.8, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": 7.72, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "y": 5.36}], "scale": [{"x": 0.996, "y": 0.996, "curve": 0.341, "c2": 0.36, "c3": 0.757}, {"time": 3.8, "x": 1.062, "y": 1.062, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.978, "y": 0.978, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 0.996, "y": 0.996}]}, "bone97": {"translate": [{"y": -2.08, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.8333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -2.08}]}, "bone98": {"translate": [{"y": -4.21, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "y": -4.21}]}, "bone99": {"translate": [{"y": -0.25, "curve": 0.285, "c2": 0.15, "c3": 0.754}, {"time": 2.4333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.8333, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 5.1, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 5.3333, "y": -0.25}]}, "bone100": {"translate": [{"y": -0.38, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.8333, "y": -3.11, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 4.4333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "y": -0.38}]}, "bone101": {"translate": [{"y": -2.08, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.8333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -2.08}]}, "bone102": {"translate": [{"y": -0.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 3.8333, "y": -5.47, "curve": 0.322, "c2": 0.3, "c3": 0.663, "c4": 0.65}, {"time": 4.4333, "y": -3.35, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 5.3333, "y": -0.48}]}, "bone103": {"translate": [{"y": -0.35, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "y": -7.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.8333, "y": -5.24, "curve": 0.33, "c2": 0.32, "c3": 0.693, "c4": 0.75}, {"time": 5.0333, "y": -1.13, "curve": 0.352, "c2": 0.42, "c3": 0.687, "c4": 0.76}, {"time": 5.3333, "y": -0.35}]}, "bone104": {"translate": [{"y": -0.38, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.8333, "y": -3.13, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "y": -0.38}]}, "bone105": {"translate": [{"y": -2.28, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 3.8333, "y": -7.11, "curve": 0.278, "c2": 0.14, "c3": 0.642, "c4": 0.58}, {"time": 5.0333, "y": -3.35, "curve": 0.337, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 5.3333, "y": -2.28}]}, "bone106": {"translate": [{"y": -2.28, "curve": 0.345, "c2": 0.37, "c3": 0.681, "c4": 0.72}, {"time": 0.3667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 3.8333, "y": -7.08, "curve": 0.274, "c2": 0.13, "c3": 0.656, "c4": 0.63}, {"time": 5.3333, "y": -2.28}]}, "bone107": {"translate": [{"y": -0.5, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 3.8333, "y": -5.46, "curve": 0.332, "c2": 0.33, "c3": 0.714, "c4": 0.82}, {"time": 5.3333, "y": -0.5}]}, "bone108": {"translate": [{"y": -4.64, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.3667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.6, "curve": 0.243, "c3": 0.693, "c4": 0.76}, {"time": 3.8333, "y": -6.7, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 4.2667, "y": -7.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "y": -4.64}]}, "bone109": {"translate": [{"y": -4.19, "curve": 0.342, "c2": 0.36, "c3": 0.692, "c4": 0.75}, {"time": 0.9, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.4667, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.8333, "y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 4.1333, "y": -7.35, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 5.3333, "y": -4.19}]}, "bone110": {"translate": [{"y": -2.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "y": -7.35, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.8333, "y": -6.97, "curve": 0.28, "c2": 0.16, "c3": 0.662, "c4": 0.65}, {"time": 5.3333, "y": -2.1}]}, "bone111": {"translate": [{"y": -6.4, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.1333, "curve": 0.244, "c3": 0.645, "c4": 0.58}, {"time": 3.8333, "y": -5.06, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 4.8, "y": -7.35, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "y": -6.4}]}, "bone112": {"translate": [{"y": -6.21, "curve": 0.31, "c2": 0.26, "c3": 0.69, "c4": 0.74}, {"time": 1.4667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.0667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.8333, "y": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.7333, "y": -7.35, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "y": -6.21}]}, "bone113": {"translate": [{"y": -4.21, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.4667, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.8333, "y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 4.1333, "y": -7.35, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 5.3333, "y": -4.21}]}, "bone114": {"translate": [{"y": -7.31, "curve": 0.34, "c2": 0.66, "c3": 0.674}, {"time": 0.0667, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.4667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.7333, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 3.8333, "y": -2.92, "curve": 0.362, "c2": 0.44, "c3": 0.746, "c4": 0.96}, {"time": 5.3333, "y": -7.31}]}}, "deform": {"default": {"y8": {"y2": [{"vertices": [16.10937, -25.62167, 9.31094, -20.06378, 9.31129, -20.06448, 9.31125, -20.06381, -0.22957, -19.53622, -0.22926, -19.53687, -0.22929, -19.53629, -8.90443, -9.26627, -8.90421, -9.26675, -8.90414, -9.2663, -8.90506, -9.26544, -12.29497, 2.03894, -12.29492, 2.03955, -12.29581, 2.04034, -18.05946, 12.37048, -18.06018, 12.37115, -17.38947, 20.59857, -19.91174, 28.26273, -15.7113, 28.72037, -7.62756, 23.15994, -0.23676, 16.43866, -0.23773, 16.43961, 1.49013, 8.659, 1.4902, 8.65955, 1.48935, 8.66028, 7.84041, 8.94171, 7.84042, 8.94223, 7.83948, 8.94296, 14.81727, 0.26367, 14.81766, 0.26318, 14.81769, 0.26367, 18.88781, -13.31808, 18.88808, -13.3186, 20.12657, -25.22876, 15.05077, -17.40222, 15.05109, -17.40259, 8.85825, -12.1402, 8.8586, -12.14072, 8.8586, -12.14023, 3.59812, -6.28696, 3.59845, -6.28735, 3.59851, -6.28696, -2.213, 0.8201, -2.21292, 0.82065, -2.21378, 0.82135, -6.58611, 7.09061, -6.58698, 7.09143, -12.3877, 16.84506, -15.0441, 22.80603]}]}, "y9": {"y2": [{"vertices": [65.15805, -67.24274, 41.02381, -54.77084, 41.02394, -54.771, 41.02385, -54.77087, 11.80891, -59.28009, 11.80899, -59.28027, 11.80891, -59.28009, -21.02075, -33.74622, -21.02066, -33.7464, -21.02065, -33.74622, -21.0209, -33.74603, -38.51949, -1.69589, -38.51949, -1.69571, -38.51978, -1.6955, -62.57953, 25.88412, -62.5799, 25.8844, -65.81412, 51.21637, -78.35391, 72.80045, -65.93307, 76.87354, -37.90634, 65.21704, -11.23717, 49.60437, -11.23749, 49.60461, -1.03134, 27.16345, -1.03134, 27.16364, -1.03152, 27.16382, 18.00871, 32.08307, 18.00871, 32.08328, 18.00851, 32.0835, 44.68142, 10.28036, 44.68153, 10.28018, 44.68153, 10.28036, 65.69395, -28.22357, 65.694, -28.22366, 77.06563, -63.48288, 56.69364, -43.04111, 56.6937, -43.04123, 34.58279, -31.0773, 34.58287, -31.07758, 34.58284, -31.07733, 14.91531, -16.7269, 14.91539, -16.72702, 14.91542, -16.7269, -7.22314, 1.06705, -7.22313, 1.06723, -7.22336, 1.06747, -24.47272, 17.24649, -24.47305, 17.2467, -48.27307, 43.0567, -60.1286, 59.39923]}]}, "y5": {"y4": [{"time": 1.8667, "vertices": [122.42432, -178.51215, 122.424, -178.51251, 108.582, -130.00452, 108.58177, -130.0047, 91.20557, -71.22211, 91.20535, -71.22235, 91.20516, -71.22229, 72.84257, -11.15509, 72.84291, -11.15533, 62.1306, 24.71179, 62.13095, 24.71161, 51.0834, 61.43243, 51.08366, 61.43213, 51.08363, 61.43152, 38.90002, 102.42249, 38.90028, 102.42224, 38.90021, 102.42249, 23.26602, 151.65863, 23.26588, 151.65894, 23.26608, 151.65851, 12.53099, 186.98663, 12.53081, 186.98694, 12.5311, 186.98627, 2.25403, 222.30444, 2.25357, 222.30411, -22.88272, 214.94757, -22.88315, 214.94724, -61.47366, 203.00299, -61.47415, 203.00278, -118.17331, 186.55267, -105.6102, 163.63507, -105.61038, 163.63403, -85.84824, 139.62912, -85.84839, 139.6282, -62.19771, 130.78552, -62.19788, 130.78662, -62.19754, 130.78601, -62.19803, 130.7857, -41.44536, 103.52863, -41.44533, 103.5296, -41.4455, 103.52972, -41.44568, 103.52878, -33.9807, 67.97516, -33.98079, 67.97455, -33.981, 67.97571, -17.14278, 41.31372, -17.14243, 41.31323, -17.14255, 41.31268, 6.08434, 46.33337, 6.08466, 46.33301, 6.08456, 46.33234, 17.603, 62.79456, 17.60326, 62.79443, 17.6032, 62.79358, 30.63833, 54.58142, 30.63858, 54.58112, 30.63855, 54.58038, 37.01707, 13.21051, 37.0174, 13.21008, 27.10822, -21.33423, 27.10828, -21.33368, 27.10864, -21.33405, 23.98895, -56.43243, 23.98868, -56.43283, 23.98876, -56.43243, 23.98909, -56.4328, 13.73555, -20.95959, 13.73567, -20.95905, -3.28304, -36.97824, -3.28292, -36.97772, 0.98465, -83.77521, 0.98442, -83.77509, 0.98413, -83.77554, 0.98425, -83.77496, -7.87476, -118.40103, -7.87526, -118.40115, -7.87512, -118.40076, -20.35046, -148.25174, -20.35095, -148.25204, -22.18073, -177.69879, -22.18117, -177.69922, -10.85037, -216.33472, -10.85088, -216.33496, 26.76328, -205.60773, 26.76324, -205.608, 26.76274, -205.6083, 69.50912, -193.54852, 69.50906, -193.54898, 6.03781, -190.59012, 6.03731, -190.59048, 26.04688, -173.46051, 26.04643, -173.46075, 53.24158, -155.87567, 53.24149, -155.87607, 53.24133, -155.87592, 53.24104, -155.87634, 83.44342, -142.34741, 83.44312, -142.34753, 83.44283, -142.34781, 61.86058, -74.79395, 61.86023, -74.7944, 61.86035, -74.79395, 34.26791, -105.94177, 34.26758, -105.94196, 34.26732, -105.94235, 34.26742, -105.94189, 9.48077, -143.79022, 9.48068, -143.79062, 9.48015, -143.79083, 56.17184, -25.35657, 56.17194, -25.35608, 56.17229, -25.35632, 7.015, 96.2547, 7.0153, 96.25439, 7.0152, 96.25366, 7.01517, 96.25458, 7.01505, 96.25482, -14.15971, 74.8396, -14.15945, 74.83929, -14.1595, 74.83856, -14.15971, 74.83978, -9.3461, 139.9331, -9.34625, 139.93231, -9.3463, 139.93329, -9.34642, 139.93341, -9.34616, 139.93298, -55.28664, 175.57019, -55.28682, 175.57037, -55.28653, 175.56982, -55.28699, 175.56952, -11.8609, 180.00232, -11.86102, 180.00262, -11.86075, 180.00201, -11.86121, 180.00159]}]}, "y7": {"y2": [{"vertices": [207.65494, -69.05829, 143.11801, -69.81058, 143.1181, -69.81039, 143.11813, -69.81036, 86.85136, -111.66437, 86.85141, -111.66412, 86.85149, -111.66409, -9.11251, -94.27563, -9.11249, -94.27548, -9.1124, -94.27542, -9.11227, -94.2756, -78.27922, -45.54047, -78.27922, -45.54045, -78.27904, -45.54068, -155.28198, -13.06992, -155.282, -13.07011, -190.00363, 36.35193, -240.1302, 67.59294, -218.67113, 89.86694, -147.1582, 96.53749, -74.84624, 93.15256, -74.84616, 93.15237, -32.87651, 55.94128, -32.87657, 55.94136, -32.87656, 55.94115, -0.33859, 86.62933, -0.33875, 86.62932, -0.33844, 86.62912, 78.45493, 70.15991, 78.45509, 70.16022, 78.45496, 70.1602, 165.20906, 12.98987, 165.20915, 12.99023, 228.38805, -48.00757, 163.13159, -27.83704, 163.13174, -27.83661, 102.80416, -27.6311, 102.80426, -27.63074, 102.80424, -27.63078, 46.40345, -19.17697, 46.40352, -19.17661, 46.40359, -19.17665, -18.23434, -5.97861, -18.23439, -5.97865, -18.23422, -5.97885, -68.9928, 9.96274, -68.99261, 9.96254, -144.28871, 38.71985, -187.17915, 59.75914]}]}}}}, "animation2": {"slots": {"y8": {"color": [{"color": "ffffff92", "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "color": "ffffff00", "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "color": "ffffff92"}]}, "y9": {"color": [{"color": "ffffff92", "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "color": "ffffff00", "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "color": "ffffff92"}]}, "y5": {"color": [{"color": "ffffff99", "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.2, "color": "ffffffff", "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 5.3333, "color": "ffffff99"}]}, "y11": {"color": [{"color": "ffffff00"}]}, "y12": {"color": [{"color": "ffffff00"}]}, "y10": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "y4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "y1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "y7": {"color": [{"color": "ffffffa8", "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "color": "ffffffff", "curve": 0.26, "c3": 0.618, "c4": 0.45}, {"time": 5.3333, "color": "ffffffa8"}]}, "y2": {"color": [{"color": "ffffff92", "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "color": "ffffff00", "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "color": "ffffff92"}]}, "y6": {"color": [{"color": "ffffff83", "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "color": "ffffff00", "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 5.3333, "color": "ffffff83"}]}, "y3": {"color": [{"color": "ffffff00"}]}}, "bones": {"yunc1": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": 1.11, "y": 1.11, "curve": "stepped"}, {"time": 5.3333}]}, "yun2": {"translate": [{"x": 6.19, "y": 3.63, "curve": 0.341, "c2": 0.36, "c3": 0.757}, {"time": 3.8, "x": 28.33, "y": 16.61, "curve": "stepped"}, {"time": 3.8333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 6.19, "y": 3.63}], "scale": [{"x": 1.052, "y": 1.052, "curve": 0.341, "c2": 0.36, "c3": 0.757}, {"time": 3.8, "x": 1.238, "y": 1.238, "curve": "stepped"}, {"time": 3.8333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 1.052, "y": 1.052}]}, "bone5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 3.84, "y": 8.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone6": {"translate": [{"x": 0.59, "y": 1.28, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 3.84, "y": 8.33, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "x": 0.59, "y": 1.28}]}, "bone7": {"translate": [{"x": 1.64, "y": 3.55, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 3.84, "y": 8.33, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": 1.64, "y": 3.55}]}, "bone8": {"translate": [{"x": 2.75, "y": 5.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": 3.84, "y": 8.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": 2.75, "y": 5.96}]}, "yun3": {"translate": [{"x": 21.35, "y": 12.52, "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 1.6, "x": 28.33, "y": 16.61, "curve": "stepped"}, {"time": 1.6667, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 5.3333, "x": 21.35, "y": 12.52}], "scale": [{"x": 1.18, "y": 1.18, "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 1.6, "x": 1.238, "y": 1.238, "curve": "stepped"}, {"time": 1.6667, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 5.3333, "x": 1.18, "y": 1.18}]}, "bone9": {"translate": [{"x": 2.54, "y": 5.5, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 3.84, "y": 8.33, "curve": 0.26, "c3": 0.618, "c4": 0.45}, {"time": 5.3333, "x": 2.54, "y": 5.5}]}, "bone10": {"translate": [{"x": 3.5, "y": 7.59, "curve": 0.294, "c2": 0.2, "c3": 0.687, "c4": 0.73}, {"time": 1.6667, "x": 0.59, "y": 1.28, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "x": 3.84, "y": 8.33, "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 3.5, "y": 7.59}]}, "bone11": {"translate": [{"x": 3.76, "y": 8.14, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.1667, "x": 3.84, "y": 8.33, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.6667, "x": 1.64, "y": 3.55, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 2.8333, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 5.3333, "x": 3.76, "y": 8.14}]}, "bone12": {"translate": [{"x": 2.97, "y": 6.43, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.7667, "x": 3.84, "y": 8.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.6667, "x": 2.75, "y": 5.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 3.4333, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 5.3333, "x": 2.97, "y": 6.43}]}, "yun4": {"translate": [{"x": 6.19, "y": 3.63, "curve": 0.341, "c2": 0.36, "c3": 0.757}, {"time": 3.8, "x": 28.33, "y": 16.61, "curve": "stepped"}, {"time": 3.8333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 6.19, "y": 3.63}], "scale": [{"x": 1.052, "y": 1.052, "curve": 0.341, "c2": 0.36, "c3": 0.757}, {"time": 3.8, "x": 1.238, "y": 1.238, "curve": "stepped"}, {"time": 3.8333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 1.052, "y": 1.052}]}, "bone13": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 3.84, "y": 8.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone14": {"translate": [{"x": 0.59, "y": 1.28, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": 3.84, "y": 8.33, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "x": 0.59, "y": 1.28}]}, "bone15": {"translate": [{"x": 1.64, "y": 3.55, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 3.84, "y": 8.33, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": 1.64, "y": 3.55}]}, "bone16": {"translate": [{"x": 2.75, "y": 5.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": 3.84, "y": 8.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": 2.75, "y": 5.96}]}, "bone17": {"translate": [{"x": 2.2, "y": 4.77, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "x": 3.84, "y": 8.33, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": 2.2, "y": 4.77}]}, "bone18": {"translate": [{"x": 1.09, "y": 2.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "x": 3.84, "y": 8.33, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.8333, "x": 0.59, "y": 1.28, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": 1.09, "y": 2.36}]}, "bone19": {"translate": [{"x": 0.2, "y": 0.43, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "x": 3.84, "y": 8.33, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.8333, "x": 1.64, "y": 3.55, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "x": 0.2, "y": 0.43}]}, "bone20": {"translate": [{"x": 0.19, "y": 0.41, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 3.84, "y": 8.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.8333, "x": 2.75, "y": 5.96, "curve": 0.338, "c2": 0.35, "c3": 0.72, "c4": 0.84}, {"time": 5.3333, "x": 0.19, "y": 0.41}]}, "bone28": {"translate": [{"x": 5.07, "y": 5.91, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 1.2, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.9667, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 3.8, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 5.3333, "x": 5.07, "y": 5.91}]}, "bone27": {"translate": [{"x": 8.06, "y": 9.41, "curve": 0.365, "c2": 0.63, "c3": 0.702}, {"time": 0.3667, "x": 8.64, "y": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": 0.244, "c3": 0.701, "c4": 0.79}, {"time": 5.3333, "x": 8.06, "y": 9.41}]}, "bone26": {"translate": [{"x": 7.36, "y": 8.58, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.6, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.3667, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 3.2, "curve": 0.243, "c3": 0.675, "c4": 0.7}, {"time": 5.3333, "x": 7.36, "y": 8.58}]}, "bone25": {"translate": [{"x": 8.34, "y": 9.73, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "x": 8.64, "y": 10.08, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 5.3333, "x": 8.34, "y": 9.73}]}, "bone24": {"translate": [{"x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 1.7667, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 8.64, "y": 10.08}]}, "bone23": {"translate": [{"x": 6.35, "y": 7.41, "curve": 0.348, "c2": 0.39, "c3": 0.757}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 8.64, "y": 10.08, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 6.35, "y": 7.41}]}, "bone22": {"translate": [{"x": 4.06, "y": 4.74, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 8.64, "y": 10.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5.3333, "x": 4.06, "y": 4.74}]}, "bone21": {"translate": [{"x": 4.83, "y": 5.63, "curve": 0.336, "c2": 0.34, "c3": 0.678, "c4": 0.7}, {"time": 0.6, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 8.64, "y": 10.08, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5.3333, "x": 4.83, "y": 5.63}]}, "bone4": {"translate": [{"x": 1.38, "y": 1.61, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 8.64, "y": 10.08, "curve": 0.242, "c3": 0.672, "c4": 0.69}, {"time": 5.3333, "x": 1.38, "y": 1.61}]}, "bone3": {"translate": [{"x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 5.3333, "x": 2.29, "y": 2.67}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 8.64, "y": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "yunc2": {"scale": [{"x": 1.086, "y": 1.086, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 1.4667, "x": 1.11, "y": 1.11, "curve": "stepped"}, {"time": 1.5333, "curve": 0.243, "c3": 0.659, "c4": 0.64}, {"time": 5.3333, "x": 1.086, "y": 1.086}]}, "bone29": {"translate": [{"x": 5.36, "y": 6.25, "curve": 0.364, "c2": 0.45, "c3": 0.755}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": 8.64, "y": 10.08, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 5.3333, "x": 5.36, "y": 6.25}]}, "bone30": {"translate": [{"x": 8.33, "y": 9.72, "curve": 0.274, "c2": 0.13, "c3": 0.662, "c4": 0.65}, {"time": 1.5333, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "x": 8.64, "y": 10.08, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 5.3333, "x": 8.33, "y": 9.72}]}, "bone31": {"translate": [{"x": 7.67, "y": 8.94, "curve": 0.3, "c2": 0.22, "c3": 0.687, "c4": 0.73}, {"time": 1.5333, "x": 1.38, "y": 1.61, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "x": 8.64, "y": 10.08, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 5.3333, "x": 7.67, "y": 8.94}]}, "bone32": {"translate": [{"x": 8.06, "y": 9.41, "curve": 0.365, "c2": 0.63, "c3": 0.702}, {"time": 0.3667, "x": 8.64, "y": 10.08, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.5333, "x": 4.83, "y": 5.63, "curve": 0.336, "c2": 0.34, "c3": 0.678, "c4": 0.7}, {"time": 2.1333, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.9667, "curve": 0.244, "c3": 0.701, "c4": 0.79}, {"time": 5.3333, "x": 8.06, "y": 9.41}]}, "bone33": {"translate": [{"x": 8.45, "y": 9.86, "curve": 0.352, "c2": 0.65, "c3": 0.686}, {"time": 0.1667, "x": 8.64, "y": 10.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.5333, "x": 4.06, "y": 4.74, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 2.7667, "curve": 0.247, "c3": 0.723, "c4": 0.88}, {"time": 5.3333, "x": 8.45, "y": 9.86}]}, "bone34": {"translate": [{"x": 6.95, "y": 8.1, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 0.7, "x": 8.64, "y": 10.08, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.5333, "x": 6.35, "y": 7.41, "curve": 0.348, "c2": 0.39, "c3": 0.757}, {"time": 3.3, "curve": 0.242, "c3": 0.664, "c4": 0.66}, {"time": 5.3333, "x": 6.95, "y": 8.1}]}, "bone35": {"translate": [{"x": 3.58, "y": 4.17, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 1.5333, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 3.3, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 4.1333, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "x": 3.58, "y": 4.17}]}, "bone36": {"translate": [{"x": 4.57, "y": 5.33, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "x": 8.64, "y": 10.08, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 1.5333, "x": 8.34, "y": 9.73, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 3.9, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5.3333, "x": 4.57, "y": 5.33}]}, "bone37": {"translate": [{"x": 1.29, "y": 1.51, "curve": 0.309, "c2": 0.25, "c3": 0.691, "c4": 0.75}, {"time": 1.5333, "x": 7.36, "y": 8.58, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 2.1333, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 3.9, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 4.7333, "curve": 0.281, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "x": 1.29, "y": 1.51}]}, "bone38": {"translate": [{"x": 2.17, "y": 2.53, "curve": 0.331, "c2": 0.33, "c3": 0.714, "c4": 0.82}, {"time": 1.5333, "x": 8.06, "y": 9.41, "curve": 0.365, "c2": 0.63, "c3": 0.702}, {"time": 1.9, "x": 8.64, "y": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 2.17, "y": 2.53}]}, "bone39": {"translate": [{"curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 1.5333, "x": 5.07, "y": 5.91, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.7333, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4.5, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 5.3333}]}, "yunc3": {"scale": [{"x": 1.021, "y": 1.021, "curve": 0.335, "c2": 0.34, "c3": 0.758}, {"time": 3.9, "x": 1.11, "y": 1.11, "curve": "stepped"}, {"time": 3.9667, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 5.3333, "x": 1.021, "y": 1.021}]}, "bone40": {"translate": [{"x": 4.32, "y": 5.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3667, "x": 8.64, "y": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 4.32, "y": 5.04}]}, "bone41": {"translate": [{"x": 1.08, "y": 1.26, "curve": 0.319, "c2": 0.28, "c3": 0.757}, {"time": 2.2, "x": 8.64, "y": 10.08, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 3.9667, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 4.8, "curve": 0.285, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "x": 1.08, "y": 1.26}]}, "bone42": {"translate": [{"x": 1.91, "y": 2.22, "curve": 0.341, "c2": 0.36, "c3": 0.757}, {"time": 1.9667, "x": 8.64, "y": 10.08, "curve": 0.242, "c3": 0.672, "c4": 0.69}, {"time": 3.9667, "x": 1.38, "y": 1.61, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 4.5667, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 1.91, "y": 2.22}]}, "bone43": {"translate": [{"x": 0.02, "y": 0.03, "curve": 0.341, "c2": 0.66, "c3": 0.674}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "x": 8.64, "y": 10.08, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 3.9667, "x": 4.83, "y": 5.63, "curve": 0.336, "c2": 0.34, "c3": 0.678, "c4": 0.7}, {"time": 4.5667, "x": 2.29, "y": 2.67, "curve": 0.377, "c2": 0.54, "c3": 0.725, "c4": 0.94}, {"time": 5.3333, "x": 0.02, "y": 0.03}]}, "bone44": {"translate": [{"x": 0.13, "y": 0.15, "curve": 0.268, "c2": 0.08, "c3": 0.752}, {"time": 2.6, "x": 8.64, "y": 10.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 3.9667, "x": 4.06, "y": 4.74, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 5.2, "curve": 0.32, "c3": 0.654, "c4": 0.34}, {"time": 5.3333, "x": 0.13, "y": 0.15}]}, "bone45": {"translate": [{"x": 0.78, "y": 0.91, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 8.64, "y": 10.08, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 3.9667, "x": 6.35, "y": 7.41, "curve": 0.332, "c2": 0.33, "c3": 0.707, "c4": 0.8}, {"time": 5.3333, "x": 0.78, "y": 0.91}]}, "bone46": {"translate": [{"x": 4.05, "y": 4.72, "curve": 0.339, "c2": 0.35, "c3": 0.676, "c4": 0.7}, {"time": 0.4, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 8.64, "y": 10.08, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5.3333, "x": 4.05, "y": 4.72}]}, "bone47": {"translate": [{"x": 3.07, "y": 3.59, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 8.64, "y": 10.08, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 3.9667, "x": 8.34, "y": 9.73, "curve": 0.275, "c2": 0.13, "c3": 0.652, "c4": 0.62}, {"time": 5.3333, "x": 3.07, "y": 3.59}]}, "bone48": {"translate": [{"x": 6.58, "y": 7.68, "curve": 0.32, "c2": 0.29, "c3": 0.676, "c4": 0.7}, {"time": 1, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 1.8333, "curve": 0.243, "c3": 0.675, "c4": 0.7}, {"time": 3.9667, "x": 7.36, "y": 8.58, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 4.5667, "x": 8.64, "y": 10.08, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 6.58, "y": 7.68}]}, "bone49": {"translate": [{"x": 5.62, "y": 6.55, "curve": 0.361, "c2": 0.44, "c3": 0.755}, {"time": 1.6, "curve": 0.244, "c3": 0.701, "c4": 0.79}, {"time": 3.9667, "x": 8.06, "y": 9.41, "curve": 0.365, "c2": 0.63, "c3": 0.702}, {"time": 4.3333, "x": 8.64, "y": 10.08, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 5.3333, "x": 5.62, "y": 6.55}]}, "bone50": {"translate": [{"x": 8.45, "y": 9.86, "curve": 0.267, "c2": 0.1, "c3": 0.66, "c4": 0.64}, {"time": 1.6, "x": 2.29, "y": 2.67, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.4333, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 3.9667, "x": 5.07, "y": 5.91, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 5.1667, "x": 8.64, "y": 10.08, "curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 5.3333, "x": 8.45, "y": 9.86}]}, "bone51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -9.1, "y": 16.55, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone52": {"translate": [{"x": -1.4, "y": 2.55, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": -9.1, "y": 16.55, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "x": -1.4, "y": 2.55}]}, "bone53": {"translate": [{"x": -3.88, "y": 7.06, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -9.1, "y": 16.55, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": -3.88, "y": 7.06}]}, "bone54": {"translate": [{"x": -6.52, "y": 11.86, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": -9.1, "y": 16.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": -6.52, "y": 11.86}]}, "bone55": {"translate": [{"x": -8.64, "y": 15.7, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": -9.1, "y": 16.55, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "x": -8.64, "y": 15.7}]}, "bone56": {"translate": [{"x": -9.1, "y": 16.55, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -9.1, "y": 16.55}]}, "bone57": {"translate": [{"x": -7.7, "y": 14, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "x": -9.1, "y": 16.55, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2.6667, "x": -1.4, "y": 2.55, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.2667, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "x": -7.7, "y": 14}]}, "bone58": {"translate": [{"x": -5.22, "y": 9.49, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "x": -9.1, "y": 16.55, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 2.6667, "x": -3.88, "y": 7.06, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 3.8333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "x": -5.22, "y": 9.49}]}, "bone59": {"translate": [{"x": -2.58, "y": 4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "x": -9.1, "y": 16.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": -6.52, "y": 11.86, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "x": -2.58, "y": 4.7}]}, "bone60": {"translate": [{"x": -0.47, "y": 0.85, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "x": -9.1, "y": 16.55, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2.6667, "x": -8.64, "y": 15.7, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "x": -0.47, "y": 0.85}]}, "bone61": {"translate": [{"y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "y": -1.13}]}, "bone62": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone63": {"translate": [{"y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5.3333, "y": -3.35}]}, "bone64": {"translate": [{"y": -3.11, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.6, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "y": -3.11}]}, "bone65": {"translate": [{"y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "y": -1.13}]}, "bone66": {"translate": [{"y": -5.47, "curve": 0.322, "c2": 0.3, "c3": 0.663, "c4": 0.65}, {"time": 0.6, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "y": -5.47}]}, "bone67": {"translate": [{"y": -5.24, "curve": 0.33, "c2": 0.32, "c3": 0.693, "c4": 0.75}, {"time": 1.2, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "y": -7.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -5.24}]}, "bone68": {"translate": [{"y": -3.13, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "y": -3.13}]}, "bone69": {"translate": [{"y": -7.11, "curve": 0.278, "c2": 0.14, "c3": 0.642, "c4": 0.58}, {"time": 1.2, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 5.3333, "y": -7.11}]}, "bone70": {"translate": [{"y": -7.08, "curve": 0.274, "c2": 0.13, "c3": 0.682, "c4": 0.72}, {"time": 1.8333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 5.3333, "y": -7.08}]}, "bone71": {"translate": [{"y": -5.46, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "y": -5.46}]}, "bone72": {"translate": [{"y": -6.7, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 0.4, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.8333, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.0667, "curve": 0.243, "c3": 0.693, "c4": 0.76}, {"time": 5.3333, "y": -6.7}]}, "bone73": {"translate": [{"y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.3, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2.3667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.9667, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 5.3333, "y": -6.97}]}, "bone74": {"translate": [{"y": -6.97, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "y": -7.35, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "y": -6.97}]}, "bone75": {"translate": [{"y": -5.06, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.9333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2.3667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.6, "curve": 0.244, "c3": 0.645, "c4": 0.58}, {"time": 5.3333, "y": -5.06}]}, "bone76": {"translate": [{"y": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2.9667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 5.3333, "y": -5.26}]}, "bone77": {"translate": [{"y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.3, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 5.3333, "y": -6.97}]}, "bone78": {"translate": [{"y": -2.92, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.5333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2.9667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.2, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 5.3333, "y": -2.92}]}, "yuwyuw1": {"translate": [{"y": 7.72}], "scale": [{"x": 0.978, "y": 0.978}]}, "yuwyuw2": {"translate": [{"y": -0.86}], "scale": [{"x": 1.045, "y": 1.045}]}, "bone79": {"translate": [{"y": -6.21, "curve": 0.31, "c2": 0.26, "c3": 0.69, "c4": 0.74}, {"time": 1.4667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "y": -7.35, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "y": -6.21}]}, "bone80": {"translate": [{"y": -4.21, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "y": -7.35, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 5.3333, "y": -4.21}]}, "bone81": {"translate": [{"y": -7.29, "curve": 0.34, "c2": 0.66, "c3": 0.674}, {"time": 0.0667, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.4667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.7333, "curve": 0.249, "c3": 0.741, "c4": 0.96}, {"time": 5.3333, "y": -7.29}]}, "bone82": {"translate": [{"y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.4667, "y": -3.11, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 2.0667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "y": -7.35}]}, "bone83": {"translate": [{"y": -6.21, "curve": 0.31, "c2": 0.26, "c3": 0.69, "c4": 0.74}, {"time": 1.4667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "y": -7.35, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "y": -6.21}]}, "bone84": {"translate": [{"y": -6.04, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.6667, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.4667, "y": -5.47, "curve": 0.322, "c2": 0.3, "c3": 0.663, "c4": 0.65}, {"time": 2.0667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.3, "curve": 0.242, "c3": 0.668, "c4": 0.67}, {"time": 5.3333, "y": -6.04}]}, "bone85": {"translate": [{"y": -6.21, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6, "y": -7.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "y": -5.24, "curve": 0.33, "c2": 0.32, "c3": 0.693, "c4": 0.75}, {"time": 2.6667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.2667, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 5.3333, "y": -6.21}]}, "bone86": {"translate": [{"y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.4667, "y": -3.13, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "y": -7.35}]}, "bone87": {"translate": [{"y": -4, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.2333, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 1.4667, "y": -7.11, "curve": 0.278, "c2": 0.14, "c3": 0.642, "c4": 0.58}, {"time": 2.6667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.9, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5.3333, "y": -4}]}, "bone88": {"translate": [{"y": -4, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.2333, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 1.4667, "y": -7.08, "curve": 0.274, "c2": 0.13, "c3": 0.682, "c4": 0.72}, {"time": 3.3, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.9, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5.3333, "y": -4}]}, "bone89": {"translate": [{"y": -6.04, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.6667, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.4667, "y": -5.46, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 3.3, "curve": 0.242, "c3": 0.668, "c4": 0.67}, {"time": 5.3333, "y": -6.04}]}, "bone90": {"translate": [{"y": -1.71, "curve": 0.327, "c2": 0.31, "c3": 0.708, "c4": 0.8}, {"time": 1.4667, "y": -6.7, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 1.9, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.3, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.5667, "curve": 0.27, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "y": -1.71}]}, "bone91": {"translate": [{"y": -2.1, "curve": 0.338, "c2": 0.35, "c3": 0.72, "c4": 0.84}, {"time": 1.4667, "y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 1.7667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.8333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -2.1}]}, "bone92": {"translate": [{"y": -4.21, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "y": -7.35, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 1.4667, "y": -6.97, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 3.8333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "y": -4.21}]}, "bone93": {"translate": [{"y": -0.25, "curve": 0.274, "c2": 0.13, "c3": 0.656, "c4": 0.63}, {"time": 1.4667, "y": -5.06, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 2.4333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.8333, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 5.1, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 5.3333, "y": -0.25}]}, "bone94": {"translate": [{"y": -0.36, "curve": 0.28, "c2": 0.16, "c3": 0.662, "c4": 0.65}, {"time": 1.4667, "y": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 4.4333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "y": -0.36}]}, "bone95": {"translate": [{"y": -2.1, "curve": 0.338, "c2": 0.35, "c3": 0.72, "c4": 0.84}, {"time": 1.4667, "y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 1.7667, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -2.1}]}, "bone96": {"translate": [{"y": -0.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3667, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 1.4667, "y": -2.92, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 3.0333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.4333, "y": -3.35, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 5.3333, "y": -0.48}]}, "yuwyuw3": {"translate": [{"y": 5.36}], "scale": [{"x": 0.996, "y": 0.996}]}, "bone97": {"translate": [{"y": -2.08, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.8333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -2.08}]}, "bone98": {"translate": [{"y": -4.21, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 5.3333, "y": -4.21}]}, "bone99": {"translate": [{"y": -0.25, "curve": 0.285, "c2": 0.15, "c3": 0.754}, {"time": 2.4333, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.8333, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 5.1, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 5.3333, "y": -0.25}]}, "bone100": {"translate": [{"y": -0.38, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.8333, "y": -3.11, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 4.4333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "y": -0.38}]}, "bone101": {"translate": [{"y": -2.08, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "y": -7.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.8333, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 4.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "y": -2.08}]}, "bone102": {"translate": [{"y": -0.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 3.8333, "y": -5.47, "curve": 0.322, "c2": 0.3, "c3": 0.663, "c4": 0.65}, {"time": 4.4333, "y": -3.35, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 5.3333, "y": -0.48}]}, "bone103": {"translate": [{"y": -0.35, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "y": -7.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.8333, "y": -5.24, "curve": 0.33, "c2": 0.32, "c3": 0.693, "c4": 0.75}, {"time": 5.0333, "y": -1.13, "curve": 0.352, "c2": 0.42, "c3": 0.687, "c4": 0.76}, {"time": 5.3333, "y": -0.35}]}, "bone104": {"translate": [{"y": -0.38, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 2.3667, "y": -7.35, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.8333, "y": -3.13, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 5.0333, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 5.3333, "y": -0.38}]}, "bone105": {"translate": [{"y": -2.28, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 3.8333, "y": -7.11, "curve": 0.278, "c2": 0.14, "c3": 0.642, "c4": 0.58}, {"time": 5.0333, "y": -3.35, "curve": 0.337, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 5.3333, "y": -2.28}]}, "bone106": {"translate": [{"y": -2.28, "curve": 0.345, "c2": 0.37, "c3": 0.681, "c4": 0.72}, {"time": 0.3667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "y": -7.35, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 3.8333, "y": -7.08, "curve": 0.274, "c2": 0.13, "c3": 0.656, "c4": 0.63}, {"time": 5.3333, "y": -2.28}]}, "bone107": {"translate": [{"y": -0.5, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "y": -7.35, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 3.8333, "y": -5.46, "curve": 0.332, "c2": 0.33, "c3": 0.714, "c4": 0.82}, {"time": 5.3333, "y": -0.5}]}, "bone108": {"translate": [{"y": -4.64, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.3667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.6, "curve": 0.243, "c3": 0.693, "c4": 0.76}, {"time": 3.8333, "y": -6.7, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 4.2667, "y": -7.35, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "y": -4.64}]}, "bone109": {"translate": [{"y": -4.19, "curve": 0.342, "c2": 0.36, "c3": 0.692, "c4": 0.75}, {"time": 0.9, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.4667, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.8333, "y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 4.1333, "y": -7.35, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 5.3333, "y": -4.19}]}, "bone110": {"translate": [{"y": -2.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "y": -7.35, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.8333, "y": -6.97, "curve": 0.28, "c2": 0.16, "c3": 0.662, "c4": 0.65}, {"time": 5.3333, "y": -2.1}]}, "bone111": {"translate": [{"y": -6.4, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.1333, "curve": 0.244, "c3": 0.645, "c4": 0.58}, {"time": 3.8333, "y": -5.06, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 4.8, "y": -7.35, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "y": -6.4}]}, "bone112": {"translate": [{"y": -6.21, "curve": 0.31, "c2": 0.26, "c3": 0.69, "c4": 0.74}, {"time": 1.4667, "y": -1.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.0667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.8333, "y": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.7333, "y": -7.35, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "y": -6.21}]}, "bone113": {"translate": [{"y": -4.21, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.4667, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.8333, "y": -6.97, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 4.1333, "y": -7.35, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 5.3333, "y": -4.21}]}, "bone114": {"translate": [{"y": -7.31, "curve": 0.34, "c2": 0.66, "c3": 0.674}, {"time": 0.0667, "y": -7.35, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.4667, "y": -3.35, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.7333, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 3.8333, "y": -2.92, "curve": 0.362, "c2": 0.44, "c3": 0.746, "c4": 0.96}, {"time": 5.3333, "y": -7.31}]}}}}}