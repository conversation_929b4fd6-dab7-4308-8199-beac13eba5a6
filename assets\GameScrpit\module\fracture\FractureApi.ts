import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess, Net_Code } from "../../game/mgr/ApiHandler";
import { ActivityCmd, AssistSubCmd } from "../../game/net/cmd/CmdData";
import {
  FractureAnswerRequest,
  FractureDrawResponse,
  FractureEventResponse,
  FractureFightResponse,
  FractureLogFightResponse,
  FractureLogMessage,
  FractureLogRequest,
  FractureMessage,
  FractureSearchResponse,
  FractureSkipResponse,
  FractureTrapResponse,
  FractureTravelRequest,
  RedeemMessage,
  RedeemRequest,
  RedeemResponse,
  SimpleRankMessage,
} from "../../game/net/protocol/Activity";
import { RewardMessage } from "../../game/net/protocol/Comm";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ByteValueList, IntValue, <PERSON>Val<PERSON> } from "../../game/net/protocol/ExternalMessage";
import MsgMgr from "../../lib/event/MsgMgr";
import { FractureData } from "./FractureData";
import { FractureModule } from "./FractureModule";
import TipMgr from "../../lib/tips/TipMgr";
import { ClubModule } from "../club/ClubModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

const ACTIVITYID = 11501;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
export class FractureApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }

  /**
   * 时空裂缝信息
   * @param id
   * @param success
   * @param fail
   */
  public fractureInfo(success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      FractureMessage,
      ActivityCmd.fractureInfo,
      LongValue.encode({ value: ACTIVITYID }),
      (data: FractureMessage) => {
        FractureModule.data.fractureData = data;
        success?.(data);
      }
    );
  }

  /**
   * 搜索时空裂缝
   * @param id
   * @param success
   * @param fail
   */
  public searchFracture(success: ApiHandlerSuccess, fail: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      FractureSearchResponse,
      ActivityCmd.searchFracture,
      LongValue.encode({ value: ACTIVITYID }),
      (data: FractureSearchResponse) => {
        FractureModule.data.fractureData.fightMessage = data.fightMessage;
        FractureModule.data.fractureData.choiceId = data.choiceId;
        FractureModule.data.fractureData.remainFloorCountMap = data.remainFloorCountMap;
        FractureModule.data.fractureData.fractureId = data.fractureId;
        FractureModule.data.fractureData.roadMessage = data.roadMessage;
        success?.(data);
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        fail?.(errorCode, msg, data);
        return false;
      }
    );
  }

  /**
   * 回答事件(除了路障) answer值从1开始表示配置表的回答顺序
   * @param request
   * @param success
   * @param fail
   */
  public answerEvent(answer: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    let request: FractureAnswerRequest = {
      activityId: ACTIVITYID,
      answer: answer,
    };
    ApiHandler.instance.requestSync(
      FractureEventResponse,
      ActivityCmd.answerEvent,
      FractureAnswerRequest.encode(request),
      (data: FractureEventResponse) => {
        FractureModule.data.fractureData.choiceId = data.choiceId;
        FractureModule.data.fractureData.fractureId = data.fractureId;
        FractureModule.data.fractureData.remainFloorCountMap = data.remainFloorCountMap;
        success?.(data);
      }
    );
  }

  /**
   * 回答路障 answer字段，1表示付费跳过，2表示求助,其他值为达到通关的条件如值0
   * @param request
   * @param success
   * @param fail
   */
  public answerTrap(answer: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    let request: FractureAnswerRequest = {
      activityId: ACTIVITYID,
      answer: answer,
    };
    if (answer == 2 && !ClubModule.data.clubMessage) {
      TipMgr.showTip("请先加入战盟");
      return;
    }
    ApiHandler.instance.requestSync(
      FractureTrapResponse,
      ActivityCmd.answerTrap,
      FractureAnswerRequest.encode(request),
      (data: FractureTrapResponse) => {
        FractureModule.data.fractureData.choiceId = data.choiceId;
        FractureModule.data.fractureData.fractureId = data.fractureId;
        FractureModule.data.fractureData.remainFloorCountMap = data.remainFloorCountMap;
        FractureModule.data.fractureData.roadMessage = data.roadMessage;
        success?.(data);
      }
    );
  }

  /**
   * 进行时空裂缝抽奖|打开各种宝箱
   * @param id
   * @param success
   * @param fail
   */
  public answerDrawOrBox(success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      FractureDrawResponse,
      ActivityCmd.answerDrawOrBox,
      LongValue.encode({ value: ACTIVITYID }),
      (data: FractureDrawResponse) => {
        FractureModule.data.fractureData.choiceId = data.choiceId;
        FractureModule.data.fractureData.fractureId = data.fractureId;
        FractureModule.data.fractureData.remainFloorCountMap = data.remainFloorCountMap;
        success?.(data);
      }
    );
  }

  /**
   * 对战怪物、BOSS、NPC(非日志)
   * @param id
   * @param success
   * @param fail
   */
  public answerFightMonsterBossPlayer(success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      FractureFightResponse,
      ActivityCmd.answerFightMonsterBossPlayer,
      LongValue.encode({ value: ACTIVITYID }),
      (data: FractureFightResponse) => {
        FractureModule.data.fractureData.fightMessage = data.fightMessage;
        FractureModule.data.fractureData.choiceId = data.choiceId;
        FractureModule.data.fractureData.fractureId = data.fractureId;
        // 新增属性检测逻辑
        const moduleMap = FractureModule.data.fractureData.remainFloorCountMap;
        const responseMap = data.remainFloorCountMap;

        // 找出新增的楼层ID
        const extraFloorIds = Object.keys(responseMap).filter(
          (key) => !Object.prototype.hasOwnProperty.call(moduleMap, key)
        );

        if (extraFloorIds.length > 0) {
          log.log("新增楼层ID:", extraFloorIds);
          // 实际项目中这里可以触发相关业务逻辑
          MsgMgr.emit(MsgEnum.ON_FRACTURE_FLOOR_UNLOCK, extraFloorIds);
        }

        // 更新本地数据
        FractureModule.data.fractureData.remainFloorCountMap = responseMap;
        success?.(data);
      }
    );
  }

  /**
   * 绕过当前时空裂缝关卡
   * @param id
   * @param success
   * @param fail
   */
  public answerSkip(success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      FractureSkipResponse,
      ActivityCmd.answerSkip,
      LongValue.encode({ value: ACTIVITYID }),
      (data: FractureSkipResponse) => {
        FractureModule.data.fractureData.choiceId = data.choiceId;
        // FractureModule.data.fractureData.fractureId = data.fractureId;
        FractureModule.data.fractureData.fractureId = -1;
        this.fractureInfo(() => {
          success?.(data);
        }, fail);
        // success?.(data);
      }
    );
  }

  /**
   * 穿梭到指定楼层
   * @param request
   * @param success
   * @param fail
   */
  public travelFloor(floorId: number, success: ApiHandlerSuccess, fail: ApiHandlerFail) {
    let request: FractureTravelRequest = {
      activityId: ACTIVITYID,
      floorId: floorId,
    };
    if (FractureModule.data.fractureData.fractureId > 0) {
      TipMgr.showTip("请先完成当前探索的事件");
      return;
    }
    ApiHandler.instance.requestSync(
      IntValue,
      ActivityCmd.travelFloor,
      FractureTravelRequest.encode(request),
      (data: IntValue) => {
        this.fractureInfo(() => {
          success?.(data);
        }, fail);
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        fail?.(errorCode, msg, data);
        return false;
      }
    );
  }
  public rankInfo(success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      SimpleRankMessage,
      ActivityCmd.simpleRankInfo, // 14-40
      LongValue.encode({ value: ACTIVITYID }),
      (data: SimpleRankMessage) => {
        success?.(data);
      }
    );
  }

  /**
   * 资源消耗-曲径通幽，繁荣度冲榜，时空裂缝  注：领取排行榜奖励，要公告期后才能领取
   * @param id
   * @param success
   * @param fail
   */
  public takeBoardReward(id: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      RewardMessage,
      ActivityCmd.takeBoardReward,
      LongValue.encode({ value: id }),
      (data: RewardMessage) => {
        success?.(data);
      }
    );
  }

  /**
   * 查看时空裂缝日志列表
   * @param id
   * @param success
   * @param fail
   */
  public fractureLogList(success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    ApiHandler.instance.list(
      FractureLogMessage,
      ActivityCmd.fractureLogList,
      LongValue.encode({ value: ACTIVITYID }),
      (data: FractureLogMessage[]) => {
        success?.(data);
      }
    );
  }

  /**
   * 发起时空裂缝日志协助
   * @param request
   * @param success
   * @param fail
   */
  public assistLogRequest(logId: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    if (!ClubModule.data.clubMessage) {
      TipMgr.showTip("请先加入战盟");
      return;
    }
    let request: FractureLogRequest = {
      activityId: ACTIVITYID,
      logId: logId,
    };
    ApiHandler.instance.requestSync(
      BoolValue,
      ActivityCmd.assistLogRequest,
      FractureLogRequest.encode(request),
      (data: BoolValue) => {
        success?.(data);
      }
    );
  }

  /**
   * 与日志的对手战斗
   * @param request
   * @param success
   * @param fail
   */
  public fightFractureLog(logId: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    let request: FractureLogRequest = {
      activityId: ACTIVITYID,
      logId: logId,
    };
    ApiHandler.instance.requestSync(
      FractureLogFightResponse,
      ActivityCmd.fightFractureLog,
      FractureLogRequest.encode(request),
      (data: FractureLogFightResponse) => {
        // 新增属性检测逻辑
        const moduleMap = FractureModule.data.fractureData.remainFloorCountMap;
        const responseMap = data.remainFloorCountMap;

        // 找出新增的楼层ID
        const extraFloorIds = Object.keys(responseMap).filter(
          (key) => !Object.prototype.hasOwnProperty.call(moduleMap, key)
        );

        if (extraFloorIds.length > 0) {
          log.log("新增楼层ID:", extraFloorIds);
          // 实际项目中这里可以触发相关业务逻辑
          MsgMgr.emit(MsgEnum.ON_FRACTURE_FLOOR_UNLOCK, extraFloorIds);
        }
        log.log("FractureLogFightResponse", data);
        // 更新本地数据
        FractureModule.data.fractureData.remainFloorCountMap = responseMap;
        success?.(data);
      }
    );
  }

  /**
   * 领取时空裂缝日志奖励
   * @param request
   * @param success
   * @param fail
   */
  public takeLogReward(logId: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    let request: FractureLogRequest = {
      activityId: ACTIVITYID,
      logId: logId,
    };
    ApiHandler.instance.requestSync(
      RewardMessage,
      ActivityCmd.takeLogReward,
      FractureLogRequest.encode(request),
      (data: RewardMessage) => {
        success?.(data);
      }
    );
  }

  /**
   *
   * @param redeemId 礼包id
   * @param success
   */
  public buyRedeem(redeemId: number, count?: number, success?: ApiHandlerSuccess) {
    let request: RedeemRequest = {
      activityId: ACTIVITYID,
      redeemId: redeemId,
      count: count,
    };
    ApiHandler.instance.requestSync(
      RedeemResponse,
      ActivityCmd.buyFixedPack,
      RedeemRequest.encode(request),
      (data: RedeemResponse) => {
        FractureModule.data.fractureData.redeemMap = data.redeemMap;
        success?.(data);
      }
    );
  }
}
