import { Sprite } from "cc";
import ResMgr from "../../lib/common/ResMgr";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import { HeroTypeIcon, HeroTypeQualityId } from "./HeroConstant";
import { HeroModule } from "./HeroModule";
import { UINode } from "../../lib/ui/UINode";
import MsgMgr from "../../lib/event/MsgMgr";
import MsgEnum from "../../game/event/MsgEnum";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import { IConfigHero, IConfigHeroBreak, IConfigHeroPicture } from "../../game/JsonDefine";
import { AttrEnum, HeroAttrEnum } from "../../game/GameDefine";
import { PetModule } from "../pet/PetModule";
import { FriendModule } from "../friend/FriendModule";
import { addAttrMap } from "../../lib/utils/AttrTool";
import { HeroType } from "./HeroConfig";
import { CityModule } from "../city/CityModule";
import { divide, times } from "../../lib/utils/NumbersUtils";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

const log = Logger.getLoger(LOG_LEVEL.INFO);
export class HeroService {
  public init() {
    MsgMgr.on(MsgEnum.ON_HERO_UPDATE, this.updatePopover, this);
  }

  private tujianBadge(): boolean {
    if (!HeroModule.data.pictureMessage) {
      return false;
    }
    let datas: IConfigHeroPicture[] = Object.values(JsonMgr.instance.jsonList.c_heroPicture);
    for (let i = 0; i < datas.length; i++) {
      let heroPicture = datas[i];
      let pictureLv = HeroModule.data.pictureMessage.pictureMap[heroPicture.id] ?? 0;
      let collectNum = 0;
      let collectLvNum = 0;
      for (let index = 0; index < heroPicture.heroId.length; index++) {
        let heroMessage = HeroModule.data.getHeroMessage(heroPicture.heroId[index]);
        if (heroMessage) {
          collectNum++;
          if (
            pictureLv > 0 &&
            pictureLv < heroPicture.level.length &&
            heroMessage.level >= heroPicture.level[pictureLv]
          ) {
            // 未满级前判断是否满足升级条件
            collectLvNum++;
          }
        }
      }
      if (collectNum < heroPicture.heroId.length) {
        continue;
      }
      if (pictureLv == heroPicture.level.length) {
        //满级
      } else if (collectLvNum == heroPicture.heroId.length) {
        //可升级
        return true;
      } else if (pictureLv > 0) {
        //不可升级
      } else {
        //可激活
        return true;
      }
    }
    return false;
  }
  //红点更新
  private updatePopover() {
    let tujianRedPoint = this.tujianBadge();
    log.log("英雄图鉴红点", tujianRedPoint);
    BadgeMgr.instance.setShowById(BadgeType.UIHeroMain.tab_tujian.id, tujianRedPoint);
  }
  /**
   * 更新英雄种族图标
   * @param icon 精灵
   * @param heroId 英雄id
   * @param mgr uinode
   */
  public updateHeroRaceIcon(icon: Sprite, heroId: number, mgr?: UINode) {
    let heroInfo = HeroModule.config.getHeroInfo(heroId);
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_UI, `atlas_imgs/${HeroTypeIcon[`type_${heroInfo.type}`]}`, icon);
  }

  /**
   * 更新英雄品质背景
   * @param bg
   * @param heroId
   * @param mgr
   */
  public updateHeroColorBg(bg: Sprite, heroId: number, mgr?: UINode) {
    let heroInfo = HeroModule.config.getHeroInfo(heroId);
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_UI, `atlas_imgs/pingzhi_${heroInfo.color}`, bg);
  }

  // public updateHeroImg(img: Node, heroId: number) {}

  /**
   * 计算下一级升级所需要的元宝
   * @param heroId
   * @returns
   */
  public getNextLevelUpCost(heroId: number): number {
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    let cost = HeroModule.config.getHeroLvData(heroMessage.level + 1);
    return cost.cost;
  }
  /**
   * 计算升十级所需要的花费
   * @param heroId
   * @returns
   */
  public getNextTenLevelUpCost(heroId: number): number {
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    let heroInfo = HeroModule.config.getHeroInfo(heroId);
    let levelMax = HeroModule.config.getBreakInfo(heroMessage.breakTopLevel + 1)?.levelMax ?? heroInfo.maxLv;
    let totalCost = 0;
    for (let i = 1; i <= 10; i++) {
      let cost = HeroModule.config.getHeroLvData(heroMessage.level + i);
      if (heroMessage.level + i > levelMax) {
        break;
      }
      totalCost += cost.cost;
    }
    return totalCost;
  }
  /**
   * 技能增加的资质
   * @param heroSkill
   * @param level
   * @returns
   */
  public getSkillQuality(heroSkillId: number, level: number) {
    let heroSkill = HeroModule.config.getHeroSkillData(heroSkillId);
    if (!heroSkill) {
      return 0;
    }
    let skillQuality: number = 0;
    // 技能

    // log.debug("---------技能---------");
    // log.debug(heroSkill);

    // 初始
    for (let i2 = 0; i2 < heroSkill.firstAttraList.length; i2++) {
      let skillAttr = heroSkill.firstAttraList[i2];
      if (skillAttr[0] == AttrEnum.资质_109) {
        //109为资质列表id
        skillQuality += skillAttr[1];
        break;
      }
    }
    // 升级
    for (let i2 = 0; i2 < heroSkill.addList.length && level > 0; i2++) {
      let skillAttr = heroSkill.addList[i2];
      if (skillAttr[0] == AttrEnum.资质_109) {
        //109为资质列表id
        skillQuality += skillAttr[1] * (level - 1);
      }
    }
    return skillQuality;
  }
  /**
   * 突破 增加 资质
   * @param hero
   * @returns
   */
  public getBreakQuality(heroId: number) {
    let breakQuality: number = 0;
    let heroMap = HeroModule.data.getHeroMessage(heroId);
    if (!heroMap) {
      return breakQuality;
    }
    for (let i = 0; i < heroMap.breakTopLevel; i++) {
      let config_heroBreak: IConfigHeroBreak = JsonMgr.instance.jsonList.c_heroBreak[i + 1];
      breakQuality += config_heroBreak.qualityAdd;
    }
    return breakQuality;
  }
  /**
   * 计算资质 英雄id
   * @param hero
   * @returns
   */
  public getHeroQuality(heroId: number) {
    let hero = HeroModule.config.getHeroInfo(heroId);
    let heroQuality: number = 0;
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    // 基础
    heroQuality += hero.quality;
    if (!heroMessage) {
      return heroQuality;
    }
    let skillDatas = [];
    skillDatas = skillDatas.concat(hero.skill1List);
    skillDatas = skillDatas.concat(hero.talentList);
    // 技能
    for (let i = 0; i < skillDatas.length; i++) {
      let skillId = skillDatas[i];
      if (!heroMessage.skillMap[skillId] || heroMessage.skillMap[skillId] < 1) {
        continue;
      }
      heroQuality += this.getSkillQuality(skillId, heroMessage.skillMap[skillId]);
    }
    // 突破
    heroQuality += this.getBreakQuality(heroId);
    // 灵兽资质加成
    if (PetModule.data.getPet(hero.petId)) {
      heroQuality += PetModule.data.getHeroPetQuality(hero.petId);
    }
    // 美名效果资质增加
    heroQuality += FriendModule.data.getFriendFameQuality(HeroTypeQualityId[`type_${hero.type}`]);
    return heroQuality;
  }
  /**
   * 获取英雄总等级
   */
  public getHeroTotalLevel(): number {
    let totalLevel = 0;
    HeroModule.data.getOwnedHeros().forEach((hero: IConfigHero) => {
      totalLevel += HeroModule.data.getHeroMessage(hero.id).level;
    });
    return totalLevel;
  }
  /**
   * 获取英雄基础属性
   * @param hero
   * @returns
   */
  public getHeroBaseAttr(heroId: number): Map<number, number> {
    // hero.
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    if (!heroMessage) {
      return null;
    }
    let heroBaseAttr: Map<number, number> = new Map<number, number>();
    let heroLevelAttr = HeroModule.config.getHeroLvData(heroMessage.level);
    let quality = this.getHeroQuality(heroId);
    for (let i = 0; i < heroLevelAttr.attrAddList.length; i++) {
      let attrs = heroLevelAttr.attrAddList[i];
      heroBaseAttr[attrs[0]] = quality * attrs[1];
    }
    let hero = HeroModule.config.getHeroInfo(heroId);
    //基础战斗属性
    for (let i = 0; i < hero.attrFirstAdd.length; i++) {
      let attr = hero.attrFirstAdd[i];
      heroBaseAttr[attr[0]] = attr[1];
    }
    log.debug("---------基础属性---------");
    log.debug(heroBaseAttr);
    return heroBaseAttr;
  }
  /**
   * 获取英雄光环加成属性
   * @param hero
   * @returns
   */
  private getHeroHaloAttr(heroId: number): Map<number, number> {
    let attrs = new Map<number, number>();
    let hero = HeroModule.config.getHeroInfo(heroId);
    for (let i = 0; i < hero?.lightList?.length; i++) {
      let haloId = hero.lightList[i];
      if (haloId == 2203 || haloId == 2103 || haloId == 2302) {
        continue;
      }
      let level = HeroModule.data.getHeroHaloLv(heroId, haloId);
      if (level > 0) {
        let skillLvInfo = HeroModule.config.getHeroHaloLvInfo(haloId, level);
        let haloAttr = new Map<number, number>();
        for (let i = 0; skillLvInfo && i < skillLvInfo.powerAdd1.length; i++) {
          let attr = skillLvInfo.powerAdd1[i];
          haloAttr[attr[0]] = attr[1];
        }
        haloAttr[HeroAttrEnum.战将生命百分比_11] = skillLvInfo?.powerAdd2 ?? 0;
        haloAttr[HeroAttrEnum.战将攻击百分比_12] = skillLvInfo?.powerAdd2 ?? 0;
        haloAttr[HeroAttrEnum.战将防御百分比_13] = skillLvInfo?.powerAdd2 ?? 0;
        addAttrMap(attrs, haloAttr);
      }
    }
    if (hero.type == HeroType.PERSON) {
      for (let i = 0; i < HeroModule.data.personHaloList.length; i++) {
        let haloAttr = new Map<number, number>();
        let level = HeroModule.data.personHaloList[i];
        let skillLvInfo = HeroModule.config.getHeroHaloLvInfo(2103, level);
        haloAttr[HeroAttrEnum.战将生命百分比_11] = skillLvInfo?.heroPowerAdd1 ?? 0;
        haloAttr[HeroAttrEnum.战将攻击百分比_12] = skillLvInfo?.heroPowerAdd1 ?? 0;
        haloAttr[HeroAttrEnum.战将防御百分比_13] = skillLvInfo?.heroPowerAdd1 ?? 0;
        addAttrMap(attrs, haloAttr);
      }
    }
    if (hero.type == HeroType.GOD) {
      for (let i = 0; i < HeroModule.data.godHaloList.length; i++) {
        let haloAttr = new Map<number, number>();
        let level = HeroModule.data.godHaloList[i];
        let skillLvInfo = HeroModule.config.getHeroHaloLvInfo(2203, level);
        haloAttr[HeroAttrEnum.战将生命百分比_11] = skillLvInfo?.heroPowerAdd2 ?? 0;
        haloAttr[HeroAttrEnum.战将攻击百分比_12] = skillLvInfo?.heroPowerAdd2 ?? 0;
        haloAttr[HeroAttrEnum.战将防御百分比_13] = skillLvInfo?.heroPowerAdd2 ?? 0;
        addAttrMap(attrs, haloAttr);
      }
    }
    //所有英雄都加
    for (let i = 0; i < HeroModule.data.allHeroHaloList.length; i++) {
      let level = HeroModule.data.allHeroHaloList[i];
      let haloAttr = new Map<number, number>();
      let skillLvInfo = HeroModule.config.getHeroHaloLvInfo(2302, level);
      haloAttr[HeroAttrEnum.战将生命百分比_11] = skillLvInfo?.heroPowerAdd3 ?? 0;
      haloAttr[HeroAttrEnum.战将攻击百分比_12] = skillLvInfo?.heroPowerAdd3 ?? 0;
      haloAttr[HeroAttrEnum.战将防御百分比_13] = skillLvInfo?.heroPowerAdd3 ?? 0;
      addAttrMap(attrs, haloAttr);
    }
    return attrs;
  }
  /**
   * 获取印记属性
   * @param heroId
   * @returns
   */
  public getImprintAttrAdd(heroId: number): Map<number, number> {
    let imprintAttr = new Map<number, number>();
    let imprintLevel = HeroModule.data.getHeroImprintsLv(heroId);
    let skillLvInfo = HeroModule.config.getHeroImprintLvInfo(imprintLevel);
    // 精进固定值加成
    for (let i = 0; skillLvInfo && i < skillLvInfo.powerAdd1.length; i++) {
      let attr = skillLvInfo.powerAdd1[i];
      imprintAttr[attr[0]] = attr[1];
    }
    imprintAttr[HeroAttrEnum.战将生命百分比_11] = skillLvInfo?.powerAdd2 ?? 0;
    imprintAttr[HeroAttrEnum.战将攻击百分比_12] = skillLvInfo?.powerAdd2 ?? 0;
    imprintAttr[HeroAttrEnum.战将防御百分比_13] = skillLvInfo?.powerAdd2 ?? 0;
    return imprintAttr;
  }
  /**
   * 获取突破增加属性
   * @param heroId
   * @returns
   */
  public getHeroBreakAttrAdd(heroId: number): Map<number, number> {
    let breakAttrAdd = new Map<number, number>();
    //突破战斗属性 战斗属性=基础战斗属性+突破战斗属性
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    for (let i = heroMessage.breakTopLevel; i > 0; i--) {
      let breakTop: IConfigHeroBreak = JsonMgr.instance.jsonList.c_heroBreak[i];
      let attrAdd = new Map<number, number>();
      for (let i = 0; i < breakTop?.attrAddList.length; i++) {
        let attr = breakTop.attrAddList[i];
        attrAdd[attr[0]] = attr[1];
      }
      addAttrMap(breakAttrAdd, attrAdd);
    }
    return breakAttrAdd;
  }

  public getItemAttrAdd(heroId: number): Map<number, number> {
    let itemAttrAdd = new Map<number, number>();
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    let keys = Object.keys(heroMessage.itemAddBattleAttrMap);
    for (let i = 0; i < keys.length; i++) {
      itemAttrAdd[keys[i]] = heroMessage.itemAddBattleAttrMap[keys[i]];
    }
    return itemAttrAdd;
  }

  private printMap(map: Map<number, number>) {
    let str = "";
    for (let key in map) {
      str += key + ":" + map[key] + " ";
    }
    log.debug(str);
  }
  /**
   *
   * @param heroId
   * @returns
   */
  public getHeroAttrById(heroId: number): Map<number, number> {
    // log.debug(`===========${heroId}==============`);
    let hero = HeroModule.config.getHeroInfo(heroId);
    //基础属性
    let heroAttr = this.getHeroBaseAttr(heroId);
    if (!heroAttr) {
      return null;
    }

    let addAttr = new Map<number, number>();
    //仙友模块加成
    addAttrMap(addAttr, FriendModule.service.getHeroAttrAdd(heroId));
    log.debug("---------仙友模块加成---------");
    this.printMap(addAttr);
    log.debug(addAttr);
    //印记加成
    addAttrMap(addAttr, this.getImprintAttrAdd(heroId));
    log.debug("---------印记加成---------");
    this.printMap(addAttr);
    //光环加成
    addAttrMap(addAttr, this.getHeroHaloAttr(heroId));
    log.debug("---------光环加成---------");
    this.printMap(addAttr);
    //城池建筑加成
    addAttrMap(addAttr, CityModule.data.getCityBuildWorkerRewadToHero());
    log.debug("---------城池建筑加成---------");
    this.printMap(addAttr);
    //灵兽加成
    addAttrMap(addAttr, PetModule.service.getHeroAttrAdd(hero.petId));
    log.debug("---------灵兽加成---------");
    this.printMap(addAttr);

    const baseAttrList = [AttrEnum.生命_1, AttrEnum.攻击_2, AttrEnum.防御_3];
    const percentAttrList = [
      HeroAttrEnum.战将生命百分比_11,
      HeroAttrEnum.战将攻击百分比_12,
      HeroAttrEnum.战将防御百分比_13,
    ];
    //其他子模块增加的百分比只增加到基础属性上
    for (let i = 0; i < baseAttrList.length; i++) {
      let attr = baseAttrList[i];
      heroAttr[attr] = times(heroAttr[attr], 1 + divide(addAttr[percentAttrList[i]], 10000));
    }
    log.debug("---------百分比加成---------");
    this.printMap(heroAttr);
    //固定值加成
    const fixAttr = [AttrEnum.生命_1, AttrEnum.攻击_2, AttrEnum.防御_3, AttrEnum.敏捷_4];
    for (let i = 0; i < fixAttr.length; i++) {
      let attr = fixAttr[i];
      heroAttr[attr] += addAttr[attr] ?? 0;
    }
    log.debug("---------固定值加成---------");
    this.printMap(heroAttr);
    // log.debug(`===========${heroId}==============`);
    //基础战斗属性
    addAttrMap(heroAttr, this.getHeroBreakAttrAdd(heroId));

    // 道具加成
    addAttrMap(heroAttr, this.getItemAttrAdd(heroId));
    return heroAttr;
  }
  /**获取英雄的属性 */
  public getHeroAttrMap(heroId: number): Map<number, number> {
    let heroAttr = this.getHeroAttrById(heroId);
    // log.debug("heroId==========", heroId, heroAttr);
    const attrList = [
      AttrEnum.击晕_21,
      AttrEnum.闪避_22,
      AttrEnum.连击_23,
      AttrEnum.反击_24,
      AttrEnum.暴击_25,
      AttrEnum.吸血_26,
    ];
    for (let i = 0; i < attrList.length; i++) {
      heroAttr[attrList[i]] = divide(heroAttr[attrList[i]], 10000);
    }
    return heroAttr;
  }
  public getAllHeroAttrMap(): Map<number, number> {
    const attr = new Map<number, number>();
    HeroModule.data.ownHeroList.forEach((val) => {
      let heroAttr = this.getHeroAttrMap(val.id);
      addAttrMap(attr, heroAttr);
    });
    return attr;
  }

  public canUpgradeImprint(heroId: number): boolean {
    if (HeroModule.data.getHeroImprintsLv(heroId) < 3) {
      return true;
    }
    let heros = HeroModule.data.getOwnedHeros();
    if (heros.length < 15) {
      return false;
    }
    // 印记等级大于等于3的英雄数量
    let heroNum = 0;
    for (let i1 = 0; i1 < heros.length; i1++) {
      let id = heros[i1].id;
      let imprintLevel = HeroModule.data.getHeroImprintsLv(id);
      if (imprintLevel >= 3) {
        heroNum++;
      }
    }

    if (heroNum < 15) {
      return false;
    }
    return true;
  }
}
