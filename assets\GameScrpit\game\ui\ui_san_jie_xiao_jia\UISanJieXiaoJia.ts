import { _decorator, instantiate, is<PERSON><PERSON><PERSON>, Node, Prefab, Sprite, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { Home } from "./Home";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityRouteName } from "../../../module/city/CityConstant";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { CityModule } from "../../../module/city/CityModule";
import ResMgr from "../../../lib/common/ResMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { Sleep } from "../../GameDefine";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { GameData } from "../../GameData";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { persistent } from "../../../lib/decorators/persistent";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UISanJieXiaoJiaNezhaChuChang } from "./UISanJieXiaoJiaNezhaChuChang";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { TopFinger } from "../../../ext_guide/TopFinger";
import { GuideRouteEnum } from "../../../ext_guide/GuideDefine";
import GuideMgr from "../../../ext_guide/GuideMgr";
const { ccclass, property } = _decorator;

@ccclass("UISanJieXiaoJia")
export class UISanJieXiaoJia extends UINode {
  protected _openAct: boolean = true; //打开动作

  @persistent
  private _lastOpenTime: string;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA}?prefab/ui/UISanJieXiaoJia`;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_SANJIE_XIAJIA_BG_UPDATE, this.setBg, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_SANJIE_XIAJIA_BG_UPDATE, this.setBg, this);
  }

  protected async onEvtShow(): Promise<void> {
    this.setBg();

    await this.initHome();

    if (GameData.guideStepNow) {
      this.moveToByName(GameData.guideStepNow.nodeClickName);
    }

    if (isValid(this.node) == true) {
      BadgeMgr.instance.setBadgeId(
        this.getNode("btn_SJXJ_btn_chengjiu"),
        BadgeType.UIMajorCity.btn_sanjiexiaojia.btn_SJXJ_btn_chengjiu.id
      );

      BadgeMgr.instance.setBadgeId(
        this.getNode("btn_SJXJ_btn_zhanjiang"),
        BadgeType.UIMajorCity.btn_sanjiexiaojia.btn_SJXJ_btn_zhanjiang.id
      );
    }
    if (this._lastOpenTime != TimeUtils.formatTimestamp(Date.now(), "YYYYMMDD")) {
      this._lastOpenTime = TimeUtils.formatTimestamp(Date.now(), "YYYYMMDD");
      RouteManager.uiRouteCtrl.showRoute(UISanJieXiaoJiaNezhaChuChang, {
        onCloseBack: () => {
          GuideMgr.startGuide({ stepId: 86 });
        },
      });
    }
  }

  private moveToByName(nodeName: string) {
    let node = NodeTool.findByName(this.node, nodeName);
    if (node) {
      let wPos = node.getWorldPosition();
      let moveX = 350 - wPos.x;
      if (moveX > 360.0) {
        moveX = 360.0;
      }
      if (moveX < -360.0) {
        moveX = -360.0;
      }

      const nodeMap = this.getNode("map");

      tween(nodeMap)
        .to(0.3, { position: v3(moveX, 0, 0) }, { easing: "sineInOut" })
        .start();
    }
  }

  private setBg() {
    let db = JsonMgr.instance.jsonList.c_home;
    let str = "Bg_sjxj_1";

    let list = [];
    for (let i in db) {
      if (db[i].bgOpen != "") {
        list.push(db[i]);
      }
      // let id = (db[i].id % 100) + 100;
      // let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[id] || 0;
      // let newId = lv * 100 + (db[i].id % 100);
      // if (db[i].bgOpen != "" && newId == db[i].id) {
      //   str = db[i].bgOpen;
      // }
    }
    let id = 105;
    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[id] || 0;

    for (let i = 0; i < list.length; i++) {
      let newId = lv * 100 + (id % 100);

      if (newId >= list[i].id) {
        str = list[i].bgOpen;
      }
    }

    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA}?bg/${str}`,
      this.getNode("map").getComponent(Sprite),
      this
    );
  }

  private async initHome() {
    let map = this.getNode("map");

    for (let i = 0; i < map.children.length; i++) {
      await Sleep(0.1);
      if (isValid(this.node) == false) return;
      this.initLayer(map.children[i]);
    }
  }

  private initLayer(nodeLayer: Node) {
    let list = nodeLayer.name.split("_");
    let id = Number(list[0]);
    this.assetMgr.loadPrefab(BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA, `prefab/home/<USER>
      if (isValid(this.node) == false) return;

      let node = instantiate(prefab);
      nodeLayer.addChild(node);
      node.walk((val) => {
        val.layer = this.node.layer;
      });
      node.getComponent(Home).setHome(id);
    });
  }

  private on_click_btn_chengjiu() {
    AudioMgr.instance.playEffect(1805);
    UIMgr.instance.showDialog(CityRouteName.UISanJieXiaoJiaCj);
  }

  private on_click_btn_zhanjiang() {
    AudioMgr.instance.playEffect(1806);
    UIMgr.instance.showDialog(CityRouteName.UISanJieXiaoJiaHero);
  }
}
