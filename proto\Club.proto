syntax = "proto3";
package sim;
import "Player.proto";
import "Comm.proto";

// 
message AuditOptionMessage {
  // 手动审核
  bool manual = 1;
  // 自动拒绝
  bool autoRefuse = 2;
  // 等级下限 (单位万 ，如10万，这里值为10)
  int32 levelLowerLimit = 3;
  // 战力下限 (单位万 ，如10万，这里值为10)
  int64 powerLowerLimit = 4;
}

// 
message BargainRecordMessage {
  int64 userId = 1;
  // 角色名称
  string nickname = 2;
  // 砍掉的价格
  int64 deductPrice = 3;
  // 是否付费
  bool paid = 4;
  // 砍价时间
  int64 createTime = 5;
}

// 
message ClubActiveMessage {
  // 当日活跃度
  int64 activeVal = 1;
  // 领取奖励的活跃度索引集合
  repeated int32 activeTaskList = 2;
}

// 
message ClubAdjustPositionMessage {
  int64 userId = 1;
  int32 position = 2;
}

// 
message ClubApplyMessage {
  // 玩家信息
  sim.PlayerSimpleMessage simpleMessage = 1;
  // 审核的截止时间
  int64 deadline = 2;
}

// 
message ClubBargainMessage {
  // 触发的砍价商品ID，如果小于等于0说明没有触发砍价活动，以下属性无效
  int64 bargainId = 1;
  // 砍价参与日志
  map<int64,BargainRecordMessage> recordMap = 2;
  // 结束时间
  int64 endTime = 3;
}

// 
message ClubBasicInfoMessage {
  // 仙盟名称
  string name = 1;
  // 口号
  string slogan = 2;
  // 旗帜
  string avatar = 3;
}

// 
message ClubBossBuddyMessage {
  // 
  ClubBossTrainMessage bossTrain = 1;
  // 战友信息
  repeated sim.PlayerSimpleMessage buddyList = 2;
}

// 
message ClubBossChanceMessage {
  // 剩余免费挑战次数
  int64 remainCnt = 1;
  // 剩余的道具挑战次数
  int32 remainItemCnt = 2;
}

// 
message ClubBossResponse {
  // 是否胜利
  bool isWin = 1;
  // 录像回放
  string replay = 2;
  // 对BOSS造成的伤害
  double damage = 3;
  // 今日贡献
  int64 todayContribute = 4;
  // 历史贡献
  int64 totalContribute = 5;
  // 获取的资源
  repeated double resAddList = 6;
  // 战友信息
  repeated sim.PlayerSimpleMessage buddyList = 7;
  // 剩余的挑战次数
  ClubBossChanceMessage bossChance = 8;
  // BOSS的信息
  ClubBossTrainMessage bossTrain = 9;
  // 已经解锁的BOSS数量
  int32 unlockBossCnt = 10;
}

// 
message ClubBossTrainMessage {
  // 号召的英雄的索引 -1则还未召唤
  int32 bossIndex = 1;
  // 剩余的BOSS血量
  double remainHp = 2;
  // 造成的总伤害血量
  double damageHp = 3;
  // 击杀的玩家ID 当remainHp=0时才有值，且当血量为0时，切换到血魂状态
  sim.PlayerSimpleMessage killUserMessage = 4;
  // 是否有领取击杀奖励
  bool isTakeKill = 5;
  // 是否有参与战斗
  bool isHurt = 6;
  // 已经领取过的BOSS总伤害的奖励集合
  repeated int32 hurtRewardIndexList = 7;
}

// 
message ClubCreateRequest {
  // 仙盟名称
  string name = 1;
  // 口号
  string slogan = 2;
  // 旗帜
  string avatar = 3;
}

// 
message ClubDeadRewardResponse {
  // 获取的资源
  repeated double resAddList = 1;
  // BOSS的信息
  bool isTakeKill = 2;
}

// 
message ClubDonateMessage {
  // 完成了几次 从0开始
  int32 count = 1;
  // 下一次可以捐献的最早时间 默认从0开始
  int64 nextDonateDeadline = 2;
}

// 
message ClubExamineMessage {
  // 0 - 审核拒绝 (clubMessage无值)  1 - 审核同意且用户加入成功(clubMessage有值) 2 - 审核同意但用户已加入其他仙盟(clubMessage无值)
  int32 code = 1;
  // 仙盟信息
  ClubMessage clubMessage = 2;
}

// 
message ClubExamineRequest {
  // 申请的用户ID
  int64 userId = 1;
  // 审核通过与否
  bool pass = 2;
}

// 
message ClubFormMessage {
  // key-clubId val:申请加入仙盟的截止时间
  map<int64,int64> applyMap = 1;
  // 申请加入新的仙盟的冷却时间
  int64 joinColdStamp = 2;
  // 加入的仙盟 小于等于0表示还未加入仙盟
  int64 clubId = 3;
  // 加入的仙盟信息 只有当clubId不为-1时有值
  ClubMessage clubMessage = 4;
  // 距离隔天重置的倒计时 (活跃度任务右侧栏的倒计时时间)
  int64 remainResetStamp = 5;
  // 剩余的打BOSS的次数
  ClubBossChanceMessage bossChance = 6;
  // 活跃
  ClubActiveMessage active = 7;
  // 仙盟任务完成情况
  ClubTaskMessage dailyTask = 8;
  // 捐献任务的进度 key:配置表ID
  map<int64,ClubDonateMessage> donateMap = 9;
  // 被踢出仙盟的弹窗信息，如果有就不是空的
  ClubPopUpMessage popUpMessage = 10;
}

// 
message ClubHurtRewardRequest {
  int64 bossId = 1;
  // 伤害阶段的索引 从0开始
  int32 hurtIndex = 2;
}

// 
message ClubHurtRewardResponse {
  // 只返回领取的BOSS伤害奖励的变动情况
  repeated int32 hurtRewardTakeList = 1;
  // 获取的资源
  repeated double resAddList = 2;
}

// 
message ClubJoinResponse {
  // 0 - 成功加入 ,1 - 待审核 2-被拒绝
  int32 code = 1;
  // 申请的记录和时长
  map<int64,int64> applyMap = 2;
  // 成功加入或已加入仙盟后，加入的仙盟的信息,前端需要更新clubID属性
  ClubMessage clubMessage = 3;
}

// 
message ClubLogMessage {
  // 1 - 捐献 2 - BOSS召唤 3 - 加入仙盟 4 - 退出仙盟 5-BOSS击杀
  int32 eventType = 1;
  // 用户
  string name = 2;
  // 数值参数1 捐献-仙玉数量 / BOSS召唤(击杀)-BOSS索引
  int32 param1 = 3;
  // 数值参数2 捐献-经验值
  int32 param2 = 4;
  // 发生时间
  int64 timeStamp = 5;
}

// 
message ClubMemberMessage {
  // 玩家信息
  sim.PlayerSimpleMessage simpleMessage = 1;
  // 是否在线
  bool isOnline = 2;
  // 职位
  int32 position = 3;
  // 今日贡献
  int64 todayContribute = 4;
  // 历史贡献
  int64 totalContribute = 5;
  // 加入仙盟时间
  int64 joinTime = 6;
  // 当日活跃度
  int64 activeVal = 7;
}

// 
message ClubMessage {
  // 仙盟ID
  int64 id = 1;
  // 仙盟名称
  string name = 2;
  // 口号
  string slogan = 3;
  // 旗帜
  string avatar = 4;
  // 服务器名称
  string serverName = 5;
  // 等级
  int32 level = 6;
  // 仙盟经验
  int64 exp = 7;
  // 总战力
  double totalPower = 8;
  // 杀死的BOSS数量
  int32 killBossCnt = 9;
  // 已经解锁的BOSS数量
  int32 unlockBossCnt = 10;
  // 审核设置
  AuditOptionMessage auditOption = 11;
  // 联盟砍价
  ClubBargainMessage bargain = 12;
  // 成员的信息
  repeated ClubMemberMessage memberList = 13;
}

// 
message ClubPopUpMessage {
  // 战盟名称
  string clubName = 1;
}

// 
message ClubRankMessage {
  // 如果积分为0，就说明没有加入仙盟
  double point = 1;
  // 未上榜 -1
  int32 rank = 2;
  repeated ClubSimplePowerMessage rankList = 3;
}

// 
message ClubRewardMessage {
  // 获得的奖励 弹窗显示
  sim.RewardMessage rewardMessage = 1;
  // 捐献进度 key:配置的ID
  map<int64,ClubDonateMessage> donateMap = 2;
  // 活跃
  ClubActiveMessage active = 3;
  // 日常任务的完成情况
  ClubTaskMessage dailyTask = 4;
  // 仙盟等级
  int32 clubLevel = 5;
  // 仙盟的总经验
  int64 clubExp = 6;
  // 今日贡献
  int64 todayContribute = 7;
  // 历史贡献
  int64 totalContribute = 8;
  // 是否触发神秘商人(只在领取活动度任务奖励时有效)
  bool trigger = 9;
}

// 
message ClubSimplePowerMessage {
  // 仙盟ID
  int64 id = 1;
  // 仙盟名称
  string name = 2;
  // 口号
  string slogan = 3;
  // 旗帜
  string avatar = 4;
  // 服务器名称
  string serverName = 5;
  // 等级
  int32 level = 6;
  // 总战力
  double totalPower = 7;
}

// 
message ClubTaskMessage {
  // 子任务及其完成次数
  map<int64,int32> taskCntMap = 1;
  // 领取过仙盟奖励的任务ID集合
  repeated int64 completeTaskList = 2;
}

