import { randomRangeInt } from "cc";
import MsgEnum from "../../../game/event/MsgEnum";
import { JsonMgr } from "../../../game/mgr/JsonMgr";
import { ChapterMessage, ChapterTreasureMessage, RegularChapterPassResponse } from "../../../game/net/protocol/Chapter";
import MsgMgr from "../../../lib/event/MsgMgr";
import { FightMsgEnum } from "./FightConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { SpGuide103002 } from "./FightConstant";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export interface IConfigMonsterMatrix {
  id: number;
  num: number;
  positionList: number[][];
  skinId: number[];
  speed: number;
}

export class FightData {
  public regularChapterPassResponse: RegularChapterPassResponse;

  private _chapterMsg: ChapterMessage = null;
  private _autoFight: boolean = false;
  public get autoFight(): boolean {
    return this._autoFight;
  }

  public set autoFight(bool: boolean) {
    this._autoFight = bool;
  }

  /**
   * 1030002 关卡是否通关,0-第一次打，1-第二次打，2-完成
   */
  private _stepPass1030002: SpGuide103002 = SpGuide103002.FIRST_FIGHT;
  public get stepPass1030002(): SpGuide103002 {
    return this._stepPass1030002;
  }
  public set stepPass1030002(v: SpGuide103002) {
    this._stepPass1030002 = v;
  }

  public get chapterMsg(): ChapterMessage {
    return this._chapterMsg;
  }

  public get chapterId(): number {
    return this._chapterMsg?.chapterId || 0;
  }

  public get chapterChildId(): number {
    return this._chapterMsg.chapterChildId;
  }

  public get treasureMap(): { [key: number]: ChapterTreasureMessage } {
    return this._chapterMsg.treasureMap;
  }

  public get energyEncourage(): number {
    return this._chapterMsg.energyEncourage;
  }
  public get itemEncourage(): number {
    return this._chapterMsg.itemEncourage;
  }

  //设置================

  public set chapterId(id: number) {
    this._chapterMsg.chapterId = id;
    MsgMgr.emit(MsgEnum.ON_CHAPTER_PASS, this._chapterMsg.chapterId);
  }
  public set energyEncourage(num: number) {
    this._chapterMsg.energyEncourage = num;
    MsgMgr.emit(FightMsgEnum.ON_FIGHT_ENERGY_UPDATE);
  }

  public set itemEncourage(num: number) {
    this._chapterMsg.itemEncourage = num;
    MsgMgr.emit(FightMsgEnum.ON_FIGHT_ITEM_UPDATE);
  }

  public pkBossSet(energyEncourage, itemEncourage) {
    this._chapterMsg.itemEncourage = itemEncourage;
    this._chapterMsg.energyEncourage = energyEncourage;
  }

  public set chapterChildId(num: number) {
    this._chapterMsg.chapterChildId = num;
    MsgMgr.emit(FightMsgEnum.ON_FIGHT_CHAPTER_UPDATE, this._chapterMsg.chapterChildId, true);
  }

  public set treasureMap(map: { [key: number]: ChapterTreasureMessage }) {
    this._chapterMsg.treasureMap = map;
    MsgMgr.emit(FightMsgEnum.ON_FIGHT_TREASURE_UPDATE);
  }

  public setChapter(chapter: ChapterMessage) {
    this._chapterMsg = chapter;
    log.log("关卡战斗模块数据设置完成", this._chapterMsg);
  }

  public bossLevelChangeTreasure(id) {
    this._chapterMsg.treasureMap[id].show = false;
  }

  /**
   * 获取阵型配置
   * @param id 阵型ID
   * @returns
   */
  public getConfigMonsterMatrix(id: number = 0): IConfigMonsterMatrix {
    let rs = JsonMgr.instance.jsonList.c_monsterMatrix[id];
    if (!rs) {
      let keyList = Object.keys(JsonMgr.instance.jsonList.c_monsterMatrix);
      let i = randomRangeInt(1, 21);

      return JsonMgr.instance.jsonList.c_monsterMatrix[keyList[i]];
    }
    return rs;
  }
}
