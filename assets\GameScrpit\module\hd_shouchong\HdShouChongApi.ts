import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>lerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import {
  FirstRechargeMessage,
  FirstRechargeSignResponse,
  FirstRechargeSignRequest,
} from "../../game/net/protocol/Activity";
import { ActivityModule } from "../activity/ActivityModule";
import { HdShouChongModule } from "./HdShouChongModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HdShouChongApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }

  public firstRecharge(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      FirstRechargeMessage,
      ActivityCmd.firstCharge,
      null,
      (data: FirstRechargeMessage) => {
        HdShouChongModule.data.firstRechargeMessage = {};
        // data?.forEach((item) => {
        //   ActivityModule.data.firstRechargeMessage[item.id] = item;
        // });
        log.log(data);
        HdShouChongModule.data.firstRechargeMessage = data.subRechargeMap;
        success && success(data);
      },
      () => {
        log.log("obtain firstRecharge fail");
        return false;
      }
    );
  }

  public takeFirstRechargeReward(subActivityId: number, index: number, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      FirstRechargeSignResponse,
      ActivityCmd.takeFirstRechargeReward,
      FirstRechargeSignRequest.encode({
        id: subActivityId,
        index: index,
      }),
      (data: FirstRechargeSignResponse) => {
        HdShouChongModule.data.firstRechargeMessage[subActivityId] = data.message;
        success && success(data);
      }
    );
  }
}
