import { _decorator, Label, Layout, ProgressBar, Node } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { PupilMessage } from "../../../../../game/net/protocol/Pupil";
import { JsonMgr } from "../../../../../game/mgr/JsonMgr";
import ToolExt from "../../../../../game/common/ToolExt";
import Formate from "../../../../../lib/utils/Formate";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import TipMgr from "../../../../../lib/tips/TipMgr";
import { PupilModule } from "../../PupilModule";
import ResMgr from "../../../../../lib/common/ResMgr";
import { Sprite } from "cc";
import { PupilHeader } from "./PupilHeader";
import { PupilAni } from "./PupilAni";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPupilDetailPop")
export class UIPupilDetailPop extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilDetailPop`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _pupilId: number = null;

  private _pupilMsg: PupilMessage = null;

  private _slotIndex: number = -1;

  public init(param) {
    super.init(param);
    this._pupilId = param["pupilId"];
    log.log("init", this._pupilId);
    this._slotIndex = PupilModule.data.pupilTrainMsg.trainSlotList.findIndex((item) => item.pupilId == this._pupilId);
  }

  protected onEvtShow(): void {
    this.showPupilDetail();

    // 头像处理
    let nodeHeadLay = this.getNode("head_lay");
    nodeHeadLay.active = this._slotIndex != -1;

    nodeHeadLay.children.forEach((child) => (child.active = false));
    for (let i = 0; i < nodeHeadLay.children.length; i++) {
      let nodeItem = nodeHeadLay.children[i];
      nodeItem["slotIndex"] = i;
      if (this._slotIndex === i) {
        nodeItem.getChildByName("bg_select").active = true;
      } else {
        nodeItem.getChildByName("bg_select").active = false;
      }

      const slotInfo = PupilModule.data.pupilTrainMsg.trainSlotList[i];
      if (slotInfo && slotInfo.pupilId > 0) {
        nodeItem.active = true;

        // 头像
        let pupilMsg = PupilModule.data.allPupilMap[slotInfo.pupilId];
        nodeItem.getChildByName("PupilHeader").getComponent(PupilHeader).setHeaderByNameId(pupilMsg.ownInfo.nameId);
      }
    }
  }

  /** 设置更新当前槽位弟子的信息 */
  private showPupilDetail() {
    this._pupilMsg = PupilModule.data.allPupilMap[this._pupilId];
    log.log("showPupilDetail", this._pupilMsg);
    let config = PupilModule.data.getConfigPupil(this._pupilMsg.ownInfo.talentId);

    // 弟子头像
    this.getNode("PupilAni")
      .getComponent(PupilAni)
      .setAniByNameId(this._pupilMsg.ownInfo.nameId, this._pupilMsg.ownInfo.adultAttrList.length);

    // 弟子名字
    PupilModule.service.setPupilNameNode(this.getNode("pupil_name_lab"), this._pupilMsg.ownInfo.nameId);

    // 天资背景
    let nodeTalent = this.getNode("bg_talent");
    nodeTalent.active = false;
    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_G_PUPIL}?images/${config.talentBg}`,
      nodeTalent.getComponent(Sprite),
      this,
      () => {
        nodeTalent.active = true;
      }
    );

    // 繁荣度
    this.getNode("lbl_bloom").getComponent(Label).string = `繁荣度:${Formate.format(
      PupilModule.data.getPupilBloom(this._pupilId)
    )}`;

    // 天生属性

    PupilModule.service.setLayoutAttr(
      this.getNode("init_attr_lay").getComponent(Layout),
      this._pupilMsg.ownInfo.initAttrList
    );

    // 领悟属性
    if (this._pupilMsg.ownInfo.adultAttrList.length > 0) {
      this.getNode("lbl_adult_attr").getComponent(Label).string = Formate.formatAttribute(
        this._pupilMsg.ownInfo.adultAttrList[0],
        this._pupilMsg.ownInfo.adultAttrList[1]
      );

      // 换背景
      ResMgr.loadSpriteFrame(
        `${BundleEnum.BUNDLE_G_PUPIL}?patterns/pupilIcon`,
        `dz_shuxing_2_${this._pupilMsg.ownInfo.talentId}`,
        this.getNode("adult_attr_bg").getComponent(Sprite)
        // this 资源临时处理方案
      );
    }

    // 培养进度
    this.getNode("ProgressBar").active = this._slotIndex != -1;
    this.getNode("train_progress_lab").active = this._slotIndex != -1;
    let progress = 1;
    // 未成年进度不为1
    if (this.getNode("ProgressBar").active) {
      let trainSlot = PupilModule.data.pupilTrainMsg.trainSlotList[this._slotIndex];
      progress = (trainSlot.train * config.trainAdd) / config.finish;

      this.getNode("ProgressBar").getComponent(ProgressBar).progress = progress;
      this.getNode("progress_lab").getComponent(Label).string = `${trainSlot.train * config.trainAdd}/${config.finish}`;
    }

    // 攻血防
    for (let i = 0; i < this._pupilMsg.ownInfo.basicAttrList.length; i += 2) {
      let base_attr_lab = this.getNode("base_attr_lay").children[i / 2];
      base_attr_lab.getComponent(Label).string = Formate.formatAttribute(
        this._pupilMsg.ownInfo.basicAttrList[i],
        this._pupilMsg.ownInfo.basicAttrList[i + 1]
      );
    }
  }

  private on_click_btn_slot(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let target: Node = event.target;
    for (let i = 0; i < target.parent.children.length; i++) {
      target.parent.children[i].getChildByName("bg_select").active = false;
    }
    target.getChildByName("bg_select").active = true;
    let slotIndex = event.node["slotIndex"];
    let trainSlotList = PupilModule.data.pupilTrainMsg.trainSlotList;

    if (slotIndex < trainSlotList.length) {
      if (trainSlotList[slotIndex].pupilId == -1) {
        // UIMgr.instance.back();
      } else {
        this._pupilId = trainSlotList[slotIndex].pupilId;
        this.showPupilDetail();
      }
    } else {
      let config = JsonMgr.instance.jsonList.c_pupil[1];
      TipMgr.showTip(`主角等级达到${config.unlockPlaceList[slotIndex]}级解锁`);
    }
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
