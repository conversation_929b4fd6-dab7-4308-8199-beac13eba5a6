import { _decorator, EventTouch, Label, Node, sp } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { FriendModule } from "../../../../module/friend/FriendModule";
import { times } from "../../../../lib/utils/NumbersUtils";
import { ItemEnum } from "db://assets/GameScrpit/lib/common/ItemEnum";
import { PlayerRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { ItemCost } from "../../../common/ItemCost";
import { FriendCitySkillMessage } from "../../../net/protocol/Friend";
import { FriendCityAdapter } from "./FriendCityAdapter";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FriendRouteItem } from "db://assets/GameScrpit/module/friend/FriendRoute";
import { FriendAudioName } from "db://assets/GameScrpit/module/friend/FriendConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;
@ccclass("FriendCityExpandViewHolder")
export class FriendCityExpandViewHolder extends ViewHolder {
  @property(Node)
  private upgrade: Node;
  @property(ItemCost)
  private itemCost1: ItemCost;
  @property(ItemCost)
  private itemCost2: ItemCost;
  @property(Label)
  private title: Label;
  @property(Node)
  up_effect: Node;

  @property(Node)
  private bgWeiManji: Node;
  @property(Node)
  private bgManji: Node;

  private _friendId: number;
  private _skillIndex: number;
  private _context: FriendCityAdapter;

  private _oldValue = 0;
  start() {
    this.up_effect.active = false;
    this.itemCost1.setCheck(FriendModule.viewModel.setting_xilianshi);
    this.itemCost1.setOnCheckChange((check: boolean) => {
      FriendModule.viewModel.setting_xilianshi = check;
    });
    this.up_effect.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
      //清空监听
      if ("animation" == trackEntry.animation.name) {
        this.up_effect.active = false;
      }
    });
  }

  update(deltaTime: number) {}

  public setContext(context: FriendCityAdapter) {
    this._context = context;
  }
  private calcEnergy(): number {
    let friendMessage = FriendModule.data.getFriendMessage(this._friendId);
    let skill: FriendCitySkillMessage = friendMessage.citySkillList[this._skillIndex];
    let energyNeed = Math.floor(Math.pow(skill.energyWashCount, 4) * 0.1 + 50);
    return energyNeed;
  }
  private onClickUpgrade(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this.itemCost1.isCheck()) {
      if (!this.itemCost1.isEnough()) {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
          itemId: ItemEnum.许愿石_1018,
        });
        return;
      }
    } else {
      if (!this.itemCost2.isEnough()) {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
          itemId: ItemEnum.气运_1,
        });
        return;
      }
    }
    AudioMgr.instance.playEffect(AudioName.Effect.通用升级);
    this.up_effect.active = true;
    this.up_effect.getComponent(sp.Skeleton).setAnimation(0, "animation", false);
    FriendModule.api.improveSkill(
      this._friendId,
      this._skillIndex,
      this.itemCost1.isCheck(),
      (data: FriendCitySkillMessage) => {
        AudioMgr.instance.playEffect(FriendAudioName.Effect.洗练成功);
        if (
          FriendModule.viewModel.isShowXilianTipsSettingForever() &&
          FriendModule.viewModel.isShowXilianTipsSetting() &&
          data.backUpSkillSkillAdd > this._oldValue
        ) {
          TipsMgr.setEnableTouch(false, 1);
          UIMgr.instance.showDialog(FriendRouteItem.UIFriendXilianTips, {
            old: times(this._oldValue, 100),
            new: times(data.backUpSkillSkillAdd, 100),
          });
        }
        this.refreshTitle(data);
        this._context.setDataOnly();
        this._oldValue = data.skillAdd;
      }
    );
  }
  public updateData(friendId: number, skillIndex: number) {
    this._friendId = friendId;
    this._skillIndex = skillIndex;
    let friendMessage = FriendModule.data.getFriendMessage(this._friendId);
    let skill: FriendCitySkillMessage = friendMessage.citySkillList[this._skillIndex];
    this._oldValue = skill.skillAdd;
    // 1.人族
    // 2.神族
    // 3.妖族
    // 4.冥族
    // 5.巫族
    if (times(skill.skillAdd, 100) >= 30) {
      this.bgManji.active = true;
      this.bgWeiManji.active = false;
    } else {
      this.bgManji.active = false;
      this.bgWeiManji.active = true;
    }
    this.refreshTitle(skill, true);
  }

  private refreshTitle(skill: FriendCitySkillMessage, isinit: boolean = false) {
    let skillId = this._skillIndex + 1;
    let friendSkill = FriendModule.config.getFriendCitySkillById(skillId);
    let skillAdd = 0;
    if (isinit) {
      skillAdd = times(skill.skillAdd, 100);
    } else {
      skillAdd = times(skill.backUpSkillSkillAdd, 100);
    }
    switch (friendSkill.type) {
      case 1:
        this.title.string = `人族类建筑增加繁荣度+${skillAdd}%`;
        break;
      case 2:
        this.title.string = `神族类建筑增加繁荣度+${skillAdd}%`;
        break;
      case 3:
        this.title.string = `妖族类建筑增加繁荣度+${skillAdd}%`;
        break;
      case 4:
        this.title.string = `冥族类建筑增加繁荣度+${skillAdd}%`;
        break;
      case 5:
        this.title.string = `巫族类建筑增加繁荣度+${skillAdd}%`;
        break;
    }
    let energyNeed = Math.floor(Math.pow(skill.energyWashCount, 4) * 0.1 + 50);
    this.itemCost1.setItemId(ItemEnum.许愿石_1018, 1);
    this.itemCost2.setItemId(ItemEnum.气运_1, energyNeed);
  }
}
