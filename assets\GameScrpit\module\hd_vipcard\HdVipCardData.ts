import MsgEnum from "../../game/event/MsgEnum";
import { VipCardMessage } from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { HdVipMsgEnum } from "./HdVipCardConfig";

export class HdVipCardData {
  private _vipCardMessage: VipCardMessage;

  public init() {
    this._vipCardMessage = null;
  }
  /**
   * 是否月卡
   * */
  public get isMonthCard(): boolean {
    if (!this._vipCardMessage) {
      return false;
    }
    return this._vipCardMessage.deadline > TimeUtils.serverTime;
  }
  /**
   * 是否年卡
   */
  public get isLifeCard(): boolean {
    if (!this._vipCardMessage) {
      return false;
    }
    return this._vipCardMessage.life;
  }

  public set vipCardMessage(value: VipCardMessage) {
    this._vipCardMessage = value;
    MsgMgr.emit(HdVipMsgEnum.HDVIPMSGENUM_RED_DOT_UPDATE);

    // MsgMgr.emit(MsgEnum.ON_ACTIVITY_VIP_UPDATE, this._vipCardMessage);
  }
  public get vipCardMessage(): VipCardMessage {
    return this._vipCardMessage;
  }
}
