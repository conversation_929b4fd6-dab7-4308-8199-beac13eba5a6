# 热更过程

## 打包 apk

1. cocoscreator 构建工程
2. 复制构建目录下 data-gg-hot-update 下的全部文件到 data 目录
3. 上传 data-gg-hot-update.zip 到服务器，测试域名：http://m.xmallx.cn/【对应的版本号】/
4. 解压缩 unzip data-gg-hot-update.zip
5. android-studio 打包 apk 或 bundle，上传到相关下载位置
6. 更新http://m.xmallx.cn/version.json 文件内容，格式如下

```json
{
  "version": "【对应的版本号】",
  "url": "http://m.xmallx.cn/【对应的版本号】/"
}
```

7. cdn 更新 version.json

## 发布更新

1. cocoscreator 构建工程
2. 更新 gg-hot-update-config.json 文件版本号

```
{
  "local_bundles": {
    "build-in": {
      "version": "1.0.8",
      "files": ["src/", "jsb-adapter/", "assets/internal/", "assets/main/", "assets/resources/"]
    }
  },
  "remote_bundles": {
    "build-in": {
      "version": "1.0.8",
      "files": ["src/", "jsb-adapter/", "assets/"]
    }
  }
}
```

3. 上传 data-gg-hot-update.zip 到服务器，测试域名：http://m.xmallx.cn/【对应的版本号】/
4. 解压缩 unzip data-gg-hot-update.zip
5. 更新http://m.xmallx.cn/version.json 文件内容，格式如下

```json
{
  "version": "【对应的版本号】",
  "url": "http://m.xmallx.cn/【对应的版本号】/"
}
```

6. cdn 更新 version.json
