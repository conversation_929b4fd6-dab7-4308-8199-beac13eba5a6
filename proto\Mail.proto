syntax = "proto3";
package sim;

// 
message MailMessage {
  int64 id = 1;
  // 发件人名称
  string sender = 2;
  // 标题
  string title = 3;
  // 副标题
  string subTitle = 4;
  // 邮件内容
  string content = 5;
  // 物品列表
  repeated int64 rewardList = 6;
  // 发送时间
  int64 sendTs = 7;
  // 过期时间
  int64 overDateTs = 8;
  // 创建时间
  int64 createTime = 9;
  // 已读/已领取奖励
  bool read = 10;
  // 读取/领取时间
  int64 readTs = 11;
}

// 
message ReadResponse {
  // 已读邮件ID
  repeated int64 idList = 1;
  // 奖励列表
  repeated double rewardList = 2;
}

