import { _decorator, Component, Label, Node, Sprite } from "cc";
import { HeroModule } from "../../../module/hero/HeroModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CCInteger } from "cc";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { CollectAudioName } from "../../../module/player/PlayerConfig";
const { ccclass, property } = _decorator;

@ccclass("Portrait")
export class Portrait extends Component {
  @property(CCInteger)
  heroId: number = 0;

  @property(CCInteger)
  shopType: number = 0;

  @property(Node)
  qualityN: Node = null;

  @property(Node)
  heroName: Node = null;

  private _time = 0.3;
  protected onLoad(): void {
    this.setGray();

    HeroModule.service.updateHeroColorBg(this.qualityN.getComponent(Sprite), this.heroId);

    let db = JsonMgr.instance.jsonList.c_hero[this.heroId];

    if (!db) {
      return;
    }
    this.heroName.getComponent(Label).string = db.name;
  }

  private on_touch_openShopCollect() {
    AudioMgr.instance.playEffect(CollectAudioName.Effect.点击角色);
    UIMgr.instance.showDialog("UIPortraitDetail", { heroId: this.heroId, shopType: this.shopType });
  }

  private setGray() {
    let info = HeroModule.data.getHeroMessage(this.heroId);
    if (info) {
      this.node.getChildByName("spr").getComponent(Sprite).grayscale = false;
    } else {
      this.node.getChildByName("spr").getComponent(Sprite).grayscale = true;
    }
  }

  protected update(dt: number): void {
    this._time -= dt;
    if (this._time > 0) return;
    this.setGray();
  }
}
