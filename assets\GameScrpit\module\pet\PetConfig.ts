import { IConfigPet } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { FriendType, FriendSort } from "../friend/FriendConstant";
import { FriendModule } from "../friend/FriendModule";
import { PetSort } from "./PetConstant";

export class PetConfig {
  public getHeroPet(petId: number) {
    let heroPet: IConfigPet = JsonMgr.instance.jsonList.c_pet[petId];
    return heroPet;
  }
  public getPetSkinList(petId: number): any[] {
    let list = [];
    let heroPet: IConfigPet = JsonMgr.instance.jsonList.c_pet[petId];
    list.push(heroPet.firstSkin);
    for (let i = 0; i < heroPet.skin.length; i++) {
      list.push(heroPet.skin[i]);
    }
    return list;
  }
  
}

export const PetAudioName = {
  Effect: {
    点击前往获得灵兽: 1481,
    点击灵兽技能图标: 1482,
    洗练成功: 1483,
    灵兽升级成功: 1484,
    点击觉醒图标: 1485,
    觉醒成功: 1486,
  },
  Sound: {},
};
