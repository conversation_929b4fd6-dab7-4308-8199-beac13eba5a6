import { GameDirector } from "../../../game/GameDirector";
import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess } from "../../../game/mgr/ApiHandler";
import { ChapterSubCmd } from "../../../game/net/cmd/CmdData";
import {
  BossChapterPassResponse,
  BossChapterRewardResponse,
  ChapterMessage,
  RegularChapterPassResponse,
} from "../../../game/net/protocol/Chapter";
import { BattleReplayMessage } from "../../../game/net/protocol/Comm";
import { DoubleValueList, IntValue, LongValue, StringValue } from "../../../game/net/protocol/ExternalMessage";
import MsgMgr from "../../../lib/event/MsgMgr";
import { FightMsgEnum } from "./FightConfig";
import { FightModule } from "./FightModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FightApi {
  /**获取关卡+宝箱列表 --- 初始化关卡模块 */
  public getChapter(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(ChapterMessage, ChapterSubCmd.getChapter, null, (data: ChapterMessage) => {
      log.info("获取关卡战斗模块基础信息===", data);
      FightModule.data.setChapter(data);
      success && success(data);
    });
  }

  /**通过普通关卡 */
  public passChildChapter(success: ApiHandlerSuccess, errorFunc?: ApiHandlerFail) {
    ApiHandler.instance.request(
      RegularChapterPassResponse,
      ChapterSubCmd.passChildChapter,
      null,
      (data: RegularChapterPassResponse) => {
        log.info("发送通过普通关卡的请求回调===", data);
        let treasureMap = FightModule.data.treasureMap;
        for (let i = 0; i < data.treasureList.length; i++) {
          let info = data.treasureList[i];
          treasureMap[info.id] = info;
        }
        FightModule.data.treasureMap = treasureMap;
        FightModule.data.chapterChildId = data.chapterChildId;
        FightModule.data.chapterMsg.speedParam = data.speedParam;
        success && success(data);
      },
      errorFunc
    );
  }

  /**气运鼓舞 */
  public energyConsumeEncourage(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(IntValue, ChapterSubCmd.energyConsumeEncourage, null, (data: IntValue) => {
      log.info("气运鼓舞请求回调===", data);
      FightModule.data.energyEncourage = data.value;
      success && success(data);
    });
  }

  /**道具鼓舞 */
  public itemConsumeEncourage(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(IntValue, ChapterSubCmd.itemConsumeEncourage, null, (data: IntValue) => {
      log.info("道具鼓舞请求回调===", data);
      FightModule.data.itemEncourage = data.value;
      success && success(data);
    });
  }

  /**挑战boss */
  public passBossChapter(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      BossChapterPassResponse,
      ChapterSubCmd.passBossChapter,
      null,
      (data: BossChapterPassResponse) => {
        log.info("请求挑战boss战斗数据===", data);
        for (let i = 0; i < data.treasureIdList.length; i++) {
          let id = data.treasureIdList[i];
          FightModule.data.bossLevelChangeTreasure(id);
        }
        FightModule.data.pkBossSet(data.energyEncourage, data.itemEncourage);
        success && success(data);
      }
    );
  }

  /**请求boss通关奖励 */
  public takePassBossChapterReward(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      BossChapterRewardResponse,
      ChapterSubCmd.takePassBossChapterReward,
      null,
      (data: BossChapterRewardResponse) => {
        log.info("请求boss通关奖励回调===", data);
        FightModule.data.chapterId = data.chapterId;
        FightModule.data.chapterChildId = 1;
        FightModule.data.itemEncourage = data.itemEncourage;
        FightModule.data.energyEncourage = data.energyEncourage;

        // 检查模块开启
        GameDirector.instance.checkAndLoadModule(true);

        success && success(data);
      }
    );
  }

  /**开启单个宝箱 */
  public taskOneTreasure(Long: number, success?) {
    ApiHandler.instance.requestSync(
      DoubleValueList,
      ChapterSubCmd.taskOneTreasure,
      LongValue.encode({ value: Long }),
      (data: DoubleValueList) => {
        log.info("请求打开单个宝箱===", data);
        let treasureMap = FightModule.data.treasureMap;
        delete treasureMap[Long];
        FightModule.data.treasureMap = treasureMap;
        MsgMgr.emit(FightMsgEnum.ON_FIGHT_TREASURE_UPDATE);
        success && success(data);
      }
    );
  }

  /**开启所有宝箱 */
  public takeAllTreasure(success) {
    ApiHandler.instance.requestSync(DoubleValueList, ChapterSubCmd.takeAllTreasure, null, (data: DoubleValueList) => {
      log.info("请求打开全部得宝箱===", data);
      FightModule.data.treasureMap = Object.create(null);
      MsgMgr.emit(FightMsgEnum.ON_FIGHT_TREASURE_UPDATE);
      success && success(data);
    });
  }

  /** 特殊关卡战斗录像 */
  public passDiffBossChapter(success) {
    ApiHandler.instance.requestSync(
      BattleReplayMessage,
      ChapterSubCmd.passDiffBossChapter,
      null,
      (data: BattleReplayMessage) => {
        success && success(data);
      }
    );
  }

  /**测试本次Boss关卡的属性和战力 */
  public testGetUserEncouragePower() {
    ApiHandler.instance.request(StringValue, ChapterSubCmd.testGetUserEncouragePower, null, (data: StringValue) => {
      log.info("测试本次Boss关卡的属性和战力===", data);
    });
  }

  /**测试获取Boss关卡鼓舞的消耗 */
  public testGetUserEncourageConsume() {
    ApiHandler.instance.request(StringValue, ChapterSubCmd.testGetUserEncourageConsume, null, (data: StringValue) => {
      log.info("测试获取Boss关卡鼓舞的消耗===", data);
      let arr = data.value.split(":");
      let json = arr[4];
      log.log(json);
    });
  }

  /**测试本次Boss关卡的属性和战力 */
  public testGetNonBossEnergyConsume() {
    ApiHandler.instance.request(StringValue, ChapterSubCmd.testGetNonBossEnergyConsume, null, (data: StringValue) => {
      log.info("测试本次Boss关卡的属性和战力", data);
    });
  }
}
