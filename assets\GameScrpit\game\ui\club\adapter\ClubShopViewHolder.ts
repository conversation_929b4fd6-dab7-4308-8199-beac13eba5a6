import { _decorator, Component, Label, Node } from "cc";
import { ClubAudioName, ShopConfig } from "../../../../module/club/ClubConfig";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { GoodsModule } from "../../../../module/goods/GoodsModule";
import MsgMgr from "../../../../lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { ClubModule } from "../../../../module/club/ClubModule";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { PublicRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("ClubShopViewHolder")
export class ClubShopViewHolder extends Component {
  @property(Label)
  private item_title: Label;

  @property(Label)
  private lblUnlock: Label;

  @property(Label)
  private price: Label;
  @property(Label)
  private limit: Label;
  @property(Node)
  private item: Node;

  @property(Node)
  private buy: Node;
  @property(Node)
  private sell_out: Node;

  shopConfig: ShopConfig;
  start() {}

  update(deltaTime: number) {}
  public updateData(data: ShopConfig) {
    this.shopConfig = data;
    let item = data.itemsList;
    let itemConfig = JsonMgr.instance.getConfigItem(item[0][0]);
    this.item_title.string = itemConfig.name;
    // ToolExt.setItemBg(this.item_bkg, itemConfig.color);
    // ToolExt.setItemIcon(this.item_img, item[0][0]);
    // this.item_num.string = `${item[0][1]}`;

    FmUtils.setItemNode(this.item, item[0][0], item[0][1]);
    this.price.string = `${GoodsModule.data.getDailyRedeemBuyCost(data.id)}`;
    let clubLevel = ClubModule.data.clubMessage.level;
    if (clubLevel < data.unlockNum) {
      this.lblUnlock.string = `战盟等级达到${data.unlockNum}级解锁`;
      this.lblUnlock.node.active = true;
    } else {
      this.lblUnlock.node.active = false;
    }
    this.refreshLimit();
  }
  private refreshLimit() {
    // 0.不限购
    // 1.每日限购
    // 2.每周限购
    // 3.每月限购
    // 4.永久限购
    // 5.活动限购
    if (this.lblUnlock.node.active) {
      this.buy.active = false;
      this.sell_out.active = false;
      this.limit.node.active = false;
      return;
    }
    let limit_num = this.shopConfig.max - GoodsModule.data.getGoodsRedeemMsgById(this.shopConfig.id);
    if (limit_num <= 0) {
      this.buy.active = false;
      this.sell_out.active = true;
      this.limit.node.active = false;
      return;
    }
    this.sell_out.active = false;
    this.buy.active = true;
    this.limit.node.active = true;

    switch (this.shopConfig.maxtype) {
      case 0:
        this.limit.string = "";
        break;
      case 1:
        this.limit.string = `每日限购:(${limit_num}/${this.shopConfig.max})`;
        break;
      case 2:
        this.limit.string = `每周限购:${limit_num}`;
        break;
      case 3:
        this.limit.string = `每月限购:${limit_num}`;
        break;
      case 4:
        this.limit.string = `永久限购:${limit_num}`;
    }
  }

  private onClickBuy() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟商店点击兑换);
    if (ClubModule.service.isInterceptOperation()) {
      return;
    }
    let numLimit = this.shopConfig.max - GoodsModule.data.getGoodsRedeemMsgById(this.shopConfig.id);
    const buyConfirm: any = {
      itemInfo: this.shopConfig.itemsList[0],
      moneyInfo: [this.shopConfig.cointype, this.shopConfig.coinPrice],
      maxNum: numLimit,
    };
    UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
      if (resp.ok) {
        GoodsModule.api.redeemGoods(this.shopConfig.id, resp.num, (data: any) => {
          log.log(data);
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data });
          this.refreshLimit();
        });
      }
    });
  }
}
