import { instantiate, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { BannerViewHolder } from "./BannerViewHolder";
import { BannerLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/BannerLayoutManager";

export class BannerAdapter extends ListAdapter {
  private _item: Node;
  private _datas: any[] = [];
  private _layoutManager: BannerLayoutManager;

  constructor(item: Node, layoutManger: BannerLayoutManager) {
    super();
    this._item = item;
    this._layoutManager = layoutManger;
  }

  public initDatas() {
    this._datas = [10406, 10409, 10408, 10407, 10417, 10420, 10419, 10418, 10416];
    this.notifyDataSetChanged();
  }
  getItem(position: number) {
    return this._datas[position];
  }

  onCreateView(viewType: number): Node {
    return instantiate(this._item);
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(BannerViewHolder).updateView(this._datas[position], position, this._layoutManager);
  }
  getCount(): number {
    return this._datas.length;
  }
}
