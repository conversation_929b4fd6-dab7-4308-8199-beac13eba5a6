import { sp, Node } from "cc";
import { HeroAttrEnum } from "../../game/GameDefine";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { PetMessage } from "../../game/net/protocol/Pet";
import ResMgr from "../../lib/common/ResMgr";
import TipMgr from "../../lib/tips/TipMgr";
import { times } from "../../lib/utils/NumbersUtils";
import { PetModule } from "./PetModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class PetService {
  // public setPetMessage(petId: number, pet: PetMessage) {
  //   PetModule.data.setPet(petId, pet);
  // }

  public getHeroAttrAdd(petId: number): Map<number, number> {
    let attrAdd = new Map<number, number>();
    let pet = PetModule.data.getPet(petId);
    let petSkills = PetModule.data.getHeroPetSkillList();
    const attrMap = {
      101: HeroAttrEnum.战将生命百分比_11,
      102: HeroAttrEnum.战将攻击百分比_12,
      103: HeroAttrEnum.战将防御百分比_13,
    };
    for (let i = 0; pet && i < pet.petSkillList?.length; i++) {
      let key = attrMap[petSkills[i % petSkills.length].id];
      if (!attrAdd[key]) {
        attrAdd[key] = 0;
      }
      attrAdd[key] += times(pet.petSkillList[i]?.skillAdd ?? 0, 10000);
    }
    return attrAdd;
  }
  /**
   * 获取灵兽最大等级
   * @param petId
   * @returns
   */
  public getPetMaxLevel(petId: number): number {
    let pet = PetModule.data.getPet(petId);
    let petLocal = PetModule.config.getHeroPet(petId);
    if (!pet) {
      return petLocal.levelMax;
    }
    let maxLevel = pet.awakeCount * petLocal.wakeLvAdd + petLocal.levelMax;
    for (let i = 0; i < pet.skinList.length; i++) {
      maxLevel += JsonMgr.instance.jsonList.c_petSkin[pet.skinList[i]].add3;
    }
    return maxLevel;
  }

  public setPetSkin(skinId: number, petNode: Node, scaleKey: string) {
    let petSkin = JsonMgr.instance.jsonList.c_petSkin[skinId];
    let spineInfo = JsonMgr.instance.jsonList.c_spineShow[petSkin.spineId];
    let spinePath = spineInfo.prefabPath.split("?");
    if (spinePath.length < 2) {
      TipMgr.showTip("配置错误:灵兽资源未配置");
    } else {
      ResMgr.setNodePrefab(spinePath[0], spinePath[1], petNode, (node: Node) => {
        if (node.name === petNode.children[0]?.name) {
          log.log("刷新皮肤", node.uuid);
          node.destroy();
          return true;
        }
        petNode.destroyAllChildren();
        petNode.addChild(node);
        petNode.setScale(petSkin[scaleKey][0], petSkin[scaleKey][1]);
        node.getComponentInChildren(sp.Skeleton).setAnimation(0, "boss_idle", true);

        petNode.walk((child) => (child.layer = petNode.layer));

        return true;
      });
    }
  }
}
