import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { DayActivityMsgEnum } from "./DayActivityConfig";
import { DayActivityModule } from "./DayActivityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class DayActivityService {
  private _tickId: number = null;
  public async init() {
    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }

    // 获取配置文件
    let redNameList = [];
    let DayVO = await DayActivityModule.data.getDayVO();
    for (let i = 0; i < DayVO.length; i++) {
      let list = DayVO[i];
      for (let j = 0; j < list.length; j++) {
        let info = list[j];
        if (info.price == 0 && info.adNum == 0 && !info.cost) {
          redNameList.push("MLGB" + info.id);
        }
      }
    }
    BadgeMgr.instance.addListBadgeItem(BadgeType.UITerritory.btn_chongzhihaoli.FundBanner001.btn_type1, redNameList);

    MsgMgr.off(DayActivityMsgEnum.DAYACTIVITY_RED_DOT_UPDATE, this.updatePopover, this);
    MsgMgr.on(DayActivityMsgEnum.DAYACTIVITY_RED_DOT_UPDATE, this.updatePopover, this);
    this._tickId = TickerMgr.setInterval(3, this.updatePopover.bind(this), false);
  }

  // 获取所有活动信息
  public async httpGetAllActivity() {
    GameHttpApi.getActivityConfigAll().then((resp: any) => {
      log.log(resp);
    });
  }

  /**
   * 红点更新方法
   */
  private updatePopover() {
    this.FundBanner001cb();
  }

  //**每日红点 */
  async FundBanner001cb() {
    let DayVO = await DayActivityModule.data.getDayVO();
    let dayMessage = DayActivityModule.data.dayMessage;
    if (dayMessage.activityId === 0) {
      return;
    }
    let redBool = false;
    for (let i = 0; i < DayVO.length; i++) {
      let list = DayVO[i];
      for (let j = 0; j < list.length; j++) {
        let info = list[j];
        if (info.price == 0 && info.adNum == 0 && !info.cost) {
          let param1 = dayMessage.redeemMap[info.id] || 0;

          let param2 = info.max;
          if (param2 > param1) {
            redBool = true;
          } else {
            redBool = false;
          }
          BadgeMgr.instance.setShowById(
            BadgeType.UITerritory.btn_chongzhihaoli.FundBanner001.btn_type1["MLGB" + info.id].id,
            redBool
          );
        }
      }
    }
  }
}
