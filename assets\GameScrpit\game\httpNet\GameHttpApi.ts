import { GHttp } from "../../lib/http/GHttp";
import { GameHealthModule } from "../../module/game_health/GameHealthModule";
import { PlayerModule } from "../../module/player/PlayerModule";
import { BaseActivityConfigVO } from "../GameDefine";

export default class GameHttpApi {
  public static gameHttpUrl = "";

  static async pay(data: any) {
    // 未成年限制
    console.log(data);
    if (!GameHealthModule.service.checkPay(data.orderAmount)) {
      return { code: 1000, msg: "未成年" };
    }
    return await GHttp.POST({
      url: GameHttpApi.gameHttpUrl + "/goods/getPayUrl",
      params: data,
    });
  }

  static async getActivityConfig(activityId: number) {
    return await GHttp.GET({
      url: GameHttpApi.gameHttpUrl + `/activity/${activityId}`,
    });
  }

  static async getActivityConfigAll(): Promise<any> {
    return await GHttp.GET({
      url: GameHttpApi.gameHttpUrl + `/activity/all`,
    });
  }
}
