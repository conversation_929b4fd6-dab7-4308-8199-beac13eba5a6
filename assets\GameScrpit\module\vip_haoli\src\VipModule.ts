import MsgEnum from "../../../game/event/MsgEnum";
import { GameData } from "../../../game/GameData";
import data from "../../../lib/data/data";
import MsgMgr from "../../../lib/event/MsgMgr";
import { VipApi } from "./VipApi";
import { VipConfig } from "./VipConfig";
import { VipData } from "./VipData";
import { VipSubscriber } from "./VipSubscriber";
import { VipViewModel } from "./VipViewModel";

export class VipModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): VipModule {
    if (!GameData.instance.VipModule) {
      GameData.instance.VipModule = new VipModule();
    }
    return GameData.instance.VipModule;
  }

  private _data = new VipData();
  private _api = new VipApi();
  private _config = new VipConfig();
  private _viewModel = new VipViewModel();
  private _subscriber = new VipSubscriber();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    this._data = new VipData();
    this._api = new VipApi();
    this._config = new VipConfig();
    this._viewModel = new VipViewModel();
    this._subscriber = new VipSubscriber();

    // 模块初始化
    this._data.init();
    this._subscriber.register();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
