import { _decorator, Component, Label, Layout, Node, ScrollView, UITransform } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import FmUtils from "../../../lib/utils/FmUtils";
import { CityModule } from "../../../module/city/CityModule";
import { RewardMessage } from "../../net/protocol/Comm";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIWorldPreviewAward")
export class UIWorldPreviewAward extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UIWorldPreviewAward`;
  }

  private _id: number = null;
  public init(args: any): void {
    super.init(args);
    this._id = args.buildShowId;
  }

  protected onEvtShow(): void {
    let bool = CityModule.data.cityAggregateMessage.raceRewardIdList.includes(this._id);
    if (bool == true) {
      this.getNode("bg_yilingqu_bai").active = true;
      this.getNode("btn_huangse").active = false;
      this.getNode("btn_zhihui").active = false;
    } else {
      this.getNode("bg_yilingqu_bai").active = false;
      this.getNode("btn_huangse").active = true;
      //还要判断是否收集满了
      let bool = this.isTrimOk();
      this.getNode("btn_huangse").active = bool;
      this.getNode("btn_zhihui").active = !bool;
    }
    const cfgBuildShow = JsonMgr.instance.jsonList.c_buildShow[this._id];
    this.getNode("world_name").getComponent(Label).string = cfgBuildShow["name"];
    this.loadItem(cfgBuildShow);
  }

  private isTrimOk() {
    const cfgBuildShow = JsonMgr.instance.jsonList.c_buildShow[this._id];
    let list = cfgBuildShow.trimId;
    let decorationIdList = CityModule.data.cityAggregateMessage.decorationIdList;

    let bool = true;
    for (let i = 0; i < list.length; i++) {
      if (decorationIdList.includes(list[i]) == false) {
        bool = false;
        break;
      }
    }

    for (let i = 0; i < cfgBuildShow.buildShowId.length; i++) {
      if (CityModule.data.cityMessageMap.has(cfgBuildShow.buildShowId[i]) == false) {
        bool = false;
        break;
      }
    }
    return bool;
  }

  private loadItem(cfgBuildShow) {
    let itemListMap = cfgBuildShow.rewardList;
    for (let i = 0; i < itemListMap.length; i++) {
      let node = null;
      if (i == 0) {
        node = this.getNode("Item");
      } else {
        node = ToolExt.clone(this.getNode("Item"), this);
      }
      this.getNode("content").addChild(node);
      FmUtils.setItemNode(node, itemListMap[i][0], itemListMap[i][1]);
    }
    this.getNode("content").getComponent(Layout).updateLayout(true);
    if (
      this.getNode("content").getComponent(UITransform).width >=
      this.getNode("ScrollView").getComponent(UITransform).width
    ) {
      this.getNode("ScrollView").getComponent(ScrollView).content = this.getNode("content");
    }
  }

  private on_click_btn_huangse() {
    AudioMgr.instance.playEffect(1903);
    CityModule.api.getTrimAward(this._id, (rs: RewardMessage) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList, transformList: rs.transformList });
      UIMgr.instance.back();
    });
  }
}
