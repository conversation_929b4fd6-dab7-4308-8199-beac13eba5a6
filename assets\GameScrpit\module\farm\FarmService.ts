import { FarmCollectorMessage, FarmSlotMessage } from "../../game/net/protocol/Farm";

import { FarmModule } from "./FarmModule";

export class FarmService {
  /**
   * 查询 葫芦标识 对应槽位 采摘人员信息
   * @param identifyId 葫芦标识
   * @returns
   */
  public getFarmSlotOtherUserIdById(identifyId: number): FarmCollectorMessage {
    for (let i = 0; i < FarmModule.data.farmTrainMessage.slotList.length; i++) {
      let farmSlotMessage: FarmSlotMessage = FarmModule.data.farmTrainMessage.slotList[i];
      if (farmSlotMessage.identifyId == identifyId) {
        if (
          farmSlotMessage.otherCollectorMessage &&
          farmSlotMessage.otherCollectorMessage.playerBaseMessage.userId != -1
        ) {
          return farmSlotMessage.otherCollectorMessage;
        }
      }
    }
    return null;
  }

  public updatePopover() {
    // 当前有空余猴子处于未派遣状态 且 今日还有可派遣次数的情况下有红点提示
    let hasWorker = false;
    let dispatchMap = FarmModule.data.farmTrainMessage.dispatchMap;
    let dispatchList = FarmModule.data.dispatchList;
    if (dispatchList.length < FarmModule.data.farmTrainMessage.beeMetric.totalBeeCnt) {
    }
    for (let idx in dispatchMap) {
      let dispatch = dispatchMap[idx];
    }
  }
}
