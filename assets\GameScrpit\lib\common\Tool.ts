import { Button, Node, UITransform, Vec2, instantiate } from "cc";
import { UINode } from "../ui/UINode";
import { bezier } from "cc";
import { Vec3 } from "cc";
import { TimeUtils } from "../utils/TimeUtils";

export default class Tool {
  public static clone(node: Node, bindTo: UINode = null): Node {
    let clone = instantiate(node);
    if (bindTo != null) {
      let button = clone.getComponent(Button);
      if (button != null) {
        this.onRegisterClick(button, bindTo);
      }
    }
    this.collectChild(clone, bindTo, clone);
    return clone;
  }

  //注册按钮监听
  private static onRegisterClick(button: Button, bindTo) {
    if (bindTo == null) {
      return;
    }
    if (bindTo["on_click_" + button.node.name] == null) {
      return;
    }
    button.node.on(
      "click",
      function () {
        bindTo.on_click_common(button);
        bindTo["on_click_" + button.node.name] && bindTo["on_click_" + button.node.name](button);
      },
      this
    );
  }

  //遍历寻找子节点
  public static collectChild(parent: Node, bindTo, clone) {
    if (parent == null || parent.children == null || parent.children.length == 0) {
      return;
    }
    for (let i = 0; i < parent.children.length; i++) {
      let child = parent.children[i];
      let name = child.name;
      if (name == "name" || name == "view") {
        name = "default_" + name;
      }
      clone[name] = child;

      let button = child.getComponent(Button);
      if (button != null) {
        this.onRegisterClick(button, bindTo);
      }
      this.collectChild(child, bindTo, clone);
    }
  }

  //转中文数字
  public static toChineseNum(num: number) {
    const keys = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    const count = ["", "十", "百", "千"];
    var str = "",
      nums = num.toString().split("").reverse();

    nums.map(function (value, index) {
      str =
        keys[value] + (Number(value) == 0 ? "" : count[index > 3 ? index % 4 : index]) + (index == 4 ? "万" : "") + str;
    });

    /*
     * 需要去掉的零：
     * 1.后面跟着零的零
     * 2.最后连续的零
     * 3.万前面的零
     */
    return str.replace(/零(?=零)|零$|零(?=万)/g, "");
  }

  public static calcZPos(pos: Vec2) {
    let zPos = Math.floor(pos.y / 20);
    return zPos;
  }

  /**转换成另一个节点坐标系
   *@param node 需要被转换的节点
   *@param converNode 转换到哪个坐标系节点
   */
  public static transferOfAxes(node: Node, converNode: Node) {
    let worldSpace = node.parent.getComponent(UITransform).convertToWorldSpaceAR(node.position);
    let nodeSpace = converNode.getComponent(UITransform).convertToNodeSpaceAR(worldSpace);
    return nodeSpace;
  }

  public static getWorldPos(node: Node) {
    return node.parent.getComponent(UITransform).convertToWorldSpaceAR(node.position);
  }

  /**日期换算 */
  public static timestampToDate(timestamp) {
    let date = new Date(timestamp);
    let year = date.getFullYear();
    let month = date.getMonth() + 1; // 月份是从0开始的，所以要加1
    let day = date.getDate();
    return year + "-" + month + "-" + day;
  }

  public static formatTimeAgo(timestamp: number) {
    const diff = TimeUtils.serverTime - timestamp;

    const hourInMillis = 60 * 60 * 1000;
    const dayInMillis = 24 * hourInMillis;

    if (diff < hourInMillis) {
      const minutes = Math.floor(diff / (60 * 1000));
      return `${minutes} 分钟前`;
    } else if (diff < dayInMillis) {
      const hours = Math.floor(diff / hourInMillis);
      return `${hours} 小时前`;
    } else {
      let days = Math.floor(diff / dayInMillis);
      days = days > 3 ? 3 : days;
      return `${days} 天前`;
    }
  }
  /**
   * 贝塞尔曲线计算
   * @param t 进度 0-1
   * @param p1 起点
   * @param cp1 控制点1
   * @param cp2 控制点2
   * @param p2 终点
   * @param out 输出坐标
   */
  public static bezierCurve(t: number, p1: Vec3, cp1: Vec3, cp2: Vec3, p2: Vec3, out: Vec3) {
    out.x = bezier(p1.x, cp1.x, cp2.x, p2.x, t);
    out.y = bezier(p1.y, cp1.y, cp2.y, p2.y, t);
    out.z = bezier(p1.z, cp1.z, cp2.z, p2.z, t);
  }

  /**
   * 产生一个随机点，和原位置有一定距离
   * @param pos 原点
   * @param opt 随机参数， area 大小， space 离中心距离， offset 偏移%;
   * @returns 返回坐标点
   */
  public static createRandomPos(
    pos: Vec3,
    opt = { area: new Vec3(0, 0, 0), space: new Vec3(0, 0, 0), offset: new Vec3(0, 0, 0) }
  ) {
    const rX = Math.random() - 0.5 + opt.offset.x;
    const rY = Math.random() - 0.5 + opt.offset.y;

    let spaceVec = new Vec3(0, 0, 0);
    if (opt.space.x > 0 && rX > 0) {
      spaceVec.x = opt.space.x;
    } else {
      spaceVec.x = -opt.space.x;
    }

    if (opt.space.y > 0 && rY > 0) {
      spaceVec.y = opt.space.y;
    } else {
      spaceVec.y = -opt.space.y;
    }

    return new Vec3(pos.x + spaceVec.x + rX * opt.area.x, pos.y + spaceVec.y + opt.area.y * rY, 0);
  }

  public static moveElementToFront<T>(arr: T[], target: T): T[] {
    const index = arr.indexOf(target); // 找到目标元素的索引
    if (index > -1) {
      arr.splice(index, 1); // 移除目标元素
      arr.unshift(target); // 将目标元素添加到数组开头
    }
    return arr;
  }

  public static moveElementToEnd<T>(arr: T[], target: T): T[] {
    const index = arr.indexOf(target); // 找到目标元素的索引
    if (index > -1) {
      arr.splice(index, 1); // 移除目标元素
      arr.push(target); // 将目标元素添加到数组结尾
    }
    return arr;
  }

  /**
   *
   * 复制一个对象，好处是防止关联，但内存会增加一份
   *
   * @param obj 任务对象
   * @returns 深度复制
   */
  public static cloneDeep(obj: any): any {
    if (typeof obj !== "object" || obj === null) return obj;
    if (typeof obj === "function") return obj; // 复制函数引用
    const result = Array.isArray(obj) ? [] : {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        result[key] = Tool.cloneDeep(obj[key]);
      }
    }
    return result;
  }
}
