{"skeleton": {"hash": "riYw3HROYBECOibAvUsTLqUKMyU=", "spine": "3.8.75", "x": -121.88, "y": -6.12, "width": 327.16, "height": 279.3, "images": "./images/", "audio": "D:/spine导出/boss动画/牛魔王"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "alll", "parent": "root", "x": 8.39, "y": -64.46, "scaleX": 0.7, "scaleY": 0.7}, {"name": "bone", "parent": "alll", "length": 96.53, "rotation": 1.9, "x": -20.62, "y": 180.66}, {"name": "bone2", "parent": "bone", "length": 37.07, "rotation": 108.2, "x": -10.71, "y": 3.98}, {"name": "bone3", "parent": "bone2", "length": 47.02, "rotation": -5.81, "x": 37.33, "y": -0.1}, {"name": "bone4", "parent": "bone3", "length": 22.38, "rotation": -1.13, "x": 46.94, "y": -1.44}, {"name": "bone5", "parent": "bone3", "x": 54.27, "y": -93.73}, {"name": "bone6", "parent": "bone5", "length": 74.09, "rotation": -166.44, "x": -5.15, "y": -35.25}, {"name": "bone7", "parent": "bone6", "length": 50.68, "rotation": -27.38, "x": 73.72, "y": 0.19}, {"name": "bone8", "parent": "bone7", "length": 27.11, "rotation": 23.76, "x": 56.45, "y": 0.37}, {"name": "bone9", "parent": "bone8", "length": 20.23, "rotation": -80.86, "x": 27.11}, {"name": "bone10", "parent": "bone9", "length": 24.73, "rotation": -22.81, "x": 20.23}, {"name": "bone11", "parent": "bone7", "length": 16.41, "rotation": -51.58, "x": 54.59, "y": -25.16}, {"name": "bone12", "parent": "bone11", "length": 19.06, "rotation": 44.91, "x": 16.41}, {"name": "bone13", "parent": "bone3", "x": 75.31, "y": 15.36}, {"name": "bone14", "parent": "bone13", "length": 46.22, "rotation": 133.86, "x": -13.29, "y": 8.36}, {"name": "bone15", "parent": "bone14", "length": 46.49, "rotation": -42.72, "x": 46.22}, {"name": "bone16", "parent": "bone15", "length": 33.84, "rotation": -17.48, "x": 46.49}, {"name": "bone17", "parent": "bone4", "rotation": -10.38, "x": 53.85, "y": 6.54}, {"name": "bone18", "parent": "bone", "x": -40, "y": -19.65, "color": "00cffbff"}, {"name": "bone19", "parent": "bone", "x": -15.06, "y": -32.74, "color": "00cffbff"}, {"name": "bone20", "parent": "bone", "x": 23.73, "y": -32.6, "color": "00cffbff"}, {"name": "bone21", "parent": "bone", "x": 45.11, "y": -15.63, "color": "00cffbff"}, {"name": "bone23", "parent": "bone", "x": 70.43, "y": -8.77, "color": "00cffbff"}, {"name": "bone24", "parent": "bone", "length": 19.76, "rotation": -65.33, "x": -23, "y": -5.67, "color": "ce0000ff"}, {"name": "bone25", "parent": "bone24", "length": 22.56, "rotation": -0.32, "x": 19.5, "y": 0.13, "color": "ce0000ff"}, {"name": "bone26", "parent": "bone25", "length": 22.89, "rotation": -7.36, "x": 22.56, "color": "ce0000ff"}, {"name": "bone27", "parent": "bone", "length": 20.27, "rotation": -64.25, "x": 3.27, "y": -4.55, "color": "ce0000ff"}, {"name": "bone28", "parent": "bone27", "length": 23.97, "rotation": -2.3, "x": 20.27, "color": "ce0000ff"}, {"name": "bone29", "parent": "bone28", "length": 21.81, "rotation": 3.56, "x": 23.97, "color": "ce0000ff"}, {"name": "bone22", "parent": "bone", "length": 39.1, "rotation": -110.33, "x": -23.67, "y": -16.5}, {"name": "bone30", "parent": "bone22", "length": 37.09, "rotation": 18.87, "x": 39.99}, {"name": "bone31", "parent": "bone", "length": 38.83, "rotation": -73.33, "x": 50.8, "y": -17.57}, {"name": "bone32", "parent": "bone31", "length": 41.55, "rotation": -7.26, "x": 38.83}, {"name": "bone33", "parent": "alll", "length": 40, "rotation": 2.35, "x": 52.2, "y": 87.5}, {"name": "target1", "parent": "bone33", "rotation": -2.35, "x": -0.97, "y": -0.51, "color": "ff3f00ff"}, {"name": "bone34", "parent": "alll", "length": 40, "rotation": -0.45, "x": -56.3, "y": 88.19}, {"name": "target2", "parent": "bone34", "rotation": 0.45, "x": 0.59, "y": 0.4, "color": "ff3f00ff"}, {"name": "bone35", "parent": "bone2", "rotation": -108.2, "x": 30.23, "y": 24.06}, {"name": "bone36", "parent": "bone3", "rotation": -102.39, "x": 23.26, "y": -83.83}, {"name": "bone37", "parent": "bone6", "x": 39.32, "y": -39.45}, {"name": "bone38", "parent": "bone6", "x": 61.2, "y": -28.51}, {"name": "bone39", "parent": "bone6", "x": 72.37, "y": -7.84}, {"name": "bone40", "parent": "bone6", "x": 76.59, "y": 18.05}, {"name": "bone41", "parent": "bone6", "x": 81.37, "y": 41.6}, {"name": "bone42", "parent": "bone16", "length": 67.8, "rotation": -67.06, "x": 33.67, "y": -23.53, "scaleX": 0.8598, "scaleY": 0.8598}, {"name": "bone44", "parent": "alll", "x": -44.65, "y": 48.46}, {"name": "bone43", "parent": "bone44", "length": 67.8, "rotation": -174.46, "x": -71.37, "y": 112.81}, {"name": "all", "parent": "alll", "x": 13.62, "y": 47.64, "scaleX": -0.1439, "scaleY": 0.1439}, {"name": "tx_act1_da<PERSON><PERSON>", "parent": "all", "x": 0.37, "y": 18.31}, {"name": "<PERSON><PERSON><PERSON>", "parent": "tx_act1_da<PERSON><PERSON>", "scaleX": 9.4159, "scaleY": 4.8511}, {"name": "ro", "parent": "<PERSON><PERSON><PERSON>"}, {"name": "daoguang2", "parent": "tx_act1_da<PERSON><PERSON>", "scaleX": 9.4159, "scaleY": 4.8511}, {"name": "ro2", "parent": "daoguang2"}, {"name": "qixuan1", "parent": "all", "x": 0.37, "y": 18.31, "scaleX": 16.384, "scaleY": 16.384}, {"name": "bone45", "parent": "alll", "x": -226.23, "y": 136.34}, {"name": "runall", "parent": "root", "x": -345.95, "y": 80.78}, {"name": "run1", "parent": "runall", "rotation": -28.76, "x": 336.81, "y": 59.25, "scaleX": 1.8601, "scaleY": -1.8601, "shearY": -11.7}, {"name": "run2", "parent": "runall"}, {"name": "run3", "parent": "runall"}, {"name": "run4", "parent": "runall"}, {"name": "qiuliu", "parent": "root", "x": 121.56, "y": 56.38, "scaleX": -3.3673, "scaleY": 3.3673}, {"name": "juqi", "parent": "root", "x": 88.49, "y": 220.68, "scaleX": 3.8791, "scaleY": 3.8791}, {"name": "boom", "parent": "root", "x": -664.3, "y": -47.43, "scaleX": 2.0282, "scaleY": 1.5717}, {"name": "dg", "parent": "root", "x": -432.51, "y": 374.25, "scaleX": -5.7191, "scaleY": 5.7191}], "slots": [{"name": "dg/dg_01", "bone": "dg", "color": "ff9a00ff", "blend": "additive"}, {"name": "wiqi", "bone": "bone42", "attachment": "wiqi"}, {"name": "wiqi2", "bone": "bone43", "color": "ff9700ff", "blend": "additive"}, {"name": "wiqi3", "bone": "bone43", "color": "ff9700ff", "blend": "additive"}, {"name": "wiqi4", "bone": "bone43", "color": "ff9700ff", "blend": "additive"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "alll", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "light111", "bone": "bone42", "attachment": "light111", "blend": "additive"}, {"name": "jio1", "bone": "alll", "attachment": "jio1"}, {"name": "jio2", "bone": "alll", "attachment": "jio2"}, {"name": "body", "bone": "alll", "attachment": "body"}, {"name": "caopi", "bone": "alll", "attachment": "caopi"}, {"name": "pddd", "bone": "alll", "attachment": "pddd"}, {"name": "ya<PERSON>i", "bone": "bone", "attachment": "ya<PERSON>i"}, {"name": "xiadai", "bone": "alll", "attachment": "xiadai"}, {"name": "tou", "bone": "bone4", "attachment": "tou"}, {"name": "facelight", "bone": "bone4", "attachment": "facelight"}, {"name": "eyelight", "bone": "bone17", "attachment": "eyelight"}, {"name": "youshou2", "bone": "alll", "attachment": "youshou2"}, {"name": "youshou1", "bone": "alll", "attachment": "youshou1"}, {"name": "A_fire_sword0001", "bone": "ro"}, {"name": "A_fire_sword1", "bone": "ro2"}, {"name": "A_qiliu_00004", "bone": "qixuan1"}, {"name": "A_stone_lizi", "bone": "bone45"}, {"name": "qiliu/luodiyan_00013", "bone": "qiuliu"}, {"name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00034", "bone": "run1", "color": "ffffffa8", "blend": "additive"}, {"name": "juqi/fashi_skill_xl_00000", "bone": "juqi"}, {"name": "boom/hyjy_skill_bzs_00000", "bone": "boom"}], "ik": [{"name": "target1", "bones": ["bone31", "bone32"], "target": "target1", "stretch": true}, {"name": "target2", "order": 1, "bones": ["bone22", "bone30"], "target": "target2", "stretch": true}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.1707, 0.1061, 0.28232, 0.03402, 0.44355, 0, 0.59445, 0, 0.71434, 0.04571, 0.8425, 0.07103, 0.97066, 0.15676, 1, 0.30093, 1, 0.47433, 0.95826, 0.63214, 0.90865, 0.75877, 0.89812, 0.82274, 0.89004, 0.87177, 0.72468, 0.91853, 0.59032, 0.97114, 0.52004, 1, 0.40221, 1, 0.29886, 0.99257, 0.21618, 0.93217, 0.15003, 0.86398, 0.16037, 0.79579, 0.07768, 0.71007, 0.00947, 0.59317, 0, 0.4529, 0.06735, 0.29509, 0.12523, 0.17234, 0.23891, 0.55226, 0.30299, 0.72955, 0.50557, 0.7276, 0.67713, 0.68864, 0.84043, 0.65941, 0.72261, 0.52693, 0.74948, 0.32041, 0.73708, 0.14507, 0.13143, 0.70812, 0.05081, 0.57953, 0.20377, 0.34184, 0.22031, 0.1665, 0.46009, 0.32431, 0.47456, 0.52888, 0.31126, 0.83865, 0.5283, 0.82307, 0.68333, 0.80748, 0.8487, 0.76657, 0.30093, 0.88736, 0.44149, 0.94191, 0.56551, 0.91269, 0.71227, 0.86983, 0.85283, 0.82696], "triangles": [28, 29, 42, 29, 30, 43, 10, 30, 9, 41, 28, 42, 43, 30, 10, 11, 43, 10, 44, 19, 40, 18, 19, 44, 44, 40, 45, 45, 41, 46, 15, 45, 46, 46, 47, 13, 16, 45, 15, 15, 46, 14, 16, 17, 44, 14, 46, 13, 13, 48, 12, 12, 48, 11, 13, 47, 48, 48, 43, 11, 47, 42, 48, 46, 42, 47, 45, 16, 44, 17, 18, 44, 42, 43, 48, 42, 29, 43, 46, 41, 42, 45, 40, 41, 19, 20, 40, 40, 28, 41, 21, 34, 20, 20, 34, 27, 21, 35, 34, 29, 31, 30, 29, 39, 31, 9, 30, 8, 8, 30, 31, 39, 38, 31, 31, 32, 8, 31, 38, 32, 36, 25, 37, 26, 23, 36, 24, 25, 36, 36, 23, 24, 25, 0, 37, 32, 7, 8, 38, 33, 32, 33, 3, 4, 33, 38, 3, 32, 6, 7, 6, 33, 5, 6, 32, 33, 33, 4, 5, 36, 37, 38, 38, 37, 2, 37, 1, 2, 38, 2, 3, 37, 0, 1, 26, 36, 38, 34, 26, 27, 27, 26, 39, 22, 35, 21, 34, 35, 26, 22, 23, 35, 23, 26, 35, 39, 26, 38, 20, 27, 40, 40, 27, 28, 27, 39, 28, 28, 39, 29], "vertices": [3, 5, 56.12, -4.38, 0.44286, 6, 48.69, 86.81, 0.06214, 14, 27.65, -22.29, 0.495, 3, 5, 64.17, -25.06, 0.7323, 6, 56.33, 65.97, 0.23623, 14, 35.29, -43.12, 0.03147, 3, 5, 63.91, -52.16, 0.55545, 6, 55.54, 38.89, 0.44313, 14, 34.5, -70.21, 0.00143, 2, 5, 58.28, -76.26, 0.34969, 6, 49.43, 14.9, 0.65031, 2, 5, 46.06, -93.59, 0.16108, 6, 36.88, -2.19, 0.83892, 2, 5, 36.98, -113.05, 0.02638, 6, 27.42, -21.47, 0.97362, 2, 6, 7.78, -38.15, 0.94645, 39, 38.62, 48.19, 0.05355, 2, 6, -17.72, -36.63, 0.66243, 39, 42.6, 22.96, 0.33757, 3, 3, 32.17, -123.12, 0.0268, 6, -46.96, -29.18, 0.17493, 39, 41.6, -7.19, 0.79827, 4, 3, 8.73, -107.26, 0.09671, 6, -71.88, -15.77, 0.00519, 39, 33.85, -34.41, 0.52211, 2, 88.46, 45.77, 0.376, 2, 3, -9.17, -92.05, 0.184, 2, 79.6, 24.02, 0.816, 1, 2, 77.5, 12.95, 1, 1, 2, 75.9, 4.47, 1, 1, 2, 48.52, -2.77, 1, 1, 2, 26.2, -11.18, 1, 1, 2, 14.51, -15.82, 1, 1, 2, -4.8, -15.18, 1, 1, 2, -21.7, -13.33, 1, 1, 2, -34.9, -2.38, 1, 1, 2, -45.35, 9.84, 1, 2, 3, 26.95, 25.41, 0.09119, 38, -0.26, -3.54, 0.90881, 3, 4, 4.89, 33.79, 0.3912, 14, -70.42, 18.43, 0.02287, 38, -13.32, 11.82, 0.58593, 3, 4, 27.36, 39.61, 0.65785, 14, -47.95, 24.25, 0.09992, 38, -23.83, 32.52, 0.24223, 3, 4, 51.4, 35.09, 0.26806, 14, -23.91, 19.73, 0.70163, 38, -24.57, 56.96, 0.03031, 3, 4, 75.28, 17.61, 0.02185, 14, -0.03, 2.26, 0.9774, 38, -12.62, 84.04, 0.00075, 3, 5, 46.59, 5.5, 0.30954, 6, 39.36, 96.88, 0.02196, 14, 18.33, -12.21, 0.6685, 3, 4, 24.97, 1.39, 0.99356, 14, -50.34, -13.97, 0.0013, 38, 14.02, 38.39, 0.00514, 4, 3, 29.74, -0.51, 0.99587, 4, -7.51, -1.18, 0.00327, 5, -54.45, -0.82, 0.00011, 39, -74.12, -47.79, 0.00075, 4, 3, 18.64, -31.83, 0.64987, 4, -15.38, -33.46, 0.07274, 5, -61.68, -33.24, 0.01477, 39, -40.9, -48.55, 0.26261, 5, 3, 15.34, -60.58, 0.2921, 4, -15.76, -62.4, 0.02227, 5, -61.49, -62.18, 0.00629, 39, -12.56, -42.71, 0.45534, 2, 42.06, 37.47, 0.224, 4, 3, 10.91, -87.48, 0.15793, 4, -17.44, -89.61, 0.0018, 39, 14.38, -38.51, 0.53627, 2, 68.99, 41.67, 0.304, 4, 3, 39.2, -77.25, 0.10179, 4, 9.67, -76.57, 0.01947, 5, -35.79, -75.85, 0.01367, 39, -4.17, -14.83, 0.86507, 4, 4, 43.4, -89.71, 0.00034, 5, -1.8, -88.32, 0.01452, 6, -10.87, 4.02, 0.65819, 39, 1.43, 20.94, 0.32696, 2, 5, 28.37, -93.29, 0.08266, 6, 19.2, -1.53, 0.91734, 4, 3, 42.9, 24.63, 0.00436, 4, 3.04, 25.16, 0.33729, 14, -72.27, 9.81, 0.01314, 38, -4.5, 11.87, 0.64521, 3, 4, 27.99, 32.45, 0.6705, 14, -47.32, 17.1, 0.09935, 38, -16.97, 34.67, 0.23015, 4, 5, 14.94, -0.33, 0.43178, 6, 7.6, 91.67, 0.00015, 14, -13.43, -17.42, 0.568, 39, -88.14, 20.17, 7e-05, 4, 5, 44.03, -9.91, 0.39723, 6, 36.5, 81.51, 0.05115, 14, 15.46, -27.58, 0.55091, 39, -84.42, 50.58, 0.00071, 5, 3, 87.1, -48.94, 0.00625, 4, 54.46, -43.55, 0.02925, 5, 8.34, -41.95, 0.56193, 6, 0.19, 50.18, 0.23456, 39, -46.03, 21.83, 0.16801, 5, 3, 52.86, -38.93, 0.17386, 4, 19.38, -37.07, 0.26843, 5, -26.86, -36.16, 0.20375, 6, -34.89, 56.67, 0.02405, 39, -44.84, -13.82, 0.32992, 3, 3, 11.44, 4.74, 0.20919, 38, 24.22, -11.81, 0.01482, 2, -18.78, 13.37, 0.776, 5, 3, 1.76, -29.62, 0.07896, 4, -32.4, -32.98, 0.00117, 5, -78.71, -33.09, 0.00016, 39, -37.72, -65.28, 0.0237, 2, 16.89, 14.9, 0.896, 5, 3, -4.43, -54.43, 0.05003, 4, -36.05, -58.28, 0.00063, 5, -81.85, -58.47, 6e-05, 39, -12.22, -63.41, 0.04529, 2, 42.39, 16.77, 0.904, 2, 3, -7.06, -82.35, 0.096, 2, 69.73, 22.99, 0.904, 1, 2, -20.75, 4.96, 1, 1, 2, 1.97, -5.29, 1, 1, 2, 22.47, -0.89, 1, 1, 2, 46.77, 5.77, 1, 1, 2, 70.06, 12.46, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 52, 54, 54, 56, 56, 58, 58, 60, 58, 62, 62, 64, 64, 66, 54, 68, 68, 70, 46, 70, 52, 72, 72, 74, 72, 76, 76, 64, 52, 78, 78, 62, 40, 80, 80, 82, 82, 84, 84, 86, 36, 88, 88, 90, 90, 92, 92, 94, 94, 96, 20, 22, 22, 24, 96, 22, 88, 34], "width": 164, "height": 174}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [79.72, -31.99, -50.2, -27.68, -48.12, 35.28, 81.81, 30.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 63}}, "wiqi2": {"wiqi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-300.82, 121.54, 217.75, 71.25, 202.7, -84.02, -315.87, -33.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 521, "height": 156}}, "wiqi3": {"wiqi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-300.82, 121.54, 217.75, 71.25, 202.7, -84.02, -315.87, -33.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 521, "height": 156}}, "wiqi4": {"wiqi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-300.82, 121.54, 217.75, 71.25, 202.7, -84.02, -315.87, -33.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 521, "height": 156}}, "zuoshou": {"zuoshou": {"type": "mesh", "uvs": [0.26717, 0.32138, 0.23153, 0.24928, 0.22524, 0.15181, 0.31958, 0.0717, 0.47262, 0.01028, 0.62357, 0, 0.8437, 0.00894, 0.94434, 0.08371, 1, 0.18786, 0.95062, 0.29334, 0.87515, 0.33607, 0.89192, 0.41751, 0.79968, 0.49629, 0.79968, 0.54703, 0.75565, 0.58308, 0.74098, 0.63648, 0.76404, 0.65384, 0.74098, 0.71393, 0.69905, 0.76466, 0.66969, 0.81273, 0.62148, 0.82341, 0.59632, 0.89685, 0.55648, 0.96361, 0.44118, 1, 0.26717, 0.99699, 0.11412, 0.97295, 0.01978, 0.94625, 0, 0.84744, 0.02607, 0.74196, 0.05333, 0.70057, 0.0701, 0.6218, 0.10364, 0.54836, 0.1267, 0.50697, 0.19798, 0.49495, 0.21895, 0.41885, 0.24201, 0.36945, 0.57535, 0.14647, 0.53971, 0.34141, 0.50407, 0.43754, 0.46005, 0.53367, 0.43279, 0.58308, 0.39715, 0.64583, 0.35732, 0.70591, 0.31748, 0.80205, 0.27346, 0.89685], "triangles": [34, 35, 38, 39, 34, 38, 36, 1, 2, 36, 2, 3, 37, 0, 1, 9, 10, 8, 36, 37, 1, 37, 36, 10, 38, 0, 37, 35, 0, 38, 11, 12, 37, 11, 37, 10, 38, 37, 12, 13, 38, 12, 36, 4, 5, 3, 4, 36, 7, 8, 36, 36, 5, 6, 36, 6, 7, 8, 10, 36, 38, 14, 39, 33, 34, 39, 40, 33, 39, 14, 40, 39, 15, 40, 14, 41, 33, 40, 33, 31, 32, 17, 15, 16, 41, 40, 15, 17, 41, 15, 33, 41, 31, 41, 30, 31, 18, 41, 17, 42, 41, 18, 41, 42, 30, 42, 29, 30, 42, 18, 20, 13, 14, 38, 42, 20, 43, 43, 29, 42, 28, 29, 43, 18, 19, 20, 27, 28, 43, 44, 27, 43, 21, 43, 20, 44, 43, 21, 25, 26, 27, 22, 44, 21, 44, 25, 27, 24, 25, 44, 23, 24, 44, 22, 23, 44], "vertices": [3, 16, -17.21, -33.51, 0.10017, 14, -22.66, 47.84, 0.01523, 15, 25.57, -31.5, 0.8846, 3, 16, -28.29, -41.27, 0.01169, 14, -9.15, 48.59, 0.09123, 15, 13.73, -38.04, 0.89709, 2, 14, 7.93, 44.98, 0.25427, 15, -3.23, -42.2, 0.74573, 2, 14, 19.17, 31.02, 0.50903, 15, -19.42, -34.5, 0.49097, 2, 14, 25.52, 11.4, 0.87307, 15, -33.65, -19.58, 0.12693, 2, 14, 23.06, -5.73, 0.99785, 15, -38.86, -3.08, 0.00215, 2, 14, 15.31, -29.66, 0.99267, 15, -42.27, 21.84, 0.00733, 2, 14, -0.49, -37.47, 0.89408, 15, -31.42, 35.73, 0.10592, 2, 14, -20.12, -39.02, 0.67156, 15, -14.4, 45.65, 0.32844, 3, 16, -45.42, 39.29, 0.00093, 14, -37.03, -28.9, 0.40375, 15, 5.22, 43.87, 0.59532, 3, 16, -35.53, 33.38, 0.01998, 14, -42.31, -18.68, 0.20336, 15, 14.42, 36.95, 0.77666, 3, 16, -22.2, 39.59, 0.10238, 14, -56.91, -16.93, 0.05477, 15, 28.33, 41.72, 0.84285, 1, 15, 44.23, 34.2, 1, 4, 17, -43.37, 36.6, 0.00086, 16, 3.07, 36.53, 0.51239, 14, -76.78, -1.02, 0.00013, 15, 53.14, 36, 0.48663, 1, 16, 10.73, 33.68, 1, 1, 16, 20.35, 34.96, 1, 1, 16, 22.52, 38.4, 1, 1, 16, 33.57, 39.13, 1, 3, 17, -2.76, 37.31, 0.38587, 16, 43.67, 37.3, 0.61395, 15, 93.59, 32.47, 0.00019, 2, 17, 6.45, 36.69, 0.55362, 16, 52.88, 36.7, 0.44638, 2, 17, 9.92, 32.01, 0.6801, 16, 56.36, 32.03, 0.3199, 2, 17, 23.32, 33.21, 0.93313, 16, 69.76, 33.24, 0.06687, 2, 17, 36.08, 32.45, 0.9919, 16, 82.52, 32.51, 0.0081, 1, 17, 46.23, 21.85, 1, 1, 17, 51.64, 2.76, 1, 1, 17, 52.76, -15.17, 1, 1, 17, 51.41, -26.86, 1, 2, 17, 35.21, -34.31, 0.98492, 16, 81.76, -34.25, 0.01508, 2, 17, 16.31, -37.12, 0.83955, 16, 62.86, -37.09, 0.16045, 2, 17, 8.31, -36.37, 0.69424, 16, 54.86, -36.36, 0.30576, 3, 17, -5.72, -38.76, 0.37359, 16, 40.84, -38.77, 0.62368, 15, 82.73, -42.87, 0.00272, 3, 17, -19.41, -39.04, 0.15894, 16, 27.15, -39.08, 0.80791, 15, 69.09, -41.73, 0.03314, 3, 17, -27.26, -38.75, 0.10185, 16, 19.29, -38.8, 0.83321, 15, 61.31, -40.62, 0.06495, 1, 16, 14.8, -31.69, 1, 3, 17, -45.46, -33.43, 0.00244, 16, 1.09, -33.51, 0.47892, 15, 43.76, -33.44, 0.51864, 4, 17, -54.69, -33.57, 2e-05, 16, -8.14, -33.66, 0.26751, 14, -30.29, 52.74, 0.0009, 15, 34.57, -32.61, 0.73157, 2, 14, -0.99, 6.07, 0.72403, 15, -12.08, -3.27, 0.27597, 2, 16, -23.13, -2.8, 0, 15, 22.93, -0.34, 1, 2, 16, -5.49, -1.5, 0.01031, 15, 40.6, -0.91, 0.98969, 2, 16, 12.43, -1.12, 0.99692, 15, 58.46, -2.42, 0.00308, 3, 17, -24.7, -1.38, 0.0001, 16, 21.8, -1.42, 0.99951, 15, 67.75, -3.72, 0.00039, 2, 17, -12.77, -1.9, 0.00191, 16, 33.73, -1.92, 0.99809, 2, 17, -1.15, -3.01, 0.34706, 16, 45.35, -3.02, 0.65294, 1, 17, 16.63, -2.2, 1, 1, 17, 34.33, -1.91, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 10, 72, 72, 74, 74, 76, 76, 78, 26, 76, 76, 70, 68, 76, 0, 76, 76, 24, 76, 28, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88], "width": 114, "height": 179}}, "caopi": {"caopi": {"type": "mesh", "uvs": [0.08969, 0.34318, 0.16132, 0.38298, 0.24358, 0.4203, 0.33379, 0.44517, 0.43594, 0.44517, 0.56065, 0.41283, 0.66546, 0.35562, 0.74373, 0.28597, 0.82411, 0.23223, 0.91034, 0.24617, 0.97724, 0.38554, 1, 0.53607, 0.92223, 0.53885, 0.85682, 0.53328, 0.78843, 0.57509, 0.71707, 0.62527, 0.72451, 0.75906, 0.71261, 0.87614, 0.61598, 0.96534, 0.52084, 0.996, 0.41231, 1, 0.32162, 0.9291, 0.22945, 0.85663, 0.14174, 0.76185, 0.06443, 0.67265, 0, 0.58903, 0, 0.54164, 0.03916, 0.47753, 0.06146, 0.40784, 0.11052, 0.53049, 0.18485, 0.60575, 0.29784, 0.66708, 0.42866, 0.7061, 0.53868, 0.68938, 0.63977, 0.58345, 0.71707, 0.46359, 0.80181, 0.40505, 0.89993, 0.40227, 0.65166, 0.71168, 0.5357, 0.84548, 0.4138, 0.82596, 0.28148, 0.78415], "triangles": [34, 5, 6, 36, 7, 8, 37, 8, 9, 35, 6, 7, 33, 4, 5, 31, 2, 3, 32, 3, 4, 29, 0, 1, 30, 1, 2, 37, 9, 10, 36, 8, 37, 13, 36, 37, 12, 37, 10, 12, 10, 11, 13, 37, 12, 14, 36, 13, 35, 7, 36, 14, 35, 36, 34, 6, 35, 15, 34, 35, 15, 35, 14, 38, 34, 15, 38, 15, 16, 33, 5, 34, 32, 4, 33, 33, 34, 38, 39, 32, 33, 39, 33, 38, 40, 32, 39, 17, 38, 16, 18, 39, 38, 18, 38, 17, 19, 40, 39, 19, 39, 18, 20, 40, 19, 30, 2, 31, 31, 3, 32, 41, 30, 31, 22, 23, 30, 40, 31, 32, 41, 31, 40, 41, 22, 30, 21, 41, 40, 22, 41, 21, 20, 21, 40, 28, 0, 29, 27, 28, 29, 25, 26, 27, 29, 1, 30, 24, 27, 29, 25, 27, 24, 30, 24, 29, 23, 24, 30], "vertices": [1, 2, -42.88, -1.37, 1, 1, 2, -32.24, -4.91, 1, 1, 2, -20.01, -8.3, 1, 1, 2, -6.55, -10.74, 1, 1, 2, 8.76, -11.24, 1, 1, 2, 27.55, -9.28, 1, 1, 2, 43.41, -5.22, 1, 1, 2, 55.33, -0.04, 1, 1, 2, 67.52, 3.85, 1, 1, 2, 80.41, 2.31, 1, 2, 23, 19.65, -0.4, 0.816, 2, 90.07, -9.17, 0.184, 1, 23, 22.66, -12.55, 1, 2, 22, 36.31, -5.52, 0.00047, 23, 10.99, -12.38, 0.99953, 2, 22, 26.52, -4.75, 0.08406, 23, 1.2, -11.61, 0.91594, 3, 21, 37.54, 9.22, 0.00432, 22, 16.16, -7.76, 0.49998, 23, -9.16, -14.62, 0.49571, 3, 21, 26.71, 5.56, 0.21747, 22, 5.33, -11.41, 0.72117, 23, -19.99, -18.27, 0.06136, 3, 21, 27.47, -5.18, 0.57988, 22, 6.09, -22.15, 0.41988, 23, -19.23, -29.01, 0.00025, 2, 21, 25.37, -14.48, 0.71146, 22, 3.99, -31.45, 0.28854, 3, 20, 49.44, -20.99, 0.01423, 21, 10.65, -21.13, 0.89365, 22, -10.73, -38.1, 0.09212, 3, 20, 35.09, -22.97, 0.14881, 21, -3.7, -23.11, 0.84711, 22, -25.07, -40.08, 0.00408, 2, 20, 18.81, -22.75, 0.45415, 21, -19.98, -22.89, 0.54585, 2, 20, 5.4, -16.63, 0.78306, 21, -33.38, -16.77, 0.21694, 3, 19, 16.72, -23.47, 0.08984, 20, -8.22, -10.38, 0.90497, 21, -47.01, -10.52, 0.00519, 2, 19, 3.82, -15.45, 0.61417, 20, -21.12, -2.36, 0.38583, 2, 19, -7.53, -7.94, 0.98184, 20, -32.48, 5.15, 0.01816, 1, 19, -16.97, -0.93, 1, 1, 19, -16.84, 2.86, 1, 2, 19, -10.8, 7.79, 0.784, 2, -50.81, -11.86, 0.216, 2, 19, -7.27, 13.25, 0.72, 2, -47.28, -6.4, 0.28, 3, 19, -0.24, 3.2, 0.97994, 20, -25.19, 16.29, 0.00406, 2, -40.25, -16.45, 0.016, 3, 19, 10.7, -3.19, 0.6296, 20, -14.25, 9.9, 0.37035, 21, -53.03, 9.76, 4e-05, 3, 19, 27.48, -8.65, 0.03646, 20, 2.53, 4.44, 0.90901, 21, -36.25, 4.3, 0.05454, 3, 20, 22.04, 0.67, 0.42576, 21, -16.75, 0.53, 0.57311, 22, -38.12, -16.44, 0.00113, 3, 20, 38.58, 1.46, 0.00592, 21, -0.21, 1.32, 0.97951, 22, -21.59, -15.65, 0.01457, 2, 21, 15.23, 9.29, 0.33381, 22, -6.15, -7.69, 0.66619, 3, 21, 27.14, 18.49, 0.00015, 22, 5.76, 1.51, 0.83074, 23, -19.56, -5.35, 0.16911, 2, 22, 18.62, 5.77, 0.22438, 23, -6.71, -1.09, 0.77562, 2, 23, 8.01, -1.35, 0.912, 2, 78.44, -10.12, 0.088, 2, 21, 16.67, -1.03, 0.59857, 22, -4.71, -18, 0.40143, 3, 20, 37.72, -11.01, 0.07614, 21, -1.07, -11.15, 0.91395, 22, -22.45, -28.12, 0.00991, 2, 20, 19.49, -8.84, 0.48701, 21, -19.29, -8.98, 0.51299, 3, 19, 24.71, -17.93, 0.00585, 20, -0.23, -4.84, 0.96818, 21, -39.02, -4.98, 0.02598], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 56, 58, 54, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74], "width": 150, "height": 80}}, "youshou1": {"youshou1": {"type": "mesh", "uvs": [0.09634, 0.6642, 0, 0.43181, 0.10627, 0.13226, 0.35331, 0, 0.81138, 0.13091, 0.86227, 0.59032, 0.83471, 0.63564, 0.87546, 0.65618, 0.89267, 0.6946, 0.94431, 0.70857, 1, 0.76329, 1, 0.80171, 0.9626, 0.79705, 0.92387, 0.86341, 0.84533, 0.89717, 0.7754, 0.91579, 0.68826, 0.89833, 0.60649, 0.91463, 0.48169, 0.93791, 0.42037, 0.94606, 0.30956, 1, 0.28158, 0.93908, 0.20306, 0.93279, 0.16286, 0.85479, 0.12821, 0.75879, 0.18643, 0.65679, 0.29733, 0.66429, 0.37634, 0.68079, 0.40545, 0.71229, 0.39852, 0.77979, 0.44149, 0.82929, 0.52328, 0.81129, 0.61199, 0.78129, 0.69655, 0.74379, 0.77557, 0.68829, 0.72163, 0.43293, 0.47627, 0.19649, 0.54951, 0.64031, 0.67036, 0.5386, 0.44245, 0.4031, 0.25707, 0.30781, 0.75667, 0.58157, 0.87455, 0.74164, 0.80364, 0.78362, 0.70196, 0.81258, 0.60563, 0.85456, 0.47852, 0.87773, 0.37684, 0.88352, 0.2765, 0.8285, 0.16946, 0.7344, 0.26445, 0.7315, 0.35008, 0.78217, 0.1989, 0.85022, 0.30058, 0.92116], "triangles": [42, 7, 8, 42, 34, 7, 43, 34, 42, 12, 9, 10, 12, 10, 11, 42, 9, 12, 9, 42, 8, 13, 42, 12, 43, 42, 13, 14, 43, 13, 44, 33, 43, 16, 45, 44, 15, 44, 43, 15, 43, 14, 16, 44, 15, 46, 17, 18, 19, 46, 18, 47, 51, 29, 48, 51, 47, 53, 48, 47, 52, 21, 22, 48, 21, 52, 48, 53, 21, 46, 19, 47, 46, 47, 30, 19, 20, 53, 19, 53, 47, 21, 53, 20, 49, 25, 50, 24, 0, 49, 50, 26, 51, 48, 50, 51, 52, 49, 50, 52, 50, 48, 23, 24, 49, 52, 23, 49, 23, 52, 22, 0, 1, 25, 50, 25, 26, 26, 25, 40, 28, 51, 27, 29, 51, 28, 30, 29, 28, 46, 30, 31, 31, 30, 28, 45, 46, 31, 34, 41, 6, 36, 3, 4, 40, 2, 3, 40, 3, 36, 39, 40, 36, 1, 2, 40, 35, 36, 4, 39, 36, 35, 38, 39, 35, 5, 41, 35, 38, 35, 41, 5, 35, 4, 37, 39, 38, 25, 1, 40, 39, 26, 40, 39, 27, 26, 37, 27, 39, 28, 27, 37, 37, 38, 33, 32, 37, 33, 31, 28, 37, 31, 37, 32, 45, 31, 32, 45, 32, 44, 17, 45, 16, 17, 46, 45, 33, 38, 41, 6, 41, 5, 33, 41, 34, 7, 34, 6, 33, 34, 43, 32, 33, 44, 47, 29, 30, 51, 26, 27, 49, 0, 25], "vertices": [1, 7, 20.89, -53.55, 1, 1, 7, -13.17, -51.36, 1, 1, 7, -41.47, -18.98, 1, 1, 7, -40.4, 20.97, 1, 1, 7, 6.13, 71.5, 1, 1, 7, 64.01, 49.27, 1, 1, 7, 67.51, 42.9, 1, 3, 7, 72.71, 46.84, 0.03933, 43, -3.89, 28.79, 0.08423, 44, -8.66, 5.24, 0.87644, 3, 7, 78.43, 46.64, 0.00637, 43, 1.83, 28.59, 0.00834, 44, -2.94, 5.04, 0.98529, 1, 44, 2.21, 10.79, 1, 1, 44, 12.47, 14.5, 1, 2, 43, 21.79, 35.65, 0.00036, 44, 17.02, 12.1, 0.99964, 2, 43, 18.71, 31.14, 0.02257, 44, 13.93, 7.59, 0.97743, 2, 43, 23.95, 22.02, 0.18966, 44, 19.17, -1.53, 0.81034, 3, 7, 99.22, 27.89, 0.0009, 43, 22.63, 9.84, 0.49475, 44, 17.85, -13.71, 0.50434, 3, 7, 96.69, 17.76, 0.03983, 43, 20.1, -0.29, 0.71987, 44, 15.32, -23.84, 0.24031, 4, 7, 88.72, 7.68, 0.34903, 42, 16.34, 15.52, 0.00944, 43, 12.13, -10.37, 0.61251, 44, 7.35, -33.92, 0.02902, 4, 7, 85.11, -3.82, 0.55043, 41, 23.92, 24.69, 0.0001, 42, 12.74, 4.01, 0.34852, 43, 8.52, -21.87, 0.10095, 2, 41, 18.22, 7.23, 0.35826, 42, 7.04, -13.44, 0.64174, 3, 40, 36.91, 9.79, 0.0019, 41, 15.03, -1.14, 0.71226, 42, 3.85, -21.82, 0.28584, 3, 40, 35.8, -7.79, 0.08918, 41, 13.92, -18.72, 0.90164, 42, 2.74, -39.4, 0.00918, 3, 40, 26.69, -7.56, 0.23368, 41, 4.81, -18.5, 0.76552, 42, -6.37, -39.17, 0.0008, 2, 40, 20.62, -17.24, 0.50943, 41, -1.26, -28.17, 0.49057, 2, 40, 8.66, -17.51, 0.74039, 41, -13.22, -28.44, 0.25961, 3, 7, 34.25, -55.39, 0.01814, 40, -5.06, -15.94, 0.96004, 41, -26.94, -26.87, 0.02181, 1, 7, 26.11, -41.54, 1, 1, 7, 34.51, -27.79, 1, 1, 7, 41.82, -18.69, 1, 1, 7, 47.52, -16.93, 1, 1, 7, 55.05, -22.05, 1, 1, 7, 63.82, -19.64, 1, 1, 7, 67.23, -8.02, 1, 1, 7, 69.69, 5.23, 1, 1, 7, 70.97, 18.42, 1, 1, 7, 69.75, 32.02, 1, 1, 7, 35.84, 41.09, 1, 1, 7, -8.79, 24.43, 1, 1, 7, 48.75, 6.04, 1, 1, 7, 44.89, 27.9, 1, 1, 7, 13.39, 7.16, 1, 1, 7, -10.45, -10.64, 1, 1, 7, 55.82, 36.28, 1, 2, 43, 6.18, 23.32, 0.01313, 44, 1.4, -0.23, 0.98687, 2, 43, 6.35, 11.6, 0.52637, 44, 1.58, -11.95, 0.47363, 3, 7, 79.49, 14.8, 0.13698, 43, 2.89, -3.25, 0.85412, 44, -1.88, -26.8, 0.0089, 3, 7, 77.94, -0.17, 0.78893, 42, 5.56, 7.66, 0.12605, 43, 1.34, -18.22, 0.08502, 2, 41, 10.88, 10.59, 0.34114, 42, -0.3, -10.08, 0.65886, 3, 40, 26.55, 8.13, 0.01597, 41, 4.67, -2.81, 0.91926, 42, -6.5, -23.48, 0.06477, 3, 7, 52.56, -40.74, 0.00113, 40, 13.24, -1.29, 0.49942, 41, -8.64, -12.23, 0.49946, 3, 7, 34.16, -48.57, 0.02294, 40, -5.16, -9.13, 0.96876, 41, -27.04, -20.06, 0.0083, 3, 7, 40.25, -36.21, 0.04041, 40, 0.93, 3.24, 0.888, 41, -20.95, -7.7, 0.07159, 4, 7, 52.05, -28.41, 0.09467, 40, 12.73, 11.04, 0.26436, 41, -9.14, 0.11, 0.63827, 42, -20.32, -20.57, 0.0027, 2, 40, 10.56, -12.6, 0.68981, 41, -11.32, -23.53, 0.31019, 3, 40, 25.85, -4, 0.19729, 41, 3.97, -14.94, 0.80022, 42, -7.21, -35.62, 0.00249], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 12, 10, 70, 70, 72, 56, 74, 74, 76, 70, 82], "width": 145, "height": 134}}, "youshou2": {"youshou2": {"type": "mesh", "uvs": [0.08529, 0.35621, 0.12476, 0.20888, 0.24106, 0.06899, 0.45499, 0, 0.7416, 0, 0.97422, 0.07048, 1, 0.27882, 0.97422, 0.44996, 0.96175, 0.54223, 0.94514, 0.61664, 0.87868, 0.70296, 0.86622, 0.79969, 0.81845, 0.92172, 0.66475, 0.97529, 0.52145, 0.99315, 0.31791, 1, 0.16006, 0.97678, 0.08229, 0.92226, 0.08229, 0.88905, 0.12279, 0.88277, 0.16371, 0.85255, 0.20088, 0.84237, 0.2109, 0.81455, 0.24767, 0.80556, 0.32096, 0.8115, 0.30503, 0.77588, 0.26934, 0.75579, 0.23301, 0.73752, 0.23046, 0.77497, 0.21325, 0.80335, 0.19479, 0.82634, 0.16358, 0.84602, 0.13369, 0.85846, 0.0522, 0.86975, 0.01264, 0.83275, 0.02583, 0.76975, 0.01374, 0.71621, 0.00055, 0.65479, 0.02253, 0.6107, 0.0599, 0.58786, 0.03462, 0.57369, 0.04164, 0.49972, 0.0601, 0.42531, 0.30397, 0.32577, 0.59454, 0.32208, 0.8234, 0.38104, 0.27569, 0.57266, 0.54054, 0.59109, 0.74626, 0.64268, 0.29626, 0.39763, 0.57911, 0.40131, 0.80026, 0.47501, 0.7694, 0.56529, 0.5534, 0.50818, 0.2654, 0.49712, 0.65111, 0.73296, 0.65626, 0.8343, 0.50197, 0.89879, 0.33997, 0.908, 0.18054, 0.91906, 0.08755, 0.83249, 0.10373, 0.77599, 0.10979, 0.68471, 0.27357, 0.63255, 0.47375, 0.66153, 0.47779, 0.78323], "triangles": [26, 63, 64, 26, 27, 63, 27, 62, 63, 37, 38, 62, 38, 39, 62, 62, 39, 63, 33, 60, 32, 33, 34, 60, 32, 60, 31, 30, 31, 61, 34, 35, 60, 31, 60, 61, 60, 35, 61, 29, 30, 61, 29, 61, 28, 27, 28, 61, 62, 61, 36, 27, 61, 62, 61, 35, 36, 36, 37, 62, 14, 15, 58, 16, 59, 15, 15, 59, 58, 16, 17, 59, 17, 19, 59, 17, 18, 19, 58, 21, 24, 24, 21, 23, 23, 21, 22, 59, 21, 58, 19, 20, 59, 59, 20, 21, 58, 24, 57, 24, 25, 65, 14, 58, 57, 14, 57, 13, 57, 56, 13, 13, 56, 12, 12, 56, 11, 24, 65, 57, 57, 65, 56, 65, 55, 56, 56, 55, 11, 11, 55, 10, 25, 64, 65, 65, 64, 55, 25, 26, 64, 64, 47, 55, 63, 39, 46, 40, 41, 39, 64, 63, 46, 48, 47, 52, 9, 52, 8, 47, 53, 52, 47, 46, 53, 46, 54, 53, 52, 51, 8, 52, 53, 51, 8, 51, 7, 54, 49, 53, 53, 50, 51, 53, 49, 50, 51, 45, 7, 51, 50, 45, 7, 45, 6, 42, 0, 49, 49, 43, 50, 50, 44, 45, 50, 43, 44, 49, 0, 43, 45, 44, 6, 0, 1, 43, 3, 44, 43, 43, 1, 2, 43, 2, 3, 44, 5, 6, 44, 4, 5, 44, 3, 4, 48, 52, 9, 10, 48, 9, 10, 55, 48, 55, 47, 48, 47, 64, 46, 39, 54, 46, 54, 42, 49, 41, 42, 54, 39, 41, 54], "vertices": [1, 8, 22.3, -39.59, 1, 1, 8, 3.62, -35.85, 1, 1, 8, -14.06, -25.12, 1, 2, 8, -22.66, -5.58, 0.99768, 12, -63.35, -48.36, 0.00232, 1, 8, -22.45, 20.5, 1, 2, 8, -13.33, 41.59, 0.9995, 9, -47.25, 65.85, 0.0005, 1, 8, 13.15, 43.72, 1, 1, 8, 34.87, 41.2, 1, 1, 8, 46.57, 39.97, 1, 1, 8, 56.01, 38.38, 1, 1, 8, 66.92, 32.24, 1, 3, 8, 79.2, 31.01, 0.04199, 9, 33.17, 18.88, 0.72387, 10, -17.68, 8.98, 0.23414, 3, 8, 94.66, 26.54, 0.00013, 9, 45.52, 8.56, 0.21445, 10, -5.52, 19.54, 0.78541, 3, 9, 45.98, -6.99, 0.00208, 10, 9.9, 17.53, 0.98175, 11, -16.31, 12.15, 0.01617, 2, 10, 22.04, 12.25, 0.57103, 11, -3.08, 11.99, 0.42897, 1, 11, 15.29, 9.45, 1, 1, 11, 28.87, 3.92, 1, 1, 11, 34.56, -4.18, 1, 2, 11, 33.79, -8.33, 0.99997, 12, 33.93, 18.24, 3e-05, 3, 9, 15.03, -47.14, 0.0004, 11, 30.02, -8.44, 0.99869, 12, 30.56, 19.93, 0.00091, 3, 9, 13.06, -42.17, 0.0049, 11, 25.65, -11.53, 0.9889, 12, 25.25, 19.28, 0.0062, 5, 9, 13.27, -38.56, 0.01494, 10, 35.87, -19.79, 0.00066, 11, 22.09, -12.18, 0.96863, 13, 18.23, 10.63, 0.00017, 12, 21.81, 20.4, 0.01561, 5, 9, 10.42, -36.28, 0.02692, 10, 33.16, -22.24, 0.00346, 11, 20.55, -15.48, 0.94273, 13, 14.62, 11.16, 0.00055, 12, 18.88, 18.22, 0.02634, 5, 9, 10.75, -32.76, 0.03974, 10, 29.74, -21.35, 0.01047, 11, 17.05, -15.99, 0.9117, 13, 13.12, 14.36, 0.00133, 12, 15.56, 19.43, 0.03676, 5, 9, 14.18, -26.98, 0.14753, 10, 24.59, -17.05, 0.08242, 11, 10.63, -14.03, 0.63306, 13, 13.15, 21.07, 0.01176, 12, 10.84, 24.21, 0.12523, 5, 9, 9.46, -26.45, 0.24969, 10, 23.31, -21.63, 0.08467, 11, 11.23, -18.74, 0.29361, 13, 8.81, 19.14, 0.05341, 12, 9.13, 19.77, 0.31862, 5, 9, 5.8, -28.36, 0.20335, 10, 24.62, -25.55, 0.0487, 11, 13.95, -21.85, 0.1478, 13, 6.62, 15.64, 0.14236, 12, 10.06, 15.75, 0.45779, 5, 9, 2.32, -30.43, 0.08577, 10, 26.1, -29.3, 0.01584, 11, 16.78, -24.73, 0.04923, 13, 4.67, 12.1, 0.39071, 12, 11.17, 11.87, 0.45845, 5, 9, 6.57, -32.59, 0.02715, 10, 28.91, -25.46, 0.00451, 11, 17.88, -20.1, 0.01622, 13, 9.43, 12.38, 0.75382, 12, 14.34, 15.42, 0.19829, 5, 9, 9.21, -35.5, 0.01049, 10, 32.2, -23.31, 0.00153, 11, 20.08, -16.84, 0.00668, 13, 13.18, 11.22, 0.89142, 12, 17.82, 17.25, 0.08988, 5, 9, 11.18, -38.23, 0.00409, 10, 35.21, -21.79, 0.0005, 11, 22.26, -14.28, 0.00285, 13, 16.26, 9.86, 0.95401, 12, 20.96, 18.47, 0.03855, 5, 9, 12.3, -41.84, 0.00088, 10, 38.96, -21.27, 6e-05, 11, 25.51, -12.34, 0.00077, 13, 19.06, 7.31, 0.98829, 12, 24.74, 18.63, 0.01, 4, 9, 12.62, -44.97, 5e-05, 11, 28.48, -11.29, 0.00013, 13, 20.92, 4.77, 0.99845, 12, 27.85, 18.15, 0.00138, 1, 13, 23.15, -2.44, 1, 1, 13, 18.86, -6.53, 1, 1, 13, 10.78, -6.2, 1, 2, 13, 4.14, -8.03, 0.95256, 12, 25.01, -2.76, 0.04744, 3, 8, 60.16, -47.61, 0.00022, 13, -3.49, -10.07, 0.41905, 12, 21.05, -9.59, 0.58073, 3, 8, 54.57, -45.57, 0.00811, 13, -9.27, -8.68, 0.14457, 12, 15.97, -12.69, 0.84733, 1, 8, 51.7, -42.14, 1, 1, 8, 49.88, -44.43, 1, 1, 8, 40.5, -43.71, 1, 1, 8, 31.06, -41.96, 1, 1, 8, 18.6, -19.66, 1, 2, 8, 18.34, 6.78, 0.99661, 9, -32.29, 21.23, 0.00339, 1, 8, 26, 27.55, 1, 1, 8, 49.93, -22.49, 1, 1, 8, 52.47, 1.59, 1, 1, 8, 59.17, 20.26, 1, 1, 8, 27.72, -20.44, 1, 2, 8, 28.39, 5.3, 0.99308, 9, -23.69, 15.82, 0.00692, 1, 8, 37.92, 25.34, 1, 1, 8, 49.36, 22.44, 1, 1, 8, 41.95, 2.85, 1, 1, 8, 40.33, -23.35, 1, 2, 8, 70.57, 11.51, 0.02169, 9, 17.41, 4.51, 0.97831, 2, 9, 29.34, -0.35, 0.1444, 10, 0.7, 2.15, 0.8556, 1, 10, 16.93, 1.27, 1, 5, 9, 26.06, -30.43, 0.00515, 10, 29.88, -5.86, 0.00204, 11, 11.17, -1.66, 0.98885, 13, 25.15, 24.12, 0.00011, 12, 17.19, 34.83, 0.00386, 3, 9, 21.39, -44.24, 0.00019, 11, 25.69, -2.94, 0.99928, 12, 29.36, 26.82, 0.00053, 2, 11, 32, -15.3, 0, 13, 18.09, 0.24, 1, 5, 9, 1.95, -43.16, 0.00039, 10, 38.62, -31.69, 5e-05, 11, 29.24, -22.09, 0.00026, 13, 10.8, 0.93, 0.99531, 12, 23.4, 8.29, 0.00399, 2, 13, -0.78, 0.23, 0.27436, 12, 15.69, -0.39, 0.72564, 6, 8, 57.54, -22.75, 0.01254, 9, -8.32, -21.59, 0.07002, 10, 15.69, -38.4, 0.00423, 11, 10.7, -37.16, 0.01083, 13, -8.98, 14.33, 0.00705, 12, -0.07, 3.81, 0.89531, 6, 8, 61.36, -4.56, 0.01708, 9, 2.51, -6.49, 0.82684, 10, 2.5, -25.31, 0.01805, 11, -6.53, -30.2, 0.01845, 13, -7.29, 32.84, 9e-05, 12, -11.94, 18.11, 0.11949, 5, 9, 16.76, -12.5, 0.45954, 10, 10.69, -12.2, 0.34375, 11, -4.06, -14.94, 0.12372, 13, 8.04, 34.87, 0.00124, 12, -2.52, 30.37, 0.07175], "hull": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 0, 84, 0, 86, 86, 88, 88, 90, 78, 92, 92, 94, 94, 96, 84, 98, 98, 100, 100, 102, 18, 104, 104, 106, 106, 108, 108, 82, 110, 112, 112, 114, 114, 116, 116, 118, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130], "width": 91, "height": 127}}, "facelight": {"facelight": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [53.29, -130.55, 98.13, 61.27, 215.95, 33.73, 171.11, -158.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 197, "height": 121}}, "tou": {"tou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-54.79, -129.42, -1.3, 99.41, 233.37, 44.55, 179.88, -184.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 235, "height": 241}}, "jio1": {"jio1": {"type": "mesh", "uvs": [0.31417, 0, 0.52511, 0, 0.72702, 0.0144, 0.85961, 0.16307, 0.92591, 0.34193, 0.9229, 0.45111, 0.91386, 0.53938, 0.89879, 0.6323, 0.91988, 0.67876, 0.93223, 0.73044, 1, 0.78707, 1, 0.91448, 0.90162, 0.98211, 0.72205, 1, 0.44454, 1, 0.24457, 0.96638, 0.20988, 0.85628, 0.2262, 0.80123, 0.19151, 0.73988, 0.17381, 0.69459, 0.14662, 0.62506, 0.10173, 0.57158, 0.1405, 0.53069, 0.05072, 0.4253, 0, 0.29318, 0.01195, 0.13588, 0.12214, 0.04308, 0.4425, 0.1988, 0.52004, 0.3671, 0.56493, 0.55113, 0.57309, 0.60147, 0.58126, 0.63764, 0.59758, 0.67697, 0.60778, 0.74303, 0.62003, 0.79651, 0.66667, 0.88954], "triangles": [14, 35, 13, 13, 35, 12, 12, 35, 11, 14, 15, 16, 11, 35, 10, 35, 14, 34, 17, 34, 14, 17, 14, 16, 35, 9, 10, 35, 34, 9, 17, 33, 34, 33, 17, 32, 34, 33, 9, 17, 18, 32, 33, 8, 9, 33, 7, 8, 33, 32, 7, 18, 31, 32, 18, 19, 31, 19, 30, 31, 19, 20, 30, 32, 31, 7, 31, 30, 7, 7, 30, 6, 20, 29, 30, 30, 29, 6, 6, 29, 5, 21, 22, 20, 20, 22, 29, 22, 28, 29, 5, 29, 28, 28, 23, 27, 28, 22, 23, 5, 28, 4, 26, 27, 25, 26, 0, 27, 27, 23, 24, 25, 27, 24, 28, 3, 4, 3, 27, 2, 2, 27, 1, 27, 3, 28, 27, 0, 1], "vertices": [1, 32, -15.15, -0.52, 1, 1, 32, -11.18, 14.58, 1, 2, 32, -6.04, 28.67, 0.99998, 33, -44.87, 28.67, 2e-05, 2, 32, 10.24, 34.52, 0.97361, 33, -28.6, 34.52, 0.02639, 2, 32, 28.07, 34.89, 0.807, 33, -10.77, 34.89, 0.193, 2, 32, 38.13, 32, 0.59508, 33, -0.7, 32, 0.40492, 2, 32, 46.14, 29.2, 0.36313, 33, 7.31, 29.2, 0.63687, 2, 32, 54.47, 25.85, 0.1318, 33, 15.64, 25.85, 0.8682, 2, 32, 59.18, 26.22, 0.05785, 33, 20.34, 26.22, 0.94215, 2, 32, 64.2, 25.84, 0.01767, 33, 25.37, 25.84, 0.98233, 3, 32, 70.73, 29.3, 0.00079, 33, 31.89, 29.3, 0.67121, 34, 25.57, 15.24, 0.328, 1, 34, 25.07, 3.02, 1, 1, 34, 17.53, -3.17, 1, 1, 34, 4.18, -4.35, 1, 1, 34, -16.33, -3.5, 1, 1, 34, -30.99, 0.33, 1, 3, 32, 62.26, -28.93, 0.01929, 33, 23.43, -28.93, 0.70871, 34, -33.12, 10.99, 0.272, 2, 32, 57.47, -26.41, 0.07554, 33, 18.63, -26.41, 0.92446, 2, 32, 51.13, -27.4, 0.19776, 33, 12.29, -27.4, 0.80224, 2, 32, 46.59, -27.55, 0.31022, 33, 7.76, -27.55, 0.68978, 2, 32, 39.64, -27.8, 0.53016, 33, 0.8, -27.8, 0.46984, 2, 32, 33.83, -29.7, 0.66084, 33, -5, -29.7, 0.33916, 2, 32, 30.77, -25.93, 0.76773, 33, -8.06, -25.93, 0.23227, 2, 32, 19.31, -29.77, 0.97099, 33, -19.52, -29.77, 0.02901, 2, 32, 6.11, -30.17, 0.99984, 33, -32.72, -30.17, 0.00016, 1, 32, -8.25, -25.47, 1, 1, 32, -14.78, -15.31, 1, 1, 32, 5.69, 3.8, 1, 2, 32, 22.76, 5.23, 0.99683, 33, -16.08, 5.23, 0.00317, 2, 32, 40.66, 3.94, 0.2069, 33, 1.83, 3.94, 0.7931, 2, 32, 45.48, 3.3, 0.01741, 33, 6.65, 3.3, 0.98259, 2, 32, 48.99, 2.99, 0.00211, 33, 10.16, 2.99, 0.99789, 1, 33, 14.11, 3.2, 1, 1, 33, 20.43, 2.31, 1, 1, 33, 25.61, 1.88, 1, 2, 33, 35.12, 2.94, 0.34282, 34, 0.52, 6.42, 0.65718], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 26, 70, 70, 68], "width": 74, "height": 96}}, "jio2": {"jio2": {"type": "mesh", "uvs": [0.00205, 0.38502, 0.03348, 0.3021, 0.08777, 0.22524, 0.17348, 0.09985, 0.33062, 0.00075, 0.53634, 0, 0.83919, 0.04322, 1, 0.19086, 1, 0.30007, 0.93062, 0.39109, 0.93062, 0.49221, 0.87634, 0.56097, 0.87348, 0.63783, 0.85062, 0.72884, 0.83634, 0.79356, 0.79634, 0.82389, 0.82491, 0.95131, 0.59919, 1, 0.33634, 1, 0.09919, 0.94726, 0.01062, 0.89266, 0.02777, 0.76929, 0.04777, 0.6985, 0.03919, 0.63378, 0.02777, 0.56299, 0.00205, 0.48007, 0.04777, 0.45783, 0.47919, 0.14839, 0.38205, 0.35266, 0.33062, 0.44974, 0.32777, 0.56299, 0.32777, 0.63378, 0.33348, 0.7349, 0.33919, 0.81378, 0.34393, 0.9027], "triangles": [19, 33, 34, 17, 34, 33, 15, 16, 17, 17, 18, 34, 18, 19, 34, 20, 21, 19, 15, 17, 33, 19, 21, 33, 32, 21, 22, 21, 32, 33, 13, 14, 15, 15, 33, 32, 13, 15, 32, 22, 31, 32, 32, 31, 13, 13, 31, 12, 22, 23, 31, 31, 30, 12, 12, 30, 11, 31, 23, 30, 30, 23, 24, 24, 26, 30, 30, 29, 11, 30, 26, 29, 24, 25, 26, 29, 28, 11, 10, 11, 9, 9, 11, 28, 9, 28, 27, 8, 9, 27, 29, 26, 1, 27, 5, 6, 26, 0, 1, 29, 1, 28, 7, 8, 27, 1, 2, 28, 2, 3, 28, 28, 3, 27, 7, 27, 6, 3, 4, 27, 27, 4, 5], "vertices": [2, 30, 36.49, -27.49, 0.64884, 31, -13.25, -24.34, 0.35116, 2, 30, 28.87, -28.06, 0.78682, 31, -20.56, -22.11, 0.21318, 2, 30, 21.28, -27.1, 0.90145, 31, -27.27, -18.46, 0.09855, 2, 30, 8.96, -25.7, 0.9873, 31, -38.24, -12.68, 0.0127, 1, 30, -2.64, -19.29, 1, 1, 30, -7, -7.08, 1, 1, 30, -9.69, 12.2, 1, 2, 30, -0.64, 26.11, 0.99914, 31, -28.37, 39.09, 0.00086, 2, 30, 8.53, 29.33, 0.98469, 31, -18.66, 38.75, 0.01531, 2, 30, 17.62, 27.89, 0.91795, 31, -10.71, 34.11, 0.08205, 2, 30, 26.11, 30.87, 0.77392, 31, -1.72, 33.8, 0.22608, 2, 30, 33.02, 29.67, 0.5998, 31, 4.28, 30.18, 0.4002, 2, 30, 39.53, 31.77, 0.38559, 31, 11.11, 29.76, 0.61441, 2, 30, 47.65, 33.09, 0.19684, 31, 19.16, 28.05, 0.80316, 2, 30, 53.38, 34.15, 0.11269, 31, 24.89, 26.95, 0.88731, 1, 31, 27.5, 24.34, 1, 1, 36, 26.39, -0.31, 1, 1, 36, 12.21, -4.76, 1, 1, 36, -4.35, -4.89, 1, 1, 36, -19.33, -0.32, 1, 1, 36, -24.95, 4.5, 1, 1, 31, 20.99, -23.89, 1, 2, 30, 61.86, -15.53, 0.00556, 31, 14.73, -22.41, 0.99444, 2, 30, 56.6, -17.95, 0.04465, 31, 8.96, -22.76, 0.95535, 2, 30, 50.89, -20.71, 0.15869, 31, 2.64, -23.26, 0.84131, 2, 30, 44.47, -24.69, 0.32057, 31, -4.79, -24.63, 0.67943, 2, 30, 41.65, -22.62, 0.42359, 31, -6.67, -21.68, 0.57641, 1, 30, 6.66, -6.1, 1, 2, 30, 25.84, -5.85, 0.97358, 31, -15.31, -0.31, 0.02642, 2, 30, 35.06, -6.05, 0.78808, 31, -6.78, -3.85, 0.21192, 2, 30, 44.63, -2.88, 0.00844, 31, 3.28, -4.37, 0.99156, 1, 31, 9.58, -4.59, 1, 1, 31, 18.59, -4.53, 1, 1, 31, 25.62, -4.41, 1, 2, 31, 33.53, -4.39, 0.21852, 36, -3.94, 3.77, 0.78148], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 10, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 63, "height": 89}}, "boom/hyjy_skill_bzs_00000": {"boom/hyjy_skill_bzs_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [221.27, -87.11, -219.73, -87.11, -219.73, 276.89, 221.27, 276.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 441, "height": 364}, "boom/hyjy_skill_bzs_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [221.27, -87.11, -219.73, -87.11, -219.73, 276.89, 221.27, 276.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 441, "height": 364}, "boom/hyjy_skill_bzs_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [221.27, -87.11, -219.73, -87.11, -219.73, 276.89, 221.27, 276.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 441, "height": 364}, "boom/hyjy_skill_bzs_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [221.27, -87.11, -219.73, -87.11, -219.73, 276.89, 221.27, 276.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 441, "height": 364}, "boom/hyjy_skill_bzs_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [221.27, -87.11, -219.73, -87.11, -219.73, 276.89, 221.27, 276.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 441, "height": 364}, "boom/hyjy_skill_bzs_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [221.27, -87.11, -219.73, -87.11, -219.73, 276.89, 221.27, 276.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 441, "height": 364}, "boom/hyjy_skill_bzs_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [221.27, -87.11, -219.73, -87.11, -219.73, 276.89, 221.27, 276.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 441, "height": 364}}, "juqi/fashi_skill_xl_00000": {"juqi/fashi_skill_xl_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}, "juqi/fashi_skill_xl_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [131.95, -127.76, -128.05, -127.76, -128.05, 132.24, 131.95, 132.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 260, "height": 260}}, "A_stone_lizi": {"A_stone_lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [57.4, -31.78, -29.62, -31.78, -29.62, 27.12, 57.4, 27.12], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00034": {"run/xingchu_skill_hdwl/xingchu_skill_hdwl_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00038": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00040": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00042": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00044": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00046": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00048": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00050": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00052": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00054": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00056": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-150.94, 127, 155.06, 127, 155.06, -127, -150.94, -127], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 306, "height": 254}}, "wiqi": {"wiqi": {"type": "mesh", "uvs": [0.33324, 0.22238, 0.34056, 0.31978, 0.6563, 0.22026, 0.67012, 0.07101, 0.7541, 0.05953, 0.78019, 0.15427, 0.78384, 0.31601, 0.74788, 0.51829, 0.70547, 0.52405, 0.6578, 0.41702, 0.37899, 0.51198, 0.31733, 1, 0.12537, 1, 0, 0.89619, 0, 0.22819, 0.11332, 0, 0.21639, 0], "triangles": [6, 9, 2, 10, 1, 2, 9, 10, 2, 6, 2, 3, 4, 6, 3, 5, 6, 4, 6, 8, 9, 7, 8, 6, 12, 13, 14, 12, 15, 16, 1, 12, 16, 1, 16, 0, 11, 12, 1, 12, 14, 15, 10, 11, 1], "vertices": [32.54, -33.83, 30.42, -18.31, -134.79, -15.67, -144.51, -38.02, -188.19, -35, -200.08, -18.81, -199.2, 6.48, -177.11, 35.78, -155.05, 34.25, -132.2, 14.92, 13.81, 13.69, 54.1, 85.84, 153.51, 74.85, 216.65, 51.58, 205.21, -52, 142.61, -80.89, 89.24, -75], "hull": 17, "edges": [12, 14, 12, 10, 14, 16, 16, 18, 18, 20, 20, 22, 6, 4, 4, 2, 8, 10, 6, 8, 2, 0, 32, 0, 30, 32, 30, 28, 26, 28, 22, 24, 26, 24], "width": 521, "height": 156}}, "dg/dg_01": {"dg/dg_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "dg/dg_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "dg/dg_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "dg/dg_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "dg/dg_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "dg/dg_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "dg/dg_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "dg/dg_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}}, "eyelight": {"eyelight": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-21, -64.68, -15.67, 45.19, 51.26, 41.95, 45.92, -67.93], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 110, "height": 67}}, "A_fire_sword0001": {"A_fire_sword0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}}, "pddd": {"pddd": {"type": "mesh", "uvs": [0, 0.13075, 0.08732, 0.04795, 0.24764, 0, 0.47767, 0, 0.64032, 0.07079, 0.79599, 0.15931, 0.80296, 0.29351, 0.77276, 0.50767, 0.84943, 0.64758, 0.94935, 0.81034, 1, 1, 0.8262, 0.99023, 0.64961, 0.93027, 0.48464, 0.9217, 0.27088, 0.96168, 0.20582, 0.74181, 0.14773, 0.56192, 0.06641, 0.43343, 0.01761, 0.31064, 0.29876, 0.23069, 0.36614, 0.50767, 0.43585, 0.7618, 0.50555, 0.23926, 0.57758, 0.51338, 0.66588, 0.75323, 0.82155, 0.87602, 0.15702, 0.26781, 0.26855, 0.5248], "triangles": [4, 6, 22, 26, 2, 19, 18, 0, 26, 26, 1, 2, 0, 1, 26, 22, 3, 4, 19, 2, 3, 6, 4, 5, 19, 3, 22, 27, 26, 19, 24, 7, 8, 21, 23, 24, 25, 24, 8, 25, 8, 9, 12, 13, 24, 12, 24, 25, 11, 25, 9, 12, 25, 11, 11, 9, 10, 6, 23, 22, 23, 20, 22, 6, 7, 23, 24, 23, 7, 21, 20, 23, 20, 19, 22, 13, 21, 24, 27, 21, 15, 14, 21, 13, 14, 15, 21, 27, 19, 20, 15, 16, 27, 21, 27, 20, 17, 18, 26, 17, 26, 27, 16, 17, 27], "vertices": [1, 2, -40.24, 1.15, 1, 1, 2, -31.11, 7.72, 1, 1, 2, -14.64, 11.16, 1, 1, 2, 8.81, 10.38, 1, 1, 2, 25.2, 3.96, 1, 1, 2, 40.83, -3.91, 1, 4, 27, 25.94, 29.56, 0.3101, 28, 4.48, 29.77, 0.38227, 29, -17.6, 30.92, 0.01164, 2, 41.17, -15.07, 0.296, 3, 27, 40.26, 18.59, 0.07235, 28, 19.22, 19.37, 0.59656, 29, -3.53, 19.63, 0.33109, 3, 27, 54.17, 20.13, 0.0005, 28, 33.07, 21.47, 0.12442, 29, 10.42, 20.86, 0.87508, 2, 28, 49.64, 24.9, 0.00038, 29, 27.17, 23.25, 0.99962, 1, 29, 43.45, 20.17, 1, 1, 29, 34.17, 5.04, 1, 2, 26, 30.99, 21.1, 0.09451, 29, 21.1, -8.32, 0.90549, 2, 26, 24.87, 5.41, 0.80342, 29, 12.35, -22.71, 0.19658, 2, 25, 41.26, -18.85, 0.01634, 26, 20.96, -16.3, 0.98366, 3, 24, 41.36, -16.73, 0.07793, 25, 21.95, -16.73, 0.44188, 26, 1.54, -16.67, 0.4802, 3, 24, 25.36, -15.35, 0.68207, 25, 5.94, -15.44, 0.30297, 26, -14.5, -17.44, 0.01496, 2, 24, 12.11, -18, 0.99926, 25, -7.29, -18.17, 0.00074, 2, 24, 0.77, -17.89, 0.608, 2, -38.94, -13.83, 0.392, 4, 24, 7.66, 10.72, 0.28809, 25, -11.9, 10.53, 0.03171, 27, -2.54, -13.57, 0.1922, 2, -10.06, -8.15, 0.488, 3, 25, 11.75, 6.53, 0.81602, 27, 21.01, -18.15, 0.07082, 28, 1.47, -18.11, 0.11316, 3, 26, 10.71, 4.99, 0.87816, 28, 23.58, -20.71, 0.0476, 29, -1.68, -20.65, 0.07425, 3, 27, 7.87, 4.78, 0.65332, 28, -12.58, 4.28, 0.01868, 2, 11, -9.56, 0.328, 3, 27, 31.44, 0.73, 0.00202, 28, 11.13, 1.18, 0.99539, 29, -12.74, 1.97, 0.00259, 2, 28, 32.98, 0.8, 0.00021, 29, 9.04, 0.23, 0.99979, 1, 29, 25.64, 9.21, 1, 2, 24, 3.95, -3.58, 0.472, 2, -24.61, -10.75, 0.528, 3, 24, 28.11, -2.95, 0.14094, 25, 8.63, -3.03, 0.85768, 26, -13.43, -4.79, 0.00137], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 4, 38, 38, 40, 40, 42, 6, 44, 44, 46, 46, 48, 48, 50, 2, 52, 52, 54], "width": 102, "height": 83}}, "A_qiliu_00004": {"A_qiliu_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}, "A_qiliu_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -64, -128, -64, -128, 64, 128, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 128}}, "light111": {"light111": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [38.98, 84.75, 217.14, 67.47, 202.47, -83.82, 24.31, -66.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 179, "height": 152}}, "xiadai": {"xiadai": {"type": "mesh", "uvs": [0.67424, 0.16835, 0.67961, 0.09113, 0.7181, 0.02478, 0.78692, 0, 0.88769, 0.0111, 0.95298, 0.07805, 1, 0.17944, 1, 0.32484, 0.97285, 0.41475, 0.91608, 0.4664, 0.8153, 0.4817, 0.73582, 0.55631, 0.65918, 0.63475, 0.57543, 0.7151, 0.49169, 0.7897, 0.41363, 0.86049, 0.31853, 0.9504, 0.24047, 0.95614, 0.2064, 0.94083, 0.14679, 0.94657, 0.06731, 0.94275, 0.03466, 0.87005, 0, 0.75718, 0.02189, 0.7304, 0.07724, 0.72275, 0.09792, 0.63628, 0.14451, 0.57112, 0.23241, 0.52373, 0.34317, 0.53083, 0.42141, 0.46804, 0.497, 0.38629, 0.56732, 0.28914, 0.57348, 0.1991, 0.89313, 0.17355, 0.8018, 0.2589, 0.6983, 0.3426, 0.6094, 0.46078, 0.53999, 0.52807, 0.4511, 0.61834, 0.40239, 0.66101, 0.2319, 0.74964, 0.10282, 0.85141, 0.13448, 0.83335], "triangles": [37, 36, 12, 12, 36, 11, 36, 35, 11, 11, 35, 10, 37, 30, 36, 35, 34, 10, 10, 34, 9, 9, 34, 8, 30, 31, 36, 36, 31, 35, 8, 34, 7, 7, 34, 33, 35, 31, 0, 31, 32, 0, 35, 0, 34, 33, 6, 7, 0, 1, 34, 1, 2, 34, 2, 3, 34, 33, 3, 4, 33, 34, 3, 33, 5, 6, 33, 4, 5, 39, 38, 14, 14, 38, 13, 40, 28, 39, 40, 27, 28, 40, 26, 27, 38, 37, 13, 13, 37, 12, 39, 28, 38, 28, 29, 38, 38, 29, 37, 29, 30, 37, 16, 17, 40, 15, 16, 40, 40, 17, 18, 18, 42, 40, 40, 39, 15, 15, 39, 14, 42, 24, 40, 24, 25, 40, 25, 26, 40, 20, 41, 19, 41, 42, 19, 19, 42, 18, 20, 21, 41, 22, 23, 21, 21, 24, 41, 21, 23, 24, 41, 24, 42], "vertices": [3, 38, 76.16, 58.94, 1e-05, 4, 31.72, -63.71, 0.10793, 39, -21.46, 3.95, 0.89206, 3, 38, 77.06, 66.02, 1e-05, 4, 38.44, -66.11, 0.04272, 39, -20.56, 11.03, 0.95728, 3, 38, 82.03, 71.97, 0, 4, 43.18, -72.24, 0.01843, 39, -15.59, 16.97, 0.98156, 3, 38, 90.63, 73.96, 0, 4, 43.28, -81.08, 0.00428, 39, -6.98, 18.96, 0.99572, 1, 39, 5.47, 17.53, 1, 1, 39, 13.36, 11.11, 1, 1, 39, 18.88, 1.59, 1, 3, 38, 116.05, 43.22, 0, 4, 7.8, -99.31, 0.00227, 39, 18.44, -11.78, 0.99773, 3, 38, 112.41, 35.06, 0, 4, 0.62, -94, 0.01256, 39, 14.8, -19.94, 0.98744, 3, 38, 105.22, 30.54, 0, 4, -2.25, -86.01, 0.03417, 39, 7.6, -24.45, 0.96583, 4, 38, 92.68, 29.55, 2e-05, 3, 29.36, -73.22, 0.00074, 4, -0.53, -73.55, 0.13417, 39, -4.93, -25.44, 0.86507, 4, 38, 82.61, 23.02, 4e-05, 3, 26.3, -61.6, 0.01232, 4, -4.75, -62.31, 0.30073, 39, -15.01, -31.98, 0.68691, 4, 38, 72.87, 16.12, 5e-05, 3, 22.79, -50.2, 0.05496, 4, -9.4, -51.32, 0.43986, 39, -24.75, -38.88, 0.50512, 4, 38, 62.24, 9.08, 6e-05, 3, 19.41, -37.91, 0.171, 4, -14, -39.43, 0.51145, 39, -35.37, -45.92, 0.31749, 4, 38, 51.64, 2.56, 5e-05, 3, 16.53, -25.8, 0.41044, 4, -18.09, -27.67, 0.43279, 39, -45.98, -52.44, 0.15673, 4, 38, 41.75, -3.63, 3e-05, 3, 13.74, -14.47, 0.75248, 4, -22.01, -16.68, 0.19869, 39, -55.87, -58.62, 0.04881, 3, 3, 10.03, -0.55, 0.99975, 4, -27.12, -3.22, 0.0002, 39, -67.93, -66.5, 5e-05, 4, 38, 20, -11.71, 0.12479, 3, 12.86, 8.72, 0.87521, 4, -25.24, 6.3, 1e-05, 39, -77.62, -66.71, 0, 4, 38, 15.82, -10.16, 0.28241, 3, 15.63, 12.2, 0.71759, 4, -22.83, 10.04, 0, 39, -81.79, -65.16, 0, 4, 38, 8.42, -10.45, 0.64311, 3, 17.67, 19.33, 0.35689, 4, -21.52, 17.34, 0, 39, -89.2, -65.44, 0, 3, 38, -1.42, -9.77, 0.95557, 3, 21.39, 28.46, 0.04443, 39, -99.04, -64.76, 0, 1, 38, -5.25, -2.95, 1, 4, 38, -9.2, 7.57, 0.99528, 3, 40.29, 30.43, 0.00061, 4, -0.14, 30.67, 0.00411, 39, -106.81, -47.43, 0, 4, 38, -6.4, 9.94, 0.97523, 3, 41.67, 27.04, 0.01443, 4, 1.57, 27.44, 0.01034, 39, -104.02, -45.05, 0, 4, 38, 0.48, 10.42, 0.71602, 3, 39.98, 20.35, 0.18711, 4, 0.56, 20.61, 0.09686, 39, -97.14, -44.58, 0, 4, 38, 3.31, 18.29, 0.30622, 3, 46.57, 15.21, 0.29665, 4, 7.64, 16.16, 0.39712, 39, -94.31, -36.71, 1e-05, 4, 38, 9.28, 24.09, 0.10074, 3, 50.21, 7.72, 0.15075, 4, 12.02, 9.08, 0.7485, 39, -88.34, -30.91, 1e-05, 4, 38, 20.32, 28.08, 0.00019, 3, 50.56, -4.01, 0.00019, 4, 13.56, -2.55, 0.99728, 39, -77.3, -26.91, 0.00234, 4, 38, 34.02, 26.97, 8e-05, 3, 45.23, -16.68, 0.04945, 4, 9.53, -15.7, 0.85128, 39, -63.59, -28.02, 0.09918, 4, 38, 43.91, 32.43, 7e-05, 3, 47.32, -27.78, 0.04814, 4, 12.74, -26.53, 0.67596, 39, -53.71, -22.57, 0.27583, 4, 38, 53.53, 39.63, 6e-05, 3, 51.16, -39.17, 0.01489, 4, 17.71, -37.47, 0.50334, 39, -44.09, -15.36, 0.48171, 4, 38, 62.54, 48.28, 4e-05, 3, 56.56, -50.43, 0.00052, 4, 24.22, -48.12, 0.30269, 39, -35.08, -6.72, 0.69675, 3, 38, 63.57, 56.53, 3e-05, 4, 32.06, -50.91, 0.22945, 39, -34.04, 1.53, 0.77052, 1, 39, 5.65, 2.57, 1, 3, 38, 91.69, 50.09, 1e-05, 4, 19.74, -76.99, 0.04242, 39, -5.93, -4.9, 0.95758, 4, 38, 78.61, 42.82, 3e-05, 3, 46.36, -63.99, 0.00047, 4, 15.45, -62.65, 0.2005, 39, -19.01, -12.17, 0.79901, 4, 38, 67.23, 32.32, 5e-05, 3, 39.94, -49.9, 0.01736, 4, 7.63, -49.28, 0.40342, 39, -30.39, -22.67, 0.57917, 4, 38, 58.42, 26.42, 6e-05, 3, 37.08, -39.69, 0.05708, 4, 3.76, -39.42, 0.52972, 39, -39.19, -28.58, 0.41314, 4, 38, 47.13, 18.48, 6e-05, 3, 33.07, -26.49, 0.18177, 4, -1.57, -26.68, 0.60896, 39, -50.48, -36.51, 0.20921, 4, 38, 40.96, 14.76, 5e-05, 3, 31.46, -19.46, 0.29775, 4, -3.88, -19.86, 0.58119, 39, -56.65, -40.24, 0.121, 2, 38, 19.56, 7.31, 0.05066, 3, 31.06, 3.19, 0.94934, 4, 38, 3.26, -1.52, 0.87841, 3, 27.77, 21.44, 0.12141, 4, -11.69, 20.46, 0.00018, 39, -94.36, -56.51, 0, 4, 38, 7.24, 0.01, 0.66786, 3, 27.98, 17.18, 0.3288, 4, -11.05, 16.24, 0.00334, 39, -90.38, -54.98, 0], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 10, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 42, 82, 82, 84], "width": 124, "height": 92}}, "qiliu/luodiyan_00013": {"qiliu/luodiyan_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}, "qiliu/luodiyan_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}, "qiliu/luodiyan_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}, "qiliu/luodiyan_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}, "qiliu/luodiyan_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}, "qiliu/luodiyan_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}, "qiliu/luodiyan_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}, "qiliu/luodiyan_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}, "qiliu/luodiyan_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43, -22, -43, -22, -43, 23, 43, 23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 45}}, "A_fire_sword1": {"A_fire_sword0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}, "A_fire_sword0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -175, -175, -175, -175, 175, 175, 175], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 350}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"A_fire_sword0001": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff11"}], "attachment": [{"time": 0.2333, "name": "A_fire_sword0001"}, {"time": 0.2667, "name": "A_fire_sword0002"}, {"time": 0.3, "name": "A_fire_sword0003"}, {"time": 0.3333, "name": "A_fire_sword0004"}, {"time": 0.3667, "name": "A_fire_sword0005"}, {"time": 0.4, "name": "A_fire_sword0006"}, {"time": 0.4333, "name": "A_fire_sword0007"}, {"time": 0.4667, "name": "A_fire_sword0008"}]}, "A_qiliu_00004": {"attachment": [{"time": 0.2667, "name": "A_qiliu_00004"}, {"time": 0.3, "name": "A_qiliu_00005"}, {"time": 0.3333, "name": "A_qiliu_00006"}, {"time": 0.3667, "name": "A_qiliu_00007"}, {"time": 0.4, "name": "A_qiliu_00008"}, {"time": 0.4333, "name": "A_qiliu_00009"}, {"time": 0.4667, "name": "A_qiliu_00010"}, {"time": 0.5, "name": "A_qiliu_00011"}, {"time": 0.5333, "name": "A_qiliu_00012"}]}}, "bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.56, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": 13.44, "curve": "stepped"}, {"time": 0.4, "angle": 13.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 3.47, "y": -23.41, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "x": -24.68, "y": -20.07, "curve": "stepped"}, {"time": 0.4, "x": -24.68, "y": -20.07, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -32.12, "curve": "stepped"}, {"time": 0.4, "angle": -32.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone3": {"rotate": [{"angle": -0.15, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 35.96, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3667, "angle": -0.99, "curve": "stepped"}, {"time": 0.4667, "angle": -0.99, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.7, "angle": -0.15}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 64.69, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -88.21, "curve": "stepped"}, {"time": 0.4, "angle": -88.21, "curve": 0.25, "c3": 0.75}, {"time": 0.7}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 10.38, "y": 31.06, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "x": -16.26, "y": 45.42, "curve": "stepped"}, {"time": 0.4, "x": -16.26, "y": 45.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -44.69, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -23.16, "curve": "stepped"}, {"time": 0.4, "angle": -23.16, "curve": 0.25, "c3": 0.75}, {"time": 0.7}], "scale": [{"time": 0.2333, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "x": 1.192, "y": 1.078, "curve": "stepped"}, {"time": 0.3333, "x": 1.192, "y": 1.078, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.4}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -115.67, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -76.94, "curve": "stepped"}, {"time": 0.4, "angle": -76.94, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 98.07, "curve": "stepped"}, {"time": 0.4, "angle": 98.07, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -3.62, "curve": "stepped"}, {"time": 0.4, "angle": -3.62, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone4": {"rotate": [{"angle": 0.05, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 20.56, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3667, "angle": 0.34, "curve": "stepped"}, {"time": 0.4667, "angle": 0.34, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.7, "angle": 0.05}]}, "bone8": {"rotate": [{"time": 0.2333, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -12.34, "curve": "stepped"}, {"time": 0.4, "angle": -12.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone9": {"rotate": [{"time": 0.2333, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": 46.09, "curve": "stepped"}, {"time": 0.4, "angle": 46.09, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone10": {"rotate": [{"time": 0.2333, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": 9.47, "curve": "stepped"}, {"time": 0.4, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone11": {"rotate": [{"time": 0.2333, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -33.36, "curve": "stepped"}, {"time": 0.4, "angle": -33.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone12": {"rotate": [{"time": 0.2333, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -40.94, "curve": "stepped"}, {"time": 0.4, "angle": -40.94, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -21.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone25": {"rotate": [{"angle": 5.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -21.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 9.44, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7, "angle": 5.97}]}, "bone26": {"rotate": [{"angle": 6.43, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -21.83, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.7, "angle": 6.43}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -21.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone28": {"rotate": [{"angle": 5.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -21.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 9.44, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7, "angle": 5.97}]}, "bone29": {"rotate": [{"angle": 6.43, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": 9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -21.83, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.7, "angle": 6.43}]}, "bone22": {"rotate": [{"angle": -0.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -63.39, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -32.48, "curve": "stepped"}, {"time": 0.4, "angle": -32.48, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -0.91}]}, "bone30": {"rotate": [{"angle": 2.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 78.8, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": 79.4, "curve": "stepped"}, {"time": 0.4, "angle": 79.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -52.46, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -0.55, "curve": "stepped"}, {"time": 0.4, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.8}]}, "bone32": {"rotate": [{"angle": 7.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 72.77, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": 7.26}]}, "bone37": {"translate": [{"x": 0.68, "y": 0.92, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 3.53, "y": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 3.59, "y": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 4.42, "y": 5.96, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.7, "x": 0.68, "y": 0.92}]}, "bone38": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 3.53, "y": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 3.59, "y": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 4.42, "y": 5.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone39": {"translate": [{"x": 0.68, "y": 0.92, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 3.53, "y": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 3.59, "y": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 4.42, "y": 5.96, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.7, "x": 0.68, "y": 0.92}]}, "bone40": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 3.53, "y": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 3.59, "y": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 4.42, "y": 5.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone41": {"translate": [{"x": 0.68, "y": 0.92, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 3.53, "y": 3.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 3.59, "y": -5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 4.42, "y": 5.96, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.7, "x": 0.68, "y": 0.92}]}, "bone42": {"rotate": [{"time": 0.2333, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -13.42, "curve": "stepped"}, {"time": 0.4, "angle": -13.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone43": {"rotate": [{"time": 0.2333, "curve": 0.487, "c2": 0.02, "c3": 0.424, "c4": 0.99}, {"time": 0.3, "angle": -13.42, "curve": "stepped"}, {"time": 0.4, "angle": -13.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "ro": {"rotate": [{"time": 0.2333}, {"time": 0.2667, "angle": 43.92}, {"time": 0.4, "angle": 100.65}, {"time": 0.5333, "angle": 106.55}]}, "daoguang": {"translate": [{"time": 0.2333, "y": 1266}], "scale": [{"time": 0.2333, "x": 1.474, "y": 1.474}]}, "qixuan1": {"rotate": [{"time": 0.2667, "angle": 14.18}], "translate": [{"time": 0.2667, "x": 312, "y": 1068}], "scale": [{"time": 0.2667, "x": 1.059, "y": 0.764}]}}, "events": [{"time": 0.3, "name": "atk"}]}, "boss_attack3": {"slots": {"wiqi4": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.4667, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ff9700ff", "curve": "stepped"}, {"time": 0.5667, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ff970000"}], "attachment": [{"time": 0.5, "name": "wiqi"}]}, "boom/hyjy_skill_bzs_00000": {"attachment": [{"time": 0.6, "name": "boom/hyjy_skill_bzs_00000"}, {"time": 0.6333, "name": "boom/hyjy_skill_bzs_00002"}, {"time": 0.6667, "name": "boom/hyjy_skill_bzs_00004"}, {"time": 0.7, "name": "boom/hyjy_skill_bzs_00006"}, {"time": 0.7333, "name": "boom/hyjy_skill_bzs_00008"}, {"time": 0.7667, "name": "boom/hyjy_skill_bzs_00010"}, {"time": 0.8, "name": "boom/hyjy_skill_bzs_00012"}, {"time": 0.8333, "name": null}]}, "A_stone_lizi": {"color": [{"time": 0.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{"time": 0.5667, "name": "A_stone_lizi"}]}, "wiqi2": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.4667, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ff9700ff", "curve": "stepped"}, {"time": 0.5667, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ff970000"}], "attachment": [{"time": 0.5, "name": "wiqi"}]}, "juqi/fashi_skill_xl_00000": {"attachment": [{"time": 0.3, "name": "juqi/fashi_skill_xl_00000"}, {"time": 0.3333, "name": "juqi/fashi_skill_xl_00002"}, {"time": 0.3667, "name": "juqi/fashi_skill_xl_00004"}, {"time": 0.4, "name": "juqi/fashi_skill_xl_00006"}, {"time": 0.4333, "name": "juqi/fashi_skill_xl_00008"}, {"time": 0.4667, "name": "juqi/fashi_skill_xl_00010"}, {"time": 0.5, "name": "juqi/fashi_skill_xl_00012"}, {"time": 0.5333, "name": "juqi/fashi_skill_xl_00014"}, {"time": 0.5667, "name": "juqi/fashi_skill_xl_00016"}, {"time": 0.6, "name": "juqi/fashi_skill_xl_00018"}, {"time": 0.6333, "name": "juqi/fashi_skill_xl_00020"}, {"time": 0.6667, "name": null}]}, "dg/dg_01": {"attachment": [{"time": 0.5667, "name": "dg/dg_01"}, {"time": 0.6, "name": "dg/dg_02"}, {"time": 0.6333, "name": "dg/dg_03"}, {"time": 0.6667, "name": "dg/dg_04"}, {"time": 0.7, "name": "dg/dg_05"}, {"time": 0.7333, "name": "dg/dg_06"}, {"time": 0.7667, "name": "dg/dg_07"}, {"time": 0.8, "name": "dg/dg_08"}, {"time": 0.8333, "name": null}]}, "wiqi3": {"color": [{"color": "ff970000", "curve": "stepped"}, {"time": 0.4667, "color": "ff970000", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ff9700ff", "curve": "stepped"}, {"time": 0.5667, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ff970000"}], "attachment": [{"time": 0.5, "name": "wiqi"}]}}, "bones": {"bone": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}], "translate": [{"x": 0.36, "y": -0.07, "curve": 0.34, "c2": 0.38, "c3": 0.674, "c4": 0.71}, {"time": 0.1, "x": -3.14, "y": -33.45, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.68}, {"time": 0.2333, "x": -3.44, "y": -24.39, "curve": 0.34, "c2": 0.38, "c3": 0.674, "c4": 0.71}, {"time": 0.3333, "x": 13.84, "y": -13.88, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.4667, "x": -0.48, "y": 91.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5667, "x": -17.89, "y": -6.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7667, "x": 0.36, "y": -0.07}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 15.09, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.2, "y": 2.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5667, "x": -5.46, "y": -15.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7667}]}, "bone3": {"rotate": [{"angle": -0.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 6.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -0.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -12.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5667, "angle": 7.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7667, "angle": -0.34}], "translate": [{"x": 0.88, "y": -0.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "x": -13.05, "y": 2.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "x": 0.88, "y": -0.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5667, "x": -18.87, "y": 15.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7667, "x": 0.88, "y": -0.29}]}, "bone4": {"rotate": [{"angle": -1.19, "curve": "stepped"}, {"time": 0.2333, "angle": -1.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333, "angle": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.19}], "translate": [{"x": 3.14, "y": -0.64, "curve": "stepped"}, {"time": 0.2333, "x": 3.14, "y": -0.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 3.14, "y": -0.64}]}, "bone5": {"translate": [{"x": 3.26, "y": -4.43, "curve": "stepped"}, {"time": 0.2333, "x": 3.26, "y": -4.43, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 3.26, "y": -4.43}]}, "bone6": {"rotate": [{"angle": 1.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "angle": -14.59, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": 1.54, "curve": 0.359, "c2": 0.43, "c3": 0.7, "c4": 0.8}, {"time": 0.6, "angle": 37.9, "curve": 0.363, "c2": 0.64, "c3": 0.699}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.54}]}, "bone7": {"rotate": [{"angle": 3.89, "curve": "stepped"}, {"time": 0.2333, "angle": 3.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -41.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -21.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 3.89}]}, "bone8": {"rotate": [{"angle": 5.06, "curve": "stepped"}, {"time": 0.2333, "angle": 5.06, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 5.06}]}, "bone9": {"rotate": [{"angle": 5.38, "curve": "stepped"}, {"time": 0.2333, "angle": 5.38, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 5.38}]}, "bone10": {"rotate": [{"angle": 5.06, "curve": "stepped"}, {"time": 0.2333, "angle": 5.06, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 5.06}]}, "bone11": {"rotate": [{"angle": -7.19, "curve": "stepped"}, {"time": 0.2333, "angle": -7.19, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -7.19}]}, "bone12": {"rotate": [{"angle": -6.76, "curve": "stepped"}, {"time": 0.2333, "angle": -6.76, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -6.76}]}, "bone13": {"translate": [{"x": 4.62, "y": 2.59, "curve": "stepped"}, {"time": 0.2333, "x": 4.62, "y": 2.59, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 4.62, "y": 2.59}]}, "bone14": {"rotate": [{"angle": -0.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 36.51, "curve": "stepped"}, {"time": 0.2333, "angle": 36.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4667, "angle": -138.5, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.5333, "angle": 37.9, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.5667, "angle": 54.79, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.7667, "angle": -0.92}]}, "bone15": {"rotate": [{"angle": -2.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": 35.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": -2.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -7.6, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.5667, "angle": -13.03, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.7667, "angle": -2.31}]}, "bone16": {"rotate": [{"angle": -3, "curve": "stepped"}, {"time": 0.2333, "angle": -3, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.4667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5667, "angle": 11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7667, "angle": -3}]}, "bone18": {"translate": [{"x": 0.51, "y": -0.59, "curve": "stepped"}, {"time": 0.2333, "x": 0.51, "y": -0.59, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4667, "x": 1.64, "y": -1.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -4.19, "y": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.51, "y": -0.59}]}, "bone19": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.64, "y": -1.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -4.19, "y": -2.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7667, "x": 1.01, "y": -1.15}]}, "bone20": {"translate": [{"x": 1.55, "y": -1.76, "curve": "stepped"}, {"time": 0.2333, "x": 1.55, "y": -1.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -4.19, "y": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.64, "y": -1.47, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7667, "x": 1.55, "y": -1.76}]}, "bone21": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.64, "y": -1.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -4.19, "y": -2.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7667, "x": 2.69, "y": -3.07}]}, "bone23": {"translate": [{"x": 3.06, "y": -3.49, "curve": "stepped"}, {"time": 0.2333, "x": 3.06, "y": -3.49, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.4667, "x": 1.64, "y": -1.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -4.19, "y": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 3.06, "y": -3.49}]}, "bone24": {"rotate": [{"angle": -8.48, "curve": "stepped"}, {"time": 0.2333, "angle": -8.48, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.4667, "angle": -15.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 9.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -8.48}]}, "bone25": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -15.43, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 9.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "angle": 3.26}]}, "bone26": {"rotate": [{"angle": 10.36, "curve": "stepped"}, {"time": 0.2333, "angle": 10.36, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.3667, "angle": 9.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -15.43, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7667, "angle": 10.36}]}, "bone27": {"rotate": [{"angle": -7.66, "curve": "stepped"}, {"time": 0.2333, "angle": -7.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -15.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 9.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -7.66}]}, "bone28": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -15.43, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 9.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "angle": 8.36}]}, "bone29": {"rotate": [{"angle": 18.12, "curve": "stepped"}, {"time": 0.2333, "angle": 18.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 9.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -15.43, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7667, "angle": 18.12}]}, "bone22": {"rotate": [{"angle": -0.91}]}, "bone30": {"rotate": [{"angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8}]}, "bone32": {"rotate": [{"angle": 7.26}]}, "bone33": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 0.89, "y": 40.24, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.5333, "x": -3.72, "y": 5.42, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.7667}]}, "bone34": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -6.82, "y": 102.68, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -24.4, "y": 0.27, "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 0.7667}]}, "bone35": {"translate": [{"time": 0.4667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5667, "x": -16.86, "y": 5.39, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7667}]}, "bone37": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 0.91, "y": 6.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -3.22, "y": -5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone38": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.91, "y": 6.88, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -3.22, "y": -5.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "x": -0.04, "y": 1.12}]}, "bone39": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.91, "y": 6.88, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -3.22, "y": -5.31, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "x": -0.12, "y": 3.08}]}, "bone40": {"translate": [{"x": -0.19, "y": 4.84, "curve": "stepped"}, {"time": 0.2333, "x": -0.19, "y": 4.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "x": -3.22, "y": -5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.91, "y": 6.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7667, "x": -0.19, "y": 4.84}]}, "bone41": {"translate": [{"x": -0.26, "y": 6.76, "curve": "stepped"}, {"time": 0.2333, "x": -0.26, "y": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -3.22, "y": -5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.91, "y": 6.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7667, "x": -0.26, "y": 6.76}]}, "bone42": {"rotate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.839, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "x": 1.411, "y": 1.586, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667}]}, "bone43": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 170.2, "curve": 0.329, "c2": 0.32, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -170.71, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.68}, {"time": 0.5333, "angle": -81.94, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.5667, "angle": 5.16, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6, "angle": 9.68}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 137.18, "y": 229.8, "curve": 0.336, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.5, "x": 196.24, "y": 277.66, "curve": 0.34, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.5333, "x": -64.45, "y": 243.6, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.5667, "x": -148.73, "y": 38.14}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.29, "y": 1.323, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6, "x": 1.865, "y": 1.913}]}, "bone45": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -429.44, "y": -29.24, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -458.22, "y": 112.41}], "scale": [{"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 3.853, "y": 3.853}]}}, "deform": {"default": {"zuoshou": {"zuoshou": [{"offset": 138, "vertices": [-1.77657, -2.00317, -2.36552, -1.25456, -2.55936, 0.78723, -1.15784, -1.12914, -1.47996, -0.65237, 0.59825, -1.50281, -1.50749, 0.58621]}]}, "wiqi4": {"wiqi": [{"vertices": [1.36567, 108.84588, 262.9449, -98.65875, 200.8099, -176.98103, -60.76668, 30.5235]}]}, "wiqi2": {"wiqi": [{"vertices": [1.36567, 108.84588, 262.9449, -98.65875, 200.8099, -176.98103, -60.76668, 30.5235]}]}, "xiadai": {"xiadai": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "offset": 80, "vertices": [1.31478, 0.51507, 0.07865, -1.40977, 0.02917, -1.41166, 1.37249, 0.33142, -0.30823, -0.23488, -0.12715, 0.36645, -0.11436, 0.3707, -0.33763, -0.19104, -7.17946, -4.74979, -7.34155, -4.49534, 5.96555, -6.20589, 4.19033, -4.85306, -5.91937, -2.46486, -6.0022, -2.25592, 3.49097, -5.37822, 0.18842, -1.68552, -1.66035, 0.34762, -1.64725, 0.40558, -0.04284, -1.69571, 2.58759, 3.80324, 2.80492, -3.64577, 2.67545, -3.74178, 3.08062, 3.41595, 9.20581, 5.68046, 2.5213, -10.51894, 9.8923, 4.37546, 3.41119, 2.72416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10989, 0.81576, 0.74043, -0.35895, 0.72734, -0.38464, 0.21959, 0.79303, 4.53504, 2.84955, 1.2905, -5.19779, 1.1075, -5.23978, 4.88005, 2.20602, 2.65067, 3.24194, 2.25148, -3.52987, 2.1262, -3.60655, 3.06615, 2.85085, 1.63379, 1.85633, 1.25293, -2.13142, 1.17735, -2.17396, 1.87061, 1.61658, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.03514, 1.52152, 1.12195, -1.45832, 1.07013, -1.49669, 1.23219, 1.36646, 1.03514, 1.52152, 1.12195, -1.45832, 1.07013, -1.49669, 1.23219, 1.36646, -1.2196, -12.15018, -11.16275, 4.95381, 1.58916, 2.55383, 1.92977, -2.30708, 1.84771, -2.37325, 1.92154, 2.31392], "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7667}]}, "wiqi3": {"wiqi": [{"vertices": [1.36567, 108.84588, 262.9449, -98.65875, 200.8099, -176.98103, -60.76668, 30.5235]}]}}}, "events": [{"time": 0.6, "name": "atk"}]}, "boss_idle": {"slots": {"light111": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff7b", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff"}]}, "eyelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff"}]}, "facelight": {"color": [{"color": "ffffffff", "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 1, "color": "ffffff00", "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2, "color": "ffffff95"}]}}, "bones": {"bone": {"translate": [{"x": 0.36, "y": -0.07, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 5.23, "y": -1.05, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": 0.36, "y": -0.07}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.66, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone3": {"rotate": [{"angle": -0.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.34}], "translate": [{"x": 0.88, "y": -0.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.09, "y": -1.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.88, "y": -0.29}]}, "bone4": {"rotate": [{"angle": -1.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.19}], "translate": [{"x": 3.14, "y": -0.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.39, "y": -0.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.14, "y": -0.64}]}, "bone5": {"translate": [{"x": 3.26, "y": -4.43, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 4.09, "y": -5.56, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": 3.26, "y": -4.43}]}, "bone6": {"rotate": [{"angle": 1.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.54}]}, "bone7": {"rotate": [{"angle": 3.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.89}]}, "bone8": {"rotate": [{"angle": 5.06, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 5.44, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 5.06}]}, "bone9": {"rotate": [{"angle": 5.38, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "angle": 5.44, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "angle": 5.38}]}, "bone10": {"rotate": [{"angle": 5.06, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 5.44, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 5.06}]}, "bone11": {"rotate": [{"angle": -7.19, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "angle": -7.19}]}, "bone12": {"rotate": [{"angle": -6.76, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": -6.76}]}, "bone13": {"translate": [{"x": 4.62, "y": 2.59, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 5.79, "y": 3.25, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": 4.62, "y": 2.59}]}, "bone14": {"rotate": [{"angle": -0.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.23, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.92}]}, "bone15": {"rotate": [{"angle": -2.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.31}]}, "bone16": {"rotate": [{"angle": -3, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -3.23, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -3}]}, "bone18": {"translate": [{"x": 0.51, "y": -0.59, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 3.09, "y": -3.53, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "x": 0.51, "y": -0.59}]}, "bone19": {"translate": [{"x": 1.01, "y": -1.15, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": 3.09, "y": -3.53, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "x": 1.01, "y": -1.15}]}, "bone20": {"translate": [{"x": 1.55, "y": -1.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.09, "y": -3.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.55, "y": -1.76}]}, "bone21": {"translate": [{"x": 2.69, "y": -3.07, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 3.09, "y": -3.53, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "x": 2.69, "y": -3.07}]}, "bone23": {"translate": [{"x": 3.06, "y": -3.49, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 3.09, "y": -3.53, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "x": 3.06, "y": -3.49}]}, "bone24": {"rotate": [{"angle": -8.48, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -9.47, "curve": 0.32, "c2": 0.29, "c3": 0.674, "c4": 0.69}, {"time": 1.1, "angle": 15.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.7667, "angle": -2.41, "curve": 0.362, "c2": 0.45, "c3": 0.704, "c4": 0.82}, {"time": 2, "angle": -8.48}]}, "bone25": {"rotate": [{"angle": 3.26, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": -7.02, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 1.4333, "angle": 17.86, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.7667, "angle": 10.8, "curve": 0.324, "c2": 0.3, "c3": 0.666, "c4": 0.66}, {"time": 2, "angle": 3.26}]}, "bone26": {"rotate": [{"angle": 10.36, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "angle": -10.38, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 14.5, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 10.36}]}, "bone27": {"rotate": [{"angle": -7.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -14.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3333, "angle": 10.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -7.66}]}, "bone28": {"rotate": [{"angle": 8.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -9.46, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.6667, "angle": 15.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 8.36}]}, "bone29": {"rotate": [{"angle": 18.12, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.77, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 18.12}]}, "bone22": {"rotate": [{"angle": -0.91}]}, "bone30": {"rotate": [{"angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8}]}, "bone32": {"rotate": [{"angle": 7.26}]}, "bone37": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.26, "y": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone38": {"translate": [{"x": -0.04, "y": 1.12, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -0.26, "y": 6.76, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "x": -0.04, "y": 1.12}]}, "bone39": {"translate": [{"x": -0.12, "y": 3.08, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -0.26, "y": 6.76, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": -0.12, "y": 3.08}]}, "bone40": {"translate": [{"x": -0.19, "y": 4.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.26, "y": 6.76, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.19, "y": 4.84}]}, "bone41": {"translate": [{"x": -0.26, "y": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -0.26, "y": 6.76}]}}}, "die": {"slots": {"wiqi4": {"color": [{"time": 0.2667, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ff553600"}]}, "eyelight": {"color": [{"time": 0.2667, "color": "ff5637ff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ff553600"}]}, "facelight": {"color": [{"time": 0.2667, "color": "ff3e1fff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ff553600"}]}, "wiqi2": {"color": [{"time": 0.2667, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ff553600"}]}, "wiqi3": {"color": [{"time": 0.2667, "color": "ff9700ff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ff553600"}]}}, "bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -86.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -94.68}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": 135.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": -4.59}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -21.33}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 14.89}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.94}]}, "bone6": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 9.01, "y": 53.28}]}, "bone14": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.69}], "translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -10.56, "y": -62.39}]}, "bone22": {"rotate": [{"angle": -0.91}]}, "bone30": {"rotate": [{"angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8}]}, "bone32": {"rotate": [{"angle": 7.26}]}, "bone33": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -78.35}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -99.77, "y": 57.57, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": -168.26, "y": 170.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "x": -163.29, "y": 135.88, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4667, "x": -168.97, "y": 47.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -168.26, "y": 39.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -168.26, "y": 27.89}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -78.35}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": -44.35, "y": 104.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": -63.1, "y": 235.26, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "x": -58.12, "y": 206.93, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4667, "x": -58.12, "y": 113.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -63.1, "y": 103.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -63.1, "y": 92.55}]}, "bone42": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -99.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -91.92}], "translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -95.69, "y": 12.73}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 0.651}]}}}, "hurt": {"bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"angle": -2.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -11.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": -2.16}]}, "bone4": {"rotate": [{"angle": 1.41, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.3, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 1.41}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -11.94, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2, "angle": 49.44, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.3333}]}, "bone7": {"rotate": [{"angle": -2.68, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.94, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": -2.68}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 28.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone15": {"rotate": [{"angle": -3.5, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -45.29, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": -3.5}]}, "bone22": {"rotate": [{"angle": -0.91}]}, "bone30": {"rotate": [{"angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8}]}, "bone32": {"rotate": [{"angle": 7.26}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 31.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}}}, "run1": {"slots": {"light111": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "color": "ffffff86", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff"}]}, "eyelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "color": "ffffff8b", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff"}]}, "facelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "color": "ffffff8b", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff"}]}}, "bones": {"bone": {"translate": [{"x": -11.86, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -11.86, "y": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -11.86, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -11.86, "y": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -11.86, "y": 3.8}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone3": {"rotate": [{"angle": -1.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -1.28}]}, "bone4": {"rotate": [{"angle": -3.35, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 7.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667, "angle": -3.35}]}, "bone6": {"rotate": [{"angle": -36.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 25.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -36.21}]}, "bone7": {"rotate": [{"angle": -36.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -17.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -36.21}]}, "bone14": {"rotate": [{"angle": 81.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 81.55}]}, "bone15": {"rotate": [{"angle": -91.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -37.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -91.22}]}, "bone16": {"rotate": [{"angle": 37.04}]}, "bone24": {"rotate": [{"angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -14.27}]}, "bone25": {"rotate": [{"angle": 4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 22.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.21}]}, "bone26": {"rotate": [{"angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 22.7}]}, "bone27": {"rotate": [{"angle": -0.67, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 22.7, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -0.67}]}, "bone28": {"rotate": [{"angle": 21.19, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.1333, "angle": 4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 22.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 21.19}]}, "bone29": {"rotate": [{"angle": 9.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 9.1}]}, "bone22": {"rotate": [{"angle": -0.91}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 74.3, "y": -2.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "x": 50.44, "y": -1.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone30": {"rotate": [{"angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -61.84, "y": -12.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "x": -46.79, "y": -5.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone32": {"rotate": [{"angle": 7.26}]}, "bone33": {"rotate": [{"angle": 88.99, "curve": "stepped"}, {"time": 0.1667, "angle": 88.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 19.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 88.99}], "translate": [{"x": 32.12, "y": 57, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 16.5, "y": 45.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -137.91, "y": 14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -84.21, "y": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -17.15, "y": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 32.12, "y": 57}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 107.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 79.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}], "translate": [{"x": -20.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 119.84, "y": 51.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "x": 100.88, "y": 50.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "x": -20.15}]}, "target2": {"translate": [{"time": 0.1667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": 2.81, "y": -1.99, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.5, "x": -0.36, "y": 3.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667}]}, "bone42": {"rotate": [{"angle": -24.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.88, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6667, "angle": -24.7}]}}}, "run2": {"slots": {"qiliu/luodiyan_00013": {"attachment": [{"name": "qiliu/luodiyan_00013"}, {"time": 0.0333, "name": "qiliu/luodiyan_00014"}, {"time": 0.1, "name": "qiliu/luodiyan_00015"}, {"time": 0.1333, "name": "qiliu/luodiyan_00016"}, {"time": 0.2, "name": "qiliu/luodiyan_00017"}, {"time": 0.2333, "name": "qiliu/luodiyan_00018"}, {"time": 0.2667, "name": "qiliu/luodiyan_00019"}, {"time": 0.3333, "name": "qiliu/luodiyan_00020"}, {"time": 0.3667, "name": "qiliu/luodiyan_00021"}]}, "light111": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff79", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}, "eyelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff8b", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00034": {"attachment": [{"name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00038"}, {"time": 0.0333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00040"}, {"time": 0.0667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00042"}, {"time": 0.1333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00044"}, {"time": 0.1667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00046"}, {"time": 0.2, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00048"}, {"time": 0.2333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00050"}, {"time": 0.2667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00052"}, {"time": 0.3, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00054"}, {"time": 0.3667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00056"}]}, "facelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff8b", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}}, "bones": {"bone": {"translate": [{"x": -11.86, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -11.86, "y": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -11.86, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -11.86, "y": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -11.86, "y": 3.8}]}, "bone2": {"rotate": [{"angle": 22.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 36.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 22.55}]}, "bone3": {"rotate": [{"angle": -1.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3667, "angle": -1.28}]}, "bone4": {"rotate": [{"angle": -5.23, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -3.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": -8.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -5.23}]}, "bone6": {"rotate": [{"angle": -50.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -50.73}]}, "bone7": {"rotate": [{"angle": -43.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -49.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -31.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -43.06}]}, "bone14": {"rotate": [{"angle": 80.92, "curve": 0.346, "c2": 0.45, "c3": 0.68, "c4": 0.79}, {"time": 0.2, "angle": 43.78, "curve": 0.346, "c2": 0.66, "c3": 0.68}, {"time": 0.3667, "angle": 80.92}]}, "bone15": {"rotate": [{"angle": -39.93, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -36.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -62.18, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -39.93}]}, "bone16": {"rotate": [{"angle": 14.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 47.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 14.68}]}, "bone24": {"rotate": [{"angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.27}]}, "bone25": {"rotate": [{"angle": 4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.21}]}, "bone26": {"rotate": [{"angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 22.7}]}, "bone27": {"rotate": [{"angle": -0.67, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.7, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -0.67}]}, "bone28": {"rotate": [{"angle": 21.19, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.0667, "angle": 4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 21.19}]}, "bone29": {"rotate": [{"angle": 9.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 9.1}]}, "bone22": {"rotate": [{"angle": -0.91}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 74.3, "y": -2.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 50.44, "y": -1.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -61.84, "y": -12.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": -46.79, "y": -5.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}]}, "bone32": {"rotate": [{"angle": 7.26}]}, "bone33": {"rotate": [{"angle": 88.99, "curve": "stepped"}, {"time": 0.1, "angle": 88.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 19.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 88.99}], "translate": [{"x": 32.12, "y": 57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "x": 16.5, "y": 45.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -137.91, "y": 14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -84.21, "y": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -17.15, "y": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 32.12, "y": 57}]}, "bone34": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 107.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 79.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"x": -20.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 119.84, "y": 51.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 100.88, "y": 50.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": -20.15}]}, "target2": {"translate": [{"time": 0.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 2.81, "y": -1.99, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "x": -0.36, "y": 3.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667}]}, "bone42": {"rotate": [{"angle": -16.43}]}}, "deform": {"default": {"zuoshou": {"zuoshou": [{"vertices": [-8.62991, 1.18104, 4.91418, -7.19196, 0.06718, 8.71062, -14.6269, -3.19264, 12.18062, -8.7047, -5.03746, 14.09862, 9.26459, -12.14131, -0.68192, 15.25727, 3.26125, -8.31104, 2.06317, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23.15669, -15.7131, 3.42651, 11.15092, 7.54881, 8.89417, -11.65968, -0.3693, 9.78625, -6.34859, -5.91684, 1.81413, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -2.52794, 0.05816, -2.29945, 1.05084, 0.74785, 2.41544, -4.11448, -2.94029, -4.94051, -1.07844, 4.1144, -2.94016, -1.70168, 4.76234], "curve": 0.25, "c3": 0.75}, {"time": 0.2, "vertices": [-18.39233, -1.55405, 5.06609, -17.32888, -0.50218, 18.83282, -23.72938, -2.57872, 9.263, -17.34865, -2.47839, 22.85547, 3.88551, -18.70852, 4.42206, 22.04045, 1.04832, -11.60429, 4.13849, 12.06806, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -8.97554, 3.15234, -9.25009, -0.63451, 0.59818, 9.56584, -12.86637, 4.16827, -16.21593, -1.11405, 1.27017, -13.85079, 0.69077, 15.78093], "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "vertices": [-8.62991, 1.18104, 4.91418, -7.19196, 0.06718, 8.71062, -14.6269, -3.19264, 12.18062, -8.7047, -5.03746, 14.09862, 9.26459, -12.14131, -0.68192, 15.25727, 3.26125, -8.31104, 2.06317, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23.15669, -15.7131, 3.42651, 11.15092, 7.54881, 8.89417, -11.65968, -0.3693, 9.78625, -6.34859, -5.91684, 1.81413, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -2.52794, 0.05816, -2.29945, 1.05084, 0.74785, 2.41544, -4.11448, -2.94029, -4.94051, -1.07844, 4.1144, -2.94016, -1.70168, 4.76234]}]}}}}, "run3": {"slots": {"qiliu/luodiyan_00013": {"attachment": [{"name": "qiliu/luodiyan_00013"}, {"time": 0.0333, "name": "qiliu/luodiyan_00014"}, {"time": 0.1, "name": "qiliu/luodiyan_00015"}, {"time": 0.1333, "name": "qiliu/luodiyan_00016"}, {"time": 0.2, "name": "qiliu/luodiyan_00017"}, {"time": 0.2333, "name": "qiliu/luodiyan_00018"}, {"time": 0.2667, "name": "qiliu/luodiyan_00019"}, {"time": 0.3333, "name": "qiliu/luodiyan_00020"}, {"time": 0.3667, "name": "qiliu/luodiyan_00021"}]}, "light111": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff79", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}, "eyelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff8b", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00034": {"attachment": [{"name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00038"}, {"time": 0.0333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00040"}, {"time": 0.0667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00042"}, {"time": 0.1333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00044"}, {"time": 0.1667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00046"}, {"time": 0.2, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00048"}, {"time": 0.2333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00050"}, {"time": 0.2667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00052"}, {"time": 0.3, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00054"}, {"time": 0.3667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00056"}]}, "facelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff8b", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}}, "bones": {"bone": {"translate": [{"x": -11.86, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -11.86, "y": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -11.86, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -11.86, "y": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -11.86, "y": 3.8}]}, "bone2": {"rotate": [{"angle": 22.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 36.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 22.55}]}, "bone3": {"rotate": [{"angle": -1.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3667, "angle": -1.28}]}, "bone4": {"rotate": [{"angle": -5.23, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -3.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": -8.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -5.23}]}, "bone6": {"rotate": [{"angle": -50.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -50.73}]}, "bone7": {"rotate": [{"angle": -43.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -49.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -31.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -43.06}]}, "bone14": {"rotate": [{"angle": 80.92, "curve": 0.346, "c2": 0.45, "c3": 0.68, "c4": 0.79}, {"time": 0.2, "angle": 43.78, "curve": 0.346, "c2": 0.66, "c3": 0.68}, {"time": 0.3667, "angle": 80.92}]}, "bone15": {"rotate": [{"angle": -39.93, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -36.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -62.18, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -39.93}]}, "bone16": {"rotate": [{"angle": 14.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 47.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 14.68}]}, "bone24": {"rotate": [{"angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.27}]}, "bone25": {"rotate": [{"angle": 4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.21}]}, "bone26": {"rotate": [{"angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 22.7}]}, "bone27": {"rotate": [{"angle": -0.67, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.7, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -0.67}]}, "bone28": {"rotate": [{"angle": 21.19, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.0667, "angle": 4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 21.19}]}, "bone29": {"rotate": [{"angle": 9.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 9.1}]}, "bone22": {"rotate": [{"angle": -0.91}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 74.3, "y": -2.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 50.44, "y": -1.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -61.84, "y": -12.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": -46.79, "y": -5.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}]}, "bone32": {"rotate": [{"angle": 7.26}]}, "bone33": {"rotate": [{"angle": 88.99, "curve": "stepped"}, {"time": 0.1, "angle": 88.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 19.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 88.99}], "translate": [{"x": 32.12, "y": 57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "x": 16.5, "y": 45.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -137.91, "y": 14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -84.21, "y": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -17.15, "y": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 32.12, "y": 57}]}, "bone34": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 107.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 79.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"x": -20.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 119.84, "y": 51.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 100.88, "y": 50.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": -20.15}]}, "target2": {"translate": [{"time": 0.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 2.81, "y": -1.99, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "x": -0.36, "y": 3.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667}]}, "bone42": {"rotate": [{"angle": -16.43}]}}, "deform": {"default": {"zuoshou": {"zuoshou": [{"vertices": [-8.62991, 1.18104, 4.91418, -7.19196, 0.06718, 8.71062, -14.6269, -3.19264, 12.18062, -8.7047, -5.03746, 14.09862, 9.26459, -12.14131, -0.68192, 15.25727, 3.26125, -8.31104, 2.06317, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23.15669, -15.7131, 3.42651, 11.15092, 7.54881, 8.89417, -11.65968, -0.3693, 9.78625, -6.34859, -5.91684, 1.81413, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -2.52794, 0.05816, -2.29945, 1.05084, 0.74785, 2.41544, -4.11448, -2.94029, -4.94051, -1.07844, 4.1144, -2.94016, -1.70168, 4.76234], "curve": 0.25, "c3": 0.75}, {"time": 0.2, "vertices": [-18.39233, -1.55405, 5.06609, -17.32888, -0.50218, 18.83282, -23.72938, -2.57872, 9.263, -17.34865, -2.47839, 22.85547, 3.88551, -18.70852, 4.42206, 22.04045, 1.04832, -11.60429, 4.13849, 12.06806, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -8.97554, 3.15234, -9.25009, -0.63451, 0.59818, 9.56584, -12.86637, 4.16827, -16.21593, -1.11405, 1.27017, -13.85079, 0.69077, 15.78093], "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "vertices": [-8.62991, 1.18104, 4.91418, -7.19196, 0.06718, 8.71062, -14.6269, -3.19264, 12.18062, -8.7047, -5.03746, 14.09862, 9.26459, -12.14131, -0.68192, 15.25727, 3.26125, -8.31104, 2.06317, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23.15669, -15.7131, 3.42651, 11.15092, 7.54881, 8.89417, -11.65968, -0.3693, 9.78625, -6.34859, -5.91684, 1.81413, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -2.52794, 0.05816, -2.29945, 1.05084, 0.74785, 2.41544, -4.11448, -2.94029, -4.94051, -1.07844, 4.1144, -2.94016, -1.70168, 4.76234]}]}}}}, "show_time": {"slots": {"qiliu/luodiyan_00013": {"attachment": [{"name": "qiliu/luodiyan_00013"}, {"time": 0.0333, "name": "qiliu/luodiyan_00014"}, {"time": 0.1, "name": "qiliu/luodiyan_00015"}, {"time": 0.1333, "name": "qiliu/luodiyan_00016"}, {"time": 0.2, "name": "qiliu/luodiyan_00017"}, {"time": 0.2333, "name": "qiliu/luodiyan_00018"}, {"time": 0.2667, "name": "qiliu/luodiyan_00019"}, {"time": 0.3333, "name": "qiliu/luodiyan_00020"}, {"time": 0.3667, "name": "qiliu/luodiyan_00021"}]}, "light111": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff79", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}, "eyelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff8b", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}, "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00034": {"attachment": [{"name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00038"}, {"time": 0.0333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00040"}, {"time": 0.0667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00042"}, {"time": 0.1333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00044"}, {"time": 0.1667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00046"}, {"time": 0.2, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00048"}, {"time": 0.2333, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00050"}, {"time": 0.2667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00052"}, {"time": 0.3, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00054"}, {"time": 0.3667, "name": "run/xingchu_skill_hdwl/xingchu_skill_hdwl_00056"}]}, "facelight": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff8b", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff"}]}}, "bones": {"bone": {"translate": [{"x": -11.86, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -11.86, "y": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -11.86, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -11.86, "y": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -11.86, "y": 3.8}]}, "bone2": {"rotate": [{"angle": 22.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 36.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 22.55}]}, "bone3": {"rotate": [{"angle": -1.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3667, "angle": -1.28}]}, "bone4": {"rotate": [{"angle": -5.23, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -3.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": -8.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -5.23}]}, "bone6": {"rotate": [{"angle": -50.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -50.73}]}, "bone7": {"rotate": [{"angle": -43.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -49.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -31.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -43.06}]}, "bone14": {"rotate": [{"angle": 80.92, "curve": 0.346, "c2": 0.45, "c3": 0.68, "c4": 0.79}, {"time": 0.2, "angle": 43.78, "curve": 0.346, "c2": 0.66, "c3": 0.68}, {"time": 0.3667, "angle": 80.92}]}, "bone15": {"rotate": [{"angle": -39.93, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -36.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -62.18, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -39.93}]}, "bone16": {"rotate": [{"angle": 14.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 47.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 14.68}]}, "bone24": {"rotate": [{"angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.27}]}, "bone25": {"rotate": [{"angle": 4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.21}]}, "bone26": {"rotate": [{"angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 22.7}]}, "bone27": {"rotate": [{"angle": -0.67, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.7, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -0.67}]}, "bone28": {"rotate": [{"angle": 21.19, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.0667, "angle": 4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -14.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 21.19}]}, "bone29": {"rotate": [{"angle": 9.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 9.1}]}, "bone22": {"rotate": [{"angle": -0.91}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 74.3, "y": -2.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 50.44, "y": -1.87, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"angle": 2.43}]}, "bone31": {"rotate": [{"angle": -3.8}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -61.84, "y": -12.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": -46.79, "y": -5.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}]}, "bone32": {"rotate": [{"angle": 7.26}]}, "bone33": {"rotate": [{"angle": 88.99, "curve": "stepped"}, {"time": 0.1, "angle": 88.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 19.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 88.99}], "translate": [{"x": 32.12, "y": 57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "x": 16.5, "y": 45.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -137.91, "y": 14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -84.21, "y": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -17.15, "y": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 32.12, "y": 57}]}, "bone34": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 107.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": 79.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"x": -20.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 119.84, "y": 51.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 100.88, "y": 50.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": -20.15}]}, "target2": {"translate": [{"time": 0.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 2.81, "y": -1.99, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "x": -0.36, "y": 3.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667}]}, "bone42": {"rotate": [{"angle": -16.43}]}}, "deform": {"default": {"zuoshou": {"zuoshou": [{"vertices": [-8.62991, 1.18104, 4.91418, -7.19196, 0.06718, 8.71062, -14.6269, -3.19264, 12.18062, -8.7047, -5.03746, 14.09862, 9.26459, -12.14131, -0.68192, 15.25727, 3.26125, -8.31104, 2.06317, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23.15669, -15.7131, 3.42651, 11.15092, 7.54881, 8.89417, -11.65968, -0.3693, 9.78625, -6.34859, -5.91684, 1.81413, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -2.52794, 0.05816, -2.29945, 1.05084, 0.74785, 2.41544, -4.11448, -2.94029, -4.94051, -1.07844, 4.1144, -2.94016, -1.70168, 4.76234], "curve": 0.25, "c3": 0.75}, {"time": 0.2, "vertices": [-18.39233, -1.55405, 5.06609, -17.32888, -0.50218, 18.83282, -23.72938, -2.57872, 9.263, -17.34865, -2.47839, 22.85547, 3.88551, -18.70852, 4.42206, 22.04045, 1.04832, -11.60429, 4.13849, 12.06806, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -8.97554, 3.15234, -9.25009, -0.63451, 0.59818, 9.56584, -12.86637, 4.16827, -16.21593, -1.11405, 1.27017, -13.85079, 0.69077, 15.78093], "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "vertices": [-8.62991, 1.18104, 4.91418, -7.19196, 0.06718, 8.71062, -14.6269, -3.19264, 12.18062, -8.7047, -5.03746, 14.09862, 9.26459, -12.14131, -0.68192, 15.25727, 3.26125, -8.31104, 2.06317, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23.15669, -15.7131, 3.42651, 11.15092, 7.54881, 8.89417, -11.65968, -0.3693, 9.78625, -6.34859, -5.91684, 1.81413, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8658, 1.56915, -2.52794, 0.05816, -2.29945, 1.05084, 0.74785, 2.41544, -4.11448, -2.94029, -4.94051, -1.07844, 4.1144, -2.94016, -1.70168, 4.76234]}]}}}}}}