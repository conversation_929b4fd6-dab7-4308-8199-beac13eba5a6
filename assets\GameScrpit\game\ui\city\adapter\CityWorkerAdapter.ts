import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { CityLevelViewHolder } from "./CityLevelViewHolder";
import { CityWorkerViewHolder } from "./CityWorkerViewHolder";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { CityModule } from "../../../../module/city/CityModule";

export class CityWorkerAdapter extends ListAdapter {
  private datas: any[] = [];
  private _item2: Node;

  constructor(item2: Node) {
    super();
    this._item2 = item2;
  }

  public setDatas(datas: any[]) {
    this.clearData();
    this.datas = datas;
    this.notifyDataSetChanged();
  }

  getViewType(position: number): number {
    return 0;
  }

  onCreateView(viewType: number): Node {
    let item = instantiate(this._item2);
    item.getComponent(CityWorkerViewHolder);
    return item;
  }

  onBindData(node: Node, position: number): void {
    node.getComponent(CityWorkerViewHolder).updateData(this.datas[position]);
  }

  getCount(): number {
    return this.datas.length;
  }
}
