import { _decorator } from "cc";
import { UINode } from "../../lib/ui/UINode";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { UIMgr } from "../../lib/ui/UIMgr";
import FmUtils from "../../lib/utils/FmUtils";
import { PlayerModule } from "../../module/player/PlayerModule";
import { PlayerRouteName } from "../../module/player/PlayerConstant";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { GuideRouteEnum } from "../../ext_guide/GuideDefine";
import MsgMgr from "../../lib/event/MsgMgr";
import { AzstMsgEnum } from "../../module/azst/AzstConfig";
import MsgEnum from "../event/MsgEnum";
import GuideMgr from "../../ext_guide/GuideMgr";

const { ccclass, property } = _decorator;

const item1Id = 1031;
const item2Id = 1032;

@ccclass("UIQiYunNo")
export class UIQiYunNo extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIQiYunNo`;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.initItmeS, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.initItmeS, this);
  }

  protected onEvtShow(): void {
    this.initItmeS();
  }

  private initItmeS() {
    FmUtils.setItemNode(this.getNode("Item1"), item1Id, PlayerModule.data.getItemNum(item1Id));
    FmUtils.setItemNode(this.getNode("Item2"), item2Id, PlayerModule.data.getItemNum(item2Id));
  }

  private on_click_btn_use_item1() {
    UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: item1Id, type: null });
  }

  private on_click_btn_use_item2() {
    UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: item2Id, type: null });
  }

  private on_click_btn_huangse() {
    GuideMgr.startGuide({ stepId: 18 });
  }

  private on_click_btn_close_lan() {
    UIMgr.instance.back();
  }
}
