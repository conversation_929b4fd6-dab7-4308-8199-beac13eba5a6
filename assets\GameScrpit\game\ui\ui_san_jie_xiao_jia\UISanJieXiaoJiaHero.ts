import { _decorator, Color, color, Component, instantiate, isValid, Label, Node, Prefab, sp, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { HeroModule } from "../../../module/hero/HeroModule";
import { CityModule } from "../../../module/city/CityModule";
import { RewardMessage } from "../../net/protocol/Comm";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import ResMgr from "../../../lib/common/ResMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UISanJieXiaoJiaHero")
export class UISanJieXiaoJiaHero extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA}?prefab/ui/UISanJieXiaoJiaHero`;
  }

  protected onEvtShow(): void {
    this.updateMain();
    TipsMgr.setEnableTouch(true);
  }

  /**
   *
   * @returns
   */
  private updateMain() {
    let db = JsonMgr.instance.jsonList.c_home;
    let reward04List = db[101].reward04List;
    let id = reward04List[0][0];

    // 加载角色
    let hero_info = JsonMgr.instance.jsonList.c_hero[id];
    this.addHero(hero_info.id);

    this.getNode("lab_hero_aptitude").getComponent(Label).string = "总资质：" + hero_info.quality;
    this.getNode("lbl_hero_name").getComponent(Label).string = hero_info.name;
    this.getNode("lab_hero_hua").getComponent(Label).string =
      hero_info.des || "角亢之精,吐云郁熙,咬雷发声,\n飞翔八极,周游四冥,未立吾左。";
    HeroModule.service.updateHeroRaceIcon(this.getNode("spr_hero_type").getComponent(Sprite), hero_info.id, this);
    HeroModule.service.updateHeroColorBg(this.getNode("color_bkg").getComponent(Sprite), hero_info.id, this);
    // 判断角色是否领取
    if (CityModule.data.cityAggregateMessage.smallHomeAllLevelReward.includes(3) == true) {
      this.getNode("btn_no_get").active = false;
      this.getNode("btn_get").active = false;
      this.getNode("done").active = true;
      return;
    } else {
      this.getNode("done").active = false;
    }

    // 判断角色是否可领取
    let isLv = this.isCityLv(3);
    if (isLv == false) {
      this.getNode("lbl_hit").getComponent(Label).color = new Color().fromHEX("#ff6f6f");
      this.getNode("btn_no_get").active = true;
      this.getNode("btn_get").active = false;
    } else {
      this.getNode("lbl_hit").getComponent(Label).color = new Color().fromHEX("#00af04");
      this.getNode("btn_no_get").active = false;
      this.getNode("btn_get").active = true;
    }
  }

  private addHero(id) {
    ResMgr.loadPrefab(
      `${BundleEnum.BUNDLE_COMMON_HERO_FULL}?prefabs/hero_${id}`,
      (prefab) => {
        if (isValid(this.node) == false) {
          return;
        }
        this.getNode("rolePoint").destroyAllChildren();
        let node: Node = instantiate(prefab);
        node.walk((val) => {
          val.layer = this.node.layer;
        });
        this.getNode("rolePoint").addChild(node);
        node.name = "heroSpine";

        node.children[0].getComponent(sp.Skeleton).setAnimation(0, "animation1", true);
      },
      this
    );
  }

  /**
   * 判断全部建筑达到指定等级没有
   * @param lv 等级
   * @returns
   */
  private isCityLv(lv: number) {
    let db = JsonMgr.instance.jsonList.c_home;
    let idList = [];
    for (let i in db) {
      if (db[i].id < 200) idList.push(db[i].id);
    }

    for (let i of idList) {
      if (!CityModule.data.cityAggregateMessage.smallHomeLevelMap[i]) return false;
      if (CityModule.data.cityAggregateMessage.smallHomeLevelMap[i] < lv) return false;
    }

    return true;
  }

  private on_click_btn_watchSkill() {
    let db = JsonMgr.instance.jsonList.c_home;
    let reward04List = db[101].reward04List;
    let id = reward04List[0][0];

    // 加载角色
    let hero_info = JsonMgr.instance.jsonList.c_hero[id];
    UIMgr.instance.showDialog(PlayerRouteName.UIWatchSkill, { roleId: hero_info.skinId });
  }

  private on_click_btn_get() {
    CityModule.api.takeAllCityLevelReward(3, (res: RewardMessage) => {
      log.log("领取英雄====", res);
      AudioMgr.instance.playEffect(1803);
      this.updateMain();
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList, transformList: res.transformList });
    });
  }
}
