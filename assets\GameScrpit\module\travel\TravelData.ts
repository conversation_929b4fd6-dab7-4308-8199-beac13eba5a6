import { TravelVitalityMessage } from "../../game/net/protocol/Friend";
import MsgMgr from "../../lib/event/MsgMgr";
import { TravelMsgEnum } from "./TravelConfig";

export class TravelData {


  private _message: TravelVitalityMessage = null;

  public get message(): TravelVitalityMessage {
    return this._message;
  }

  public set message(message: TravelVitalityMessage) {
    this._message = message;
    MsgMgr.emit(TravelMsgEnum.TRAVEL_TOTAL_UPDATE);
  }

  public get vitality(): number {
    return this._message?.vitality || 0;
  }

  public set vitality(vitality: number) {
    this._message.vitality = vitality;
    MsgMgr.emit(TravelMsgEnum.TRAVEL_VITALITY_UPDATE);
  }

  public get vitalitySize(): number {
    return this._message?.vitalitySize || 1;
  }

  public set vitalitySize(vitalitySize: number) {
    if (vitalitySize > this._message.vitalitySize) {
      this._message.vitalitySize = vitalitySize;
      MsgMgr.emit(TravelMsgEnum.TRAVEL_VITALITY_UPDATE);
    }
  }

  public get lastUpdateTime(): number {
    return this._message.lastUpdateTime;
  }

  public set lastUpdateTime(lastUpdateTime: number) {
    this._message.lastUpdateTime = lastUpdateTime;
  }
}
