import { _decorator, Component, instantiate, Label, math, Node, SpringJoint2D, Sprite } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BossConfig, ClubAudioName } from "../../../module/club/ClubConfig";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { ClubModule } from "../../../module/club/ClubModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { ClubBossBuddyMessage, ClubDeadRewardResponse } from "../../net/protocol/Club";
import TipMgr from "../../../lib/tips/TipMgr";
import data from "../../../lib/data/data";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import FmUtils from "../../../lib/utils/FmUtils";
import { FmColor } from "../../common/FmConstant";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Wed Aug 28 2024 16:22:47 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubBossAward.ts
 *
 */

@ccclass("UIClubBossAward")
export class UIClubBossAward extends UINode {
  protected _openAct: boolean = true; //打开动作
  bossData: BossConfig;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubBossAward`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this.bossData = args.bossInfo;
  }
  private refreshUI() {
    let rewardKill = instantiate(this.getNode("item_layout"));
    let rewardFloor = instantiate(this.getNode("item_layout"));
    let rewardChallenge = instantiate(this.getNode("item_layout"));
    rewardKill.getChildByName("node_title").getChildByName("title").getComponent(Label).string = "击杀奖励";
    rewardKill.getChildByName("node_title").getChildByName("tips").getComponent(Label).string =
      "*赢得对boss最后一击专属奖励";
    rewardFloor.getChildByName("node_title").getChildByName("title").getComponent(Label).string = "击杀后";
    rewardFloor.getChildByName("node_title").getChildByName("tips").getComponent(Label).string =
      "未参与挑战战盟成员可领取";
    rewardChallenge.getChildByName("node_title").getChildByName("title").getComponent(Label).string = "击杀后";
    rewardChallenge.getChildByName("node_title").getChildByName("tips").getComponent(Label).string =
      "所有参与挑战成员可领取";
    this.getNode("content").removeAllChildren();

    ClubModule.api.getBossTrain((buddyMessage: ClubBossBuddyMessage) => {
      for (let i = 0; i < this.bossData.rewardFloor.length; i++) {
        let data = this.bossData.rewardFloor[i];
        let item = instantiate(this.getNode("Item"));
        rewardFloor.getChildByName("node_layout").getChildByName("item_list").addChild(item);
        FmUtils.setItemNode(item, data[0], data[1]);
      }

      for (let i = 0; i < this.bossData.rewardChallenge.length; i++) {
        let data = this.bossData.rewardChallenge[i];
        let item = instantiate(this.getNode("Item"));
        rewardChallenge.getChildByName("node_layout").getChildByName("item_list").addChild(item);
        FmUtils.setItemNode(item, data[0], data[1]);
      }
      if (buddyMessage.bossTrain.isHurt) {
        if (
          buddyMessage.bossTrain.bossIndex == this.bossData.id - 1 &&
          buddyMessage.bossTrain.remainHp == 0 &&
          !buddyMessage.bossTrain.isTakeKill
        ) {
          //没有领取过奖励并且boss血量为0的时候可以领取
          rewardChallenge.getChildByName("node_layout").getChildByName("enable").active = true;
          rewardChallenge.getChildByName("node_layout").getChildByName("disable").active = false;
        } else {
          rewardChallenge.getChildByName("node_layout").getChildByName("enable").active = false;
          rewardChallenge.getChildByName("node_layout").getChildByName("disable").active = true;
        }
      } else {
        if (
          buddyMessage.bossTrain.bossIndex == this.bossData.id - 1 &&
          buddyMessage.bossTrain.remainHp == 0 &&
          !buddyMessage.bossTrain.isTakeKill &&
          TimeUtils.serverTime > TimeUtils.getTimestampFromFormat("22:00:00", "HH:mm:ss")
        ) {
          //没有领取过奖励并且boss血量为0的时候可以领取
          rewardFloor.getChildByName("node_layout").getChildByName("enable").active = true;
          rewardFloor.getChildByName("node_layout").getChildByName("disable").active = false;
        } else {
          rewardFloor.getChildByName("node_layout").getChildByName("enable").active = false;
          rewardFloor.getChildByName("node_layout").getChildByName("disable").active = true;
        }
      }
      rewardChallenge
        .getChildByName("node_layout")
        .getChildByName("enable")
        .on(
          Node.EventType.TOUCH_END,
          () => {
            AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟首领领取击杀奖励);
            if (ClubModule.service.isInterceptOperation()) {
              return;
            }
            if (buddyMessage.bossTrain.bossIndex == -1) {
              TipMgr.showTip("BOSS还未召唤");
              return;
            }
            if (buddyMessage.bossTrain.isTakeKill) {
              TipMgr.showTip("今日奖励已领取");
              return;
            }
            if (buddyMessage.bossTrain.remainHp == 0) {
              ClubModule.api.takeBossDeadReward(this.bossData.id, (data: ClubDeadRewardResponse) => {
                buddyMessage.bossTrain.isTakeKill = data.isTakeKill;
                MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.resAddList });
                this.refreshUI();
              });
            } else {
              TipMgr.showTip("击杀后才能领取奖励");
            }
          },
          this
        );
      rewardFloor
        .getChildByName("node_layout")
        .getChildByName("enable")
        .on(
          Node.EventType.TOUCH_END,
          () => {
            if (ClubModule.service.isInterceptOperation()) {
              return;
            }
            if (buddyMessage.bossTrain.bossIndex == -1) {
              TipMgr.showTip("BOSS还未召唤");
              return;
            }
            if (buddyMessage.bossTrain.isTakeKill) {
              TipMgr.showTip("今日奖励已领取");
              return;
            }
            if (buddyMessage.bossTrain.remainHp == 0) {
              ClubModule.api.takeBossDeadReward(this.bossData.id, (data: ClubDeadRewardResponse) => {
                MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.resAddList });
                this.refreshUI();
              });
            } else {
              TipMgr.showTip("击杀后才能领取奖励");
            }
          },
          this
        );
      // this.getNode("content").addChild(rewardKill);
      this.getNode("content").addChild(rewardChallenge);
      this.getNode("content").addChild(rewardFloor);
    });
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.refreshUI();
  }
}
