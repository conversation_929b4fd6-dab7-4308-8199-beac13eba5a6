import { _decorator, Label, Node, tween, UITransform, v2, v3, Vec3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import MsgMgr from "../../../lib/event/MsgMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import MsgEnum from "../../event/MsgEnum";
import FightManager from "../../fight/manager/FightManager";

import { HuntRouteName } from "../../../module/hunt/HuntRoute";
import { FightRouteItem } from "../../../module/fight/src/FightModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const skipLabNum = 0;
@ccclass("UIPrepareFight")
export class UIPrepareFight extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIPrepareFight`;
  }

  private _args = null;

  private _round: number = 0;

  // 抖动cd
  private shakeCd: number = 0;

  private _maxRound = null;
  public init(args: any): void {
    super.init(args);
    this._args = args;
    this._maxRound = args.data.fightData.f;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
    MsgMgr.on(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
    MsgMgr.on(MsgEnum.ON_SHAKE_WORLD, this.shakeWorld, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
    MsgMgr.off(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
    MsgMgr.off(MsgEnum.ON_SHAKE_WORLD, this.shakeWorld, this);
  }

  private shakeWorld() {
    let ts = new Date().valueOf();
    if (this.shakeCd < ts) {
      // 0.2秒内不在抖动
      this.shakeCd = ts + 200;
      this.shake(this.getNode("world_bg"), v3(0, 0));
    }
  }

  /** 抖动具体方法 */
  private shake(node: Node, endPos: Vec3, amplitude: number = 20) {
    const tickTime = 0.05;

    // x轴震动
    let t1 = tween(node)
      .set({ position: endPos })
      .by(tickTime / 2, { position: v3(-amplitude / 2, 0) })
      .by(tickTime, { position: v3(+amplitude, 0) })
      .by(tickTime, { position: v3(-amplitude / 2, 0) });

    let t2 = tween(node)
      .delay(tickTime / 4)
      .by(tickTime / 2, { position: v3(0, amplitude / 2) })
      .by(tickTime, { position: v3(0, -amplitude) })
      .by(tickTime, { position: v3(0, +amplitude / 2) })
      .set({ position: endPos });
    tween(node).parallel(t1, t2).start();
  }
  protected onEvtShow(): void {
    this.getNode("btn_fightSkip").active = false;
    this.setTimeScaleLab();

    let posMap = new Map([
      [1, v2(-200, -100)],
      [2, v2(200, -100)],
    ]);
    let contentSize = this.getNode("main").getComponent(UITransform);
    this.getNode("main").setScale(0, 0, 0);
    FightManager.instance.start({
      main: this.node,
      parent: this.getNode("fightPoint"),
      fight: this._args.data.fightData,
      posMap: posMap,
      playId: this._args.data.fightData.a,
      contentSize: contentSize,
    });
  }

  private fightEnd() {
    this.getAzstAward();
  }

  /**更新回合显示 */
  private async setRoundShow(roundNumber: number) {
    this._round = roundNumber;

    this["roundLab"].getComponent(Label).string = `第${this._round}/${this._maxRound}回合`;
    if (this._round >= skipLabNum) {
      this.getNode("btn_fightSkip").active = true;
      this.getNode("skipLab").active = false;
    } else {
      this.getNode("btn_fightSkip").active = false;
      this.getNode("skipLab").active = true;
      this.getNode("skipLab").getComponent(Label).string = skipLabNum - this._round + "回合可后跳过战斗";
    }
  }

  /**时间倍数按钮的显示 */
  private setTimeScaleLab() {
    this["scaleTimeLab"].getComponent(Label).string = "x" + (PlayerModule.data.fightSpeed + 1);
  }

  private getAzstAward() {
    if (this._args.data.win == false) {
      UIMgr.instance.showDialog(
        HuntRouteName.UIHuntPrimitvieLose,
        {},
        () => {
          UIMgr.instance.replaceDialog(HuntRouteName.UIHuntPrepareSpirit);
        },
        () => {
          MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
        }
      );
    } else {
      UIMgr.instance.showDialog(
        HuntRouteName.UIPrepareWin,
        { resAddList: this._args.data.resAddList },
        () => {
          UIMgr.instance.replaceDialog(HuntRouteName.UIHuntPrepareSpirit);
        },
        () => {
          MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
        }
      );
    }
  }

  private on_click_btn_fightSkip() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._round < skipLabNum) {
      TipMgr.showTip("第五回合后跳过战斗");
      return;
    }
    TipsMgr.setEnableTouch(false, 3);
    // if (FightManager.instance.fightOver == true) {
    //   log.error("结算中");
    //   return;
    // }
    FightManager.instance.fightOver = true;
    MsgMgr.emit(MsgEnum.ON_FIGHT_SKIP);
  }

  private on_click_btn_timeScale() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    PlayerModule.service.changeFightSpeed();
    this.setTimeScaleLab();
  }

  public tick(dt: any): void {
    FightManager.instance.tick(dt);
  }

  protected onEvtClose(): void {}
}
