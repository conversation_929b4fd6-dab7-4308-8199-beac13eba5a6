import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { TopUpMessage } from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import TipMgr from "../../lib/tips/TipMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import { AfterMsgEnum } from "./AfterConfig";
import { AfterModule } from "./AfterModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class AfterData {
  private _topUpMessage: TopUpMessage;

  public get topUpMessage() {
    if (!this._topUpMessage) {
      this._topUpMessage = {
        activityId: 0,
        /** 物料ID:兑换次数 */
        redeemMap: {},
        /** 物料ID:广告次数 */
        adMap: {},
        /** 限制条件 兑换活动ID解锁要求的值 */
        limitMap: {},
        /** 自选礼包的选中情况 */
        chosenMap: {},
        /** 今日领取的充值ID 对应配置表的ID */
        signId: 0,
        /** 一轮循环的签到状态 */
        signMap: {},
        /** 总消费金额 */
        totalRecharge: 0,
        /** 领取的累计金额索引列表，索引从0开始 */
        takeList: [],
        /** 截止时间 */
        deadline: 0,
      };
    }
    return this._topUpMessage;
  }

  public set topUpMessage(value: TopUpMessage) {
    this._topUpMessage = value;
    MsgMgr.emit(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE);
  }

  public get takeList(): number[] {
    return this.topUpMessage.takeList;
  }
  public set takeList(val: number[]) {
    this.topUpMessage.takeList = val;
    MsgMgr.emit(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE);
  }

  /** 解锁条件：值 */
  public get limitMap(): { [key: number]: number } {
    return this.topUpMessage.limitMap;
  }
  /** 解锁条件：值 */
  public set limitMap(value: { [key: number]: number }) {
    this.topUpMessage.limitMap = value;
    MsgMgr.emit(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE);
  }

  public get deadline(): number {
    return this.topUpMessage.deadline;
  }
  public set deadline(val: number) {
    this.topUpMessage.deadline = val;
    MsgMgr.emit(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE);
  }

  public get signMap(): { [key: number]: number } {
    return this.topUpMessage.signMap;
  }
  public set signMap(val: { [key: number]: number }) {
    this.topUpMessage.signMap = val;
    MsgMgr.emit(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE);
  }

  public get redeemMap(): { [key: number]: number } {
    return this.topUpMessage.redeemMap;
  }
  public set redeemMap(val: { [key: number]: number }) {
    this.topUpMessage.redeemMap = val;
    MsgMgr.emit(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE);
  }

  public get adMap(): { [key: number]: number } {
    return this.topUpMessage.adMap;
  }
  public set adMap(val: { [key: number]: number }) {
    this.topUpMessage.adMap = val;
    MsgMgr.emit(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE);
  }

  public get totalRecharge(): number {
    return this.topUpMessage.totalRecharge;
  }
  public set totalRecharge(val: number) {
    this.topUpMessage.totalRecharge = val;
    MsgMgr.emit(AfterMsgEnum.AFTERMSGENUM_RED_DOT_UPDATE);
  }

  private _afterPackVO: any;

  /**获取配置 */
  public getafterVO(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (this._afterPackVO) {
        resolve(this._afterPackVO);
      } else {
        GameHttpApi.getActivityConfig(10901).then((resp: any) => {
          if (resp.code != 200) {
            log.error(resp);
          }
          let db = JSON.parse(resp.msg);
          this._afterPackVO = db;
          resolve(this._afterPackVO);
        });
      }
    });
  }

  public upVO() {
    this._afterPackVO = null;
    this.getafterVO();

    let rotueTables = AfterModule.route.rotueTables;
    for (let i = 0; i < rotueTables.length; i++) {
      UIMgr.instance.closeByName(rotueTables[i].uiName);
    }
    TipMgr.showTip("活动内容已变更");
  }
}
