import { _decorator, Node } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PupilModule } from "../PupilModule";
import { Label } from "cc";
import { JsonMgr } from "../../../../game/mgr/JsonMgr";
import { BasePupilInfoMessage } from "../../../../game/net/protocol/Pupil";
import FmUtils from "../../../../lib/utils/FmUtils";
import { Sprite } from "cc";
import { TimeUtils } from "../../../../lib/utils/TimeUtils";
import Formate from "../../../../lib/utils/Formate";
import { PupilHeader } from "../prefab/ui/PupilHeader";

const { ccclass, property } = _decorator;
@ccclass("PupilCompleteItemViewHolder")
export class PupilCompleteItemViewHolder extends ViewHolder {
  @property(Node)
  private nodeSelf: Node;

  @property(Node)
  private nodeOther: Node;

  @property(Label)
  private lblName: Label;

  @property(Label)
  private lblDate: Label;

  @property(Node)
  private nodeJob: Node;

  private _pupilId: number;

  public init() {}

  public updateData(position: number, args: any) {
    this._pupilId = args;
    this.refresh();
  }

  private setPupilInfo(nodePlayer: Node, pupilInfo: BasePupilInfoMessage) {
    const cfg = JsonMgr.instance.jsonList.c_pupil[pupilInfo.talentId];

    // 形象
    nodePlayer.getChildByName("PupilHeader").getComponent(PupilHeader).setHeaderByNameId(pupilInfo.nameId);

    // 结伴奖励数量
    nodePlayer.getChildByName("lbl_reward_num").getComponent(Label).string = `+${cfg.rewardList[1]}`;

    // 结伴图标
    FmUtils.setItemIcon(nodePlayer.getChildByName("bg_reward_item"), cfg.rewardList[0]);

    // 弟子名称
    nodePlayer.getChildByName("lbl_name").getComponent(Label).string = PupilModule.service.getPupilName(
      pupilInfo.nameId
    );

    // 设置属性天资背景
    PupilModule.service.setPupilAdultAttrBg(nodePlayer.getChildByName("bg_2").getComponent(Sprite), pupilInfo.talentId);

    // 设置天资属性
    nodePlayer.getChildByPath("bg_2/lbl_attr_main").getComponent(Label).string = Formate.formatAttribute(
      pupilInfo.adultAttrList[0],
      pupilInfo.adultAttrList[1]
    );

    // 任职显示
    this.nodeJob.getChildByName("bg_job3").active = PupilModule.data.pupilTrainMsg.workSlotList[0] == this._pupilId;
    this.nodeJob.getChildByName("bg_job2").active =
      PupilModule.data.pupilTrainMsg.workSlotList[1] == this._pupilId ||
      PupilModule.data.pupilTrainMsg.workSlotList[2] == this._pupilId;
    this.nodeJob.getChildByName("bg_job1").active =
      PupilModule.data.pupilTrainMsg.workSlotList[3] == this._pupilId ||
      PupilModule.data.pupilTrainMsg.workSlotList[4] == this._pupilId;
  }

  public refresh() {
    const pupilMsg = PupilModule.data.allPupilMap[this._pupilId];

    // 弟子归属玩家名称
    this.lblName.string = pupilMsg.partnerInfo.userName;

    // 结伴日期
    this.lblDate.string = TimeUtils.formatTimestamp(pupilMsg.marryStamp).replace(" ", "\n");

    // 设置自己的弟子信息
    this.setPupilInfo(this.nodeSelf, pupilMsg.ownInfo);

    // 设置结伴弟子的信息
    this.setPupilInfo(this.nodeOther, pupilMsg.partnerInfo);
  }
}
