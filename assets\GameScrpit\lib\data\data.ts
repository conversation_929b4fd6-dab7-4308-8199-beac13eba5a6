import { sys } from "cc";

export default abstract class data {
  constructor() {
    let content = this.getJson();
    this.decode(content);
  }

  protected _ready;

  public get ready() {
    return this._ready;
  }

  public abstract init(data?: any, completedCallback?: Function);

  protected initModel(res?: any) {}

  protected onViewLoad() {}

  public decode(json?) {
    if (json) {
      json = JSON.parse(json);
      for (var key in json) {
        this[key] = json[key];
      }
    }
  }
  public encode(): any {
    var data: any = Object.create(null);
    var user = JSON.parse(JSON.stringify(this));
    for (var key in user) {
      if (key == "_saveKey" || key == "saveDelay" || key == "_delaytime" || key == "instance") continue;
      data[key] = user[key];
    }
    return data;
  }
  public save() {
    let data = this.encode();
    sys.localStorage.setItem(this.saveKey(), JSON.stringify(data));
  }

  public clear() {
    let data = Object.create(null);
    sys.localStorage.setItem(this.saveKey(), JSON.stringify(data));
  }

  public getJson() {
    return sys.localStorage.getItem(this.saveKey());
  }

  public get key() {
    return this.saveKey();
  }

  protected abstract saveKey(): string;
}
