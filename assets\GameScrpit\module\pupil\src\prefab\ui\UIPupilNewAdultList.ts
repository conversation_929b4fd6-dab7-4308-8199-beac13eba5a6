import { _decorator, instantiate, Label } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { PupilAdultMessage } from "../../../../../game/net/protocol/Pupil";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import ToolExt from "../../../../../game/common/ToolExt";
import { PupilRouteName } from "../../PupilRoute";
import Formate from "../../../../../lib/utils/Formate";
import { PupilAni } from "./PupilAni";
import { PupilModule } from "../../PupilModule";
import ResMgr from "../../../../../lib/common/ResMgr";
import { Sprite } from "cc";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPupilNewAdultList")
export class UIPupilNewAdultList extends UINode {
  protected _openAct = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilNewAdultList`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI];
  }

  private _popList: PupilAdultMessage[] = [];

  public init(param) {
    super.init(param);
    this._popList = param["newAdultList"];
    log.log("newAdultList", this._popList);
  }

  protected onEvtShow(): void {
    this.initItemNode(this._popList.pop());
  }

  private initItemNode(pupilAdultMessage: PupilAdultMessage) {
    if (!pupilAdultMessage) {
      return;
    }
    let pupilMsg = pupilAdultMessage.pupilMessage;
    let config = PupilModule.data.getConfigPupil(pupilMsg.ownInfo.talentId);

    // 形象
    this.getNode("PupilAni")
      .getComponent(PupilAni)
      .setAniByNameId(pupilMsg.ownInfo.nameId, pupilMsg.ownInfo.adultAttrList.length);

    // 弟子姓名
    PupilModule.service.setPupilNameNode(this.getNode("pupil_name_lab"), pupilMsg.ownInfo.nameId);

    // 天资背景
    let nodeTalent = this.getNode("bg_talent");
    nodeTalent.active = false;
    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_G_PUPIL}?images/${config.talentBg}`,
      nodeTalent.getComponent(Sprite),
      this,
      () => {
        nodeTalent.active = true;
      }
    );

    // 攻血防
    let base_attr = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.basicAttrList);
    this.getNode("base_attr_lay").removeAllChildren();
    for (let i = 0; i < base_attr.length; i++) {
      let base_attr_lab = ToolExt.clone(this.getNode("base_attr_lab"), this);
      base_attr_lab.active = true;
      this.getNode("base_attr_lay").addChild(base_attr_lab);
      PupilModule.service.setPupilBaseAttrNode(base_attr_lab, base_attr[i]);
    }

    let list_v = [];
    let list_hor = [];
    for (let i = 0; i < pupilMsg.ownInfo.initAttrList.length; i += 2) {
      list_hor.push(pupilMsg.ownInfo.initAttrList[i]);
      list_hor.push(pupilMsg.ownInfo.initAttrList[i + 1]);

      if (list_hor.length >= 3) {
        let str = JSON.stringify(list_hor);
        list_v.push(JSON.parse(str));
        list_hor = [];
      }
    }

    if (list_hor.length >= 0) {
      let str = JSON.stringify(list_hor);
      list_v.push(JSON.parse(str));
      list_hor = [];
    }

    // 天生属性
    let nodeInitAttrLay = this.getNode("init_attr_lay");
    for (let i = 0; i < list_v.length; i++) {
      let list = list_v[i];
      let hor_node = instantiate(this.getNode("attr_hori_lay"));
      nodeInitAttrLay.addChild(hor_node);
      hor_node.active = true;

      for (let j = 0; j < list.length; j += 2) {
        let nodeItem = instantiate(this.getNode("init_attr_item"));
        hor_node.addChild(nodeItem);
        nodeItem.active = true;
        nodeItem.getComponentInChildren(Label).string = Formate.formatAttribute(list[j], list[j + 1]);
      }
    }

    // // 天生属性
    // let nodeInitAttrLay = this.getNode("init_attr_lay");
    // nodeInitAttrLay.children.forEach((item) => (item.active = false));
    // for (let i = 0; i < pupilMsg.ownInfo.initAttrList.length; i += 2) {
    //   let nodeItem = nodeInitAttrLay.children[i / 2];
    //   nodeItem.getComponentInChildren(Label).string = Formate.formatAttribute(
    //     pupilMsg.ownInfo.initAttrList[i],
    //     pupilMsg.ownInfo.initAttrList[i + 1]
    //   );
    //   nodeItem.active = true;
    // }

    // let width = 526;
    // if (pupilMsg.ownInfo.adultAttrList.length <= 4) {
    //   width = ((164 + 11) * pupilMsg.ownInfo.adultAttrList.length) / 2 - 11;
    // }
    // nodeInitAttrLay.getComponent(UITransform).setContentSize(width, 200);

    // 领悟属性
    let adult_attr = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.adultAttrList);
    PupilModule.service.setPupilAttrNode(this.getNode("adult_attr_bg").getChildByName("Label"), adult_attr[0]);
    PupilModule.service.setPupilAdultAttrBgNode(this.getNode("adult_attr_bg"), config.color);

    // 繁荣度
    let bloom = PupilModule.data.getPupilBloom(pupilAdultMessage.pupilMessage.id);
    this.getNode("lbl_bloom").getComponent(Label).string = `繁荣度:+${Formate.format(bloom)}`;
  }

  private on_click_btn_cancel(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._popList.length > 0) {
      UIMgr.instance.replaceDialog(PupilRouteName.UIPupilNewAdultList, {
        newAdultList: this._popList,
      });
    } else {
      UIMgr.instance.back();
    }
  }

  private on_click_btn_jieban() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.back({ resp: { goMarry: true } });
  }
}
