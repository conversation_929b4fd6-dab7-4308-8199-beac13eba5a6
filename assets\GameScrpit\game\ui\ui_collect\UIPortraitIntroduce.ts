import { _decorator, Component, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIPortraitIntroduce")
export class UIPortraitIntroduce extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COLLECT}?prefab/ui/UIPortraitIntroduce`;
  }

  private _heroId: number = null;
  public init(args: any): void {
    super.init(args);

    this._heroId = args.heroId;
  }

  protected onEvtShow(): void {
    let db = JsonMgr.instance.jsonList.c_hero[this._heroId];

    if (!db) {
      return;
    }

    this.getNode("lbl1").getComponent(Label).string = db.name;
    this.getNode("lbl2").getComponent(Label).string = db.des == "" ? "没有介绍" : db.des;
  }

  on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
