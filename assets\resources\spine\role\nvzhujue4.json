{"skeleton": {"hash": "aY25so50h7nMKrrMJe+t8saTE0Q=", "spine": "3.8.75", "x": -82.67, "y": -11.86, "width": 198.31, "height": 359.17, "images": "./images/", "audio": "D:/spine导出/主角换皮动画/主角女/女主角4"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -12.9}, {"name": "bone2", "parent": "bone", "length": 75.15, "rotation": 0.7, "x": 9.52, "y": 187.18, "color": "00376fff"}, {"name": "bone3", "parent": "bone2", "length": 30, "rotation": 97.88, "x": -1.99, "y": -2.35}, {"name": "bone4", "parent": "bone3", "length": 34.8, "rotation": 6.28, "x": 29.96, "y": -0.09}, {"name": "bone5", "parent": "bone4", "length": 10.59, "rotation": -7.45, "x": 35.36, "y": -0.07}, {"name": "bone6", "parent": "bone5", "rotation": -5.37, "x": 25.57, "y": -15.54}, {"name": "bone13", "parent": "bone6", "x": 27.12, "y": -3.13}, {"name": "bone14", "parent": "bone13", "length": 11.27, "rotation": 59.56, "x": 3.16, "y": 4.9}, {"name": "bone15", "parent": "bone14", "length": 14.53, "rotation": 96.05, "x": 11.27}, {"name": "bone16", "parent": "bone15", "length": 18, "rotation": -15.48, "x": 14.53}, {"name": "bone31", "parent": "bone4", "rotation": -6.12, "x": 28.49, "y": 25.75}, {"name": "bone36", "parent": "bone31", "length": 37.62, "rotation": 153.83, "x": -1.83, "y": 1.87}, {"name": "bone37", "parent": "bone36", "length": 40, "rotation": 23.95, "x": 37.62}, {"name": "bone38", "parent": "bone37", "length": 16.25, "rotation": 1.18, "x": 40.38, "y": 0.51}, {"name": "bone62", "parent": "bone38", "length": 42.77, "rotation": 69.27, "x": 6.61, "y": 0.58}, {"name": "bone32", "parent": "bone4", "rotation": -6.12, "x": 12.61, "y": -20.2}, {"name": "bone33", "parent": "bone32", "length": 35.85, "rotation": -175.38, "x": -0.05, "y": -1.81}, {"name": "bone34", "parent": "bone33", "length": 35.66, "rotation": -1.06, "x": 38.76, "y": 0.66}, {"name": "bone35", "parent": "bone34", "length": 15.47, "rotation": -1.56, "x": 35.83, "y": -0.04}, {"name": "bone39", "parent": "bone2", "x": -19.61, "y": -20.35}, {"name": "bone7", "parent": "bone2", "x": -5.1, "y": -25.11}, {"name": "bone44", "parent": "bone7", "length": 20, "rotation": -77.72, "x": 11.82, "y": 14.45, "color": "52f94bff"}, {"name": "bone45", "parent": "bone44", "length": 20, "rotation": 3.07, "x": 19.35, "y": -0.15, "color": "52f94bff"}, {"name": "bone46", "parent": "bone45", "length": 20, "rotation": 2.55, "x": 20.09, "y": 0.17, "color": "52f94bff"}, {"name": "bone49", "parent": "bone7", "length": 20, "rotation": -107.74, "x": -10.91, "y": 13.49, "color": "52f94bff"}, {"name": "bone50", "parent": "bone49", "length": 20, "rotation": 3.09, "x": 17.66, "y": -0.29, "color": "52f94bff"}, {"name": "bone51", "parent": "bone50", "length": 20, "rotation": -2.05, "x": 19.4, "y": 0.2, "color": "52f94bff"}, {"name": "bone52", "parent": "bone2", "length": 73.79, "rotation": -97.23, "x": -26.16, "y": -24.54}, {"name": "bone53", "parent": "bone52", "length": 60, "rotation": -0.27, "x": 73.79}, {"name": "bone54", "parent": "bone53", "length": 20, "rotation": 30.08, "x": 60.49}, {"name": "bone56", "parent": "bone2", "length": 76.08, "rotation": -85.57, "x": 1.67, "y": -24.29}, {"name": "bone57", "parent": "bone56", "length": 55.47, "rotation": -8.81, "x": 76.08}, {"name": "bone58", "parent": "bone57", "length": 23, "rotation": 55.02, "x": 55.47}, {"name": "bone8", "parent": "bone2", "length": 28.81, "rotation": -109.03, "x": -23.6, "y": -8.7, "color": "ff6d6dff"}, {"name": "bone9", "parent": "bone8", "length": 22.89, "rotation": -5.35, "x": 28.81, "color": "ff6d6dff"}, {"name": "bone10", "parent": "bone9", "length": 25.77, "rotation": -6.66, "x": 22.89, "color": "ff6d6dff"}, {"name": "bone11", "parent": "bone2", "length": 26.43, "rotation": -79.31, "x": 12.82, "y": -8.57, "color": "ff6d6dff"}, {"name": "bone12", "parent": "bone11", "length": 25.49, "rotation": 5.58, "x": 26.43, "color": "ff6d6dff"}, {"name": "bone18", "parent": "bone12", "length": 25.46, "rotation": 5.39, "x": 25.49, "color": "ff6d6dff"}, {"name": "bone19", "parent": "bone2", "length": 17.24, "rotation": -131.13, "x": -17.8, "y": 0.83}, {"name": "bone20", "parent": "bone19", "length": 17.76, "rotation": 4.71, "x": 17.24}, {"name": "bone21", "parent": "bone20", "length": 21.19, "rotation": -10.21, "x": 17.76}, {"name": "bone17", "parent": "bone16", "length": 14.28, "rotation": -11.69, "x": 17.93, "y": -0.25}, {"name": "bone25", "parent": "bone13", "length": 8.59, "rotation": -68.85, "x": 1.45, "y": -6.72}, {"name": "bone26", "parent": "bone25", "length": 14.51, "rotation": -96.64, "x": 8.59}, {"name": "bone27", "parent": "bone26", "length": 13.54, "rotation": 8.94, "x": 14.51}, {"name": "bone28", "parent": "bone27", "length": 13.77, "rotation": 13.41, "x": 13.54}, {"name": "bone29", "parent": "bone5", "length": 14.15, "rotation": 9.58, "x": 80.82, "y": -14.57}, {"name": "bone130", "parent": "bone29", "length": 12.7, "rotation": 45.69, "x": 14.15}, {"name": "bone40", "parent": "bone130", "length": 13.91, "rotation": 26.55, "x": 12.7}, {"name": "bone22", "parent": "bone5", "length": 53.56, "rotation": 160.69, "x": 51.95, "y": 9.82, "color": "e509ddff"}, {"name": "bone23", "parent": "bone22", "length": 52.72, "rotation": -4.68, "x": 53.56, "color": "e509ddff"}, {"name": "bone24", "parent": "bone23", "length": 38.95, "rotation": 6.15, "x": 52.72, "color": "e509ddff"}, {"name": "bone41", "parent": "bone5", "length": 52.77, "rotation": -175.85, "x": 44, "y": -23.98, "color": "e509ddff"}, {"name": "bone42", "parent": "bone41", "length": 50.61, "rotation": 2.41, "x": 52.54, "y": 0.05, "color": "e509ddff"}, {"name": "bone43", "parent": "bone42", "length": 40.88, "rotation": -5.38, "x": 50.44, "y": 0.28, "color": "e509ddff"}, {"name": "bone47", "parent": "bone32", "length": 7.65, "rotation": -26.27, "x": 7.3, "y": 0.88}, {"name": "bone48", "parent": "bone47", "length": 7.25, "rotation": -36.82, "x": 7.65}, {"name": "1bone63", "parent": "bone48", "length": 8.14, "rotation": -5.46, "x": 7.25}, {"name": "bone164", "parent": "bone31", "length": 8.66, "rotation": 29.65, "x": 1.95, "y": -11.22}, {"name": "bone165", "parent": "bone164", "length": 10.94, "rotation": 35.29, "x": 8.66}, {"name": "bone166", "parent": "bone165", "length": 9.4, "rotation": -21.49, "x": 10.94}, {"name": "bone67", "parent": "bone2", "length": 16, "rotation": -110.93, "x": -17.25, "y": -7.44, "color": "022c6fff"}, {"name": "bone68", "parent": "bone67", "length": 21.29, "rotation": -3.87, "x": 15.96, "y": -0.35, "color": "022c6fff"}, {"name": "bone69", "parent": "bone68", "length": 19.01, "rotation": -0.98, "x": 21.35, "y": -0.14, "color": "022c6fff"}, {"name": "bone70", "parent": "bone2", "length": 20.9, "rotation": -70.35, "x": 9.36, "y": -2.71, "color": "022c6fff"}, {"name": "bone71", "parent": "bone70", "length": 20.45, "rotation": -3.74, "x": 20.75, "y": 0.05, "color": "022c6fff"}, {"name": "bone72", "parent": "bone71", "length": 18.94, "rotation": 2.88, "x": 20.29, "y": 0.05, "color": "022c6fff"}, {"name": "bone63", "parent": "root", "x": 49.46, "y": 384.69, "scaleX": 1.7354, "scaleY": 1.7354}, {"name": "bone30", "parent": "bone2", "length": 78.51, "rotation": -73.89, "x": -27.04, "y": -23.02, "color": "00376fff"}, {"name": "bone64", "parent": "bone30", "length": 69.83, "rotation": -48.72, "x": 78.51, "color": "00376fff"}, {"name": "bone65", "parent": "bone2", "length": 85.74, "rotation": -64.25, "x": 1.02, "y": -23.5, "color": "00376fff"}, {"name": "bone66", "parent": "bone65", "length": 64.91, "rotation": -58.05, "x": 85.57, "y": 0.08, "color": "00376fff"}, {"name": "bone55", "parent": "bone", "length": 36.17, "rotation": -0.65, "x": -42.98, "y": 13.69}, {"name": "bone59", "parent": "bone55", "length": 35.35, "rotation": 89.98, "x": -0.55, "y": -0.01}, {"name": "rjio1", "parent": "bone59", "rotation": -89.33, "x": 75.8, "y": -17.69, "color": "ff3f00ff"}, {"name": "rjio2", "parent": "bone59", "rotation": -89.33, "x": 15.44, "y": -10.86, "color": "ff3f00ff"}, {"name": "rjio3", "parent": "rjio2", "x": 7.72, "y": -18.55, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone60", "parent": "bone", "length": 43.86, "rotation": -1.33, "x": 9.36, "y": 14.37}, {"name": "bone61", "parent": "bone60", "length": 33.91, "rotation": 92.36, "x": -0.81, "y": -0.22}, {"name": "ljio1", "parent": "bone61", "rotation": -91.03, "x": 72.85, "y": -11.36, "color": "ff3f00ff"}, {"name": "ljio2", "parent": "bone61", "rotation": -91.03, "x": 17.19, "y": -7.21, "color": "ff3f00ff"}, {"name": "ljio3", "parent": "ljio2", "x": 18.4, "y": -14.03, "transform": "noScale", "color": "ff3f00ff"}], "slots": [{"name": "tf6", "bone": "root", "attachment": "tf6"}, {"name": "pd3", "bone": "root", "attachment": "pd3"}, {"name": "pd2", "bone": "root", "attachment": "pd2"}, {"name": "ss2", "bone": "root", "attachment": "ss2"}, {"name": "jio2", "bone": "bone56", "attachment": "jio2"}, {"name": "jio1", "bone": "bone52", "attachment": "jio1"}, {"name": "tf 41", "bone": "bone29", "attachment": "tf-41"}, {"name": "pd4", "bone": "root", "attachment": "pd4"}, {"name": "tf3", "bone": "bone5", "attachment": "tf3"}, {"name": "xiabai", "bone": "bone3", "attachment": "xiabai"}, {"name": "pd1", "bone": "bone2", "attachment": "pd1"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "yd", "bone": "bone3", "attachment": "yd"}, {"name": "tou", "bone": "bone5", "attachment": "tou"}, {"name": "biyan", "bone": "bone6"}, {"name": "tf1", "bone": "bone5", "attachment": "tf1"}, {"name": "erduo", "bone": "bone5", "attachment": "erduo"}, {"name": "tf2", "bone": "bone5", "attachment": "tf2"}, {"name": "pd4111", "bone": "bone13", "attachment": "pd4111"}, {"name": "toushi", "bone": "bone13", "attachment": "toushi"}, {"name": "tf5", "bone": "bone13", "attachment": "tf5"}, {"name": "sss2", "bone": "bone33"}, {"name": "yiyi4", "bone": "bone2"}, {"name": "zanzi", "bone": "bone5"}, {"name": "jio4", "bone": "bone56"}, {"name": "jio3", "bone": "bone52"}, {"name": "yiyi2", "bone": "bone2"}, {"name": "body2", "bone": "bone3"}, {"name": "tou2", "bone": "bone5"}, {"name": "toufa111", "bone": "bone5"}, {"name": "erduo2", "bone": "bone5"}, {"name": "yi2", "bone": "bone2"}, {"name": "mutou", "bone": "bone62"}, {"name": "biyan2", "bone": "bone6"}, {"name": "ss3", "bone": "bone36"}, {"name": "tf234", "bone": "bone14"}, {"name": "wuqi", "bone": "bone62", "attachment": "wuqi"}, {"name": "ss1", "bone": "root", "attachment": "ss1"}, {"name": "mutou3", "bone": "bone63"}, {"name": "xuanz", "bone": "bone63", "color": "ffffff6f", "blend": "additive"}], "ik": [{"name": "ljio1", "order": 7, "bones": ["bone56"], "target": "ljio1", "compress": true, "stretch": true}, {"name": "ljio2", "order": 8, "bones": ["bone57"], "target": "ljio2", "compress": true, "stretch": true}, {"name": "ljio3", "order": 9, "bones": ["bone58"], "target": "ljio3"}, {"name": "ljio4", "order": 5, "bones": ["bone65", "bone66"], "target": "ljio2", "bendPositive": false}, {"name": "rjio1", "order": 2, "bones": ["bone52"], "target": "rjio1", "compress": true, "stretch": true}, {"name": "rjio2", "order": 3, "bones": ["bone53"], "target": "rjio2", "compress": true, "stretch": true}, {"name": "rjio3", "order": 4, "bones": ["bone54"], "target": "rjio3"}, {"name": "rjio4", "bones": ["bone30", "bone64"], "target": "rjio2", "bendPositive": false}], "transform": [{"name": "ljio5", "order": 6, "bones": ["ljio1"], "target": "bone66", "rotation": 121.07, "x": 15.56, "y": -26.07, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "rjio5", "order": 1, "bones": ["rjio1"], "target": "bone64", "rotation": 121.82, "x": 14.65, "y": -25.38, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"pd4": {"pd4": {"type": "mesh", "uvs": [0.52556, 0, 0.67697, 0, 0.81003, 0.03024, 0.86509, 0.20433, 0.90409, 0.41459, 0.95456, 0.68363, 1, 0.91876, 1, 1, 0.84444, 1, 0.58062, 1, 0.25027, 1, 0.02774, 1, 0, 0.94589, 0.0438, 0.76502, 0.08739, 0.52989, 0.11492, 0.26989, 0.14244, 0.08902, 0.31221, 0, 0.53244, 0.20885, 0.53933, 0.43493, 0.54621, 0.69493, 0.55539, 0.87128, 0.76874, 0.78537, 0.73203, 0.51633, 0.7068, 0.23372, 0.29156, 0.76954, 0.3168, 0.48467, 0.33744, 0.22015], "triangles": [26, 27, 19, 26, 15, 27, 27, 18, 19, 15, 16, 27, 16, 17, 27, 27, 0, 18, 27, 17, 0, 25, 14, 26, 14, 15, 26, 10, 21, 9, 10, 12, 13, 10, 25, 21, 10, 13, 25, 10, 11, 12, 25, 20, 21, 13, 14, 25, 25, 26, 20, 8, 6, 7, 9, 22, 8, 8, 22, 6, 9, 21, 22, 22, 5, 6, 22, 23, 5, 21, 20, 22, 20, 23, 22, 26, 19, 20, 20, 19, 23, 23, 4, 5, 23, 19, 24, 4, 23, 24, 19, 18, 24, 24, 3, 4, 18, 1, 24, 24, 2, 3, 24, 1, 2, 18, 0, 1], "vertices": [2, 22, -9.76, -5.44, 0.70858, 25, -13.57, 11.76, 0.29142, 2, 22, -7.44, 4.59, 0.99041, 25, -16.59, 21.6, 0.00959, 1, 22, -3.38, 12.94, 1, 2, 22, 9.17, 13.89, 0.99974, 23, -9.41, 14.56, 0.00026, 3, 22, 23.9, 13.21, 0.54518, 23, 5.26, 13.1, 0.38049, 24, -14.24, 13.58, 0.07433, 3, 22, 42.76, 12.38, 0.00427, 23, 24.05, 11.27, 0.02412, 24, 4.45, 10.91, 0.97161, 1, 24, 20.81, 8.67, 1, 1, 24, 26.13, 6.88, 1, 3, 23, 42.96, -1.96, 0.01827, 24, 22.75, -3.14, 0.97149, 27, 9.96, 51.58, 0.01024, 4, 23, 38, -19.21, 0.35784, 24, 17.03, -20.15, 0.30767, 27, 14.9, 34.34, 0.33117, 25, 51.3, 35.56, 0.00333, 4, 23, 31.79, -40.79, 0.11981, 24, 9.87, -41.44, 0.01819, 27, 21.1, 12.74, 0.86156, 25, 57.89, 14.08, 0.00044, 2, 23, 27.61, -55.34, 6e-05, 27, 25.27, -1.8, 0.99994, 1, 27, 22.2, -4.64, 1, 2, 27, 9.38, -5.22, 0.86773, 26, 28.59, -5.35, 0.13227, 1, 26, 12.13, -6.39, 1, 2, 26, -5.73, -8.9, 0.24424, 25, 12.42, -9.48, 0.75576, 1, 25, -0.06, -11.35, 1, 2, 22, -13.01, -19.58, 0.08634, 25, -9.32, -2.11, 0.91366, 4, 22, 4.39, -8.23, 0.64257, 23, -15.37, -7.26, 0.07922, 27, -36.67, 16.14, 0.00144, 25, 0.07, 16.43, 0.27676, 4, 22, 19.7, -11.27, 0.06062, 23, -0.24, -11.12, 0.63148, 27, -21.8, 20.89, 0.06549, 25, 14.85, 21.45, 0.24241, 4, 23, 17.13, -15.63, 0.63518, 24, -3.66, -15.65, 0.01663, 27, -4.69, 26.29, 0.27857, 25, 31.86, 27.15, 0.06963, 4, 23, 28.99, -18.4, 0.43918, 24, 8.07, -18.94, 0.19129, 27, 6.84, 30.24, 0.35553, 25, 43.31, 31.31, 0.01401, 4, 23, 27.31, -2.82, 0.23164, 24, 7.08, -3.3, 0.74034, 27, -2.86, 42.55, 0.02674, 25, 33.4, 43.45, 0.00127, 3, 23, 8.78, -0.08, 0.99955, 27, -20.02, 35.03, 0.00023, 25, 16.38, 35.62, 0.00022, 1, 22, 8.73, 2.94, 1, 4, 23, 17.29, -33.7, 0.16478, 24, -4.31, -33.71, 0.00782, 27, 5.04, 11.06, 0.79856, 25, 41.86, 12.1, 0.02884, 5, 22, 19.64, -26.79, 0.00272, 23, -1.13, -26.61, 0.15473, 27, -14.33, 7.29, 0.14335, 26, 5.34, 8, 0.14957, 25, 22.56, 7.98, 0.54963, 4, 22, 2.17, -21.32, 0.0628, 23, -18.28, -20.22, 0.02644, 27, -32.26, 3.61, 0.00078, 25, 4.7, 3.98, 0.90998], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 16, 44, 44, 46, 46, 48, 20, 50, 50, 52, 52, 54], "width": 68, "height": 69}}, "tf3": {"tf3": {"x": 21.6, "y": -33.53, "rotation": -97.41, "width": 13, "height": 21}}, "tou": {"tou": {"x": 43.45, "y": -5.12, "rotation": -97.41, "width": 72, "height": 86}}, "tf5": {"tf5": {"type": "mesh", "uvs": [0.55909, 0.32652, 0.50875, 0.32937, 0.45527, 0.31652, 0.40808, 0.32652, 0.37583, 0.41366, 0.33178, 0.53937, 0.29167, 0.66366, 0.24999, 0.8008, 0.16583, 0.87651, 0.08718, 0.94937, 0.02269, 0.96794, 0.02583, 0.81651, 0.04392, 0.66366, 0.12729, 0.47223, 0.16426, 0.34509, 0.21145, 0.1808, 0.25549, 0.07223, 0.34673, 0.0108, 0.43167, 0, 0.47886, 0.02223, 0.50246, 0.09652, 0.53313, 0.16366, 0.56381, 0.17794, 0.59369, 0.15509, 0.61886, 0.09366, 0.65111, 0.04509, 0.69308, 0.03724, 0.74153, 0.06124, 0.78392, 0.10524, 0.81641, 0.17024, 0.81802, 0.31698, 0.83383, 0.40175, 0.87448, 0.50294, 0.9076, 0.60822, 0.93169, 0.73128, 0.95879, 0.80512, 0.9859, 0.87896, 1, 0.96373, 0.94826, 0.97467, 0.86545, 0.961, 0.80522, 0.89673, 0.75704, 0.79692, 0.7194, 0.69437, 0.68628, 0.56037, 0.66972, 0.44278, 0.65466, 0.33749, 0.61476, 0.32655, 0.51222, 0.24373, 0.5582, 0.25719, 0.61011, 0.24642, 0.65831, 0.17773, 0.71319, 0.17773, 0.73544, 0.27875, 0.74359, 0.38111, 0.77029, 0.51581, 0.7918, 0.62087, 0.82442, 0.72054, 0.8615, 0.82425, 0.906, 0.88756, 0.9594, 0.92846, 0.46626, 0.18134, 0.41755, 0.14144, 0.35261, 0.14665, 0.30103, 0.24726, 0.27525, 0.36348, 0.23991, 0.47277, 0.20362, 0.56817, 0.16637, 0.67226, 0.10907, 0.77807, 0.06323, 0.88389], "triangles": [56, 55, 33, 56, 33, 34, 41, 55, 56, 57, 56, 34, 58, 57, 34, 35, 58, 34, 59, 58, 35, 40, 41, 56, 40, 56, 57, 36, 59, 35, 39, 57, 58, 40, 57, 39, 59, 36, 37, 38, 58, 59, 38, 59, 37, 39, 58, 38, 54, 53, 31, 54, 31, 32, 43, 44, 54, 55, 54, 32, 55, 32, 33, 55, 42, 43, 55, 43, 54, 41, 42, 55, 51, 26, 27, 51, 27, 28, 29, 52, 51, 29, 51, 28, 52, 29, 30, 53, 52, 30, 53, 30, 31, 50, 52, 45, 52, 50, 51, 53, 45, 52, 53, 44, 45, 44, 53, 54, 50, 25, 26, 24, 25, 50, 23, 24, 50, 50, 26, 51, 49, 23, 50, 22, 23, 49, 45, 46, 49, 0, 49, 46, 50, 45, 49, 67, 13, 66, 12, 13, 67, 68, 12, 67, 11, 12, 68, 8, 68, 67, 8, 67, 7, 69, 11, 68, 9, 69, 68, 8, 9, 68, 10, 11, 69, 10, 69, 9, 14, 15, 64, 65, 14, 64, 13, 14, 65, 5, 64, 4, 65, 64, 5, 66, 13, 65, 6, 65, 5, 66, 65, 6, 6, 67, 66, 7, 67, 6, 62, 17, 61, 16, 17, 62, 63, 16, 62, 15, 16, 63, 3, 62, 61, 63, 62, 3, 64, 15, 63, 4, 64, 63, 3, 4, 63, 61, 17, 18, 61, 18, 19, 61, 19, 20, 60, 61, 20, 60, 20, 21, 47, 60, 21, 60, 3, 61, 2, 60, 47, 2, 3, 60, 48, 21, 22, 48, 22, 49, 47, 21, 48, 0, 48, 49, 1, 2, 47, 1, 47, 48, 1, 48, 0], "vertices": [4, 7, -3.38, -1.33, 0.84353, 8, -8.69, 2.48, 0.00184, 44, -6.77, -2.56, 0.15334, 45, 4.32, -14.96, 0.00129, 3, 7, -3.36, 3.15, 0.69667, 8, -4.81, 4.73, 0.28327, 9, 6.4, 15.49, 0.02006, 3, 7, -2.56, 7.88, 0.16437, 8, -0.32, 6.44, 0.64003, 9, 7.63, 10.85, 0.1956, 4, 7, -2.9, 12.1, 0.01327, 8, 3.14, 8.87, 0.33917, 9, 9.68, 7.15, 0.62894, 10, -6.58, 5.6, 0.01862, 3, 8, 3.63, 13.99, 0.04233, 9, 14.72, 6.12, 0.53989, 10, -1.45, 5.95, 0.41778, 2, 9, 21.91, 4.84, 0.00557, 10, 5.82, 6.63, 0.99443, 1, 10, 12.82, 7.55, 1, 2, 10, 20.4, 8.74, 0.7661, 43, 0.6, 9.3, 0.2339, 2, 10, 27.93, 5.09, 0.11767, 43, 8.71, 7.26, 0.88233, 1, 43, 16.35, 5.42, 1, 1, 43, 21.3, 2.39, 1, 1, 43, 16.27, -3.07, 1, 1, 43, 10.19, -7.72, 1, 3, 9, 25.78, -13.25, 0.0081, 10, 14.38, -9.76, 0.37624, 43, -1.55, -10.04, 0.61566, 3, 9, 18.77, -12.57, 0.16117, 10, 7.44, -10.98, 0.68471, 43, -8.1, -12.64, 0.15411, 3, 9, 9.73, -11.75, 0.72847, 10, -1.49, -12.6, 0.26258, 43, -16.52, -16.03, 0.00895, 2, 9, 3.31, -10.14, 0.95304, 10, -8.1, -12.77, 0.04696, 2, 8, 15.3, -2.14, 0.21254, 9, -2.56, -3.78, 0.78746, 2, 8, 8.9, -6.2, 0.98419, 9, -5.92, 3.02, 0.01581, 1, 8, 4.68, -7.24, 1, 2, 7, 8.06, 3.3, 0.01554, 8, 1.11, -5.04, 0.98446, 3, 7, 4.68, 0.69, 0.40319, 8, -2.86, -3.44, 0.54418, 44, -5.75, 5.69, 0.05263, 3, 7, 3.88, -2.01, 0.52214, 8, -5.59, -4.13, 0.10392, 44, -3.52, 3.97, 0.37393, 3, 7, 4.9, -4.71, 0.1347, 8, -7.4, -6.38, 0.00561, 44, -0.63, 3.95, 0.85969, 2, 7, 7.83, -7.06, 0.0017, 44, 2.61, 5.83, 0.9983, 1, 44, 6.19, 6.89, 1, 2, 44, 9.77, 5.77, 0.88484, 45, -5.87, 0.51, 0.11516, 2, 44, 13.27, 2.99, 0.42525, 45, -3.51, 4.31, 0.57475, 2, 44, 15.89, -0.47, 0.07836, 45, -0.37, 7.31, 0.92164, 2, 44, 17.3, -4.54, 0.00043, 45, 3.5, 9.17, 0.99957, 2, 45, 10.44, 7.26, 0.84563, 46, -2.9, 7.81, 0.15437, 3, 45, 14.82, 7.42, 0.37904, 46, 1.46, 7.29, 0.61666, 47, -10.06, 9.89, 0.00431, 3, 45, 20.6, 9.48, 0.01881, 46, 7.49, 8.42, 0.83546, 47, -3.93, 9.59, 0.14573, 2, 46, 13.41, 8.86, 0.38731, 47, 1.93, 8.65, 0.61269, 2, 46, 19.78, 8.2, 0.01875, 47, 7.97, 6.53, 0.98125, 1, 47, 12.3, 6.13, 1, 1, 47, 16.63, 5.74, 1, 1, 47, 20.65, 4.11, 1, 1, 47, 18.18, 0.18, 1, 1, 47, 13.03, -5.13, 1, 2, 46, 22.25, -5.45, 0.02489, 47, 7.21, -7.32, 0.97511, 2, 46, 15.99, -7.22, 0.42573, 47, 0.71, -7.59, 0.57427, 2, 46, 10.01, -8.08, 0.92983, 47, -5.3, -7.04, 0.07017, 3, 44, -0.88, -17.55, 0.0013, 45, 18.53, -7.38, 0.12819, 46, 2.82, -7.91, 0.87051, 3, 44, 0.03, -11.67, 0.06353, 45, 12.58, -7.15, 0.65236, 46, -3.02, -6.76, 0.28411, 4, 7, -4.22, -9.81, 0.01324, 44, 0.84, -6.4, 0.45282, 45, 7.26, -6.96, 0.52376, 46, -8.25, -5.75, 0.01018, 3, 7, -3.56, -6.28, 0.22436, 44, -2.22, -4.51, 0.66898, 45, 5.73, -10.21, 0.10666, 3, 7, 0.82, 2.69, 0.55825, 8, -3.09, 0.89, 0.43519, 9, 2.4, 14.19, 0.00655, 2, 7, 0.02, -1.38, 0.84794, 44, -5.51, 0.6, 0.15206, 3, 7, 0.38, -6.01, 0.1304, 44, -1.05, -0.74, 0.85353, 45, 1.85, -9.49, 0.01606, 1, 44, 4.22, 0.66, 1, 1, 45, 1.24, 0.26, 1, 1, 45, 6.55, 0.75, 1, 1, 45, 11.56, 0.02, 1, 1, 46, 4.07, -0.22, 1, 1, 46, 9.54, -0.71, 1, 1, 47, 1.57, -0.57, 1, 1, 47, 7.59, -1.19, 1, 1, 47, 12.49, -0.06, 1, 1, 47, 17.04, 2.38, 1, 2, 8, 1.96, 0.15, 0.99676, 9, 1.13, 9.24, 0.00324, 2, 8, 6.71, 0.49, 0.96413, 9, 0.97, 4.49, 0.03587, 2, 9, 3.4, -0.76, 0.99995, 10, -10.52, -3.7, 5e-05, 3, 9, 9.71, -3.13, 0.93231, 10, -3.81, -4.31, 0.06732, 43, -20.47, -8.38, 0.00038, 3, 9, 15.85, -3.09, 0.2123, 10, 2.1, -2.63, 0.78134, 43, -15.02, -5.54, 0.00637, 3, 9, 22, -3.97, 0.00782, 10, 8.26, -1.83, 0.96297, 43, -9.15, -3.51, 0.0292, 2, 10, 13.93, -1.51, 0.83176, 43, -3.66, -2.05, 0.16824, 1, 43, 2.17, -0.32, 1, 2, 10, 27.21, -1.85, 0.00088, 43, 9.42, 0.31, 0.99912, 1, 43, 15.89, 1.6, 1], "hull": 47, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 0, 92, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 94, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138], "width": 89, "height": 49}}, "tf6": {"tf6": {"type": "mesh", "uvs": [0.39472, 0, 0.29669, 0.02515, 0.22391, 0.06741, 0.18678, 0.10657, 0.18381, 0.18386, 0.17489, 0.27353, 0.16895, 0.36731, 0.12291, 0.46831, 0.04864, 0.55592, 0, 0.65073, 0.00706, 0.74555, 0, 0.83933, 0.01894, 0.92797, 0.06647, 0.98671, 0.23728, 0.99186, 0.39472, 1, 0.5581, 1, 0.71851, 0.98465, 0.86853, 0.94858, 0.93833, 0.88159, 1, 0.80635, 1, 0.75482, 0.94428, 0.70329, 0.92645, 0.57756, 0.85219, 0.45595, 0.7705, 0.35907, 0.73931, 0.26322, 0.72, 0.14985, 0.63682, 0.05195, 0.54919, 0.01175, 0.4675, 0, 0.42888, 0.10657, 0.46007, 0.27353, 0.47492, 0.42812, 0.47789, 0.58993, 0.48829, 0.71875, 0.49423, 0.88983, 0.28184, 0.82594, 0.29817, 0.65485, 0.316, 0.51984, 0.31451, 0.40442, 0.32045, 0.29826, 0.32639, 0.17974, 0.31897, 0.08802, 0.78238, 0.83315, 0.71851, 0.68474, 0.67841, 0.56416, 0.63385, 0.44152, 0.5982, 0.32197, 0.58483, 0.23436, 0.56107, 0.1447, 0.54028, 0.08183], "triangles": [36, 15, 37, 15, 14, 37, 37, 14, 12, 14, 13, 12, 12, 11, 37, 37, 35, 36, 11, 10, 37, 37, 38, 35, 37, 10, 38, 10, 9, 38, 15, 36, 16, 16, 36, 17, 17, 44, 18, 17, 36, 44, 18, 44, 19, 44, 35, 45, 44, 36, 35, 19, 44, 20, 20, 44, 21, 44, 45, 22, 44, 22, 21, 45, 23, 22, 45, 34, 46, 45, 35, 34, 45, 46, 23, 34, 47, 46, 34, 33, 47, 46, 24, 23, 46, 47, 24, 47, 25, 24, 38, 34, 35, 9, 8, 38, 38, 8, 39, 38, 39, 34, 39, 8, 7, 39, 33, 34, 7, 40, 39, 39, 40, 33, 7, 6, 40, 40, 41, 33, 41, 32, 33, 40, 6, 41, 6, 5, 41, 41, 5, 42, 42, 5, 4, 42, 4, 3, 41, 42, 32, 42, 3, 43, 42, 31, 32, 32, 31, 50, 3, 2, 43, 42, 43, 31, 43, 0, 31, 31, 30, 51, 31, 0, 30, 2, 1, 43, 43, 1, 0, 33, 48, 47, 47, 48, 25, 33, 32, 48, 48, 26, 25, 32, 49, 48, 48, 49, 26, 32, 50, 49, 49, 27, 26, 49, 50, 27, 50, 28, 27, 31, 51, 50, 50, 51, 28, 51, 29, 28, 51, 30, 29], "vertices": [2, 54, -35.8, -22.74, 0.19216, 51, -27.47, -0.58, 0.80784, 2, 54, -33.65, -36.79, 0.0379, 51, -19.89, -12.61, 0.9621, 2, 54, -27.52, -48.14, 0.00026, 51, -9.75, -20.59, 0.99974, 1, 51, -1.2, -23.95, 1, 1, 51, 13.71, -21.22, 1, 2, 51, 31.16, -18.78, 0.99691, 52, -20.79, -20.54, 0.00309, 2, 51, 49.31, -15.78, 0.65883, 52, -2.94, -16.07, 0.34117, 2, 51, 69.97, -17.83, 0.01363, 52, 17.82, -16.43, 0.98637, 2, 52, 37.15, -21.21, 0.94112, 53, -17.74, -19.42, 0.05888, 2, 52, 56.85, -22.25, 0.43736, 53, 1.73, -22.56, 0.56264, 2, 52, 74.39, -16.03, 0.02767, 53, 19.83, -18.26, 0.97233, 1, 53, 38.08, -15.87, 1, 2, 56, 34.96, -110.05, 0.00189, 53, 54.7, -10.19, 0.99811, 2, 56, 47.31, -105.38, 0.01508, 53, 64.86, -1.75, 0.98492, 2, 56, 51.78, -82.56, 0.13523, 53, 61.65, 21.28, 0.86477, 3, 55, 100.98, -66.37, 0.00013, 56, 56.56, -61.63, 0.37455, 53, 59.34, 42.62, 0.62532, 2, 56, 59.88, -39.66, 0.65352, 53, 55.32, 64.48, 0.34648, 2, 56, 60.16, -17.64, 0.88152, 53, 48.41, 85.39, 0.11848, 2, 56, 56.22, 3.59, 0.9928, 53, 37.76, 104.17, 0.0072, 1, 56, 44.66, 14.94, 1, 1, 56, 31.33, 25.44, 1, 2, 55, 74.22, 25.12, 0.00427, 56, 21.35, 26.95, 0.99573, 2, 55, 62.59, 20.2, 0.12634, 56, 10.23, 20.97, 0.87366, 2, 55, 38.09, 23.8, 0.91617, 56, -14.5, 22.25, 0.08383, 2, 54, 64.22, 20.31, 0.0379, 55, 12.52, 19.75, 0.9621, 2, 54, 43.39, 13.23, 0.8491, 55, -8.59, 13.55, 0.1509, 1, 54, 24.13, 12.84, 1, 1, 54, 1.84, 14.72, 1, 2, 54, -19.23, 7.48, 0.83773, 51, -24.29, 33.73, 0.16227, 2, 54, -29.34, -2.62, 0.57988, 51, -29.55, 20.45, 0.42012, 2, 54, -33.82, -13.04, 0.35878, 51, -29.51, 9.1, 0.64122, 2, 54, -14.41, -22.37, 0.23077, 51, -7.99, 8.27, 0.76923, 4, 54, 18.5, -24.77, 0.40753, 51, 23.16, 19.17, 0.58935, 52, -31.86, 16.63, 0.0021, 55, -35.05, -23.37, 0.00102, 4, 54, 48.59, -28.87, 0.25754, 51, 52.39, 27.39, 0.23594, 52, -3.39, 27.21, 0.2867, 55, -5.16, -28.72, 0.21982, 6, 54, 79.74, -34.83, 0.00673, 51, 83.34, 34.33, 0.00297, 52, 26.89, 36.64, 0.4412, 55, 25.71, -35.99, 0.43729, 56, -21.22, -38.43, 0.05736, 53, -21.76, 39.2, 0.05445, 4, 52, 50.68, 45.2, 0.21362, 55, 50.55, -40.71, 0.23491, 56, 3.95, -40.81, 0.30129, 53, 2.82, 45.16, 0.25018, 4, 52, 82.59, 55.54, 0.02046, 55, 83.29, -48.02, 0.02084, 56, 37.23, -45.02, 0.5296, 53, 35.65, 52.02, 0.42909, 4, 52, 78.83, 24.29, 0.0267, 55, 64.16, -73.03, 0.02075, 56, 20.53, -71.71, 0.13236, 53, 28.56, 21.35, 0.8202, 4, 52, 46.06, 16.85, 0.62947, 55, 32.16, -62.78, 0.08409, 56, -12.29, -64.5, 0.04435, 53, -4.82, 17.46, 0.24209, 6, 54, 61.87, -53.64, 0.00957, 51, 74.44, 9.95, 0.00235, 52, 20, 11.62, 0.90155, 55, 7.06, -54.04, 0.08139, 56, -38.1, -58.15, 0.00347, 53, -31.28, 15.06, 0.00167, 4, 54, 39.67, -49.31, 0.0224, 51, 52.34, 5.09, 0.58915, 52, -1.62, 4.97, 0.3723, 55, -14.94, -48.77, 0.01615, 3, 54, 19.44, -44.35, 0.00821, 51, 31.82, 1.59, 0.99159, 55, -34.93, -42.96, 0.0002, 1, 51, 8.92, -2.41, 1, 2, 54, -20.97, -36.29, 0.01378, 51, -8.46, -7.11, 0.98622, 4, 52, 60.76, 89.93, 0.00134, 55, 81.97, -7.31, 8e-05, 56, 32.1, -4.61, 0.98633, 53, 17.63, 88.55, 0.01225, 4, 52, 35.36, 73.31, 0.02363, 55, 51.65, -8.72, 0.37708, 56, 2.04, -8.85, 0.58099, 53, -9.4, 74.75, 0.01829, 5, 51, 72.77, 59.97, 0.00045, 52, 14.27, 61.34, 0.04796, 55, 27.39, -8.3, 0.94224, 56, -22.14, -10.71, 0.0045, 53, -31.66, 65.11, 0.00485, 4, 54, 55.49, -8.22, 0.26666, 51, 50.5, 49.09, 0.02547, 52, -7.04, 48.67, 0.04977, 55, 2.6, -8.38, 0.65809, 4, 54, 31.57, -8.27, 0.8992, 51, 28.57, 39.51, 0.09134, 52, -28.12, 37.34, 0.00816, 55, -21.31, -7.43, 0.0013, 2, 54, 14.38, -6.61, 0.90553, 51, 12.15, 34.19, 0.09447, 2, 54, -3.48, -6.26, 0.83311, 51, -4.38, 27.4, 0.16689, 2, 54, -16.12, -6.56, 0.6602, 51, -15.85, 22.09, 0.3398], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 0, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 28, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 36, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102], "width": 136, "height": 196}}, "tf1": {"tf1": {"x": 59.01, "y": -5.63, "rotation": -97.41, "width": 80, "height": 60}}, "yd": {"yd": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-20.25, -20.79, -11.14, 39.53, 26.43, 33.86, 17.33, -26.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 61, "height": 38}}, "ss1": {"ss1": {"type": "mesh", "uvs": [0.74916, 0.48802, 0.82477, 0.46203, 0.86867, 0.4178, 0.92233, 0.33127, 0.9955, 0.22453, 1, 0.14184, 0.9833, 0.05723, 0.88818, 0.00915, 0.70525, 0, 0.57842, 0.02261, 0.49794, 0.09954, 0.39062, 0.19761, 0.34672, 0.27742, 0.24672, 0.32453, 0.11013, 0.30819, 0, 0.31684, 0.00525, 0.35242, 0.09062, 0.38992, 0.16379, 0.46973, 0.23452, 0.54761, 0.27355, 0.63703, 0.33208, 0.72357, 0.37599, 0.8005, 0.41013, 0.85338, 0.40855, 0.92074, 0.43006, 0.97633, 0.5687, 0.99895, 0.75514, 0.99801, 0.88182, 0.97068, 0.89855, 0.91037, 0.77426, 0.82368, 0.76948, 0.78222, 0.7647, 0.70212, 0.75274, 0.62014, 0.73362, 0.53439, 0.74241, 0.09706, 0.63315, 0.19533, 0.54778, 0.29494, 0.47607, 0.39187, 0.4351, 0.45917, 0.46583, 0.55206, 0.49997, 0.64225, 0.52729, 0.72706, 0.56144, 0.80783, 0.58534, 0.8509, 0.63656, 0.9384], "triangles": [43, 31, 30, 44, 43, 30, 45, 44, 30, 45, 30, 29, 28, 45, 29, 24, 44, 45, 44, 24, 23, 25, 24, 45, 27, 45, 28, 26, 25, 45, 26, 45, 27, 0, 39, 38, 39, 18, 38, 0, 40, 39, 19, 18, 39, 34, 40, 0, 19, 39, 40, 40, 34, 33, 20, 19, 40, 41, 40, 33, 20, 40, 41, 41, 33, 32, 21, 20, 41, 42, 41, 32, 21, 41, 42, 42, 32, 31, 22, 21, 42, 43, 42, 31, 22, 42, 43, 23, 22, 43, 23, 43, 44, 35, 8, 7, 35, 7, 6, 9, 8, 35, 10, 9, 35, 35, 6, 5, 36, 10, 35, 36, 35, 5, 11, 10, 36, 4, 36, 5, 37, 11, 36, 12, 11, 37, 3, 36, 4, 37, 36, 3, 16, 15, 14, 17, 16, 14, 17, 14, 13, 38, 12, 37, 13, 12, 38, 2, 37, 3, 38, 37, 2, 38, 17, 13, 38, 18, 17, 1, 38, 2, 1, 0, 38], "vertices": [2, 12, 39.13, 12.75, 0.33712, 13, 6.55, 11.04, 0.66288, 2, 12, 35.62, 14.9, 0.59135, 13, 4.22, 14.43, 0.40865, 2, 12, 30.69, 15.24, 0.7893, 13, -0.14, 16.74, 0.2107, 2, 12, 21.45, 14.65, 0.97871, 13, -8.84, 19.95, 0.02129, 1, 12, 9.96, 14.18, 1, 1, 12, 1.7, 11.79, 1, 1, 12, -6.49, 8.5, 1, 1, 12, -10.1, 3.28, 1, 1, 12, -8.76, -4.16, 1, 1, 12, -4.96, -8.42, 1, 1, 12, 3.66, -9.17, 1, 1, 12, 14.71, -10.32, 1, 1, 12, 23.17, -9.55, 1, 2, 12, 29.07, -11.99, 0.97437, 13, -12.68, -7.49, 0.02563, 2, 12, 29.13, -17.84, 0.97086, 13, -15, -12.86, 0.02914, 2, 12, 31.34, -21.88, 0.97242, 13, -14.62, -17.45, 0.02758, 2, 12, 34.81, -20.57, 0.95895, 13, -10.92, -17.66, 0.04105, 2, 12, 37.48, -16.06, 0.85418, 13, -6.65, -14.62, 0.14582, 2, 12, 44.5, -10.71, 0.33635, 13, 1.94, -12.58, 0.66365, 2, 12, 51.36, -5.52, 0.01302, 13, 10.31, -10.62, 0.98698, 1, 13, 19.74, -10.09, 1, 1, 13, 28.95, -8.73, 1, 2, 13, 37.1, -7.85, 0.997, 14, -3.45, -8.28, 0.003, 2, 13, 42.73, -7.08, 0.64928, 14, 2.19, -7.63, 0.35072, 2, 13, 49.68, -7.94, 0.10696, 14, 9.12, -8.64, 0.89304, 2, 13, 55.52, -7.72, 0.01249, 14, 14.97, -8.54, 0.98751, 1, 14, 18.06, -3.22, 1, 1, 14, 18.99, 4.37, 1, 1, 14, 16.87, 9.9, 1, 1, 14, 10.75, 11.42, 1, 2, 13, 41.35, 8.1, 0.02285, 14, 1.13, 7.57, 0.97715, 2, 13, 37.05, 8.4, 0.33062, 14, -3.17, 7.96, 0.66938, 2, 13, 28.75, 9.15, 0.90361, 14, -11.45, 8.88, 0.09639, 2, 13, 20.22, 9.63, 0.99848, 14, -19.97, 9.54, 0.00152, 2, 12, 43.92, 13.59, 0.07183, 13, 11.27, 9.86, 0.92817, 1, 12, 0.42, 0.31, 1, 1, 12, 11.51, -0.9, 1, 1, 12, 22.44, -1.14, 1, 1, 12, 32.94, -0.92, 1, 2, 12, 40.12, -0.43, 0.00639, 13, 2.11, -1.41, 0.99361, 1, 13, 11.85, -1.25, 1, 1, 13, 21.33, -0.93, 1, 1, 13, 30.22, -0.82, 1, 1, 13, 38.72, -0.38, 1, 2, 13, 43.29, 0.09, 0.04793, 14, 2.9, -0.48, 0.95207, 1, 14, 12.2, 0.38, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 14, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90], "width": 41, "height": 104}}, "pd4111": {"pd4111": {"x": 13.69, "y": 14.33, "rotation": -92.05, "width": 103, "height": 25}}, "ss2": {"ss2": {"type": "mesh", "uvs": [0.19525, 0, 0.39295, 0.00228, 0.47342, 0.05011, 0.51732, 0.15025, 0.56487, 0.25188, 0.59048, 0.31316, 0.70021, 0.28178, 0.85019, 0.28028, 1, 0.30868, 1, 0.35651, 0.94529, 0.40583, 0.85019, 0.47309, 0.79898, 0.54632, 0.72948, 0.59116, 0.73679, 0.6928, 0.73679, 0.79593, 0.75874, 0.82432, 0.85019, 0.86767, 0.8575, 0.95735, 0.70387, 1, 0.53561, 1, 0.371, 0.96781, 0.31979, 0.90055, 0.32345, 0.80938, 0.3015, 0.69878, 0.26127, 0.58817, 0.23932, 0.53138, 0.13324, 0.49252, 0.10764, 0.45067, 0.15885, 0.4133, 0.09666, 0.30419, 0.04179, 0.21153, 0, 0.11886, 0.04545, 0.02918, 0.2418, 0.1013, 0.28391, 0.20363, 0.34283, 0.32266, 0.39371, 0.40867, 0.4038, 0.45967, 0.42678, 0.53031, 0.48124, 0.65825, 0.51676, 0.79338, 0.53939, 0.90888], "triangles": [42, 16, 17, 23, 41, 42, 22, 23, 42, 42, 17, 18, 21, 22, 42, 20, 21, 42, 19, 20, 42, 18, 19, 42, 39, 38, 11, 26, 38, 39, 12, 39, 11, 25, 26, 39, 13, 39, 12, 40, 39, 13, 25, 39, 40, 40, 13, 14, 24, 25, 40, 41, 40, 14, 24, 40, 41, 41, 14, 15, 23, 24, 41, 16, 42, 41, 16, 41, 15, 34, 0, 1, 34, 1, 2, 33, 0, 34, 32, 33, 34, 34, 2, 3, 35, 34, 3, 31, 32, 34, 35, 31, 34, 35, 3, 4, 30, 31, 35, 36, 35, 4, 36, 4, 5, 30, 35, 36, 7, 8, 9, 10, 7, 9, 37, 36, 5, 29, 30, 36, 29, 36, 37, 11, 38, 37, 10, 6, 7, 10, 5, 6, 11, 5, 10, 11, 37, 5, 27, 28, 29, 29, 38, 27, 38, 29, 37, 26, 27, 38], "vertices": [1, 17, -7.92, -1.34, 1, 1, 17, -6.24, 5.99, 1, 1, 17, -1.28, 8.12, 1, 1, 17, 8.18, 7.93, 1, 2, 17, 17.8, 7.86, 0.99772, 18, -21.08, 6.81, 0.00228, 2, 17, 23.58, 7.7, 0.93314, 18, -15.3, 6.76, 0.06686, 2, 17, 21.53, 12.36, 0.83823, 18, -17.44, 11.38, 0.16177, 2, 17, 22.51, 17.97, 0.78421, 18, -16.56, 17.01, 0.21579, 2, 17, 26.21, 23.04, 0.74179, 18, -12.96, 22.15, 0.25821, 2, 17, 30.58, 22.17, 0.70846, 18, -8.58, 21.36, 0.29154, 2, 17, 34.67, 19.24, 0.61726, 18, -4.43, 18.5, 0.38274, 2, 17, 40.1, 14.47, 0.31977, 18, 1.08, 13.83, 0.68023, 2, 17, 46.39, 11.23, 0.04145, 18, 7.44, 10.71, 0.95855, 2, 17, 49.97, 7.82, 0.00047, 18, 11.08, 7.37, 0.99953, 1, 18, 20.43, 5.97, 1, 1, 18, 29.87, 4.27, 1, 2, 18, 32.62, 4.62, 0.98668, 19, -3.34, 4.57, 0.01332, 2, 18, 37.2, 7.33, 0.50086, 19, 1.17, 7.4, 0.49914, 2, 18, 45.46, 6.12, 0.01072, 19, 9.46, 6.42, 0.98928, 1, 19, 12.5, 0.05, 1, 1, 19, 11.54, -6.27, 1, 1, 19, 7.64, -12, 1, 2, 18, 36.63, -13.05, 0.0715, 19, 1.16, -12.99, 0.9285, 2, 18, 28.31, -11.41, 0.58292, 19, -7.2, -11.57, 0.41708, 2, 18, 18.04, -10.41, 0.97137, 19, -17.49, -10.85, 0.02863, 2, 17, 46.22, -9.57, 0.08579, 18, 7.65, -10.09, 0.91421, 2, 17, 40.87, -9.36, 0.43586, 18, 2.3, -9.97, 0.56414, 2, 17, 36.54, -12.6, 0.81094, 18, -1.97, -13.3, 0.18906, 2, 17, 32.53, -12.8, 0.91108, 18, -5.97, -13.57, 0.08892, 2, 17, 29.51, -10.21, 0.97806, 18, -9.05, -11.04, 0.02194, 1, 17, 19.09, -10.54, 1, 1, 17, 10.23, -10.9, 1, 1, 17, 1.47, -10.77, 1, 1, 17, -6.37, -7.45, 1, 1, 17, 1.67, -1.44, 1, 1, 17, 11.31, -1.74, 1, 1, 17, 22.61, -1.7, 1, 1, 17, 30.83, -1.37, 1, 2, 17, 35.56, -1.92, 0.92609, 18, -3.15, -2.64, 0.07391, 2, 17, 42.17, -2.35, 0.08574, 18, 3.47, -2.94, 0.91426, 2, 18, 15.55, -3.02, 0.99899, 19, -20.19, -3.53, 0.00101, 2, 18, 28.15, -3.92, 0.84339, 19, -7.56, -4.09, 0.15661, 2, 18, 38.88, -4.97, 0.00046, 19, 3.18, -4.85, 0.99954], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84], "width": 38, "height": 93}}, "mutou3": {"mutou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.4, -51.34, -68.62, 12.5, -48.9, 51.12, 76.12, -12.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 164, "height": 119}}, "toushi": {"toushi": {"x": 22.43, "y": 7.01, "rotation": -92.05, "width": 55, "height": 33}}, "body": {"body": {"type": "mesh", "uvs": [0.46593, 0, 0.52494, 0.00688, 0.57774, 0.0563, 0.59327, 0.12501, 0.59741, 0.19011, 0.63882, 0.2323, 0.68541, 0.2552, 0.70301, 0.19372, 0.7496, 0.11778, 0.82931, 0.08523, 0.89345, 0.08543, 0.97145, 0.04981, 0.99592, 0.09967, 0.99439, 0.18515, 0.94851, 0.24392, 0.87204, 0.28132, 0.82616, 0.31159, 0.81392, 0.34364, 0.83839, 0.37926, 0.8338, 0.45227, 0.87663, 0.53063, 0.91639, 0.62679, 0.89345, 0.72652, 0.85063, 0.78351, 0.83075, 0.86008, 0.80322, 0.93666, 0.74051, 0.99186, 0.64263, 1, 0.54322, 1, 0.41475, 0.98118, 0.31686, 0.91172, 0.25416, 0.80666, 0.18075, 0.65885, 0.14098, 0.50214, 0.11498, 0.39351, 0.14251, 0.33652, 0.07369, 0.31871, 0.02475, 0.27775, 0, 0.19762, 0, 0.12816, 0.00028, 0.01063, 0.05687, 0.05871, 0.11498, 0.11926, 0.19145, 0.13529, 0.27863, 0.15488, 0.31686, 0.20118, 0.34439, 0.17447, 0.34134, 0.09611, 0.35051, 0.03734, 0.40404, 0.00529, 0.46828, 0.07474, 0.47439, 0.17268, 0.50192, 0.29022, 0.58451, 0.42022, 0.63345, 0.54666, 0.65792, 0.72652, 0.63345, 0.8672, 0.70381, 0.36857, 0.73439, 0.43981, 0.77416, 0.37748, 0.75428, 0.5609, 0.79557, 0.64638, 0.76498, 0.74433, 0.7451, 0.8583, 0.74051, 0.92419, 0.38569, 0.91529, 0.39028, 0.84227, 0.41016, 0.74077, 0.44686, 0.64816, 0.43157, 0.55378, 0.39486, 0.42912, 0.35051, 0.29556, 0.20981, 0.34721, 0.28169, 0.36323, 0.32145, 0.44337, 0.23734, 0.43625, 0.26639, 0.55022, 0.30769, 0.66953, 0.3291, 0.79775, 0.74357, 0.32762, 0.76192, 0.24926, 0.82463, 0.18337, 0.9011, 0.16912, 0.35969, 0.40775, 0.36636, 0.34328, 0.31687, 0.3294, 0.24804, 0.25638, 0.14404, 0.22433, 0.05992, 0.17447, 0.02475, 0.09077], "triangles": [89, 40, 41, 39, 40, 89, 42, 89, 41, 88, 89, 42, 39, 89, 88, 38, 39, 88, 88, 42, 87, 37, 38, 88, 37, 88, 87, 36, 37, 87, 87, 42, 43, 86, 43, 44, 86, 44, 45, 87, 43, 86, 35, 36, 87, 86, 72, 87, 85, 86, 71, 85, 73, 86, 72, 35, 87, 74, 73, 83, 75, 72, 73, 75, 73, 74, 34, 75, 33, 72, 34, 35, 75, 34, 72, 76, 75, 74, 33, 75, 76, 32, 33, 76, 69, 77, 76, 69, 76, 74, 32, 76, 77, 31, 32, 77, 73, 72, 86, 73, 85, 83, 57, 79, 59, 18, 59, 17, 58, 57, 59, 19, 59, 18, 58, 59, 19, 60, 58, 19, 60, 19, 20, 54, 58, 60, 61, 60, 20, 61, 20, 21, 55, 54, 60, 55, 60, 61, 22, 61, 21, 22, 23, 61, 58, 53, 57, 59, 79, 17, 12, 82, 10, 12, 10, 11, 82, 81, 9, 82, 9, 10, 13, 82, 12, 14, 82, 13, 15, 82, 14, 81, 8, 9, 81, 80, 8, 15, 81, 82, 15, 16, 81, 80, 7, 8, 6, 7, 80, 16, 80, 81, 79, 6, 80, 79, 80, 16, 17, 79, 16, 86, 45, 71, 50, 0, 1, 49, 0, 50, 50, 3, 51, 2, 50, 1, 3, 50, 2, 51, 46, 47, 47, 48, 49, 51, 3, 4, 52, 51, 4, 51, 47, 50, 49, 50, 47, 71, 46, 51, 45, 46, 71, 57, 6, 79, 71, 51, 52, 5, 53, 52, 5, 52, 4, 57, 53, 5, 70, 84, 52, 70, 52, 53, 58, 54, 53, 69, 70, 53, 69, 53, 54, 68, 69, 54, 74, 70, 69, 77, 69, 68, 68, 54, 55, 67, 77, 68, 57, 5, 6, 74, 83, 70, 83, 84, 70, 84, 71, 52, 83, 85, 84, 85, 71, 84, 62, 55, 61, 23, 62, 61, 78, 77, 67, 31, 77, 78, 66, 78, 67, 63, 55, 62, 24, 63, 62, 23, 24, 62, 55, 67, 68, 56, 55, 63, 56, 67, 55, 66, 67, 56, 30, 31, 78, 30, 78, 66, 65, 30, 66, 28, 29, 65, 64, 56, 63, 25, 63, 24, 64, 63, 25, 65, 66, 28, 30, 65, 29, 64, 27, 56, 26, 64, 25, 56, 28, 66, 27, 28, 56, 64, 26, 27], "vertices": [1, 5, 15.17, -1.54, 1, 2, 5, 14.03, -6.45, 0.99729, 57, 17.72, 25.23, 0.00271, 2, 4, 43.8, -11.7, 0.01562, 5, 9.87, -10.44, 0.98438, 2, 4, 38.61, -11.69, 0.13313, 5, 4.73, -11.1, 0.86687, 2, 4, 33.93, -10.81, 0.45955, 5, -0.03, -10.83, 0.54045, 3, 4, 30.05, -13.42, 0.75179, 5, -3.54, -13.93, 0.24817, 58, -8.78, 7.22, 5e-05, 2, 4, 27.41, -16.82, 0.86007, 5, -5.71, -17.64, 0.13993, 4, 4, 31.37, -19.42, 0.02081, 5, -1.45, -19.7, 0.0029, 57, 9.27, 6.69, 0.5358, 58, -2.71, 6.32, 0.44049, 3, 57, 15.75, 4.58, 0.04354, 58, 3.74, 8.52, 0.87428, 59, -4.3, 8.15, 0.08218, 2, 58, 10.63, 6.5, 0.35799, 59, 2.75, 6.79, 0.64201, 2, 58, 15.05, 3.31, 0.02508, 59, 7.46, 4.04, 0.97492, 1, 59, 14.49, 2.95, 1, 1, 59, 14.46, -1.24, 1, 1, 59, 11.21, -6.57, 1, 3, 57, 12.06, -14.32, 0.00333, 58, 12.11, -8.82, 0.06516, 59, 5.68, -8.31, 0.9315, 4, 57, 7.5, -8.94, 0.16771, 58, 5.24, -7.25, 0.52405, 59, -1.31, -7.41, 0.30743, 16, 10.06, -10.45, 0.00082, 4, 57, 4.22, -5.89, 0.61895, 58, 0.78, -6.77, 0.30901, 59, -5.79, -7.35, 0.01761, 16, 8.47, -6.26, 0.05443, 2, 59, -7.87, -8.85, 0.00037, 16, 6.32, -4.88, 0.99963, 3, 3, 48.16, -25.31, 0.0002, 58, -1.25, -11.39, 0.00977, 16, 3.43, -6.54, 0.99004, 3, 3, 42.95, -24.13, 0.03804, 57, -5.38, -9.6, 0.04348, 16, -1.78, -5.34, 0.91848, 3, 3, 36.75, -26.88, 0.1803, 4, 3.81, -27.37, 8e-05, 16, -7.98, -8.07, 0.81962, 2, 3, 29.3, -29.17, 0.31501, 16, -15.44, -10.34, 0.68499, 2, 3, 22.39, -26.15, 0.44801, 16, -22.33, -7.31, 0.55199, 3, 3, 18.82, -21.93, 0.59972, 4, -13.46, -20.5, 0.00112, 16, -25.89, -3.08, 0.39916, 2, 3, 13.55, -19.43, 0.76949, 16, -31.16, -0.56, 0.23051, 2, 3, 8.37, -16.28, 0.87139, 16, -36.33, 2.6, 0.12861, 2, 3, 5.18, -10.41, 0.93844, 16, -39.5, 8.48, 0.06156, 2, 3, 5.83, -2.09, 0.99432, 16, -38.82, 16.8, 0.00568, 3, 3, 7.1, 6.26, 0.98484, 4, -22.04, 8.81, 0.00046, 11, -48.44, -22.23, 0.0147, 4, 3, 10.08, 16.85, 0.86845, 4, -17.91, 19.01, 0.03858, 11, -45.42, -11.65, 0.09286, 60, -41.38, 23.06, 0.00011, 4, 3, 16.34, 24.32, 0.68604, 4, -10.87, 25.75, 0.11294, 11, -39.14, -4.19, 0.19591, 60, -32.24, 26.43, 0.00512, 4, 3, 24.72, 28.45, 0.45418, 4, -2.09, 28.94, 0.18314, 11, -30.75, -0.09, 0.34212, 60, -22.92, 25.85, 0.02056, 4, 3, 36.32, 33.01, 0.17762, 4, 9.94, 32.2, 0.15955, 11, -19.14, 4.43, 0.62235, 60, -10.59, 24.04, 0.04048, 4, 3, 48.14, 34.64, 0.03481, 4, 21.86, 32.53, 0.03925, 11, -7.32, 6.04, 0.91664, 60, 0.48, 19.58, 0.00929, 5, 3, 56.31, 35.65, 0.00135, 4, 30.09, 32.63, 0.00033, 11, 0.86, 7.01, 0.93173, 61, 8.99, 13.72, 0.05876, 62, -6.84, 12.05, 0.00782, 3, 11, 4.61, 4.07, 0.56415, 61, 7.91, 9.07, 0.3309, 62, -6.14, 7.33, 0.10495, 3, 11, 6.79, 9.65, 0.13448, 61, 13.89, 9.46, 0.23837, 62, -0.72, 9.89, 0.62715, 3, 11, 10.38, 13.31, 0.04472, 61, 18.72, 7.76, 0.06404, 62, 4.4, 10.07, 0.89124, 2, 11, 16.48, 14.5, 0.00445, 62, 9.65, 6.74, 0.99555, 1, 62, 12.75, 2.74, 1, 1, 62, 18, -4.06, 1, 1, 62, 12.04, -4.23, 1, 2, 61, 14.61, -5.5, 0.04978, 62, 5.43, -3.77, 0.95022, 2, 61, 8.04, -6.2, 0.79692, 62, -0.42, -6.83, 0.20308, 4, 4, 43.36, 14.72, 0.00042, 5, 6.01, 15.7, 0.0243, 60, 13.08, -5.33, 0.20086, 61, 0.53, -6.91, 0.77442, 2, 4, 39.26, 12.45, 0.13559, 5, 2.24, 12.92, 0.86441, 2, 4, 40.55, 9.69, 0.06354, 5, 3.88, 10.35, 0.93646, 2, 5, 9.58, 9.87, 0.976, 60, 13.13, -12.18, 0.024, 1, 5, 13.73, 8.54, 1, 1, 5, 15.47, 3.72, 1, 2, 5, 9.74, -1.04, 0.99949, 57, 11.55, 28.33, 0.00051, 3, 4, 37.84, -1.03, 0.00949, 5, 2.58, -0.63, 0.98854, 57, 4.89, 25.68, 0.00196, 3, 4, 28.94, -1.09, 0.97529, 5, -6.23, -1.84, 0.01194, 57, -2.59, 20.86, 0.01277, 4, 4, 17.97, -5.44, 0.80949, 5, -16.55, -7.58, 0.00041, 57, -9.53, 11.31, 0.12479, 16, 3.76, 15.24, 0.06531, 4, 3, 38.67, -6.26, 0.03447, 4, 7.98, -7.09, 0.70839, 57, -17.08, 4.56, 0.01968, 16, -6, 12.54, 0.23746, 3, 3, 25.38, -6.36, 0.80621, 4, -5.24, -5.73, 0.06945, 16, -19.29, 12.48, 0.12434, 2, 3, 15.54, -2.77, 0.98432, 16, -29.12, 16.09, 0.01568, 4, 4, 19.01, -16.21, 0.48295, 5, -14.12, -18.12, 0.00752, 57, -2.88, 2.78, 0.016, 16, 5.94, 4.65, 0.49353, 3, 3, 45.11, -15.91, 0.00647, 4, 13.32, -17.39, 0.12081, 16, 0.41, 2.87, 0.87273, 2, 4, 16.85, -21.82, 0.00275, 16, 4.39, -1.16, 0.99725, 3, 3, 36.11, -16.26, 0.16529, 4, 4.34, -16.75, 0.1437, 16, -8.59, 2.54, 0.69101, 3, 3, 29.42, -18.8, 0.36516, 4, -2.59, -18.54, 0.05372, 16, -15.29, 0.02, 0.58112, 3, 3, 22.74, -15.16, 0.63273, 4, -8.83, -14.2, 0.02653, 16, -21.96, 3.68, 0.34074, 3, 3, 14.76, -12.25, 0.85173, 4, -16.44, -10.43, 0.00011, 16, -29.92, 6.62, 0.14815, 2, 3, 10.06, -11.15, 0.91091, 16, -34.62, 7.73, 0.08909, 4, 3, 15.21, 18.58, 0.78696, 4, -12.62, 20.17, 0.07485, 11, -40.29, -9.94, 0.1363, 60, -36.08, 22.01, 0.0019, 4, 3, 20.42, 17.4, 0.70395, 4, -7.57, 18.42, 0.12552, 11, -35.08, -11.13, 0.16358, 60, -32.14, 18.39, 0.00695, 4, 3, 27.5, 14.62, 0.51891, 4, -0.84, 14.89, 0.28036, 11, -28.01, -13.93, 0.17666, 60, -27.39, 12.47, 0.02406, 4, 3, 33.71, 10.53, 0.25029, 4, 4.89, 10.14, 0.57922, 11, -21.81, -18.04, 0.12558, 60, -24.03, 5.82, 0.04491, 4, 3, 40.72, 10.78, 0.06383, 4, 11.88, 9.63, 0.68369, 11, -14.8, -17.8, 0.13257, 60, -17.82, 2.56, 0.1199, 4, 3, 50.18, 12.51, 0.00748, 4, 21.48, 10.31, 0.48843, 11, -5.33, -16.1, 0.09457, 60, -8.75, -0.64, 0.40952, 2, 4, 31.87, 11.45, 0.70989, 5, -4.96, 10.97, 0.29011, 3, 11, 2.97, -1.47, 0.78257, 61, 2.2, 8.21, 0.21432, 62, -11.14, 4.44, 0.00311, 4, 3, 56.38, 21.3, 0.00516, 4, 28.6, 18.37, 0.01991, 11, 0.89, -7.33, 0.9615, 61, -3.99, 7.62, 0.01343, 3, 3, 50.09, 18.84, 0.03538, 4, 22.07, 16.61, 0.33953, 11, -5.41, -9.78, 0.62509, 4, 3, 51.67, 25.83, 0.01408, 4, 24.41, 23.38, 0.03401, 11, -3.81, -2.79, 0.76751, 60, -0.84, 10.17, 0.1844, 4, 3, 43.07, 24.63, 0.09008, 4, 15.74, 23.13, 0.17386, 11, -12.41, -3.97, 0.59898, 60, -8.89, 13.41, 0.13708, 4, 3, 33.94, 22.46, 0.26469, 4, 6.42, 21.97, 0.26496, 11, -21.55, -6.11, 0.40676, 60, -17.9, 16.06, 0.06359, 4, 3, 24.41, 22.05, 0.53224, 4, -3.1, 22.61, 0.18318, 11, -31.08, -6.49, 0.26711, 60, -26.37, 20.45, 0.01746, 3, 4, 21.04, -20.24, 0.00602, 5, -11.59, -21.86, 0.00041, 57, 0.99, 0.45, 0.99358, 4, 4, 26.16, -23.22, 0.0027, 5, -6.12, -24.14, 0.00036, 57, 6.91, 0.69, 0.89194, 58, -1, 0.11, 0.105, 2, 58, 6.13, 0.91, 0.91454, 59, -1.2, 0.8, 0.08546, 2, 57, 16.06, -8.83, 0.00018, 59, 4.94, -1.57, 0.99982, 4, 3, 52.17, 15.23, 0.01515, 4, 23.75, 12.8, 0.66486, 11, -3.33, -13.39, 0.29599, 60, -5.67, 0.73, 0.024, 4, 3, 56.74, 13.97, 0.00068, 4, 28.16, 11.04, 0.93361, 5, -8.59, 10.08, 0.03733, 11, 1.23, -14.66, 0.02837, 3, 5, -7.04, 14.12, 0.00011, 11, 2.87, -10.66, 0.0013, 60, 1.08, 0.03, 0.99859, 3, 11, 9.03, -5.69, 0.02127, 60, 8.89, 1.31, 0.23575, 61, 0.94, 0.93, 0.74298, 3, 11, 12.69, 2.69, 0.02348, 61, 10.08, 1.17, 0.82225, 62, -1.22, 0.78, 0.15427, 2, 11, 17.37, 9.21, 0.00219, 62, 6.66, 2.28, 0.99781, 1, 62, 12.77, -0.71, 1], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 0, 98, 0, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 12, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 58, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 144, 146, 146, 148, 150, 152, 152, 154, 154, 156, 158, 160, 160, 162, 162, 164, 148, 166, 140, 168, 168, 142, 166, 168, 166, 170, 170, 172, 172, 174, 174, 176, 176, 178], "width": 85, "height": 73}}, "jio1": {"jio1": {"type": "mesh", "uvs": [0.63875, 0, 0.83689, 0.02591, 0.9954, 0.08467, 0.98219, 0.18096, 0.96568, 0.28053, 0.86992, 0.37846, 0.78405, 0.46578, 0.72461, 0.52209, 0.71471, 0.56616, 0.64866, 0.6192, 0.60243, 0.68612, 0.61894, 0.75141, 0.63677, 0.80433, 0.54821, 0.86178, 0.66721, 0.89187, 0.80316, 0.92569, 0.8813, 0.9627, 0.81618, 0.99006, 0.67292, 0.9965, 0.46455, 1, 0.33106, 0.96994, 0.25618, 0.92891, 0.09013, 0.90155, 0.10967, 0.85408, 0.03804, 0.81707, 0, 0.75673, 0.00874, 0.69316, 0.03478, 0.62155, 0.07306, 0.57754, 0.10967, 0.53546, 0.10316, 0.47029, 0.08688, 0.35845, 0.0413, 0.25627, 0.06936, 0.14557, 0.15494, 0.04722, 0.38601, 0, 0.58433, 0.06665, 0.54226, 0.16817, 0.48279, 0.26896, 0.44167, 0.37365, 0.42786, 0.46934, 0.4214, 0.54688, 0.37461, 0.6188, 0.33658, 0.7054, 0.32819, 0.7766, 0.3388, 0.85382, 0.43622, 0.90609, 0.58463, 0.94258], "triangles": [46, 13, 14, 21, 45, 46, 47, 46, 14, 47, 14, 15, 20, 21, 46, 20, 46, 47, 17, 15, 16, 47, 15, 17, 18, 47, 17, 19, 20, 47, 18, 19, 47, 23, 24, 45, 13, 45, 12, 46, 45, 13, 45, 22, 23, 21, 22, 45, 36, 35, 0, 36, 0, 1, 34, 35, 36, 36, 33, 34, 37, 33, 36, 36, 1, 2, 2, 37, 36, 3, 37, 2, 32, 33, 37, 38, 32, 37, 3, 38, 37, 4, 38, 3, 31, 32, 38, 39, 31, 38, 5, 38, 4, 39, 38, 5, 6, 39, 5, 39, 30, 31, 40, 39, 6, 40, 30, 39, 7, 40, 6, 29, 30, 40, 24, 44, 45, 41, 29, 40, 41, 40, 7, 8, 41, 7, 41, 28, 29, 9, 42, 41, 42, 28, 41, 8, 9, 41, 27, 28, 42, 10, 42, 9, 43, 27, 42, 43, 42, 10, 26, 27, 43, 43, 10, 11, 25, 26, 43, 44, 25, 43, 44, 43, 11, 44, 11, 12, 24, 25, 44, 45, 44, 12], "vertices": [1, 28, -12.3, 4.75, 1, 1, 28, -8.12, 13.44, 1, 1, 28, 1.83, 20.65, 1, 1, 28, 18.59, 20.73, 1, 1, 28, 35.93, 20.7, 1, 1, 28, 53.12, 17.24, 1, 2, 29, -5.38, 14.11, 0.1268, 28, 68.45, 14.14, 0.8732, 2, 29, 4.42, 12, 0.72762, 28, 78.34, 11.97, 0.27238, 2, 29, 12.02, 11.92, 0.98209, 28, 86.01, 11.84, 0.01791, 1, 29, 21.27, 9.5, 1, 1, 29, 32.88, 8.05, 1, 2, 29, 44.07, 9.27, 0.99996, 30, -9.29, 16.4, 4e-05, 2, 29, 53.14, 10.46, 0.91348, 30, -0.91, 12.75, 0.08652, 2, 29, 63.2, 7.11, 0.1098, 30, 5.99, 4.7, 0.8902, 1, 30, 12.98, 6.74, 1, 1, 30, 20.89, 9.12, 1, 1, 30, 28.09, 9.04, 1, 1, 30, 30.91, 4.34, 1, 1, 30, 28.95, -1.6, 1, 1, 30, 25.22, -9.77, 1, 1, 30, 17.94, -12.36, 1, 2, 29, 75.31, -4.91, 6e-05, 30, 10.19, -11.83, 0.99994, 2, 29, 70.93, -12.26, 0.0645, 30, 2.64, -15.88, 0.9355, 2, 29, 62.72, -11.79, 0.47287, 30, -4.15, -11.26, 0.52713, 2, 29, 56.49, -15.16, 0.90039, 30, -11.23, -10.94, 0.09961, 1, 29, 46.19, -17.27, 1, 1, 29, 35.23, -17.4, 1, 1, 29, 22.86, -16.85, 1, 2, 29, 15.22, -15.55, 0.9892, 28, 89.06, -15.65, 0.0108, 2, 29, 7.91, -14.31, 0.86274, 28, 81.69, -14.36, 0.13726, 2, 29, -3.29, -15.1, 0.26835, 28, 70.37, -15.08, 0.73165, 1, 28, 50.95, -16.54, 1, 1, 28, 33.26, -19.19, 1, 1, 28, 13.96, -18.73, 1, 1, 28, -3.28, -15.72, 1, 1, 28, -11.87, -6.11, 1, 1, 28, -0.62, 2.87, 1, 1, 28, 17.1, 1.74, 1, 1, 28, 34.73, -0.13, 1, 1, 28, 53, -1.19, 1, 2, 29, -4.08, -1.16, 0.01508, 28, 69.66, -1.14, 0.98492, 2, 29, 9.27, -0.83, 0.99896, 28, 83.15, -0.89, 0.00104, 1, 29, 21.73, -2.27, 1, 1, 29, 36.71, -3.22, 1, 1, 29, 48.97, -3.02, 1, 1, 30, 0.5, -2.57, 1, 1, 30, 10.41, -3.16, 1, 1, 30, 18.98, -0.53, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 0, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94], "width": 43, "height": 174}}, "jio2": {"jio2": {"type": "mesh", "uvs": [0.35501, 0, 0.56421, 0.02083, 0.73334, 0.09874, 0.75115, 0.23306, 0.71999, 0.36335, 0.64878, 0.4708, 0.64432, 0.55408, 0.62207, 0.60647, 0.58646, 0.69512, 0.55976, 0.76496, 0.63987, 0.81869, 0.62652, 0.88182, 0.77785, 0.90063, 0.97815, 0.90331, 1, 0.94495, 0.92919, 0.99196, 0.70219, 0.99062, 0.51079, 0.96644, 0.23929, 0.97047, 0.14582, 0.94226, 0.16362, 0.86839, 0, 0.8469, 0, 0.77168, 0, 0.69243, 0.10131, 0.68571, 0.10697, 0.64584, 0.11466, 0.59169, 0.10131, 0.54468, 0.07905, 0.47215, 0.04344, 0.37006, 0, 0.26261, 0, 0.14709, 0.03009, 0.04367, 0.17252, 0.00875, 0.36836, 0.08128, 0.39507, 0.19142, 0.37727, 0.28544, 0.37282, 0.37947, 0.37727, 0.45737, 0.37282, 0.55408, 0.34611, 0.59706, 0.34611, 0.68706, 0.33276, 0.77437, 0.32831, 0.8563, 0.38617, 0.91137, 0.58647, 0.93152, 0.75115, 0.94361], "triangles": [45, 44, 11, 45, 11, 12, 46, 45, 12, 17, 44, 45, 16, 45, 46, 17, 45, 16, 46, 13, 14, 13, 46, 12, 15, 46, 14, 16, 46, 15, 11, 44, 10, 44, 43, 10, 20, 44, 19, 44, 20, 43, 18, 44, 17, 18, 19, 44, 39, 27, 38, 6, 39, 5, 26, 27, 39, 40, 26, 39, 7, 39, 6, 40, 39, 7, 25, 26, 40, 41, 40, 7, 25, 40, 41, 24, 25, 41, 8, 41, 7, 9, 41, 8, 22, 23, 24, 42, 24, 41, 42, 41, 9, 22, 24, 42, 20, 21, 22, 22, 42, 20, 43, 20, 42, 9, 10, 42, 42, 10, 43, 34, 0, 1, 33, 0, 34, 34, 1, 2, 35, 34, 2, 35, 2, 3, 35, 30, 31, 32, 33, 34, 34, 31, 32, 36, 35, 3, 31, 34, 35, 36, 30, 35, 4, 36, 3, 29, 30, 36, 37, 29, 36, 37, 36, 4, 5, 38, 37, 4, 5, 37, 28, 29, 37, 28, 37, 38, 27, 28, 38, 39, 38, 5], "vertices": [1, 31, -13.09, 5.02, 1, 1, 31, -8.62, 15.33, 1, 1, 31, 5.26, 22.74, 1, 1, 31, 27.95, 21.61, 1, 1, 31, 49.74, 18.06, 1, 2, 31, 67.5, 12.82, 0.95178, 32, -10.33, 11.39, 0.04822, 2, 31, 81.5, 11.33, 0.20143, 32, 3.64, 12.01, 0.79857, 2, 31, 90.21, 9.41, 0.00237, 32, 12.49, 11.41, 0.99763, 1, 32, 27.45, 10.5, 1, 2, 32, 39.23, 9.85, 0.94198, 33, -0.89, 18.97, 0.05802, 2, 32, 47.99, 14.48, 0.57184, 33, 7.84, 14.29, 0.42816, 2, 32, 58.61, 14.44, 0.05177, 33, 13.74, 5.45, 0.94823, 1, 33, 21.79, 7.63, 1, 1, 33, 30.18, 13.47, 1, 1, 33, 35.31, 8.6, 1, 1, 33, 37.24, 0.14, 1, 1, 33, 27.9, -6.71, 1, 1, 33, 17.68, -9.41, 1, 2, 32, 74.64, -4.37, 0.0004, 33, 7.08, -18.35, 0.9996, 2, 32, 70.2, -9.42, 0.03096, 33, 0.42, -17.48, 0.96904, 2, 32, 57.77, -9.26, 0.67893, 33, -6.4, -7.08, 0.32107, 2, 32, 54.67, -17.81, 0.99185, 33, -15.22, -9.28, 0.00815, 1, 32, 42.06, -18.57, 1, 1, 32, 28.79, -19.38, 1, 1, 32, 27.35, -14.29, 1, 2, 31, 94.49, -17.35, 0.00028, 32, 20.65, -14.41, 0.99972, 2, 31, 85.41, -16.14, 0.06778, 32, 11.56, -14.57, 0.93222, 2, 31, 77.44, -16.11, 0.37017, 32, 3.72, -15.73, 0.62983, 2, 31, 65.13, -16.14, 0.90692, 32, -8.37, -17.6, 0.09308, 1, 31, 47.78, -16.41, 1, 1, 31, 29.5, -16.99, 1, 1, 31, 10.05, -15.24, 1, 1, 31, -7.22, -12.15, 1, 1, 31, -12.45, -4.39, 1, 1, 31, 0.66, 4.47, 1, 1, 31, 19.32, 4.16, 1, 1, 31, 35.06, 1.83, 1, 1, 31, 50.87, 0.18, 1, 2, 31, 64, -0.77, 0.99987, 32, -11.75, -2.56, 0.00013, 2, 31, 80.26, -2.46, 0.01711, 32, 4.47, -1.81, 0.98289, 1, 32, 11.75, -2.73, 1, 1, 32, 26.83, -1.81, 1, 1, 32, 41.5, -1.61, 1, 2, 32, 55.24, -1, 0.65724, 33, -0.95, -0.37, 0.34276, 1, 33, 7.01, -5.92, 1, 1, 33, 17.18, -2.41, 1, 1, 33, 25.09, 1.07, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 68, 68, 70, 70, 72, 72, 74, 72, 60, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 51, "height": 169}}, "xuanz": {"xuanz": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-104.92, 12.46, -21.22, -104.34, 105.49, -13.54, 21.79, 103.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 256}}, "biyan": {"biyan": {"x": 3.01, "y": 3.54, "rotation": -92.05, "width": 51, "height": 20}}, "tf2": {"tf2": {"x": 26.48, "y": 11.71, "rotation": -97.41, "width": 20, "height": 27}}, "tf 41": {"tf-41": {"type": "mesh", "uvs": [0.8211, 1, 0.91183, 0.93579, 1, 0.82234, 1, 0.57337, 0.98042, 0.28028, 0.84323, 0.08174, 0.664, 0, 0.42281, 0, 0.23915, 0.08804, 0.07983, 0.29919, 0, 0.51034, 0.01123, 0.68367, 0.144, 0.79398, 0.32102, 0.80658, 0.47149, 0.89798, 0.55336, 1, 0.73038, 1, 0.8012, 0.78455, 0.73039, 0.49462, 0.50248, 0.31498, 0.24359, 0.39062, 0.10639, 0.5671], "triangles": [19, 20, 8, 19, 8, 7, 9, 8, 20, 21, 9, 20, 10, 9, 21, 11, 10, 21, 13, 12, 21, 11, 21, 12, 13, 20, 19, 13, 21, 20, 19, 7, 6, 18, 6, 5, 19, 6, 18, 14, 13, 19, 18, 5, 4, 18, 4, 3, 17, 18, 3, 18, 14, 19, 17, 3, 2, 17, 14, 18, 1, 17, 2, 15, 14, 17, 16, 15, 17, 0, 16, 17, 1, 0, 17], "vertices": [2, 48, -4, -1.27, 1, 50, -18.11, 22.58, 0, 1, 48, -3.22, -5.97, 1, 1, 48, -0.85, -11.03, 1, 2, 48, 7.01, -13.43, 0.96685, 49, -14.6, -4.27, 0.03315, 2, 48, 16.52, -15.38, 0.65453, 49, -9.35, -12.44, 0.34547, 3, 48, 24.67, -11.12, 0.24289, 49, -0.61, -15.3, 0.75703, 50, -18.74, -7.74, 8e-05, 3, 48, 29.72, -3.86, 0.02072, 49, 8.11, -13.83, 0.91534, 50, -10.28, -10.32, 0.06393, 2, 49, 18.18, -8.62, 0.30971, 50, 1.05, -10.17, 0.69029, 2, 49, 24.52, -2.08, 0.00074, 50, 9.65, -7.15, 0.99926, 1, 50, 17.04, -0.08, 1, 3, 48, 22.73, 30.91, 0.00034, 49, 28.11, 15.46, 0.00031, 50, 20.7, 6.94, 0.99935, 3, 48, 17.1, 32.08, 0.00584, 49, 25.01, 20.3, 0.00806, 50, 20.09, 12.65, 0.9861, 3, 48, 11.8, 27.17, 0.03706, 49, 17.8, 20.67, 0.05042, 50, 13.8, 16.21, 0.91252, 3, 48, 8.97, 19.34, 0.19817, 49, 10.22, 17.22, 0.19373, 50, 5.48, 16.51, 0.60811, 3, 48, 4.02, 13.46, 0.60094, 49, 2.55, 16.65, 0.19931, 50, -1.63, 19.43, 0.19975, 3, 48, -0.32, 10.76, 0.82072, 49, -2.41, 17.88, 0.10225, 50, -5.53, 22.75, 0.07704, 3, 48, -2.76, 2.8, 0.98764, 49, -9.81, 14.06, 0.00731, 50, -13.85, 22.63, 0.00506, 1, 48, 3.07, -2.46, 1, 2, 48, 13.19, -2.07, 0.99684, 49, -2.15, -0.76, 0.00316, 1, 49, 10.09, -1.11, 1, 3, 48, 23.16, 18.81, 0.00528, 49, 19.75, 6.69, 0.01431, 50, 9.3, 2.84, 0.98041, 3, 48, 19.48, 26.68, 0.00809, 49, 22.81, 14.83, 0.0122, 50, 15.67, 8.75, 0.97972], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 47, "height": 33}}, "erduo": {"erduo": {"x": 31.07, "y": 23.72, "rotation": -97.41, "width": 19, "height": 21}}, "wuqi": {"wuqi": {"x": 53.37, "y": -0.81, "rotation": -34.77, "width": 164, "height": 119}}, "xiabai": {"xiabai": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-30.08, -12.22, -25.6, 17.44, 4.06, 12.96, -0.42, -16.7], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 30}}, "pd1": {"pd1": {"type": "mesh", "uvs": [0.03732, 1, 0.13139, 0.92831, 0.23009, 0.81587, 0.32416, 0.6775, 0.40281, 0.5149, 0.46141, 0.35404, 0.50151, 0.20182, 0.52156, 0.10669, 0.66344, 0.10323, 0.66806, 0.2122, 0.68965, 0.35923, 0.73438, 0.53739, 0.78218, 0.70171, 0.85004, 0.84009, 0.91327, 0.91966, 1, 0.97155, 1, 0.8522, 0.97495, 0.7415, 0.93486, 0.59447, 0.8963, 0.46301, 0.85621, 0.31252, 0.82999, 0.17242, 0.80686, 0.05825, 0.76213, 0, 0.71433, 0.00117, 0.66447, 0.05065, 0.66381, 0.09753, 0.52035, 0.10231, 0.46099, 0.028, 0.37352, 0, 0.28243, 0.00286, 0.22278, 0.04646, 0.1794, 0.1816, 0.13844, 0.34646, 0.08784, 0.48836, 0.03362, 0.63295, 0.00972, 0.77227, 0, 0.88189, 0, 1, 0.35213, 0.11485, 0.29384, 0.31736, 0.23698, 0.47044, 0.18295, 0.60279, 0.12751, 0.7479, 0.07064, 0.8659, 0.73696, 0.10437, 0.76711, 0.2753, 0.79558, 0.41242, 0.85084, 0.60026, 0.90443, 0.73363, 0.94128, 0.8745], "triangles": [1, 0, 44, 44, 0, 37, 0, 38, 37, 1, 44, 2, 37, 36, 44, 44, 43, 2, 44, 36, 43, 3, 2, 42, 36, 35, 43, 2, 43, 42, 43, 35, 42, 35, 34, 42, 42, 41, 3, 3, 41, 4, 42, 34, 41, 41, 40, 4, 4, 40, 5, 34, 33, 41, 41, 33, 40, 33, 32, 40, 40, 32, 39, 5, 40, 6, 39, 32, 31, 40, 39, 6, 39, 31, 30, 7, 6, 39, 7, 39, 27, 27, 39, 28, 39, 29, 28, 39, 30, 29, 26, 7, 27, 14, 50, 15, 50, 16, 15, 14, 13, 50, 13, 49, 50, 50, 17, 16, 50, 49, 17, 13, 12, 49, 49, 18, 17, 12, 48, 49, 49, 48, 18, 12, 11, 48, 19, 48, 47, 48, 19, 18, 47, 48, 11, 11, 10, 47, 47, 20, 19, 47, 46, 20, 10, 46, 47, 10, 9, 46, 46, 21, 20, 9, 45, 46, 46, 45, 21, 9, 8, 45, 45, 22, 21, 8, 7, 26, 8, 26, 45, 26, 25, 45, 25, 24, 45, 45, 23, 22, 45, 24, 23], "vertices": [1, 65, 32.28, 0.27, 1, 1, 65, 24.17, 5.09, 1, 2, 64, 34.67, 8.61, 0.00329, 65, 13.16, 8.98, 0.99671, 3, 63, 38.82, 9.69, 0.01471, 64, 22.13, 11.56, 0.50768, 65, 0.58, 11.72, 0.47761, 3, 63, 25.27, 11.66, 0.37604, 64, 8.48, 12.61, 0.62169, 65, -13.09, 12.53, 0.00227, 3, 66, 12.81, -25.53, 3e-05, 63, 12.42, 12.1, 0.98946, 64, -4.37, 12.19, 0.01052, 2, 66, 3.41, -18.49, 0.00528, 63, 0.7, 11.33, 0.99472, 2, 66, -2.61, -14.48, 0.0438, 63, -6.48, 10.46, 0.9562, 2, 66, 1.25, -3.35, 0.98213, 63, -10.79, 21.42, 0.01787, 1, 66, 8.94, -5.8, 1, 2, 66, 19.76, -7.9, 0.52778, 67, -0.46, -8, 0.47222, 2, 67, 13.23, -8.22, 0.97558, 68, -7.47, -7.9, 0.02442, 2, 67, 26.02, -7.89, 0.07289, 68, 5.32, -8.21, 0.92711, 1, 68, 16.85, -6.32, 1, 1, 68, 24.15, -3.34, 1, 1, 68, 30.17, 2.16, 1, 1, 68, 21.85, 5.11, 1, 1, 68, 13.43, 5.89, 1, 2, 67, 22.04, 6.52, 0.28801, 68, 2.07, 6.38, 0.71199, 1, 67, 11.8, 6.24, 1, 2, 66, 21.33, 6.26, 0.49225, 67, 0.18, 6.23, 0.50775, 1, 66, 10.85, 7.83, 1, 1, 66, 2.27, 8.96, 1, 1, 66, -3.07, 6.98, 1, 1, 66, -4.37, 3.23, 1, 2, 66, -2.37, -1.92, 0.99938, 63, -14.47, 20.16, 0.00062, 2, 66, 0.86, -3.18, 0.98323, 63, -11.2, 21.31, 0.01677, 2, 66, -2.95, -14.47, 0.0394, 63, -6.75, 10.25, 0.9606, 2, 66, -9.82, -17.17, 0.00583, 63, -10.21, 3.73, 0.99417, 3, 66, -14.29, -23.26, 0.00066, 63, -9.64, -3.8, 0.98311, 64, -25.31, -5.17, 0.01624, 2, 63, -6.83, -10.82, 0.90532, 64, -22.03, -11.98, 0.09468, 2, 63, -2.09, -14.35, 0.80366, 64, -17.06, -15.18, 0.19634, 2, 63, 8.54, -14.27, 0.35312, 64, -6.46, -14.39, 0.64688, 2, 64, 6.06, -12.51, 0.99978, 65, -15.08, -12.63, 0.00022, 2, 64, 17.36, -12.06, 0.78044, 65, -3.79, -11.98, 0.21956, 2, 64, 28.97, -11.8, 0.08797, 65, 7.81, -11.52, 0.91203, 1, 65, 17.99, -8.95, 1, 1, 65, 25.68, -6.24, 1, 1, 65, 33.6, -2.54, 1, 2, 63, -1.05, -2.53, 0.97281, 64, -16.83, -3.32, 0.02719, 1, 64, -1.17, -1.62, 1, 1, 64, 11.1, -1.3, 1, 2, 64, 21.87, -1.4, 0.34541, 65, 0.54, -1.24, 0.65459, 1, 65, 12.21, -0.86, 1, 1, 65, 22.12, -1.44, 1, 1, 66, 3.45, 2.34, 1, 1, 66, 16.18, 0.28, 1, 2, 66, 26.51, -1.03, 0.00042, 67, 5.82, -0.71, 0.99958, 2, 67, 20.45, -0.28, 0.45306, 68, 0.14, -0.34, 0.54694, 1, 68, 10.93, 0.56, 1, 1, 68, 21.78, -0.03, 1], "hull": 39, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 0, 76, 58, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 48, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100], "width": 83, "height": 74}}, "pd2": {"pd2": {"type": "mesh", "uvs": [0.03253, 0, 0.2211, 0.0007, 0.38872, 0.13005, 0.52701, 0.31453, 0.64015, 0.53506, 0.76586, 0.73226, 0.89158, 0.87645, 1, 1, 0.72815, 0.95279, 0.38872, 0.86797, 0.19177, 0.70045, 0.09958, 0.50537, 0.08282, 0.34209, 0.09958, 0.1979, 0, 0.07915, 0.20434, 0.11944, 0.29653, 0.26788, 0.32167, 0.42055, 0.41806, 0.59019, 0.54377, 0.74711, 0.69882, 0.85949, 0.84548, 0.93583], "triangles": [5, 19, 4, 20, 19, 5, 9, 10, 19, 9, 19, 20, 20, 5, 6, 21, 20, 6, 8, 20, 21, 9, 20, 8, 21, 6, 7, 8, 21, 7, 11, 12, 17, 17, 3, 4, 18, 17, 4, 11, 17, 18, 10, 11, 18, 19, 18, 4, 10, 18, 19, 0, 15, 14, 1, 15, 0, 15, 1, 2, 13, 14, 15, 16, 15, 2, 13, 15, 16, 16, 2, 3, 12, 13, 16, 17, 16, 3, 12, 16, 17], "vertices": [1, 37, -7.37, -5.6, 1, 1, 37, -5.75, 2.16, 1, 1, 37, 6.16, 6.94, 1, 2, 37, 22.32, 9.6, 0.73001, 38, -3.16, 9.96, 0.26999, 2, 38, 15.74, 9.16, 0.99294, 39, -8.85, 10.03, 0.00706, 2, 38, 32.93, 9.43, 0.02498, 39, 8.3, 8.69, 0.97502, 1, 39, 21.38, 9.02, 1, 1, 39, 32.59, 9.32, 1, 1, 39, 24.62, 0.26, 1, 2, 38, 39.08, -9.01, 0.00436, 39, 12.69, -10.25, 0.99564, 2, 38, 23.37, -12.86, 0.71794, 39, -3.32, -12.6, 0.28206, 2, 37, 34.3, -11.12, 0.03296, 38, 6.75, -11.84, 0.96704, 2, 37, 20.88, -9.14, 0.87419, 38, -6.41, -8.55, 0.12581, 1, 37, 9.28, -6.08, 1, 1, 37, -1.2, -8.23, 1, 1, 37, 3.77, -0.48, 1, 2, 37, 16.61, 0.88, 0.99988, 38, -9.69, 1.83, 0.00012, 1, 38, 2.74, -0.86, 1, 1, 38, 17.39, -1.1, 1, 1, 39, 5.89, -0.41, 1, 1, 39, 16.99, 2.06, 1, 1, 39, 25.2, 5.35, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 42, "height": 83}}, "pd3": {"pd3": {"type": "mesh", "uvs": [1, 0.00077, 0.94474, 0.09602, 0.86689, 0.22026, 0.84659, 0.34242, 0.86689, 0.50393, 0.83305, 0.65922, 0.72474, 0.80002, 0.51489, 0.92011, 0.24412, 0.97395, 0, 0.9988, 0.06813, 0.88077, 0.22043, 0.69856, 0.28474, 0.49771, 0.4032, 0.31964, 0.51828, 0.17677, 0.64689, 0.05254, 0.79582, 0, 0.80935, 0.08153, 0.67735, 0.22026, 0.59274, 0.35277, 0.54535, 0.51428, 0.46074, 0.69235, 0.34905, 0.81451, 0.1392, 0.92426], "triangles": [22, 11, 21, 22, 21, 6, 10, 11, 22, 7, 22, 6, 23, 10, 22, 8, 23, 22, 8, 22, 7, 9, 10, 23, 9, 23, 8, 12, 13, 20, 19, 4, 20, 5, 20, 4, 21, 12, 20, 21, 20, 5, 11, 12, 21, 6, 21, 5, 17, 16, 0, 15, 16, 17, 1, 17, 0, 18, 15, 17, 14, 15, 18, 2, 17, 1, 18, 17, 2, 3, 18, 2, 19, 14, 18, 19, 18, 3, 13, 14, 19, 20, 13, 19, 4, 19, 3], "vertices": [1, 34, -12.95, 8.46, 1, 1, 34, -4.36, 8.28, 1, 1, 34, 6.94, 7.75, 1, 2, 34, 17.13, 10.02, 0.96801, 35, -12.56, 8.88, 0.03199, 3, 34, 29.83, 15.33, 0.35133, 35, -0.42, 15.36, 0.64846, 36, -24.93, 12.56, 0.00021, 3, 34, 42.91, 17.81, 0.0185, 35, 12.38, 19.05, 0.88334, 36, -12.65, 17.7, 0.09816, 2, 35, 25.6, 18.7, 0.46293, 36, 0.52, 18.89, 0.53707, 2, 35, 39.33, 12.8, 0.0278, 36, 14.84, 14.62, 0.9722, 1, 36, 25.91, 4.78, 1, 1, 36, 34.14, -5.11, 1, 1, 36, 23.69, -7.12, 1, 2, 35, 28.23, -8.78, 0.08673, 36, 6.32, -8.1, 0.91327, 3, 34, 38.84, -13.57, 0.03445, 35, 11.26, -12.57, 0.94785, 36, -10.1, -13.84, 0.0177, 2, 34, 22.54, -12.48, 0.81334, 35, -5.08, -13.01, 0.18666, 1, 34, 9.13, -10.62, 1, 1, 34, -3, -7.59, 1, 1, 34, -9.67, -1.64, 1, 1, 34, -3.32, 1.21, 1, 1, 34, 10.04, -1.6, 1, 2, 34, 22.11, -2.24, 0.99099, 35, -6.46, -2.85, 0.00901, 1, 35, 7.1, 0.4, 1, 2, 35, 22.73, 2.45, 0.52697, 36, -0.44, 2.42, 0.47303, 1, 36, 11.45, 2.65, 1, 1, 36, 25.02, -2.06, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 52, "height": 85}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"bones": {"bone26": {"rotate": [{"angle": -10.18, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": -36.49, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.0667, "angle": -34.46, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2667, "angle": -10.18}]}, "bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -20.44, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -36.08, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -9.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -80.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -113.18, "curve": "stepped"}, {"time": 0.3667, "angle": -113.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.27}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -92.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.81, "curve": "stepped"}, {"time": 0.3667, "angle": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.44}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -30.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 74.78, "curve": "stepped"}, {"time": 0.3667, "angle": 74.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.62}]}, "bone27": {"rotate": [{"angle": -20.36, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -41.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "angle": -46.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -20.36}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": "stepped"}, {"time": 0.1, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -30.18, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -3.87}]}, "bone45": {"rotate": [{"angle": -19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -16.95, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": 2.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -22.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": -19}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -10.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -26.31, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.5}]}, "bone9": {"rotate": [{"angle": -2.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 3.45, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "angle": -9.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -2.89}]}, "bone44": {"rotate": [{"angle": -16.76, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -20.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.76}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -34.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.6}]}, "bone23": {"rotate": [{"angle": -17, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.0333, "angle": -14.57, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "angle": -11.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -25.15, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5333, "angle": -17}]}, "bone10": {"rotate": [{"angle": -5.3, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 5.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": -7.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.64, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -5.3}]}, "bone28": {"rotate": [{"angle": -5.78, "curve": "stepped"}, {"time": 0.1, "angle": -5.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -32.09, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -5.78}]}, "bone22": {"rotate": [{"angle": -5.66, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.75, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5333, "angle": -5.66}]}, "bone29": {"rotate": [{"angle": -18.41, "curve": "stepped"}, {"time": 0.2, "angle": -18.41, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 0.4667, "angle": -44.73, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -18.41}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.51, "curve": "stepped"}, {"time": 0.3667, "angle": -46.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone24": {"rotate": [{"angle": -26.21, "curve": 0.322, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.0333, "angle": -23.83, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1667, "angle": -16.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -29.13, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.5333, "angle": -26.21}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 68.61, "curve": "stepped"}, {"time": 0.3667, "angle": 68.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.77}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": "stepped"}, {"time": 0.1, "angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 58.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 44.1, "curve": "stepped"}, {"time": 0.4667, "angle": 44.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -4.1}]}, "bone49": {"rotate": [{"angle": -3.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -3.16}]}, "bone5": {"rotate": [{"angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24}]}, "bone19": {"rotate": [{"angle": -2.76}]}, "bone11": {"rotate": [{"angle": 1.66}]}, "bone30": {"rotate": [{"angle": -14.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -40.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.58}]}, "bone4": {"rotate": [{"angle": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.71, "curve": "stepped"}, {"time": 0.3667, "angle": 17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 117.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.39, "curve": "stepped"}, {"time": 0.3667, "angle": 31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.22}]}, "bone50": {"rotate": [{"angle": -16.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -16.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -16.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": -16.19}]}, "bone8": {"rotate": [{"angle": -12.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -25.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -25.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.61}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -18.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -70.9, "curve": "stepped"}, {"time": 0.3667, "angle": -70.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.48}]}, "bone51": {"rotate": [{"angle": -11.55, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -6.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3, "angle": 13.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.28, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -11.55}]}, "bone21": {"rotate": [{"angle": -13.37, "curve": "stepped"}, {"time": 0.1, "angle": -13.37, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 0.3667, "angle": -39.68, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -13.37}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 76.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 38.17, "curve": "stepped"}, {"time": 0.3667, "angle": 38.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.08}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 30.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.55, "curve": "stepped"}, {"time": 0.3667, "angle": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.52}]}, "bone12": {"rotate": [{"angle": -4.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "angle": -30.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.33}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 54.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -19.21, "curve": "stepped"}, {"time": 0.3667, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.69}]}, "bone2": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -24.9, "y": -51.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 48.13, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 36.43, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone46": {"rotate": [{"angle": -16.25, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -12.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "angle": 7.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -16.87, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -16.25}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 28.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -57.6, "curve": "stepped"}, {"time": 0.3667, "angle": -57.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.235, "y": 1.832, "curve": "stepped"}, {"time": 0.3667, "x": 1.235, "y": 1.832, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone55": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -57.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 53.48, "curve": "stepped"}, {"time": 0.3333, "x": 53.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone59": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.55, "curve": "stepped"}, {"time": 0.3667, "angle": -46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 15.13, "y": 86.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 48.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.291, "angle": -13.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone42": {"rotate": [{"angle": -2.93, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.05, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5333, "angle": -2.93}]}, "bone43": {"rotate": [{"angle": -7.76, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -13.05, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.5333, "angle": -7.76}]}, "bone67": {"rotate": [{"angle": -12.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -21.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.48}]}, "bone68": {"rotate": [{"angle": -14.23, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -12.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -21.96, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -14.23}]}, "bone69": {"rotate": [{"angle": -17.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -12.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -21.96, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -17.22}]}}, "events": [{"time": 0.2667, "name": "atk"}]}, "boss_attack3": {"slots": {"xuanz": {"attachment": [{"time": 0.2667, "name": "xuanz"}, {"time": 0.5333, "name": null}]}, "mutou3": {"attachment": [{"time": 0.2667, "name": "mutou"}, {"time": 0.5333, "name": null}]}, "wuqi": {"attachment": [{"time": 0.2667, "name": null}]}}, "bones": {"bone63": {"rotate": [{"time": 0.2667}, {"time": 0.3333, "angle": -120}, {"time": 0.4, "angle": 120}, {"time": 0.4333}, {"time": 0.5333, "angle": -120}, {"time": 0.5667, "angle": 120}, {"time": 0.6667}], "translate": [{"x": -6.78, "y": -184.11, "curve": "stepped"}, {"time": 0.2667, "x": -6.78, "y": -184.11, "curve": 0.321, "c2": 0.51, "c3": 0.75}, {"time": 0.6667, "x": 777.72, "y": -196.09}]}, "bone2": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 5.42, "curve": "stepped"}, {"time": 0.2, "angle": 5.42, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": -28, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -11.2, "y": -18.17, "curve": "stepped"}, {"time": 0.2, "x": -11.2, "y": -18.17, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "x": 8.84, "y": -18.08, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 9.89, "curve": "stepped"}, {"time": 0.2, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone4": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone5": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 9.26, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "angle": 3.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -22.94, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone9": {"rotate": [{"angle": -7.38, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -22.94, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.7, "angle": -7.38}]}, "bone10": {"rotate": [{"angle": -18.14, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -22.94, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.7, "angle": -18.14}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -22.94, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone12": {"rotate": [{"angle": -7.38, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -22.94, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.7, "angle": -7.38}]}, "bone15": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone16": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone17": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone18": {"rotate": [{"angle": -18.14, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -22.94, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.7, "angle": -18.14}]}, "bone22": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone23": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone24": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone25": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone26": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone27": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone28": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone29": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone33": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 15.99, "curve": "stepped"}, {"time": 0.2, "angle": 15.99, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": -39.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone34": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 62.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "angle": -10.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone36": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 37.6, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.1667, "angle": -177.28, "curve": "stepped"}, {"time": 0.2, "angle": -177.28, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 71.15, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.7}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.74, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": 18.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7}]}, "bone44": {"rotate": [{"curve": 0.278, "c3": 0.622, "c4": 0.4}, {"time": 0.1667, "angle": 21.58, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.2519, "angle": 38.51, "curve": 0.328, "c2": 0.32, "c3": 0.663, "c4": 0.66}, {"time": 0.3333, "angle": 36.9, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone45": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -2.01}]}, "bone46": {"rotate": [{"angle": -5.81, "curve": 0.349, "c2": 0.39, "c3": 0.687, "c4": 0.74}, {"time": 0.3333, "angle": -42.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7, "angle": -5.81}]}, "bone49": {"rotate": [{"angle": -3.8, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9, "curve": 0.339, "c2": 0.35, "c3": 0.681, "c4": 0.71}, {"time": 0.7, "angle": -3.8}]}, "bone50": {"rotate": [{"angle": -7.91, "curve": 0.349, "c2": 0.39, "c3": 0.695, "c4": 0.76}, {"time": 0.2, "angle": -2.01, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.7, "angle": -7.91}]}, "bone51": {"rotate": [{"angle": -6.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -9, "curve": 0.335, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.2, "angle": -5.81, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 0.4667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.7, "angle": -6.82}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -37.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone41": {"rotate": [{"angle": -4.82, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.57, "curve": 0.35, "c2": 0.39, "c3": 0.692, "c4": 0.75}, {"time": 0.7, "angle": -4.82}]}, "bone42": {"rotate": [{"angle": -9.03, "curve": 0.36, "c2": 0.45, "c3": 0.7, "c4": 0.81}, {"time": 0.1333, "angle": -1.52, "curve": 0.359, "c2": 0.64, "c3": 0.694}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -16.57, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.7, "angle": -9.03}]}, "bone43": {"rotate": [{"angle": -13.98, "curve": 0.35, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1333, "angle": -4.82, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -16.57, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.7, "angle": -13.98}]}, "bone67": {"translate": [{"x": -1.49, "y": -0.83}]}, "bone70": {"rotate": [{"curve": 0.26, "c3": 0.618, "c4": 0.45}, {"time": 0.2667, "angle": 36.23, "curve": 0.36, "c2": 0.43, "c3": 0.756}, {"time": 0.7}]}}, "deform": {"default": {"mutou3": {"mutou": [{"time": 0.2667, "vertices": [-37.74933, -20.70584, 19.49765, 38.3871, 37.74922, 20.70564, -19.49762, -38.38722]}]}}}, "events": [{"time": 0.4667, "name": "atk"}]}, "boss_idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"translate": [{"x": 0.51, "y": -0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.8, "y": -0.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.51, "y": -0.08}]}, "bone5": {"rotate": [{"angle": 1.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.52, "y": -0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.09, "y": -0.24}]}, "bone8": {"rotate": [{"angle": -10.21, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -16.19, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -10.21}]}, "bone9": {"rotate": [{"angle": 10.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.33, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 10.74}]}, "bone10": {"rotate": [{"angle": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.64}]}, "bone11": {"rotate": [{"angle": 1.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 10.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.66}]}, "bone12": {"rotate": [{"angle": -4.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.33}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.64, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.87}]}, "bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.64, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -9.77}]}, "bone19": {"rotate": [{"angle": -2.76}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -13.64, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -8.6}]}, "bone21": {"rotate": [{"angle": -13.37, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 0.2667, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -13.64, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -13.37}]}, "bone22": {"rotate": [{"angle": -4.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -16.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -4.56}]}, "bone23": {"rotate": [{"angle": -11.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -16.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -11.51}]}, "bone24": {"rotate": [{"angle": -16.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -16.08}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.63, "y": 2.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.82, "y": 1.09}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.22, "y": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.11, "y": -1.69}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.48}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.22}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.62}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.72, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.1}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.04}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.67}]}, "bone49": {"rotate": [{"angle": 7.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.66}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10.7}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 7.67}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone67": {"rotate": [{"angle": -13.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -22.2, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -13.49}]}, "bone68": {"rotate": [{"angle": -2.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -0.41, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.88}]}, "bone69": {"rotate": [{"angle": -3.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 2.62, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.62}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.85, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone27": {"rotate": [{"angle": 2.63, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 10.85, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 2.63}]}, "bone28": {"rotate": [{"angle": 7.32, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 10.85, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 7.32}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.61, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone48": {"rotate": [{"angle": 2.16, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.61, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.16}]}, "1bone63": {"rotate": [{"angle": 5.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.61, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 5.45}]}, "bone164": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.48, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone165": {"rotate": [{"angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.41}]}, "bone166": {"rotate": [{"angle": -6.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.48, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.07}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -12.88, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone42": {"rotate": [{"angle": -3.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -12.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.65}]}, "bone43": {"rotate": [{"angle": -9.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -12.88, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -9.22}]}, "bone70": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 13.95, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone71": {"rotate": [{"angle": 3.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.96}]}, "bone72": {"rotate": [{"angle": 9.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 13.95, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 9.99}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.2, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -23.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 52.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 103.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 84.48}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 11.86, "y": -31.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.74, "y": 42.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.74, "y": 57.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.74, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.7, "y": -124.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 3.7, "y": -99.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 3.7, "y": -124.49}]}, "bone8": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -69.36, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667}]}, "bone9": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667}]}, "bone10": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667}]}, "bone11": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -29.41, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667}]}, "bone12": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667}]}, "bone18": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.5}]}, "bone33": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 45.03, "curve": "stepped"}, {"time": 0.3333, "angle": 45.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -24.05}]}, "bone34": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 30.81, "curve": "stepped"}, {"time": 0.3667, "angle": 30.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.76}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 73.21, "curve": "stepped"}, {"time": 0.3, "angle": 73.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 22.07}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.22, "curve": "stepped"}, {"time": 0.3, "angle": -15.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.67}]}, "bone44": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": 2.35, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone45": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": 2.35, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone46": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": 2.35, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone49": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": -3.02, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone50": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": -3.02, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone51": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": -3.02, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.73}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 16.4}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.98}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.81}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 49.39}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -45.22, "curve": "stepped"}, {"time": 0.3, "angle": -45.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -72.27}]}, "bone55": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 177.46}]}, "bone60": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 139.15}]}, "bone59": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 82.57}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 7.16, "y": 100.94, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 7.16, "y": 132.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 7.16, "y": 55.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 7.16, "y": 3.52}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 82.57}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2413, "x": 7.16, "y": 100.94, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "x": 7.16, "y": 124.74, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "x": 7.16, "y": 51.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 7.16, "y": 5.73}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.1667, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.12, "y": -8.93, "curve": "stepped"}, {"time": 0.2, "x": -1.12, "y": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone4": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone33": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone36": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 62, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone37": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -52.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 29.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 35.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -40.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -39.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}, "run1": {"bones": {"ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone": {"translate": [{"y": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 3.84}]}, "bone22": {"rotate": [{"angle": -1.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -12.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.64}]}, "bone23": {"rotate": [{"angle": -4.58, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0667, "angle": -1.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -4.58}]}, "bone24": {"rotate": [{"angle": -7.94, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": -4.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.94}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone27": {"rotate": [{"angle": -1.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.65, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.26}]}, "bone28": {"rotate": [{"angle": -3.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.65, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.55}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone16": {"rotate": [{"angle": -0.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.31, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -0.43}]}, "bone17": {"rotate": [{"angle": -1.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.31, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -1.22}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone8": {"rotate": [{"angle": -8.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -17.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.05}]}, "bone9": {"rotate": [{"angle": 0.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 3.79, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2333, "angle": -6.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -6.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 0.17}]}, "bone10": {"rotate": [{"angle": -0.45, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 8.09, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3, "angle": -1.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 8.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.73, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -0.45}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.82}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 7.87}]}, "bone11": {"rotate": [{"angle": 36.32}]}, "bone12": {"rotate": [{"angle": 5.22, "curve": 0.34, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 12.11, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.0667, "angle": 5.19, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.2, "angle": -6.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.5333, "angle": -17.92, "curve": 0.339, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 0.6667, "angle": 5.22}]}, "bone18": {"rotate": [{"angle": 0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 6.62, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.62, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 0.04}]}, "bone66": {"rotate": [{"angle": -26.49}]}, "bone38": {"rotate": [{"angle": 16.21}]}, "bone60": {"translate": [{"x": 41.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -103.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -28.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 41.64}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -67.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -50.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"x": 0.48, "y": 33.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.79, "y": 70.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.48, "y": 33.04}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 60.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -58.5}], "translate": [{"x": -1.86, "y": 67.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 16.3, "y": 58.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.7, "y": 52.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.86, "y": 67.28}]}, "bone55": {"translate": [{"x": -65.06, "y": -0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 100.15, "y": 0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 16.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -65.06, "y": -0.4}]}, "bone2": {"rotate": [{"angle": -22.85}]}, "bone35": {"rotate": [{"angle": 35.12}]}, "bone44": {"rotate": [{"angle": 40.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 40.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 24.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 40.08}]}, "bone45": {"rotate": [{"angle": 0.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2333, "angle": -9.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -9.3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 0.57}]}, "bone46": {"rotate": [{"angle": -7.1, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -7.1}]}, "bone49": {"rotate": [{"angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 22.3}]}, "bone50": {"rotate": [{"angle": -5.08, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 0.66, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -14.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -14.95, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -5.08}]}, "bone51": {"rotate": [{"angle": -2, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 11.57, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.3, "angle": -4.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -4.03, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -2}]}, "bone36": {"rotate": [{"angle": 24.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 24.85}]}, "bone37": {"rotate": [{"angle": 25.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 24.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 27.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 25.91}]}, "bone34": {"rotate": [{"angle": 53.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 43.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 69.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 53.22}]}, "bone33": {"rotate": [{"angle": -54.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -54.26}]}, "bone70": {"rotate": [{"angle": 40.39}]}, "bone72": {"rotate": [{"angle": -6.08, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -1.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -13.71, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -6.08}]}, "bone67": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone68": {"rotate": [{"angle": -2.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -7.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -2.68}]}, "bone69": {"rotate": [{"angle": -6.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -7.28, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -6.34}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -12.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone42": {"rotate": [{"angle": -1.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -12.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.64}]}, "bone43": {"rotate": [{"angle": -4.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -4.63}]}}}, "run2": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 75.4}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone22": {"rotate": [{"angle": -8.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -16.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -8.11}]}, "bone23": {"rotate": [{"angle": -14.65, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -4.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.21, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.3667, "angle": -14.65}]}, "bone24": {"rotate": [{"angle": -14.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -16.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": -11.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -14.1}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone18": {"rotate": [{"angle": -17.92, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.0333, "angle": -2.21, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1, "angle": 12.11, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -6.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": -17.92}]}, "bone46": {"rotate": [{"angle": -12.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -20.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -20.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -12.22}]}, "bone51": {"rotate": [{"angle": -13.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "angle": -5.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -13.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.68}]}, "bone50": {"rotate": [{"angle": -22.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0333, "angle": -9.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -31.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -31.13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -22.66}]}, "bone9": {"rotate": [{"angle": -10.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -26.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -10.54}]}, "bone10": {"rotate": [{"angle": 4.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 10.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "angle": -6.18, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 4.06}]}, "bone44": {"rotate": [{"angle": 50.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 20.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 50.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 20.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 50.68}], "translate": [{"x": 4.54, "y": 11.49}]}, "bone11": {"rotate": [{"angle": 72.51}]}, "bone45": {"rotate": [{"angle": -0.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -22.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -0.98}]}, "bone8": {"rotate": [{"angle": -2.07}]}, "bone12": {"rotate": [{"angle": 5.22, "curve": 0.34, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 5.19, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.1, "angle": -6.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3, "angle": -17.92, "curve": 0.339, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 0.3667, "angle": 5.22}]}, "bone49": {"rotate": [{"angle": 35.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 43.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 13.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 43.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 13.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 35.26}]}, "bone70": {"rotate": [{"angle": 59.24}], "translate": [{"x": 6.56, "y": 5.44}]}, "bone68": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone69": {"rotate": [{"angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -3.09}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone72": {"rotate": [{"angle": 2.21, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.8, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 2.21}]}, "bone67": {"rotate": [{"angle": -2.63}], "translate": [{"x": -1.78, "y": -1.79}]}, "bone26": {"rotate": [{"angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 15.38}]}, "bone27": {"rotate": [{"angle": 10.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 10.82}]}, "bone28": {"rotate": [{"angle": 7.55, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 7.55}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone17": {"rotate": [{"angle": -9.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -25.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -9.3}]}, "bone130": {"rotate": [{"angle": 3.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 3.5}]}, "bone40": {"rotate": [{"angle": 8.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": 8.28}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone42": {"rotate": [{"angle": -4.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.21, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -4.6}]}, "bone43": {"rotate": [{"angle": -11.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -11.61}]}}, "deform": {"default": {"yd": {"yd": [{"vertices": [8.56131, -13.30523, -0.36668, -12.62235, 0.05881, -7.06073, 8.98657, -7.74365]}]}, "xiabai": {"xiabai": [{"vertices": [1.20261, -8.87585, 1.20261, -8.87585, 1.20261, -8.87585, 1.20261, -8.87585]}]}}}}, "show_time": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 75.4}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone22": {"rotate": [{"angle": -8.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -16.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -8.11}]}, "bone23": {"rotate": [{"angle": -14.65, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -4.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.21, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.3667, "angle": -14.65}]}, "bone24": {"rotate": [{"angle": -14.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -16.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": -11.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -14.1}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone18": {"rotate": [{"angle": -17.92, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.0333, "angle": -2.21, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1, "angle": 12.11, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -6.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": -17.92}]}, "bone46": {"rotate": [{"angle": -12.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -20.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -20.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -12.22}]}, "bone51": {"rotate": [{"angle": -13.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "angle": -5.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -13.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.68}]}, "bone50": {"rotate": [{"angle": -22.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0333, "angle": -9.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -31.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -31.13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -22.66}]}, "bone9": {"rotate": [{"angle": -10.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -26.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -10.54}]}, "bone10": {"rotate": [{"angle": 4.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 10.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "angle": -6.18, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 4.06}]}, "bone44": {"rotate": [{"angle": 50.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 20.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 50.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 20.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 50.68}], "translate": [{"x": 4.54, "y": 11.49}]}, "bone11": {"rotate": [{"angle": 72.51}]}, "bone45": {"rotate": [{"angle": -0.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -22.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -0.98}]}, "bone8": {"rotate": [{"angle": -2.07}]}, "bone12": {"rotate": [{"angle": 5.22, "curve": 0.34, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 5.19, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.1, "angle": -6.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3, "angle": -17.92, "curve": 0.339, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 0.3667, "angle": 5.22}]}, "bone49": {"rotate": [{"angle": 35.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 43.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 13.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 43.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 13.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 35.26}]}, "bone70": {"rotate": [{"angle": 59.24}], "translate": [{"x": 6.56, "y": 5.44}]}, "bone68": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone69": {"rotate": [{"angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -3.09}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone72": {"rotate": [{"angle": 2.21, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.8, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 2.21}]}, "bone67": {"rotate": [{"angle": -2.63}], "translate": [{"x": -1.78, "y": -1.79}]}, "bone26": {"rotate": [{"angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 15.38}]}, "bone27": {"rotate": [{"angle": 10.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 10.82}]}, "bone28": {"rotate": [{"angle": 7.55, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 7.55}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone17": {"rotate": [{"angle": -9.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -25.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -9.3}]}, "bone130": {"rotate": [{"angle": 3.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 3.5}]}, "bone40": {"rotate": [{"angle": 8.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": 8.28}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone42": {"rotate": [{"angle": -4.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.21, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -4.6}]}, "bone43": {"rotate": [{"angle": -11.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -11.61}]}}, "deform": {"default": {"yd": {"yd": [{"vertices": [8.56131, -13.30523, -0.36668, -12.62235, 0.05881, -7.06073, 8.98657, -7.74365]}]}, "xiabai": {"xiabai": [{"vertices": [1.20261, -8.87585, 1.20261, -8.87585, 1.20261, -8.87585, 1.20261, -8.87585]}]}}}}}}