import { _decorator, Label, Node, ProgressB<PERSON>, Slider } from "cc";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { UINode } from "../../../../../lib/ui/UINode";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import TipMgr from "../../../../../lib/tips/TipMgr";
import TickerMgr from "../../../../../lib/ticker/TickerMgr";
import { PlayerModule } from "../../../../player/PlayerModule";
import { PupilModule } from "../../PupilModule";
import { PlayerRouteName } from "../../../../player/PlayerConstant";
import { GoodsAddRequest } from "../../../../../game/net/protocol/Goods";
import { GoodsModule } from "../../../../goods/GoodsModule";
import { ItemEnum } from "../../../../../lib/common/ItemEnum";
import FmUtils from "../../../../../lib/utils/FmUtils";
import ToolExt from "../../../../../game/common/ToolExt";
import { PupilHeader } from "./PupilHeader";
import { ItemCost } from "../../../../../game/common/ItemCost";
import { IConfigPupil } from "../../PupilData";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PupilAudioName } from "../../PupilConstant";
const { ccclass, property } = _decorator;

@ccclass("UIAddVitalityPop")
export class UIAddVitalityPop extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIAddVitalityPop`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  // 进度条为1 对应的 数量
  private _joinMax: number = 0;

  /**补充活力列表 */
  private slotIndexAndCountMap: { [key: number]: number } = Object.create(null);

  /**定时器id */
  private _recoverTickId: number = null;

  /**弟子补充活力道具id */
  private _needItemId: number = ItemEnum.活力单_1005;

  public init(args: any): void {
    super.init(args);
  }

  protected onEvtShow(): void {
    let itemNum = PlayerModule.data.getItemNum(this._needItemId);
    this._joinMax = itemNum * 5 < 100 ? 100 : itemNum * 5;
    this.refresh();
  }

  protected onEvtClose(): void {
    TickerMgr.clearInterval(this._recoverTickId);
  }

  private refresh() {
    // 资源图标
    ToolExt.setItemIcon(this.getNode("bg_item_icon"), this._needItemId);

    // 剩余数量
    let itemNum = PlayerModule.data.getItemNum(this._needItemId);

    // 更新资源数量
    this.getNode("lbl_res_num").getComponent(Label).string = PlayerModule.data.getItemNum(this._needItemId) + "";

    // 刷新训练槽
    for (let i = 0; i < 5; i++) {
      // 槽位节点
      const nodeSlot = this.getNode("layout_slots").children[i];

      if (i >= PupilModule.data.pupilTrainMsg.trainSlotList.length) {
        nodeSlot.active = false;
        continue;
      }

      let slotInfo = PupilModule.data.pupilTrainMsg.trainSlotList[i];

      const trainMax = slotInfo.vitalitySize;

      // 弟子信息
      const pupilMsg = PupilModule.data.allPupilMap[slotInfo.pupilId];
      let cfgThis: IConfigPupil;
      if (pupilMsg) {
        // 弟子头像
        nodeSlot.getChildByName("PupilHeader").getComponent(PupilHeader).setHeaderByNameId(pupilMsg.ownInfo.nameId);

        // 弟子名称
        const lblName = nodeSlot.getChildByPath("bg_name/lbl_pupil_name").getComponent(Label);
        lblName.string = PupilModule.service.getPupilName(pupilMsg.ownInfo.nameId);

        // 弟子天资信息
        cfgThis = PupilModule.data.getConfigPupil(pupilMsg.ownInfo.talentId);

        // 当前培养进度
        nodeSlot.getChildByName("lbl_progress").getComponent(Label).string = `当前培养进度:${
          cfgThis.trainAdd * slotInfo.train
        }/${cfgThis.finish}`;
      } else {
        nodeSlot.active = false;
        continue;
      }

      // 当前剩余体力
      nodeSlot.getChildByPath("center_bg/Layout/lbl_now").getComponent(Label).string = `${slotInfo.vitality}`;

      // 还需要次数
      let vNeed = Math.ceil(cfgThis.finish / cfgThis.trainAdd) - slotInfo.train - slotInfo.vitality;
      let itemNeed = Math.ceil(vNeed / 5);
      itemNeed = Math.max(itemNeed, 0);

      itemNeed = Math.min(itemNeed, itemNum);
      // 当前需要的体力值
      itemNum -= itemNeed;
      if (itemNum < 0) {
        itemNum = 0;
      }

      this.onAddChange(i, this.slotIndexAndCountMap[i] || itemNeed);

      // 下一次回复cd
      const showCd = slotInfo.vitality < trainMax;
      nodeSlot.getChildByName("lbl_cd").active = showCd;
      nodeSlot.getChildByName("lbl_time").active = showCd;

      if (showCd) {
        let newTime = slotInfo.lastUpdateTime + cfgThis.time * 1000;
        FmUtils.setCd(nodeSlot.getChildByName("lbl_cd"), newTime, false, () => {
          PupilModule.api.getTrain(() => {
            this.refresh();
          });
        });
      }

      // 绑定减少按钮事件
      nodeSlot.getChildByName("btn_minus").on(Node.EventType.TOUCH_END, () => {
        this.onAddChange(i, (this.slotIndexAndCountMap[i] || 0) - 1);
      });

      // 绑定增加按钮事件
      nodeSlot.getChildByName("btn_add").on(Node.EventType.TOUCH_END, () => {
        this.onAddChange(i, (this.slotIndexAndCountMap[i] || 0) + 1);
      });

      // 绑定滑动组件事件
      nodeSlot.getChildByName("Slider").on("slide", (slider: Slider) => {
        this.onAddChange(i, Math.round((slider.progress * this._joinMax) / 5));
      });
    }
  }

  private costNum() {
    let numCostAll = 0;
    for (let i = 0; i < 5; i++) {
      numCostAll += this.slotIndexAndCountMap[i] || 0;
    }
    return numCostAll; //Math.max(1, numCostAll);
  }

  private onAddChange(slotIdx: number, num: number) {
    num = num < 1 ? 1 : num;
    num = num * 5 > this._joinMax ? this._joinMax / 5 : num;

    // 实际数量
    this.slotIndexAndCountMap[slotIdx] = num;

    // 节点
    const nodeSlot = this.getNode("layout_slots").children[slotIdx];

    // 进度组件
    const progressBar = nodeSlot.getComponentInChildren(ProgressBar);
    progressBar.progress = (num * 5) / this._joinMax;
    const slider = nodeSlot.getComponentInChildren(Slider);
    slider.progress = progressBar.progress;

    // 消耗量
    const lblCostNum = nodeSlot.getChildByPath("Slider/Handle/lbl_cost_num").getComponent(Label);
    lblCostNum.string = `${num}`;

    // 增加的量
    nodeSlot.getChildByPath("center_bg/Layout/lbl_add").getComponent(Label).string = `+${num * 5}`;

    // 计算总量
    let numCostAll = this.costNum();
    // 玩家总量
    // const playerHas = PlayerModule.data.getItemNum(ItemEnum.活力单_1005);

    // 总消耗label
    this.getNode("ItemCost").getComponent(ItemCost).setItemId(ItemEnum.活力单_1005, numCostAll);
  }

  private on_click_btn_use() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击使用体力丹按钮);
    for (const key in this.slotIndexAndCountMap) {
      if (this.slotIndexAndCountMap[key] == 0) {
        delete this.slotIndexAndCountMap[key];
      }
    }
    let numCostAll = this.costNum();
    if (numCostAll <= 0) {
      TipMgr.showTip(`请选择使用数量`);
      return;
    }

    // this._needItemId 活力丹
    if (PlayerModule.data.getItemNum(this._needItemId) < numCostAll) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: this._needItemId,
        needNum: numCostAll,
      });
      return;
    }

    PupilModule.api.addVitality(this.slotIndexAndCountMap, () => {
      UIMgr.instance.back();
    });
  }

  private on_click_btn_add_res() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
      itemId: this._needItemId,
    });
  }

  private on_click_btn_test1() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let data: GoodsAddRequest = {
      resId: this._needItemId,
      num: 100,
    };
    GoodsModule.api.addRes(data, () => {
      this.refresh();
    });
  }
}
