import { _decorator, Component, instantiate, Label, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { RankReward } from "db://assets/GameScrpit/module/fracture/FractureConstant";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { divide } from "db://assets/GameScrpit/lib/utils/NumbersUtils";
const { ccclass, property } = _decorator;

@ccclass("FractureFightAwardPreviewViewHolder")
export class FractureFightAwardPreviewViewHolder extends ViewHolder {
  @property(Label)
  private lblRate: Label;
  @property(Node)
  private item: Node;

  start() {}

  update(deltaTime: number) {}
  updateData(item: number[]) {
    //
    this.lblRate.getComponent(Label).string = divide(item[2], 100) + "%";
    FmUtils.setItemNode(this.item, item[0], item[1]);
  }
}

export class FractureFightAwardPreviewAdapter extends ListAdapter {
  private _item: Node;
  private _data: number[][];
  constructor(item: Node) {
    super();
    this._item = item;
  }
  public setData(data: number[][]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(FractureFightAwardPreviewViewHolder).updateData(this._data[position]);
  }
  getCount(): number {
    return this._data.length;
  }
}
