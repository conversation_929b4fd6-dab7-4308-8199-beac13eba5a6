import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import MsgMgr from "../../lib/event/MsgMgr";
import { Hero<PERSON><PERSON> } from "./HeroApi";
import { HeroConfig } from "./HeroConfig";
import { HeroData } from "./HeroData";
import { HeroRoute } from "./HeroRoute";
import { HeroService } from "./HeroService";
import { HeroSubScriber } from "./HeroSubScriber";
import { HeroViewModel } from "./HeroViewModel";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HeroModule extends data {
  private constructor() {
    super();
  }

  protected saveKey(): string {
    return "HeroModule";
  }
  public static get instance(): HeroModule {
    if (!GameData.instance.HeroModule) {
      GameData.instance.HeroModule = new HeroModule();
    }
    return GameData.instance.HeroModule;
  }
  private _api = new HeroApi();
  private _data = new HeroData();
  private _viewModel = new HeroViewModel();
  private _config = new HeroConfig();
  private _service = new HeroService();
  private _route = new HeroRoute();
  private _subscriber = new HeroSubScriber();

  public static get api() {
    return this.instance._api;
  }
  public static get data() {
    return this.instance._data;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }
  public static get service() {
    return this.instance._service;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._api = new HeroApi();
    this._data = new HeroData();
    this._viewModel = new HeroViewModel();
    this._config = new HeroConfig();
    this._service = new HeroService();
    this._route = new HeroRoute();

    // 初始化模块
    this._data.init(); //清除脏数据

    this._subscriber.register();

    HeroModule.api.getAll(() => {
      this._route.init();
      completedCallback && completedCallback();
      HeroModule.api.getPicture(() => {
        let attrs = HeroModule.data.getAllHeroPictureAttrMap();
        log.log("图鉴属性", attrs);
      }); //获得图鉴信息
    });
    HeroModule.service.init();
  }
  public notifyDataChange(data?: any) {
    MsgMgr.emit(this.saveKey(), data);
  }
  public unSubscribeData(callback: (...any: any[]) => void) {
    MsgMgr.off(this.saveKey(), callback, this);
    log.log("unSubscribeData");
  }
  public subscribeData(callback: (...any: any[]) => void) {
    MsgMgr.on(this.saveKey(), callback, this);
  }
}
