{"skeleton": {"hash": "pYL2sMestQoj5QCveshdGXiQUVg=", "spine": "3.8.75", "x": -114, "y": -12.98, "width": 296.05, "height": 126, "images": "./images/", "audio": "D:/spine导出/号召角色新增/人族（从马车下来）"}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "x": -0.47, "y": -16.38}, {"name": "chelun", "parent": "all", "x": 66.82, "y": 36.37}, {"name": "chelun2", "parent": "chelun", "x": -1.59, "scaleX": 0.9258}, {"name": "chelun3", "parent": "chelun2", "x": 0.04, "y": -0.01}, {"name": "chelun4", "parent": "all", "x": 42.57, "y": 38.27, "scaleX": 0.9398, "scaleY": 0.9398}, {"name": "chelun5", "parent": "chelun4", "x": -1.59, "scaleX": 0.9258}, {"name": "chelun6", "parent": "chelun5", "x": 0.04, "y": -0.01}, {"name": "all2", "parent": "all", "length": 15.43, "rotation": 6.67, "x": -53.89, "y": 62.01}, {"name": "all3", "parent": "all", "length": 15, "rotation": 158.2, "x": -61.45, "y": 63.21}, {"name": "all4", "parent": "all3", "length": 16.46, "rotation": -60.56, "x": 15}, {"name": "all5", "parent": "all4", "length": 15.62, "rotation": 26.45, "x": 16.46}, {"name": "all6", "parent": "all5", "length": 10.99, "rotation": 91.32, "x": 15.46, "y": 0.11}, {"name": "all7", "parent": "all2", "length": 12.47, "rotation": -79.97, "x": 23.89, "y": 7.23}, {"name": "all8", "parent": "all7", "length": 10.06, "rotation": 7.84, "x": 12.47}, {"name": "all9", "parent": "all8", "length": 14.76, "rotation": -34.64, "x": 10.15, "y": 0.18}, {"name": "all10", "parent": "all3", "length": 19.26, "rotation": 131.73, "x": 9.98, "y": 13.64}, {"name": "all11", "parent": "all10", "length": 17.35, "rotation": 20.88, "x": 19.26}, {"name": "all12", "parent": "all3", "length": 21.47, "rotation": 42.42, "x": 1.88, "y": 10.83}, {"name": "all13", "parent": "all12", "length": 18.34, "rotation": 54.29, "x": 21.47}, {"name": "all14", "parent": "all2", "length": 15.85, "rotation": -85.07, "x": 0.76, "y": -5.5}, {"name": "all15", "parent": "all14", "length": 18.19, "rotation": -48.59, "x": 15.85}, {"name": "all16", "parent": "all2", "length": 14.53, "rotation": -60.58, "x": 7.58, "y": -8.9}, {"name": "all17", "parent": "all16", "length": 20.52, "rotation": -44.45, "x": 14.53}, {"name": "all18", "parent": "all", "x": -33.82, "y": 55.08}, {"name": "all19", "parent": "all6", "rotation": -144.58, "x": 12.47, "y": 9.54, "transform": "noRotationOrReflection"}, {"name": "all20", "parent": "all3", "x": -0.34, "y": 3.84}, {"name": "all21", "parent": "all", "x": 13.69, "y": 33.6, "scaleX": -0.4631, "scaleY": 0.4631}, {"name": "all22", "parent": "all", "x": 115.37, "y": 36.92, "scaleX": -0.4631, "scaleY": 0.4631}, {"name": "all23", "parent": "all", "length": 15.01, "rotation": -87.27, "x": 99.85, "y": 99.91}, {"name": "all24", "parent": "all23", "length": 17.27, "rotation": 9.2, "x": 15.01}, {"name": "all25", "parent": "all24", "length": 15.23, "rotation": 13.03, "x": 17.27}], "slots": [{"name": "bb1", "bone": "root", "attachment": "bb1"}, {"name": "l3", "bone": "chelun4", "attachment": "l2"}, {"name": "l4", "bone": "chelun6", "attachment": "l1"}, {"name": "t4", "bone": "all", "attachment": "t4"}, {"name": "t3", "bone": "all", "attachment": "t3"}, {"name": "b1", "bone": "all", "attachment": "b111"}, {"name": "t2", "bone": "all", "attachment": "t2"}, {"name": "t1", "bone": "all", "attachment": "t1"}, {"name": "s2", "bone": "all", "attachment": "s2"}, {"name": "chexiang", "bone": "all", "attachment": "chexiang"}, {"name": "l2", "bone": "chelun", "attachment": "l2"}, {"name": "l1", "bone": "chelun3", "attachment": "l1"}, {"name": "x1", "bone": "all18", "attachment": "x1"}, {"name": "sd", "bone": "all", "color": "ffffff85", "attachment": "sd"}, {"name": "diyan1_0014", "bone": "all21", "color": "ffffffa3", "attachment": "diyan1_0014"}, {"name": "diyan1_14", "bone": "all22", "color": "ffffffa3", "attachment": "diyan1_0014"}], "skins": [{"name": "default", "attachments": {"t4": {"t4": {"type": "mesh", "uvs": [0.54548, 0, 0.80004, 0, 0.95988, 0.06193, 0.98948, 0.2494, 0.9658, 0.41878, 0.96876, 0.52073, 0.88588, 0.61282, 0.73788, 0.74931, 0.58692, 0.91047, 0.43892, 1, 0.19028, 0.99269, 0.05412, 0.91047, 0, 0.8266, 0.0482, 0.70327, 0.32348, 0.575, 0.47444, 0.49771, 0.54844, 0.45989, 0.44188, 0.39904, 0.29092, 0.25104, 0.27612, 0.10304, 0.6846, 0.1606, 0.74084, 0.35958, 0.6994, 0.52895, 0.4922, 0.70327, 0.2702, 0.85291], "triangles": [6, 22, 5, 22, 23, 15, 22, 15, 16, 7, 23, 22, 14, 15, 23, 13, 14, 23, 6, 7, 22, 24, 13, 23, 12, 13, 24, 11, 12, 24, 8, 23, 7, 24, 23, 8, 10, 11, 24, 9, 10, 24, 8, 9, 24, 20, 0, 1, 20, 1, 2, 19, 0, 20, 20, 2, 3, 18, 19, 20, 21, 20, 3, 17, 18, 20, 21, 17, 20, 4, 21, 3, 16, 17, 21, 22, 16, 21, 22, 21, 4, 22, 4, 5], "vertices": [1, 20, -7.1, -0.99, 1, 1, 20, -5.82, 5.25, 1, 1, 20, -2.29, 8.6, 1, 1, 20, 6.12, 7.63, 1, 2, 20, 13.47, 5.52, 0.97626, 21, -5.71, 1.87, 0.02374, 2, 20, 17.98, 4.67, 0.51332, 21, -2.09, 4.69, 0.48668, 2, 20, 21.62, 1.81, 0.02411, 21, 2.47, 5.53, 0.97589, 1, 21, 9.6, 6.27, 1, 1, 21, 17.66, 7.62, 1, 1, 21, 23.1, 7.09, 1, 1, 21, 26.58, 1.92, 1, 1, 21, 25.68, -3.02, 1, 1, 21, 23.48, -6.37, 1, 1, 21, 18.32, -8.75, 1, 2, 20, 17.13, -11.62, 0.00163, 21, 9.57, -6.73, 0.99837, 2, 20, 14.48, -7.23, 0.1504, 21, 4.52, -5.81, 0.8496, 2, 20, 13.18, -5.07, 0.54943, 21, 2.05, -5.35, 0.45057, 2, 20, 9.97, -7.13, 0.93592, 21, 1.46, -9.13, 0.06408, 1, 20, 2.68, -9.49, 1, 1, 20, -3.91, -8.52, 1, 1, 20, 0.68, 0.97, 1, 1, 20, 9.73, 0.55, 1, 2, 20, 16.99, -2, 0.0272, 21, 2.26, -0.47, 0.9728, 1, 21, 11.64, 0.12, 1, 1, 21, 20.36, -0.26, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 23, "height": 43}}, "l1": {"l1": {"x": -0.41, "y": 0.54, "width": 39, "height": 39}}, "l2": {"l2": {"x": -7.86, "y": 1.03, "scaleY": 0.9389, "width": 30, "height": 40}}, "l3": {"l2": {"x": -7.86, "y": 1.03, "scaleY": 0.9389, "width": 30, "height": 40}}, "l4": {"l1": {"x": -0.41, "y": 0.54, "width": 39, "height": 39}}, "b1": {"b111": {"type": "mesh", "uvs": [0.24715, 0.36789, 0.23002, 0.43585, 0.21959, 0.53151, 0.21214, 0.64143, 0.23076, 0.73373, 0.34026, 0.80169, 0.47583, 0.79834, 0.60842, 0.80002, 0.71271, 0.7933, 0.7872, 0.7438, 0.81178, 0.77317, 0.77305, 0.82854, 0.77379, 0.89735, 0.81104, 0.95525, 0.88329, 0.99384, 0.95182, 0.99216, 0.91458, 0.94434, 0.90638, 0.91329, 0.95182, 0.90742, 1, 0.83983, 1, 0.74783, 0.97915, 0.65265, 0.91907, 0.62516, 0.95474, 0.55537, 0.93503, 0.44645, 0.8618, 0.37031, 0.7576, 0.36502, 0.68625, 0.45914, 0.62054, 0.383, 0.53417, 0.44539, 0.54074, 0.38829, 0.59305, 0.33525, 0.57117, 0.21665, 0.49323, 0.18276, 0.4276, 0.08419, 0.37017, 0.08727, 0.36197, 0, 0.23754, 0, 0.06936, 0, 0, 0.10113, 0, 0.24437, 0.04748, 0.21357, 0.04064, 0.33679, 0.0844, 0.42458, 0.15823, 0.41072, 0.2184, 0.36451, 0.7455, 0.50545, 0.77407, 0.56982, 0.79464, 0.67409, 0.82435, 0.51189, 0.84721, 0.66122, 0.91578, 0.72945, 0.88607, 0.81699, 0.84492, 0.87749, 0.85064, 0.938, 0.89521, 0.97018, 0.15475, 0.26185, 0.28953, 0.22612, 0.37221, 0.33585, 0.35749, 0.4545, 0.36429, 0.60505, 0.51153, 0.64333, 0.65991, 0.64588], "triangles": [41, 39, 38, 40, 39, 41, 56, 38, 37, 56, 37, 57, 41, 38, 56, 42, 41, 56, 45, 56, 57, 44, 56, 45, 42, 56, 44, 43, 42, 44, 35, 57, 37, 35, 37, 36, 35, 33, 57, 33, 35, 34, 58, 57, 33, 0, 45, 57, 58, 0, 57, 30, 33, 32, 58, 33, 30, 30, 32, 31, 29, 58, 30, 59, 0, 58, 59, 58, 29, 1, 0, 59, 2, 1, 59, 2, 59, 60, 60, 59, 29, 3, 2, 60, 61, 60, 29, 4, 3, 60, 6, 60, 61, 5, 4, 60, 6, 61, 7, 6, 5, 60, 46, 27, 26, 48, 47, 50, 50, 9, 48, 27, 29, 28, 62, 27, 46, 62, 46, 47, 62, 29, 27, 61, 29, 62, 62, 47, 48, 9, 62, 48, 8, 62, 9, 7, 61, 62, 7, 62, 8, 50, 47, 49, 47, 46, 49, 49, 26, 25, 49, 25, 24, 46, 26, 49, 49, 24, 23, 22, 49, 23, 22, 50, 49, 51, 50, 22, 51, 22, 21, 51, 21, 20, 10, 50, 51, 10, 9, 50, 52, 10, 51, 52, 51, 20, 19, 52, 20, 53, 10, 52, 11, 10, 53, 12, 11, 53, 18, 52, 19, 17, 53, 52, 18, 17, 52, 54, 53, 17, 13, 12, 53, 55, 54, 17, 54, 13, 53, 16, 55, 17, 55, 16, 15, 14, 54, 55, 13, 54, 14, 14, 55, 15], "vertices": [3, 10, 15.94, 8.89, 0.36949, 11, 3.49, 8.19, 0.40223, 12, 8.35, 11.78, 0.22828, 4, 9, 29.94, -3.4, 0.00523, 10, 10.3, 11.34, 0.88669, 11, -0.47, 12.9, 0.08382, 12, 13.15, 15.62, 0.02426, 3, 9, 27.8, 4.71, 0.19324, 10, 2.19, 13.46, 0.80569, 11, -6.79, 18.41, 0.00107, 2, 9, 24.92, 13.86, 0.61568, 10, -7.19, 15.45, 0.38432, 3, 8, -35.01, -6.43, 0, 9, 20.25, 20.64, 0.81611, 10, -15.39, 14.71, 0.18389, 3, 8, -25.04, -13.55, 0.01655, 9, 8.09, 22.14, 0.95626, 10, -22.68, 4.86, 0.02718, 3, 14, -2.34, -39.03, 0.00544, 8, -11.81, -14.8, 0.28312, 9, -4.14, 16.94, 0.71144, 3, 14, 3.19, -27.27, 0.07001, 8, 1.08, -16.45, 0.79556, 9, -16.26, 12.25, 0.13442, 1, 8, 11.3, -17.06, 1, 1, 8, 19.05, -13.63, 1, 3, 15, 4.08, -7.33, 0.60635, 14, 9.34, -8.17, 0.3055, 8, 21.15, -16.44, 0.08815, 3, 15, 9.49, -10.22, 0.9605, 14, 12.15, -13.62, 0.03315, 8, 16.82, -20.79, 0.00635, 2, 15, 15.37, -9.1, 0.99956, 14, 17.62, -16.04, 0.00044, 1, 15, 19.69, -4.62, 1, 1, 15, 21.76, 2.94, 1, 1, 15, 20.43, 9.52, 1, 1, 15, 16.98, 5.2, 1, 1, 15, 14.46, 3.94, 1, 1, 15, 13.18, 8.23, 1, 2, 15, 6.56, 11.85, 0.95646, 14, 22.28, 6.2, 0.04354, 2, 15, -1.32, 10.45, 0.63347, 14, 14.99, 9.53, 0.36653, 3, 15, -9.12, 6.98, 0.17243, 14, 6.61, 11.11, 0.81663, 13, 17.5, 11.91, 0.01094, 3, 15, -10.44, 0.77, 0.01207, 14, 1.99, 6.74, 0.64555, 13, 13.52, 6.95, 0.34238, 2, 14, -2.08, 12.45, 0.12026, 13, 8.71, 12.05, 0.87974, 2, 14, -11.5, 14.62, 0.00177, 13, -0.92, 12.92, 0.99823, 1, 13, -9.33, 7.95, 1, 2, 13, -12.7, -1.7, 0.90569, 8, 20, 19.44, 0.09431, 1, 8, 12.1, 12.12, 1, 4, 8, 6.48, 19.45, 0.77208, 9, -3.89, -21.88, 0.16476, 10, 9.77, -27.21, 0.06225, 11, -18.11, -21.38, 0.00092, 4, 8, -2.56, 15.04, 0.31506, 9, 1.96, -13.7, 0.3651, 10, 5.52, -18.1, 0.26222, 11, -17.86, -11.32, 0.05762, 4, 8, -1.35, 19.9, 0.07516, 9, 3.2, -18.55, 0.22493, 10, 10.35, -19.39, 0.45676, 11, -14.11, -14.64, 0.24315, 4, 8, 4.28, 23.88, 0.01916, 9, 0.16, -24.74, 0.13748, 10, 14.25, -25.09, 0.45118, 11, -13.16, -21.47, 0.39217, 4, 8, 3.35, 34.38, 0.00208, 9, 5.98, -33.52, 0.08359, 10, 24.76, -24.34, 0.37404, 11, -3.42, -25.48, 0.54028, 4, 8, -3.89, 38.2, 0, 9, 14.17, -33.42, 0.04222, 10, 28.69, -17.16, 0.23727, 11, 3.31, -20.81, 0.7205, 4, 9, 23.32, -39, 0.00517, 10, 38.05, -11.92, 0.0514, 11, 14.01, -20.29, 0.92743, 12, -20.36, 1.91, 0.016, 4, 9, 28.45, -36.66, 0.00099, 10, 38.53, -6.31, 0.0142, 11, 16.95, -15.48, 0.86581, 12, -15.62, -1.13, 0.11899, 2, 11, 23.68, -19.07, 0.72207, 12, -19.37, -7.78, 0.27793, 2, 11, 30.52, -8.97, 0.35471, 12, -9.43, -14.85, 0.64529, 2, 11, 39.76, 4.68, 0.00446, 12, 4, -24.4, 0.99554, 1, 12, 14.64, -21.17, 1, 1, 12, 21.86, -11.02, 1, 1, 12, 16.52, -10.5, 1, 1, 12, 23.28, -2.16, 1, 2, 11, 8.34, 24.16, 0.00348, 12, 24.21, 6.55, 0.99652, 3, 10, 13.4, 18.02, 0.0054, 11, 5.29, 17.5, 0.05404, 12, 17.61, 9.76, 0.94056, 3, 10, 16.6, 11.64, 0.1105, 11, 5.31, 10.36, 0.30187, 12, 10.48, 9.91, 0.58762, 1, 8, 17.4, 7.44, 1, 1, 8, 19.53, 1.56, 1, 1, 8, 20.48, -7.69, 1, 1, 13, 1.41, 0.89, 1, 3, 15, -6.12, -5.62, 0.00306, 14, 1.92, -0.96, 0.97403, 8, 25.73, -7.17, 0.02291, 2, 15, -1.45, 2.04, 0.38567, 14, 10.11, 2.68, 0.61433, 1, 15, 6.56, 0.51, 1, 1, 15, 12.45, -2.54, 1, 1, 15, 17.53, -1.06, 1, 1, 15, 19.52, 3.73, 1, 1, 12, 10.39, -0.99, 1, 1, 11, 11.37, -2.16, 1, 4, 8, -17.22, 26.35, 0.00104, 9, 20.23, -16.65, 0.02052, 10, 17.07, -3.63, 0.51332, 11, -1.07, -3.52, 0.46511, 3, 8, -19.85, 16.26, 0.00046, 9, 17.74, -6.53, 0.0203, 10, 7.03, -0.83, 0.97924, 2, 9, 12.25, 5.38, 0.94616, 10, -6.04, 0.25, 0.05384, 3, 14, -13.16, -30.24, 0.00109, 8, -6.76, -1.81, 0.25459, 9, -2.38, 3.12, 0.74431, 3, 14, -6.91, -17.11, 0.03411, 8, 7.65, -3.72, 0.96588, 9, -15.96, -2.08, 1e-05], "hull": 46, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90, 54, 92, 92, 94, 94, 96, 96, 18, 92, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 84, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 94], "width": 96, "height": 85}}, "sd": {"sd": {"x": -1.03, "y": 17.4, "width": 225, "height": 28}}, "s2": {"s2": {"type": "mesh", "uvs": [0.00249, 0.2119, 0.02306, 0.1132, 0.06565, 0.00275, 0.10384, 0.02155, 0.11265, 0.19075, 0.17434, 0.34585, 0.25365, 0.49155, 0.37996, 0.6161, 0.5474, 0.6866, 0.73834, 0.75475, 0.90284, 0.80175, 1, 0.8652, 1, 0.9357, 1, 1, 0.88815, 0.9639, 0.71631, 0.9122, 0.52243, 0.8511, 0.35793, 0.7712, 0.20812, 0.6349, 0.08915, 0.53385, 0.02893, 0.35995, 0.07887, 0.29415, 0.13909, 0.4563, 0.23602, 0.57615, 0.36527, 0.69365, 0.53712, 0.77355, 0.72952, 0.837, 0.88962, 0.88635], "triangles": [19, 22, 18, 18, 22, 23, 23, 22, 6, 19, 20, 22, 22, 5, 6, 20, 21, 22, 22, 21, 5, 20, 0, 21, 21, 4, 5, 4, 21, 1, 1, 21, 0, 1, 3, 4, 1, 2, 3, 16, 25, 15, 15, 25, 26, 16, 17, 25, 26, 25, 9, 17, 24, 25, 25, 24, 8, 25, 8, 9, 8, 24, 7, 17, 18, 24, 24, 18, 23, 24, 23, 7, 23, 6, 7, 14, 12, 13, 14, 15, 27, 14, 27, 12, 27, 15, 26, 27, 11, 12, 27, 10, 11, 10, 27, 9, 27, 26, 9], "vertices": [2, 26, 37.85, -8.99, 0.00201, 25, 3.3, -1.24, 0.99799, 1, 25, -0.06, -3.69, 1, 1, 25, -4.84, -5.71, 1, 1, 25, -6.4, -3.69, 1, 2, 26, 31.62, -12.4, 0.01267, 25, -2.94, 2.16, 0.98733, 2, 26, 25.65, -8.1, 0.15776, 25, -2.56, 9.5, 0.84224, 2, 26, 18.77, -4.58, 0.43417, 25, -3.32, 17.19, 0.56583, 2, 26, 9.42, -2.96, 0.8187, 25, -7.02, 25.94, 0.1813, 3, 24, -25.67, 7.86, 0.03143, 26, -1.58, -4.32, 0.96842, 25, -14.12, 34.45, 0.00015, 2, 24, -13.45, 5.13, 0.51537, 26, -13.94, -6.32, 0.48463, 2, 24, -2.92, 3.25, 0.94419, 26, -24.41, -8.49, 0.05581, 1, 24, 3.3, 0.71, 1, 1, 24, 3.3, -2.11, 1, 1, 24, 3.3, -4.68, 1, 2, 24, -3.86, -3.24, 0.95194, 26, -25.95, -2.12, 0.04806, 2, 24, -14.86, -1.17, 0.50709, 26, -14.97, 0.05, 0.49291, 2, 24, -27.27, 1.28, 0.02656, 26, -2.54, 2.39, 0.97344, 2, 26, 8.42, 3.33, 0.85646, 25, -2.27, 30.18, 0.14354, 2, 26, 19.35, 1.83, 0.46706, 25, 2.38, 20.18, 0.53294, 2, 26, 27.92, 0.9, 0.20807, 25, 6.24, 12.47, 0.79193, 2, 26, 34.08, -4.12, 0.05965, 25, 5.35, 4.57, 0.94035, 2, 26, 32.09, -7.76, 0.04537, 25, 1.22, 4.28, 0.95463, 2, 26, 26.1, -3.16, 0.21089, 25, 1.84, 11.79, 0.78911, 2, 26, 18.56, -1.02, 0.47954, 25, -0.44, 19.3, 0.52046, 2, 26, 9.14, 0.27, 0.83263, 25, -4.45, 27.92, 0.16737, 2, 24, -26.33, 4.38, 0.0306, 26, -2.26, -0.84, 0.9694, 2, 24, -14.01, 1.84, 0.51968, 26, -14.64, -3.06, 0.48032, 2, 24, -3.77, -0.13, 0.94199, 26, -24.88, -5.03, 0.05801], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 2, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 24], "width": 62, "height": 38}}, "chexiang": {"chexiang": {"x": 53.97, "y": 80.9, "width": 119, "height": 97}}, "diyan1_14": {"diyan1_0014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}}, "bb1": {"bb1": {"type": "mesh", "uvs": [0.08716, 0.03878, 0.38324, 0, 0.6118, 0.04558, 0.63258, 0.28698, 0.75724, 0.52838, 0.87152, 0.71878, 1, 0.91938, 0.81958, 0.97718, 0.59102, 1, 0.18585, 1, 0.08716, 0.75278, 0.01963, 0.52158, 0, 0.31418, 0, 0.15098, 0.36766, 0.25298, 0.40402, 0.48758, 0.49752, 0.75278], "triangles": [7, 8, 16, 8, 9, 16, 9, 10, 16, 7, 5, 6, 7, 16, 5, 10, 15, 16, 16, 4, 5, 16, 15, 4, 10, 11, 15, 15, 3, 4, 11, 12, 15, 12, 14, 15, 15, 14, 3, 12, 13, 14, 14, 2, 3, 13, 0, 14, 14, 1, 2, 14, 0, 1], "vertices": [1, 29, -3.93, -8.15, 1, 1, 29, -5.55, 2.6, 1, 1, 29, -2.66, 10.7, 1, 3, 29, 10.64, 10.81, 0.76411, 30, -2.59, 11.37, 0.23485, 31, -16.78, 15.56, 0.00104, 3, 29, 24.12, 14.66, 0.04544, 30, 11.33, 13.02, 0.66372, 31, -2.85, 14.02, 0.29085, 2, 30, 22.43, 14.88, 0.11634, 31, 8.38, 13.33, 0.88366, 1, 31, 20.33, 12.87, 1, 1, 31, 20.48, 5.64, 1, 1, 31, 18.14, -2.35, 1, 2, 30, 32.46, -12.47, 0.00611, 31, 11.99, -15.57, 0.99389, 2, 30, 18.42, -13.14, 0.48036, 31, -1.84, -13.06, 0.51964, 3, 29, 22.48, -11.84, 0.13995, 30, 5.48, -12.89, 0.84965, 31, -14.4, -9.89, 0.01039, 2, 29, 11.05, -12.01, 0.82981, 30, -5.83, -11.22, 0.17019, 2, 29, 2.09, -11.58, 0.99942, 30, -14.61, -9.36, 0.00058, 2, 29, 8.32, 1.37, 0.99674, 30, -6.39, 2.43, 0.00326, 3, 29, 21.27, 2.07, 0.00753, 30, 6.51, 1.04, 0.99059, 31, -10.25, 3.44, 0.00188, 2, 30, 21.48, 1.32, 0.00219, 31, 4.39, 0.34, 0.99781], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 2, 28, 28, 30, 30, 32], "width": 36, "height": 55}}, "diyan1_0014": {"diyan1_0014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}, "diyan1_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146, -48, -145, -48, -145, 49, 146, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 39}}, "x1": {"x1": {"x": 9.3, "y": -5.68, "width": 44, "height": 20}}, "t1": {"t1": {"type": "mesh", "uvs": [0.66732, 0.01284, 0.79497, 0.01116, 0.93557, 0.04984, 1, 0.09693, 1, 0.25166, 0.93372, 0.34416, 0.83567, 0.41143, 0.66732, 0.40975, 0.50082, 0.40975, 0.46197, 0.41143, 0.45087, 0.45179, 0.45642, 0.51739, 0.53412, 0.63343, 0.44347, 0.78984, 0.36762, 0.90588, 0.16782, 1, 0.05868, 0.82516, 0, 0.72761, 0, 0.64184, 0.05313, 0.57961, 0.16967, 0.4602, 0.19927, 0.34752, 0.20667, 0.28025, 0.30842, 0.21802, 0.43237, 0.16757, 0.52857, 0.09693, 0.82272, 0.17261, 0.60812, 0.24493, 0.42682, 0.29202, 0.31952, 0.37275, 0.29362, 0.53757, 0.21592, 0.72088], "triangles": [22, 23, 29, 21, 22, 29, 10, 29, 9, 29, 20, 21, 30, 29, 10, 30, 10, 11, 30, 20, 29, 30, 19, 20, 31, 19, 30, 18, 19, 31, 17, 18, 31, 12, 13, 30, 12, 30, 11, 31, 30, 13, 16, 17, 31, 14, 31, 13, 15, 16, 31, 15, 31, 14, 26, 1, 2, 26, 2, 3, 27, 25, 0, 24, 25, 27, 0, 1, 26, 27, 0, 26, 26, 3, 4, 28, 23, 24, 28, 24, 27, 5, 26, 4, 29, 23, 28, 8, 28, 27, 9, 29, 28, 7, 27, 26, 6, 7, 26, 8, 27, 7, 8, 9, 28, 5, 6, 26], "vertices": [1, 18, 3.73, -8.86, 1, 1, 18, -1.07, -7.13, 1, 1, 18, -5.74, -3.56, 1, 1, 18, -7.42, -0.71, 1, 1, 18, -5.02, 5.66, 1, 1, 18, -1.11, 8.53, 1, 1, 18, 3.6, 9.92, 1, 1, 18, 9.88, 7.48, 1, 2, 18, 16.11, 5.14, 0.89516, 19, 1.04, 7.35, 0.10484, 2, 18, 17.59, 4.66, 0.68217, 19, 1.52, 5.87, 0.31783, 2, 18, 18.64, 6.17, 0.32117, 19, 3.35, 5.9, 0.67883, 2, 18, 19.44, 8.94, 0.06731, 19, 6.08, 6.87, 0.93269, 2, 18, 18.33, 14.82, 0.0001, 19, 10.2, 11.2, 0.9999, 1, 19, 17.79, 9.49, 1, 1, 19, 23.51, 7.89, 1, 1, 19, 29.59, 1.25, 1, 1, 19, 23.3, -4.97, 1, 1, 19, 19.76, -8.35, 1, 1, 19, 16.12, -9.33, 1, 1, 19, 12.92, -7.99, 1, 1, 19, 6.64, -4.86, 1, 2, 18, 26.44, -1.67, 0.05194, 19, 1.54, -5.01, 0.94806, 2, 18, 25.12, -4.34, 0.28105, 19, -1.4, -5.49, 0.71895, 2, 18, 20.35, -5.47, 0.89008, 19, -5.1, -2.28, 0.10992, 1, 18, 14.92, -5.8, 1, 1, 18, 10.23, -7.35, 1, 1, 18, 0.39, -0.09, 1, 1, 18, 9.54, -0.14, 1, 1, 18, 17.06, -0.75, 1, 1, 19, 1.36, -0.07, 1, 2, 18, 25.85, 7.48, 0.00067, 19, 8.63, 0.81, 0.99933, 1, 19, 17.23, -0.09, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 6, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 28], "width": 38, "height": 42}}, "t2": {"t2": {"type": "mesh", "uvs": [0.25628, 0.98742, 0.50196, 0.99372, 0.75652, 0.98427, 0.8542, 0.92602, 0.99332, 0.87564, 0.92228, 0.71032, 0.86604, 0.58121, 0.87948, 0.44892, 0.8838, 0.40645, 0.83428, 0.37595, 0.77132, 0.33717, 0.84532, 0.23168, 0.77132, 0.07266, 0.53452, 0.00811, 0.17044, 0, 0.0254, 0.1073, 0.055, 0.31513, 0.25628, 0.40487, 0.4582, 0.45178, 0.5138, 0.4647, 0.50314, 0.49021, 0.47828, 0.54972, 0.37172, 0.6993, 0.21484, 0.87091, 0.36284, 0.14981, 0.55524, 0.35134, 0.7062, 0.42219, 0.6766, 0.5387, 0.62628, 0.71032, 0.56412, 0.87091], "triangles": [8, 26, 9, 7, 26, 8, 27, 19, 26, 27, 26, 7, 20, 19, 27, 21, 20, 27, 6, 27, 7, 28, 21, 27, 28, 27, 6, 22, 21, 28, 28, 6, 5, 29, 22, 28, 23, 22, 29, 3, 28, 5, 3, 5, 4, 29, 28, 3, 2, 29, 3, 1, 0, 23, 29, 1, 23, 1, 29, 2, 24, 14, 13, 15, 14, 24, 16, 15, 24, 12, 24, 13, 12, 11, 24, 11, 25, 24, 10, 25, 11, 17, 16, 24, 25, 17, 24, 26, 25, 10, 26, 10, 9, 18, 17, 25, 19, 18, 25, 26, 19, 25], "vertices": [1, 23, 27.55, -6.41, 1, 1, 23, 26.95, -0.29, 1, 1, 23, 25.58, 5.94, 1, 1, 23, 22.52, 7.96, 1, 1, 23, 19.67, 11.06, 1, 1, 23, 12.24, 8.17, 1, 1, 23, 6.44, 5.9, 1, 2, 22, 18.43, 3.63, 0.15558, 23, 0.24, 5.33, 0.84442, 2, 22, 16.88, 4.9, 0.355, 23, -1.75, 5.14, 0.645, 2, 22, 14.99, 4.74, 0.59689, 23, -2.99, 3.71, 0.40311, 2, 22, 12.59, 4.54, 0.94066, 23, -4.56, 1.89, 0.05934, 1, 22, 9.67, 8.96, 1, 1, 22, 2.54, 11.86, 1, 1, 22, -3.39, 8.87, 1, 1, 22, -9.06, 1.74, 1, 1, 22, -7.12, -4.16, 1, 1, 22, 1.21, -9.32, 1, 2, 22, 7.58, -7.74, 0.98898, 23, 0.46, -10.39, 0.01102, 2, 22, 12.33, -4.96, 0.70475, 23, 1.9, -5.08, 0.29525, 2, 22, 13.64, -4.19, 0.37486, 23, 2.3, -3.61, 0.62514, 2, 22, 14.46, -5.11, 0.13263, 23, 3.53, -3.7, 0.86737, 2, 22, 16.35, -7.26, 0.00357, 23, 6.39, -3.91, 0.99643, 1, 23, 13.73, -5.52, 1, 1, 23, 22.28, -8.23, 1, 1, 22, -0.54, 1.48, 1, 2, 22, 9.95, -0.22, 0.99809, 23, -3.12, -3.36, 0.00191, 2, 22, 14.86, 0.87, 0.479, 23, -0.37, 0.86, 0.521, 1, 23, 5.15, 0.92, 1, 1, 23, 13.31, 0.85, 1, 1, 23, 21.01, 0.41, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 42, 44, 44, 46, 0, 46, 34, 36, 36, 38, 38, 40, 40, 42, 16, 18, 18, 20, 12, 14, 14, 16, 28, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 23, "height": 45}}, "t3": {"t3": {"type": "mesh", "uvs": [1, 0.95674, 0.96946, 0.82921, 0.93563, 0.6513, 0.77071, 0.64342, 0.62694, 0.571, 0.61848, 0.52219, 0.6206, 0.31751, 0.59523, 0.11283, 0.41974, 0.02781, 0.147, 0.00262, 0.03071, 0.0656, 0.00746, 0.1947, 0.17237, 0.36474, 0.29923, 0.54266, 0.46837, 0.68593, 0.50643, 0.82606, 0.62271, 0.92683, 0.7686, 1, 0.95043, 0.98351, 0.81511, 0.82291, 0.61848, 0.69696, 0.51911, 0.58517, 0.37957, 0.35057, 0.27386, 0.1396], "triangles": [21, 5, 4, 14, 13, 21, 20, 21, 4, 20, 4, 3, 14, 21, 20, 19, 3, 2, 20, 3, 19, 15, 14, 20, 15, 20, 19, 19, 2, 1, 16, 15, 19, 18, 19, 1, 18, 1, 0, 17, 16, 19, 18, 17, 19, 23, 9, 8, 10, 9, 23, 11, 10, 23, 7, 22, 23, 7, 23, 8, 22, 7, 6, 12, 11, 23, 12, 23, 22, 5, 22, 6, 13, 12, 22, 21, 13, 22, 5, 21, 22], "vertices": [1, 17, 24.96, 1.81, 1, 1, 17, 19.73, 4.92, 1, 1, 17, 12.62, 9.49, 1, 1, 17, 8.57, 5.36, 1, 2, 16, 20.45, 4.49, 0.14889, 17, 2.71, 3.78, 0.85111, 2, 16, 18.19, 5, 0.60853, 17, 0.78, 5.05, 0.39147, 1, 16, 9.17, 8.35, 1, 1, 16, -0.18, 10.79, 1, 1, 16, -6.03, 6.38, 1, 1, 16, -10.39, -2.19, 1, 1, 16, -9, -7.03, 1, 1, 16, -3.57, -9.86, 1, 1, 16, 5.91, -7.16, 1, 2, 16, 15.28, -5.84, 0.98652, 17, -5.8, -4.03, 0.01348, 2, 16, 23.63, -2.57, 5e-05, 17, 3.17, -3.95, 0.99995, 1, 17, 9.02, -7.25, 1, 1, 17, 15.27, -7.27, 1, 1, 17, 21.21, -5.65, 1, 1, 17, 24.78, -0.33, 1, 1, 17, 15.97, 1.02, 1, 1, 17, 6.99, -0.32, 1, 2, 16, 19.78, 0.72, 0.03089, 17, 0.74, 0.48, 0.96911, 1, 16, 7.75, -0.11, 1, 1, 16, -2.83, -0.21, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 2, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 35, "height": 47}}}}], "animations": {"che1": {"slots": {"x1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "t4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "chexiang": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "diyan1_0014": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffa3"}], "attachment": [{"time": 0.0333, "name": "diyan1_0015"}, {"time": 0.0667, "name": "diyan1_0016"}, {"time": 0.1, "name": "diyan1_0017"}, {"time": 0.1333, "name": "diyan1_0018"}, {"time": 0.1667, "name": "diyan1_0019"}, {"time": 0.2, "name": "diyan1_0014"}, {"time": 0.2333, "name": "diyan1_0015"}, {"time": 0.2667, "name": "diyan1_0016"}, {"time": 0.3, "name": "diyan1_0017"}, {"time": 0.3333, "name": "diyan1_0018"}, {"time": 0.3667, "name": "diyan1_0019"}, {"time": 0.4, "name": "diyan1_0014"}, {"time": 0.4333, "name": "diyan1_0015"}, {"time": 0.4667, "name": "diyan1_0016"}, {"time": 0.5, "name": "diyan1_0017"}, {"time": 0.5333, "name": "diyan1_0018"}, {"time": 0.5667, "name": "diyan1_0019"}, {"time": 0.6, "name": null}]}, "l3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "s2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "l1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "t3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "bb1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "l2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "diyan1_14": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffa3"}], "attachment": [{"time": 0.0333, "name": "diyan1_0015"}, {"time": 0.0667, "name": "diyan1_0016"}, {"time": 0.1, "name": "diyan1_0017"}, {"time": 0.1333, "name": "diyan1_0018"}, {"time": 0.1667, "name": "diyan1_0019"}, {"time": 0.2, "name": "diyan1_0014"}, {"time": 0.2333, "name": "diyan1_0015"}, {"time": 0.2667, "name": "diyan1_0016"}, {"time": 0.3, "name": "diyan1_0017"}, {"time": 0.3333, "name": "diyan1_0018"}, {"time": 0.3667, "name": "diyan1_0019"}, {"time": 0.4, "name": "diyan1_0014"}, {"time": 0.4333, "name": "diyan1_0015"}, {"time": 0.4667, "name": "diyan1_0016"}, {"time": 0.5, "name": "diyan1_0017"}, {"time": 0.5333, "name": "diyan1_0018"}, {"time": 0.5667, "name": "diyan1_0019"}, {"time": 0.6, "name": null}]}, "t2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "sd": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff85"}]}, "t1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "b1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}, "l4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffffff"}]}}, "bones": {"all12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 33.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -13.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 33.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -13.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all13": {"rotate": [{"angle": 2.37, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 18.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 2.37, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4667, "angle": 18.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 2.37}]}, "all10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -113.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -32.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -113.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -32.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all11": {"rotate": [{"angle": 9.9, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 78.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 33.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 9.9, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4667, "angle": 78.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "angle": 33.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 9.9}]}, "all14": {"rotate": [{"angle": 38.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 38.19, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 24.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 38.19}]}, "all15": {"rotate": [{"angle": 46.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 53.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 46.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": 53.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 46.16}]}, "all16": {"rotate": [{"angle": -33.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -33.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -33.75}]}, "all17": {"rotate": [{"angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 74.38, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": 8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 74.38, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6333, "angle": 8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6667, "angle": -1.7}]}, "all3": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -8.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -8.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "y": 3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "y": 3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all4": {"rotate": [{"angle": -0.87, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.38, "curve": 0.353, "c2": 0.4, "c3": 0.705, "c4": 0.8}, {"time": 0.3333, "angle": -0.87, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.38, "curve": 0.353, "c2": 0.4, "c3": 0.705, "c4": 0.8}, {"time": 0.6667, "angle": -0.87}]}, "all5": {"rotate": [{"angle": -2.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -5.38, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.3333, "angle": -2.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.38, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.6667, "angle": -2.6}]}, "all6": {"rotate": [{"angle": -4.66, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.66, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.66}]}, "all2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "y": 3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "y": 3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 12.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "all8": {"rotate": [{"angle": 4.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 4.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 12.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 4.7}]}, "all9": {"rotate": [{"angle": 12.24, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.24, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.24}]}, "chelun6": {"rotate": [{}, {"time": 0.1667, "angle": 90.78}, {"time": 0.3333}, {"time": 0.5, "angle": 90.78}, {"time": 0.6667, "angle": 180}]}, "chelun3": {"rotate": [{}, {"time": 0.1667, "angle": 90.78}, {"time": 0.3333}, {"time": 0.5, "angle": 90.78}, {"time": 0.6667, "angle": 180}]}, "all23": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 33.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.14}]}, "all24": {"rotate": [{"angle": 12.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 33.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 12.26}]}, "all25": {"rotate": [{"angle": 28.81, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 33.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 28.81}]}}}, "che2": {"slots": {"diyan1_0014": {"attachment": [{"name": null}]}, "diyan1_14": {"attachment": [{"name": null}]}}, "bones": {"all15": {"rotate": [{"angle": 46.16}]}, "chelun3": {"rotate": [{"angle": 180}]}, "all16": {"rotate": [{"angle": -33.75}]}, "chelun6": {"rotate": [{"angle": 180}]}, "all8": {"rotate": [{"angle": 4.7}]}, "all9": {"rotate": [{"angle": 12.24}]}, "all25": {"rotate": [{"angle": 44.32, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": 28.81, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 46.64, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 44.32}]}, "all6": {"rotate": [{"angle": -4.66}]}, "all5": {"rotate": [{"angle": -2.6}]}, "all24": {"rotate": [{"angle": 18.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 12.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 30.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 18.82}]}, "all23": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.14}]}, "all13": {"rotate": [{"angle": 2.37}]}, "all11": {"rotate": [{"angle": 9.9}]}, "all14": {"rotate": [{"angle": 38.19}]}, "all17": {"rotate": [{"angle": -1.7}]}, "all4": {"rotate": [{"angle": -0.87}]}}}, "che3": {"slots": {"diyan1_0014": {"attachment": [{"time": 0.0333, "name": "diyan1_0015"}, {"time": 0.0667, "name": "diyan1_0016"}, {"time": 0.1, "name": "diyan1_0017"}, {"time": 0.1333, "name": "diyan1_0018"}, {"time": 0.1667, "name": "diyan1_0019"}, {"time": 0.2, "name": "diyan1_0014"}, {"time": 0.2333, "name": "diyan1_0015"}, {"time": 0.2667, "name": "diyan1_0016"}, {"time": 0.3, "name": "diyan1_0017"}, {"time": 0.3333, "name": "diyan1_0018"}, {"time": 0.3667, "name": "diyan1_0019"}, {"time": 0.4, "name": "diyan1_0014"}, {"time": 0.4333, "name": "diyan1_0015"}, {"time": 0.4667, "name": "diyan1_0016"}, {"time": 0.5, "name": "diyan1_0017"}, {"time": 0.5333, "name": "diyan1_0018"}, {"time": 0.5667, "name": "diyan1_0019"}, {"time": 0.6, "name": null}]}, "diyan1_14": {"attachment": [{"time": 0.0333, "name": "diyan1_0015"}, {"time": 0.0667, "name": "diyan1_0016"}, {"time": 0.1, "name": "diyan1_0017"}, {"time": 0.1333, "name": "diyan1_0018"}, {"time": 0.1667, "name": "diyan1_0019"}, {"time": 0.2, "name": "diyan1_0014"}, {"time": 0.2333, "name": "diyan1_0015"}, {"time": 0.2667, "name": "diyan1_0016"}, {"time": 0.3, "name": "diyan1_0017"}, {"time": 0.3333, "name": "diyan1_0018"}, {"time": 0.3667, "name": "diyan1_0019"}, {"time": 0.4, "name": "diyan1_0014"}, {"time": 0.4333, "name": "diyan1_0015"}, {"time": 0.4667, "name": "diyan1_0016"}, {"time": 0.5, "name": "diyan1_0017"}, {"time": 0.5333, "name": "diyan1_0018"}, {"time": 0.5667, "name": "diyan1_0019"}, {"time": 0.6, "name": null}]}}, "bones": {"all12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 33.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -13.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 33.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -13.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all13": {"rotate": [{"angle": 2.37, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 18.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 2.37, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4667, "angle": 18.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 2.37}]}, "all10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -113.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -32.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -113.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -32.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all11": {"rotate": [{"angle": 9.9, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 78.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 33.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 9.9, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4667, "angle": 78.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "angle": 33.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 9.9}]}, "all14": {"rotate": [{"angle": 38.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 38.19, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 24.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 38.19}]}, "all15": {"rotate": [{"angle": 46.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 53.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 46.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": 53.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 46.16}]}, "all16": {"rotate": [{"angle": -33.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -33.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -33.75}]}, "all17": {"rotate": [{"angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 74.38, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": 8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 74.38, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6333, "angle": 8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6667, "angle": -1.7}]}, "all3": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -8.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -8.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "y": 3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "y": 3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all4": {"rotate": [{"angle": -0.87, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.38, "curve": 0.353, "c2": 0.4, "c3": 0.705, "c4": 0.8}, {"time": 0.3333, "angle": -0.87, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.38, "curve": 0.353, "c2": 0.4, "c3": 0.705, "c4": 0.8}, {"time": 0.6667, "angle": -0.87}]}, "all5": {"rotate": [{"angle": -2.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -5.38, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.3333, "angle": -2.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.38, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.6667, "angle": -2.6}]}, "all6": {"rotate": [{"angle": -4.66, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.66, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.66}]}, "all2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "y": 3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "y": 3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "all7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 12.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "all8": {"rotate": [{"angle": 4.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 4.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 12.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 4.7}]}, "all9": {"rotate": [{"angle": 12.24, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.24, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.24}]}, "chelun6": {"rotate": [{}, {"time": 0.1667, "angle": 90.78}, {"time": 0.3333}, {"time": 0.5, "angle": 90.78}, {"time": 0.6667, "angle": 180}]}, "chelun3": {"rotate": [{}, {"time": 0.1667, "angle": 90.78}, {"time": 0.3333}, {"time": 0.5, "angle": 90.78}, {"time": 0.6667, "angle": 180}]}, "all23": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 33.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.14}]}, "all24": {"rotate": [{"angle": 12.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 33.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 12.26}]}, "all25": {"rotate": [{"angle": 28.81, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 33.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 28.81}]}}}}}