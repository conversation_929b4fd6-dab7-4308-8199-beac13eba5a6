{"skeleton": {"hash": "cTQWsLHc5xtRub2YKR77dABVUcI=", "spine": "3.8.75", "x": -42.88, "y": -18.8, "width": 94.95, "height": 115.91, "images": "./images/", "audio": "D:/spine导出/弟子spine/青蛙弟子"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 177.47, "x": -0.07, "y": -5.19, "scaleX": 0.361, "scaleY": 0.361}, {"name": "bone2", "parent": "bone", "length": 76.87, "rotation": -0.93, "x": -8.97, "y": 96.55}, {"name": "bone3", "parent": "bone2", "length": 28.62, "rotation": 77.5, "x": -9.21, "y": 4.42}, {"name": "bone4", "parent": "bone3", "length": 21.39, "rotation": 5.62, "x": 28.62}, {"name": "bone5", "parent": "bone4", "length": 29.92, "rotation": 17.41, "x": 21.39}, {"name": "bone6", "parent": "bone5", "x": 76.43, "y": 0.99}, {"name": "bone7", "parent": "bone4", "length": 37.87, "rotation": -79.04, "x": 6.69, "y": -12.08}, {"name": "bone8", "parent": "bone7", "length": 41.24, "rotation": 9.07, "x": 37.87}, {"name": "bone9", "parent": "bone8", "length": 39.16, "rotation": -16.47, "x": 41.15, "y": -0.41}, {"name": "bone10", "parent": "bone4", "length": 26.67, "rotation": 93.35, "x": 4.87, "y": 19.54}, {"name": "bone11", "parent": "bone10", "length": 29.94, "rotation": 6.85, "x": 26.67}, {"name": "bone12", "parent": "bone11", "length": 30.74, "rotation": -11.72, "x": 29.94}, {"name": "bone13", "parent": "bone2", "length": 22.98, "rotation": -129.67, "x": -51.53, "y": 0.41}, {"name": "bone14", "parent": "bone13", "length": 29.51, "rotation": -14.14, "x": 22.98}, {"name": "bone15", "parent": "bone2", "length": 28.12, "rotation": -108.04, "x": -23.61, "y": -4.54}, {"name": "bone16", "parent": "bone15", "length": 26.93, "rotation": -19.14, "x": 28.12}, {"name": "bone17", "parent": "bone2", "length": 25.23, "rotation": -71.83, "x": 15.5, "y": -7.64}, {"name": "bone18", "parent": "bone17", "length": 29.38, "rotation": 28.9, "x": 25.23}, {"name": "bone19", "parent": "bone2", "length": 34.26, "rotation": -38.16, "x": 53.3, "y": -6.62}, {"name": "bone20", "parent": "bone19", "length": 37.73, "rotation": 2.6, "x": 34.26}, {"name": "bone21", "parent": "bone2", "length": 37.34, "rotation": -54.5, "x": 35, "y": -5.67}, {"name": "bone22", "parent": "bone21", "length": 36.24, "rotation": -6.54, "x": 37.34}, {"name": "bone23", "parent": "bone22", "length": 22.45, "rotation": 10.96, "x": 36.24}, {"name": "bone24", "parent": "bone23", "length": 24.23, "rotation": 28.84, "x": 22.45}, {"name": "bone25", "parent": "bone2", "length": 34.97, "rotation": -112.9, "x": -31.17, "y": 0.33}, {"name": "bone26", "parent": "bone25", "length": 38.25, "rotation": 32.57, "x": 34.97}, {"name": "bone27", "parent": "bone26", "length": 27.13, "rotation": -71.4, "x": 38.25}, {"name": "bone28", "parent": "bone27", "length": 28.93, "rotation": -6.31, "x": 27.13}, {"name": "bone29", "parent": "bone4", "x": 20.35, "y": -94.68}, {"name": "bone30", "parent": "bone29", "length": 25.23, "rotation": -176.21, "x": -9.7, "y": -4.91}, {"name": "bone31", "parent": "bone30", "length": 27.06, "rotation": -17.49, "x": 25.23}, {"name": "bone32", "parent": "bone4", "x": 11.58, "y": 52.63}, {"name": "bone33", "parent": "bone32", "length": 26.02, "rotation": 167.9, "x": -8.77, "y": 1.3}, {"name": "bone34", "parent": "bone33", "length": 24.89, "rotation": 2.51, "x": 26.02}, {"name": "bone35", "parent": "bone", "length": 100.42, "rotation": -177.61, "x": -48.27, "y": 16.85}, {"name": "target1", "parent": "bone35", "rotation": 177.61, "x": -0.05, "y": -10.68, "color": "ff3f00ff"}, {"name": "target2", "parent": "bone35", "rotation": 177.61, "x": 54.25, "y": 11.06, "color": "ff3f00ff"}, {"name": "bone36", "parent": "bone", "length": 54.64, "rotation": -178.65, "x": 69.67, "y": 13.16}, {"name": "target3", "parent": "bone36", "rotation": 178.65, "x": 5.42, "y": -14.46, "color": "ff3f00ff"}, {"name": "target4", "parent": "bone36", "rotation": 178.65, "x": -30.15, "y": 13.36, "color": "ff3f00ff"}, {"name": "bone37", "parent": "bone5", "length": 13.37, "rotation": -156.86, "x": 40.21, "y": -31.35}, {"name": "bone38", "parent": "bone37", "length": 11.5, "rotation": -7.96, "x": 13.37}, {"name": "bone39", "parent": "bone38", "length": 11.59, "rotation": -10.74, "x": 11.5}], "slots": [{"name": "sd", "bone": "bone", "color": "ffffff87", "attachment": "sd"}, {"name": "ss1", "bone": "bone", "attachment": "ss1"}, {"name": "tou2", "bone": "bone6", "attachment": "tou2"}, {"name": "j1", "bone": "bone", "attachment": "j1"}, {"name": "j2", "bone": "bone", "attachment": "j2"}, {"name": "bdbd", "bone": "bone", "attachment": "bdbd"}, {"name": "ss2", "bone": "bone", "attachment": "ss2"}, {"name": "hudi<PERSON>", "bone": "bone", "attachment": "hudi<PERSON>"}, {"name": "tou1", "bone": "bone6"}, {"name": "st", "bone": "bone5", "attachment": "st"}], "ik": [{"name": "target1", "bones": ["bone25", "bone26"], "target": "target1", "stretch": true}, {"name": "target2", "order": 1, "bones": ["bone27", "bone28"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 2, "bones": ["bone21", "bone22"], "target": "target3", "bendPositive": false, "stretch": true}, {"name": "target4", "order": 3, "bones": ["bone23", "bone24"], "target": "target4", "stretch": true}], "skins": [{"name": "default", "attachments": {"bdbd": {"bdbd": {"type": "mesh", "uvs": [0.45736, 0.10026, 0.56504, 0.10886, 0.66884, 0.15186, 0.76614, 0.25721, 0.86215, 0.39481, 0.95297, 0.56681, 1, 0.71516, 0.93221, 0.80976, 0.85047, 0.86351, 0.7363, 0.86781, 0.59359, 0.87641, 0.51444, 0.82696, 0.43011, 0.77321, 0.34967, 0.76676, 0.2861, 0.87211, 0.16933, 0.82911, 0.07203, 0.77321, 0.00067, 0.68936, 0, 0.60981, 0.07981, 0.45286, 0.20047, 0.28301, 0.29648, 0.14756, 0.38081, 0.09596, 0.43957, 0.29799, 0.42954, 0.52401, 0.42954, 0.65364, 0.60605, 0.3412, 0.65419, 0.54396, 0.67424, 0.70683, 0.70634, 0.30132, 0.75648, 0.45754, 0.80061, 0.60379, 0.8327, 0.75336, 0.32524, 0.29135, 0.26708, 0.47748, 0.2069, 0.7035, 0.25103, 0.28802, 0.16478, 0.46086, 0.10461, 0.647], "triangles": [31, 4, 5, 32, 31, 5, 7, 32, 5, 28, 31, 32, 6, 7, 5, 8, 32, 7, 9, 28, 32, 9, 32, 8, 29, 2, 3, 26, 2, 29, 30, 29, 3, 30, 3, 4, 27, 26, 29, 27, 29, 30, 31, 30, 4, 27, 30, 31, 27, 24, 26, 28, 27, 31, 11, 25, 27, 11, 12, 25, 27, 28, 11, 10, 11, 28, 10, 28, 9, 24, 27, 25, 13, 25, 12, 13, 35, 34, 15, 38, 35, 14, 35, 13, 15, 35, 14, 34, 33, 24, 34, 24, 25, 25, 13, 34, 38, 19, 37, 18, 19, 38, 17, 18, 38, 38, 37, 35, 16, 17, 38, 16, 38, 15, 37, 19, 20, 37, 20, 36, 34, 36, 33, 37, 36, 34, 35, 37, 34, 36, 20, 21, 23, 1, 26, 23, 22, 0, 23, 0, 1, 26, 1, 2, 33, 21, 22, 36, 21, 33, 33, 22, 23, 24, 33, 23, 26, 24, 23], "vertices": [1, 4, 13.02, -9.02, 1, 1, 4, 15.53, -31.14, 1, 1, 4, 16.55, -55.31, 1, 7, 3, 39.56, -81.35, 0.00027, 4, 2.93, -82.03, 0.0564, 13, -102.45, 84.32, 1e-05, 15, -60.21, 88.07, 0, 17, -11.7, 70.43, 0.00877, 19, 0.23, 40.94, 0.93108, 20, -32.14, 42.44, 0.00346, 5, 4, -14.76, -104.88, 0.00372, 13, -99.95, 113.11, 0, 15, -47.28, 113.9, 0, 19, 28.93, 37.69, 0.75779, 20, -3.61, 37.89, 0.2385, 2, 19, 60.47, 32.29, 0.13297, 20, 27.65, 31.06, 0.86703, 2, 19, 82.04, 23.05, 0.00331, 20, 48.77, 20.85, 0.99669, 1, 20, 44.01, 0.85, 1, 2, 18, 74.1, 29.46, 0.03881, 20, 33.23, -16.47, 0.96119, 3, 18, 55.42, 10.67, 0.42888, 19, 48.03, -32.12, 0.07712, 20, 12.3, -32.71, 0.494, 4, 15, 36.74, 76.9, 0.00035, 16, -17.06, 75.48, 9e-05, 18, 32.38, -13.14, 0.99904, 20, -13.6, -53.37, 0.00053, 4, 15, 36.16, 57.28, 0.03546, 16, -11.18, 56.76, 0.01371, 17, 47.87, -11.34, 0.03021, 18, 14.34, -20.87, 0.92062, 5, 3, -50.61, -20.63, 5e-05, 15, 35.41, 36.34, 0.23091, 16, -5.03, 36.72, 0.13065, 17, 34.89, -27.79, 0.29649, 18, -4.98, -29, 0.3419, 4, 15, 40.62, 18.39, 0.22446, 16, 5.78, 21.48, 0.60205, 17, 28.49, -45.35, 0.11918, 18, -19.06, -41.27, 0.05431, 4, 15, 59.36, 9.24, 0.00072, 16, 26.49, 18.97, 0.99281, 17, 38.21, -63.8, 0.00476, 18, -19.48, -62.13, 0.00171, 3, 14, 19.51, 36.51, 0.21533, 16, 38.47, -6.06, 0.78467, 17, 24.43, -87.89, 0, 3, 14, 33.42, 17.09, 0.82057, 16, 46.24, -28.65, 0.17943, 17, 10.26, -107.13, 0, 2, 14, 40.16, -2.05, 0.99942, 16, 47.22, -48.92, 0.00058, 1, 14, 33.86, -11.24, 1, 2, 13, 24.34, -19.41, 0.32833, 14, 6.06, -18.49, 0.67167, 6, 3, 9.84, 51.03, 0.0666, 4, -13.7, 52.62, 0.34437, 13, -15.55, -19.88, 0.55744, 15, -17.84, -40.83, 0.03157, 17, -53.66, -58.59, 0, 19, -106.22, -43.18, 0, 1, 4, 4.89, 41.63, 1, 6, 3, 37.95, 19.63, 0.01293, 4, 11.21, 18.63, 0.984, 13, -54.9, -4.79, 0.00233, 15, -48.86, -12.29, 0.00071, 17, -61.83, -17.24, 1e-05, 19, -90.1, -4.24, 2e-05, 5, 3, 14.61, -7.31, 0.88108, 4, -14.66, -5.91, 0.01359, 13, -46.44, 29.85, 0, 17, -28.01, -5.98, 0.08964, 19, -55.7, -13.61, 0.01568, 5, 3, -16.71, -12.4, 0.20353, 15, 2.46, 24.87, 0.25868, 16, -32.4, 15.08, 0.00281, 17, 1.53, -17.58, 0.53405, 18, -29.25, -3.93, 0.00093, 5, 3, -34.36, -16.61, 0.02153, 15, 19.62, 30.77, 0.30331, 16, -18.12, 26.28, 0.07358, 17, 18.86, -22.96, 0.47822, 18, -16.67, -17.02, 0.12337, 6, 3, 17.7, -46.28, 0.0549, 4, -15.4, -44.99, 0.10277, 13, -66.98, 63.11, 2e-05, 15, -35.06, 55.28, 0, 17, -10.78, 29.12, 0.25773, 19, -21.91, 6.05, 0.58458, 3, 17, 19.64, 31.37, 0.0661, 18, 10.27, 30.17, 0.11962, 19, 4.66, -8.94, 0.81428, 4, 17, 42.8, 29.06, 0.00215, 18, 29.42, 16.95, 0.55828, 19, 22.65, -23.7, 0.36336, 20, -12.68, -23.15, 0.07621, 6, 3, 28.53, -67.62, 0.00437, 4, -6.7, -67.28, 0.07726, 13, -86.36, 77.14, 2e-05, 15, -47.9, 75.46, 0, 17, -9.22, 53, 0.03141, 19, -7.38, 25.05, 0.88695, 5, 4, -26.79, -81.78, 0.00638, 13, -77.33, 100.21, 0, 15, -31, 93.57, 0, 19, 15.44, 15.41, 0.974, 20, -18.1, 16.25, 0.01962, 4, 13, -68.45, 121.3, 0, 15, -14.97, 109.91, 0, 19, 36.3, 5.98, 0.43665, 20, 2.31, 5.88, 0.56335, 2, 18, 60.44, 37.72, 0.02376, 20, 20.75, -6.53, 0.97624, 6, 3, 9.35, 18.7, 0.55047, 4, -17.34, 20.5, 0.03169, 13, -29.88, 9.1, 0.19669, 15, -20.48, -8.6, 0.22113, 17, -36.76, -31.03, 1e-05, 19, -76.88, -29.62, 1e-05, 4, 13, -1.32, 15.82, 0.3834, 15, 8.55, -12.89, 0.61133, 16, -14.26, -18.6, 0.00527, 19, -70.92, -58.35, 0, 5, 13, 31.79, 25.81, 0.03929, 14, 2.24, 27.18, 0.18112, 15, 43.01, -15.81, 0.0014, 16, 19.25, -10.05, 0.77819, 17, 10.22, -74.36, 0, 6, 3, 5.81, 35.56, 0.21975, 4, -19.22, 37.62, 0.02229, 13, -19.03, -4.27, 0.60832, 15, -15.33, -25.04, 0.14963, 17, -42.3, -47.34, 1e-05, 19, -90.53, -40.11, 1e-05, 2, 13, 12.36, -3.72, 0.96148, 14, -9.39, -6.2, 0.03852, 3, 14, 17.05, 7.02, 0.9109, 16, 27.67, -33.61, 0.0891, 17, -4.37, -94.68, 0], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 2, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 44, 66, 66, 68, 68, 70, 72, 74, 74, 76], "width": 232, "height": 140}}, "j1": {"j1": {"type": "mesh", "uvs": [0.35102, 0.68761, 0.22329, 0.60185, 0.13921, 0.47469, 0.18125, 0.28394, 0.31868, 0.14939, 0.4836, 0.04589, 0.64851, 0, 0.83445, 0.01483, 0.98158, 0.15826, 1, 0.36527, 0.95247, 0.52644, 0.89427, 0.69353, 0.80049, 0.71127, 0.82636, 0.77929, 0.73582, 0.82217, 0.58546, 0.81921, 0.43671, 0.87244, 0.33485, 0.94933, 0.23461, 1, 0.10041, 0.93159, 0.01634, 0.86209, 0, 0.74084, 0.08101, 0.71718, 0.19742, 0.72606, 0.29281, 0.71423, 0.74169, 0.10196, 0.63089, 0.27422, 0.53395, 0.38567, 0.51456, 0.48446, 0.56442, 0.58326, 0.62258, 0.70738, 0.47855, 0.75804, 0.36222, 0.8011, 0.19603, 0.85177, 0.09631, 0.85683], "triangles": [24, 0, 32, 23, 34, 22, 33, 23, 24, 33, 24, 32, 33, 34, 23, 21, 22, 34, 20, 21, 34, 19, 34, 33, 20, 34, 19, 17, 33, 32, 17, 32, 16, 18, 33, 17, 19, 33, 18, 14, 12, 13, 31, 0, 29, 30, 31, 29, 32, 0, 31, 15, 31, 30, 14, 30, 12, 15, 30, 14, 16, 32, 31, 16, 31, 15, 12, 30, 29, 3, 4, 27, 28, 2, 3, 27, 28, 3, 27, 26, 10, 29, 28, 27, 10, 29, 27, 1, 2, 28, 0, 1, 28, 0, 28, 29, 10, 12, 29, 10, 11, 12, 25, 6, 7, 25, 7, 8, 25, 26, 5, 25, 5, 6, 4, 5, 26, 27, 4, 26, 26, 8, 9, 8, 26, 25, 10, 26, 9], "vertices": [3, 25, 77.14, -5.17, 0, 27, 23.9, -13.85, 0.72639, 28, -1.69, -14.12, 0.27361, 3, 25, 73.49, -21.73, 0.035, 26, 20.76, -39.05, 0.96374, 28, 7.46, -28.39, 0.00126, 2, 25, 63.51, -35.97, 0.13593, 26, 4.69, -45.68, 0.86407, 2, 25, 41.28, -40.87, 0.41168, 26, -16.69, -37.84, 0.58832, 2, 25, 20.94, -33.78, 0.78933, 26, -30.01, -20.91, 0.21067, 3, 25, 2.73, -22.53, 0.99114, 26, -39.3, -1.63, 0.00824, 27, -23.2, -74.02, 0.00062, 1, 25, -9.31, -8.55, 1, 2, 25, -15.76, 10.35, 0.99105, 26, -37.18, 36.03, 0.00895, 2, 25, -6.76, 31.53, 0.88379, 26, -18.2, 49.04, 0.11621, 2, 25, 14.59, 43.12, 0.59496, 26, 6.04, 47.3, 0.40504, 3, 25, 33.9, 46.08, 0.28311, 26, 23.9, 39.41, 0.71291, 27, -41.93, -1.03, 0.00398, 2, 25, 54.3, 48.28, 0.09118, 26, 42.28, 30.28, 0.90882, 1, 27, -17.55, 10.71, 1, 1, 27, -16.35, 19.05, 1, 2, 25, 74.91, 38.85, 0.0006, 27, -5.44, 19.05, 0.9994, 1, 27, 8.69, 11.35, 1, 2, 27, 25.69, 9.57, 0.64601, 28, -2.49, 9.36, 0.35399, 2, 27, 39.5, 12.56, 0.0096, 28, 10.91, 13.84, 0.9904, 1, 28, 23.05, 15.52, 1, 1, 28, 33.58, 2.9, 1, 1, 28, 39.06, -7.92, 1, 1, 28, 35.6, -21.79, 1, 1, 28, 26.51, -21.26, 1, 2, 27, 40.56, -17.4, 0.00434, 28, 15.26, -15.82, 0.99566, 2, 27, 30.86, -13.94, 0.21172, 28, 5.24, -13.45, 0.78828, 2, 25, -2.42, 5.39, 0.99395, 26, -28.61, 24.67, 0.00605, 2, 25, 20.8, 2.68, 0.98179, 26, -10.5, 9.89, 0.01821, 3, 25, 36.92, -1.54, 0.1382, 26, 0.81, -2.35, 0.85831, 27, -9.72, -36.23, 0.00349, 2, 26, 11.92, -6.15, 0.96693, 27, -2.57, -26.92, 0.03307, 2, 26, 24.16, -2.64, 0.97178, 27, -1.99, -14.2, 0.02822, 2, 25, 67.52, 22.34, 0.00033, 27, -0.85, 1.56, 0.99967, 2, 26, 42.97, -14.83, 0.00239, 27, 15.56, -0.26, 0.99761, 3, 26, 46.06, -27.9, 0.00425, 27, 28.93, -1.5, 0.05909, 28, 1.96, -1.29, 0.93666, 1, 28, 20.68, -2.14, 1, 1, 28, 30.85, -5.42, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 14, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68], "width": 107, "height": 117}}, "j2": {"j2": {"type": "mesh", "uvs": [0.0917, 0.03936, 0.21044, 0, 0.35165, 0.00173, 0.46879, 0.07016, 0.60358, 0.17451, 0.73195, 0.31821, 0.82823, 0.491, 0.80576, 0.59193, 0.74158, 0.64497, 0.6806, 0.68602, 0.78009, 0.73563, 0.86193, 0.75616, 0.94216, 0.72708, 0.99993, 0.77498, 0.96944, 0.89644, 0.83144, 0.94948, 0.65332, 0.99738, 0.55383, 0.99396, 0.5073, 0.91697, 0.5057, 0.83828, 0.44311, 0.78182, 0.38856, 0.74419, 0.27142, 0.73221, 0.18958, 0.71339, 0.09651, 0.58509, 0.01949, 0.42428, 0, 0.29426, 0.01628, 0.13516, 0.21186, 0.12852, 0.31793, 0.28727, 0.39137, 0.41775, 0.46888, 0.55911, 0.52395, 0.68306, 0.59127, 0.7744, 0.68918, 0.86574, 0.81157, 0.89618], "triangles": [16, 35, 15, 16, 34, 35, 16, 17, 34, 17, 18, 34, 15, 35, 14, 35, 11, 14, 14, 11, 13, 13, 11, 12, 34, 10, 35, 35, 10, 11, 34, 9, 10, 18, 33, 34, 33, 9, 34, 18, 19, 33, 33, 19, 32, 19, 20, 32, 20, 21, 32, 33, 32, 9, 9, 32, 31, 32, 21, 31, 8, 9, 31, 31, 21, 22, 31, 22, 30, 30, 22, 24, 22, 23, 24, 7, 8, 6, 5, 6, 8, 8, 31, 5, 31, 30, 5, 30, 4, 5, 24, 29, 30, 24, 25, 29, 29, 25, 26, 29, 26, 28, 28, 27, 0, 27, 28, 26, 4, 29, 3, 4, 30, 29, 29, 2, 3, 29, 28, 2, 0, 1, 28, 28, 1, 2], "vertices": [2, 21, -20.4, -6.21, 0.99482, 22, -56.65, -12.75, 0.00518, 3, 21, -15.62, 9.11, 0.98967, 22, -53.66, 3.01, 0.01016, 23, -87.69, 20.05, 0.00018, 3, 21, -5.12, 23.99, 0.93019, 22, -44.91, 18.99, 0.06563, 23, -76.07, 34.07, 0.00417, 3, 21, 10.28, 31.73, 0.77511, 22, -30.5, 28.44, 0.19772, 23, -60.12, 40.61, 0.02716, 3, 21, 30.54, 38.88, 0.52844, 22, -11.18, 37.85, 0.37999, 23, -39.37, 46.18, 0.09157, 4, 21, 54.26, 42.65, 0.2705, 22, 11.95, 44.3, 0.53095, 23, -15.43, 48.11, 0.19833, 24, -9.98, 60.42, 0.00021, 4, 21, 78.52, 41.01, 0.09356, 22, 36.24, 45.44, 0.57722, 23, 8.63, 44.61, 0.32575, 24, 9.41, 45.74, 0.00347, 4, 21, 86.93, 31.7, 0.01667, 22, 45.66, 37.14, 0.49569, 23, 16.3, 34.67, 0.45194, 24, 11.34, 33.34, 0.03571, 4, 21, 87.51, 21.24, 0.00092, 22, 47.43, 26.81, 0.33848, 23, 16.08, 24.2, 0.49257, 24, 6.09, 24.27, 0.16803, 4, 21, 87.14, 11.94, 3e-05, 22, 48.12, 17.54, 0.15971, 23, 14.99, 14.96, 0.43535, 24, 0.68, 16.7, 0.40492, 2, 23, 27.73, 21.16, 0.26894, 24, 14.83, 15.99, 0.73106, 2, 23, 36.3, 27.8, 0.008, 24, 25.54, 17.67, 0.992, 1, 24, 33.8, 24.83, 1, 2, 23, 49.27, 40.2, 0.00046, 24, 42.89, 22.28, 0.99954, 2, 23, 58.22, 27.9, 0.00613, 24, 44.79, 7.18, 0.99387, 1, 24, 30.73, -5.48, 1, 1, 24, 11.64, -19.51, 1, 1, 24, -0.41, -23.97, 1, 2, 23, 22.64, -20, 0.128, 24, -9.48, -17.61, 0.872, 3, 22, 53.77, -11.04, 0.064, 23, 15.11, -14.17, 0.70992, 24, -13.26, -8.87, 0.22608, 4, 21, 79.3, -19.86, 0.01502, 22, 43.95, -14.95, 0.22145, 23, 4.72, -16.15, 0.68469, 24, -23.32, -5.59, 0.07884, 4, 21, 71.56, -23.07, 0.05512, 22, 36.62, -19.03, 0.46479, 23, -3.24, -18.75, 0.46675, 24, -31.55, -4.03, 0.01334, 4, 21, 61.79, -34.69, 0.1498, 22, 28.24, -31.68, 0.63666, 23, -13.88, -29.58, 0.21348, 24, -46.09, -8.39, 5e-05, 3, 21, 53.92, -42.09, 0.30537, 22, 21.27, -39.93, 0.64182, 23, -22.29, -36.36, 0.05281, 3, 21, 34.33, -43.17, 0.51582, 22, 1.92, -43.23, 0.48126, 23, -41.91, -35.92, 0.00292, 3, 21, 12.67, -40.31, 0.72798, 22, -19.92, -42.86, 0.27188, 23, -63.29, -31.4, 0.00014, 2, 21, -1.71, -33.45, 0.88441, 22, -34.99, -37.68, 0.11559, 2, 21, -16.37, -20.8, 0.96701, 22, -50.99, -26.78, 0.03299, 2, 21, -2.72, 0.43, 0.88871, 22, -39.84, -4.13, 0.11129, 3, 21, 20.87, 0.8, 0.66667, 22, -16.46, -1.08, 0.33321, 23, -51.95, 8.95, 0.00012, 3, 21, 39.24, -0.36, 0.33333, 22, 1.93, -0.14, 0.55532, 23, -33.72, 6.38, 0.11135, 3, 21, 59, -1.83, 0.11111, 22, 21.73, 0.65, 0.5552, 23, -14.13, 3.39, 0.33369, 4, 21, 75.38, -4.5, 0, 22, 38.3, -0.13, 0.3331, 23, 2, -0.52, 0.55579, 24, -18.17, 9.41, 0.11111, 4, 21, 89.41, -3.62, 0, 22, 52.14, 2.34, 0.11099, 23, 16.05, -0.73, 0.55567, 24, -5.96, 2.45, 0.33333, 2, 21, 105.67, 0.51, 0, 24, 9.91, -3.02, 1, 1, 24, 25.92, -0.47, 1], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 129, "height": 121}}, "ss1": {"ss1": {"type": "mesh", "uvs": [0.71344, 0.02441, 0.88344, 0.08353, 0.9887, 0.20995, 0.96382, 0.40266, 0.87005, 0.59999, 0.77819, 0.76803, 0.67867, 0.91141, 0.5491, 1, 0.29193, 0.96142, 0.15082, 0.89909, 0, 0.75609, 0, 0.59476, 0.05524, 0.41326, 0.1531, 0.21526, 0.26689, 0.05759, 0.474, 0, 0.5698, 0.16554, 0.4763, 0.32304, 0.38279, 0.49081, 0.31053, 0.70309, 0.30203, 0.86743], "triangles": [18, 12, 13, 11, 12, 18, 19, 11, 18, 5, 19, 18, 10, 11, 19, 4, 5, 18, 19, 9, 10, 6, 20, 19, 20, 9, 19, 5, 6, 19, 8, 9, 20, 7, 8, 20, 6, 7, 20, 16, 15, 0, 16, 14, 15, 17, 14, 16, 13, 14, 17, 2, 16, 1, 17, 18, 13, 3, 16, 2, 1, 16, 0, 3, 17, 16, 4, 17, 3, 18, 17, 4], "vertices": [1, 33, -10.58, 7.13, 1, 2, 33, -9.93, 17.85, 0.99955, 34, -35.13, 19.41, 0.00045, 2, 33, -3.45, 26.69, 0.98244, 34, -28.27, 27.95, 0.01756, 2, 33, 10.08, 30.06, 0.86947, 34, -14.6, 30.73, 0.13053, 2, 33, 25.29, 29.78, 0.52645, 34, 0.58, 29.79, 0.47355, 2, 33, 38.48, 28.9, 0.18847, 34, 13.72, 28.32, 0.81153, 2, 33, 50.16, 26.99, 0.03997, 34, 25.3, 25.9, 0.96003, 2, 33, 58.71, 22.09, 0.00475, 34, 33.63, 20.64, 0.99525, 1, 34, 35.44, 5.57, 1, 1, 34, 33.61, -3.58, 1, 1, 34, 26.4, -15, 1, 2, 33, 42.13, -17.79, 0.00234, 34, 15.31, -18.48, 0.99766, 2, 33, 28.75, -19.23, 0.21784, 34, 1.89, -19.33, 0.78216, 2, 33, 13.41, -18.75, 0.81201, 34, -13.41, -18.18, 0.18799, 2, 33, 0.49, -16.41, 0.98531, 34, -26.22, -15.28, 0.01469, 1, 33, -7.5, -6.53, 1, 1, 33, 1.82, 2.76, 1, 1, 33, 14.33, 1.52, 1, 1, 34, 1.53, 0.47, 1, 2, 33, 43.33, 1.8, 0.00031, 34, 17.37, 1.04, 0.99969, 1, 34, 28.81, 4.11, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 58, "height": 72}}, "sd": {"sd": {"x": -0.58, "y": 6.29, "width": 222, "height": 88}}, "ss2": {"ss2": {"type": "mesh", "uvs": [0.68799, 0.01308, 0.85817, 0.08779, 1, 0.28773, 0.95177, 0.53381, 0.86951, 0.76232, 0.79861, 0.88536, 0.63126, 1, 0.26537, 0.98204, 0, 0.87877, 0, 0.65686, 0.0413, 0.52722, 0.09803, 0.29872, 0.20297, 0.07021, 0.37599, 0, 0.63574, 0.16765, 0.55718, 0.405, 0.44719, 0.64843, 0.35291, 0.80666], "triangles": [16, 10, 15, 16, 15, 3, 9, 10, 16, 4, 16, 3, 17, 9, 16, 8, 9, 17, 5, 16, 4, 17, 16, 5, 7, 8, 17, 6, 17, 5, 7, 17, 6, 14, 13, 0, 14, 0, 1, 14, 12, 13, 15, 12, 14, 11, 12, 15, 10, 11, 15, 2, 14, 1, 2, 15, 14, 3, 15, 2], "vertices": [1, 30, -9.97, 6.92, 1, 2, 30, -5.34, 16.63, 0.99966, 31, -34.16, 6.67, 0.00034, 2, 30, 8.27, 25.41, 0.92851, 31, -23.81, 19.13, 0.07149, 2, 30, 25.89, 23.99, 0.4955, 31, -6.59, 23.07, 0.5045, 2, 30, 42.39, 20.61, 0.04703, 31, 10.17, 24.82, 0.95297, 2, 30, 51.38, 17.34, 0.00139, 31, 19.72, 24.39, 0.99861, 1, 31, 30.67, 18.82, 1, 1, 31, 36.87, -0.37, 1, 1, 31, 35.4, -16.64, 1, 2, 30, 38.28, -27.62, 0.03534, 31, 20.74, -22.42, 0.96466, 2, 30, 28.94, -26, 0.18113, 31, 11.35, -23.68, 0.81887, 2, 30, 12.54, -24.03, 0.69954, 31, -4.89, -26.73, 0.30046, 2, 30, -4.05, -19.41, 0.96919, 31, -22.1, -27.31, 0.03081, 2, 30, -9.69, -10.27, 0.99809, 31, -30.23, -20.29, 0.00191, 1, 30, 1.18, 4.82, 1, 1, 30, 18.29, 1.69, 1, 1, 31, 11.16, 0.24, 1, 1, 31, 23.52, -0.46, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 32, 32, 34], "width": 55, "height": 71}}, "tou1": {"tou1": {"x": 18.9, "y": -26.2, "rotation": -99.59, "width": 170, "height": 46}}, "tou2": {"tou2": {"x": -25.15, "y": -22.81, "rotation": -99.59, "width": 216, "height": 158}}, "st": {"st": {"type": "mesh", "uvs": [0.05218, 0.08887, 0.15253, 0.03227, 0.28801, 0.06802, 0.4285, 0.17229, 0.49122, 0.23783, 0.57401, 0.214, 0.6869, 0.214, 0.81485, 0.27656, 0.91269, 0.41658, 0.98795, 0.62214, 0.99046, 0.83664, 0.93276, 0.97368, 0.76969, 1, 0.53888, 0.9707, 0.33316, 0.86047, 0.2278, 0.64597, 0.2002, 0.46425, 0.15755, 0.2855, 0.10487, 0.17229, 0.55758, 0.36341, 0.63651, 0.52641, 0.70604, 0.71971, 0.73097, 0.86369, 0.30546, 0.24173, 0.40625, 0.44641, 0.46988, 0.64631, 0.52213, 0.84238, 0.75791, 0.381, 0.83013, 0.58122, 0.85488, 0.732, 0.86566, 0.86865], "triangles": [27, 7, 8, 6, 19, 5, 4, 23, 3, 19, 4, 5, 1, 2, 23, 23, 2, 3, 18, 0, 1, 27, 6, 7, 19, 6, 27, 23, 18, 1, 13, 22, 12, 12, 30, 11, 12, 22, 30, 11, 30, 10, 14, 26, 13, 13, 26, 22, 22, 29, 30, 30, 29, 10, 26, 21, 22, 22, 21, 29, 29, 9, 10, 14, 25, 26, 26, 25, 21, 21, 28, 29, 29, 28, 9, 25, 20, 21, 21, 20, 28, 28, 8, 9, 20, 27, 28, 28, 27, 8, 14, 15, 25, 15, 24, 25, 20, 24, 19, 20, 25, 24, 15, 16, 24, 20, 19, 27, 16, 23, 24, 16, 17, 23, 24, 4, 19, 4, 24, 23, 17, 18, 23], "vertices": [2, 41, -21.97, -15.5, 0.152, 5, 54.32, -8.46, 0.848, 2, 41, -21.36, -9.2, 0.152, 5, 56.23, -14.5, 0.848, 2, 41, -15.91, -3.46, 0.152, 5, 53.48, -21.92, 0.848, 2, 41, -7.5, 0.83, 0.152, 5, 47.43, -29.17, 0.848, 2, 41, -2.96, 2.28, 0.152, 5, 43.83, -32.28, 0.848, 3, 41, -1.52, 6.91, 0.15049, 42, -15.7, 4.79, 0.00151, 5, 44.32, -37.11, 0.848, 3, 41, 1.79, 12.43, 0.13712, 42, -13.19, 10.71, 0.01488, 5, 43.44, -43.49, 0.848, 4, 41, 8.12, 17.14, 0.10254, 42, -7.57, 16.25, 0.04881, 43, -21.77, 12.41, 0.00065, 5, 39.48, -50.31, 0.848, 4, 41, 16.75, 18.47, 0.22695, 42, 0.79, 18.76, 0.38672, 43, -14.02, 16.43, 0.05033, 5, 32.06, -54.92, 0.336, 3, 41, 27.42, 17.07, 0.0594, 42, 11.55, 18.85, 0.55553, 43, -3.46, 18.53, 0.38507, 3, 41, 36.32, 11.9, 0.00057, 42, 21.09, 14.96, 0.21104, 43, 6.63, 16.48, 0.7884, 2, 42, 25.86, 9.36, 0.05597, 43, 12.36, 11.87, 0.94403, 1, 43, 11.62, 2.52, 1, 3, 41, 28.6, -13.49, 0.03213, 42, 16.95, -11.25, 0.23632, 43, 7.45, -10.04, 0.73155, 3, 41, 18.03, -20.82, 0.30589, 42, 7.5, -19.98, 0.47698, 43, -0.21, -20.37, 0.21713, 3, 41, 6.11, -20.67, 0.6839, 42, -4.33, -21.48, 0.27519, 43, -11.55, -24.05, 0.04091, 3, 41, -2.18, -17.54, 0.90349, 42, -12.97, -19.52, 0.09307, 43, -20.4, -23.74, 0.00344, 2, 41, -10.79, -15.21, 0.98974, 42, -21.82, -18.41, 0.01026, 3, 41, -16.99, -14.99, 0.66377, 42, -27.99, -19.05, 0.00023, 5, 49.94, -10.89, 0.336, 3, 41, 4.15, 2.42, 0.66265, 42, -9.47, 1.12, 0.00135, 5, 37.34, -35.21, 0.336, 2, 41, 13.17, 2.26, 0.57746, 42, -0.51, 2.21, 0.42254, 2, 42, 9.58, 2.23, 0.92295, 43, -2.3, 1.83, 0.07705, 1, 43, 4.76, 1.75, 1, 3, 41, -8.25, -6.9, 0.661, 42, -20.46, -9.82, 0.003, 5, 45.08, -21.77, 0.336, 3, 41, 3.13, -7.02, 0.93076, 42, -9.17, -8.37, 0.06771, 43, -18.75, -12.08, 0.00153, 3, 41, 13.22, -8.85, 0.44214, 42, 1.08, -8.78, 0.49987, 43, -8.6, -10.57, 0.058, 3, 41, 22.83, -11.14, 0.07864, 42, 10.91, -9.72, 0.448, 43, 1.23, -9.66, 0.47336, 4, 41, 10.75, 11.78, 0.42619, 42, -4.23, 11.3, 0.23358, 43, -17.56, 8.17, 0.00422, 5, 34.95, -46.41, 0.336, 3, 41, 21.11, 10.37, 0.13013, 42, 6.23, 11.34, 0.70411, 43, -7.29, 10.16, 0.16576, 3, 41, 28.04, 7.85, 0.01041, 42, 13.44, 9.81, 0.42841, 43, 0.08, 10, 0.56117, 2, 42, 19.72, 7.81, 0.10203, 43, 6.62, 9.21, 0.89797], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 8, 38, 38, 40, 40, 42, 42, 44, 4, 46, 2, 46, 46, 48, 48, 50, 50, 52, 12, 54, 54, 56, 56, 58, 58, 60], "width": 57, "height": 48}}, "hudiej": {"hudiej": {"type": "mesh", "uvs": [0.33666, 0.58935, 0.35115, 0.63976, 0.38346, 0.65236, 0.42913, 0.66181, 0.44361, 0.62716, 0.51492, 0.70277, 0.61964, 0.79413, 0.70542, 0.72167, 0.79677, 0.6114, 0.91821, 0.6051, 1, 0.44627, 1, 0.32101, 0.92463, 0.17677, 0.88302, 0.0667, 0.78504, 0.05531, 0.65216, 0.154, 0.52868, 0.24889, 0.45352, 0.32101, 0.40788, 0.30203, 0.35956, 0.29064, 0.26561, 0.15779, 0.17031, 0.07429, 0.11662, 0.01356, 0.05891, 0.17677, 0, 0.34378, 0.02133, 0.54116, 0.1032, 0.70058, 0.21326, 0.78029, 0.28574, 0.66642, 0.33123, 0.43449, 0.39162, 0.48488, 0.4431, 0.47368, 0.53319, 0.45968, 0.61239, 0.45129, 0.70545, 0.38409, 0.79455, 0.3337, 0.85692, 0.3001, 0.13959, 0.2868, 0.21599, 0.38375, 0.28357, 0.42253], "triangles": [36, 14, 13, 36, 13, 12, 35, 14, 36, 12, 11, 10, 9, 36, 12, 9, 12, 10, 8, 35, 36, 8, 36, 9, 34, 35, 8, 34, 15, 14, 35, 34, 14, 33, 16, 15, 33, 15, 34, 7, 33, 34, 7, 34, 8, 6, 33, 7, 6, 5, 33, 23, 22, 37, 37, 25, 24, 37, 24, 23, 26, 25, 37, 37, 22, 21, 38, 21, 20, 37, 21, 38, 26, 37, 38, 27, 26, 38, 28, 27, 38, 39, 20, 19, 38, 20, 39, 29, 39, 19, 29, 19, 30, 0, 29, 30, 1, 0, 30, 0, 28, 39, 0, 39, 29, 28, 38, 39, 32, 16, 33, 17, 16, 32, 31, 17, 32, 4, 31, 32, 30, 31, 4, 5, 4, 32, 5, 32, 33, 2, 1, 30, 30, 3, 2, 4, 3, 30, 31, 18, 17, 30, 19, 18, 30, 18, 31], "vertices": [4, 4, -8.88, 17.53, 0.00379, 3, 18.06, 16.58, 0.32846, 10, -1.2, 13.84, 0.65925, 11, -26.02, 17.07, 0.00851, 3, 3, 14.39, 11.79, 0.63348, 10, -5.36, 18.22, 0.36618, 11, -29.63, 21.91, 0.00033, 2, 3, 15.22, 3.25, 0.92188, 10, -13.92, 18.73, 0.07812, 2, 3, 17.16, -8.64, 0.79782, 7, -8.12, -17.72, 0.20218, 3, 4, -8.54, -10.81, 0.00287, 3, 21.18, -11.59, 0.5561, 7, -4.14, -14.71, 0.44103, 3, 3, 18.69, -31.47, 0.06069, 7, 14.19, -22.76, 0.91702, 8, -26.97, -18.75, 0.02229, 4, 3, 16.83, -60.23, 5e-05, 7, 41.23, -32.76, 0.44762, 8, -1.84, -32.88, 0.50918, 9, -32.02, -43.33, 0.04315, 3, 7, 64.12, -27.27, 0.06046, 8, 21.63, -31.07, 0.6511, 9, -10.02, -34.94, 0.28843, 2, 8, 47.28, -26.13, 0.06569, 9, 13.18, -22.92, 0.93431, 1, 9, 44.98, -19.97, 1, 1, 9, 65.34, -3.64, 1, 1, 9, 64.47, 7.98, 1, 2, 8, 88.7, 6.26, 0.05731, 9, 43.71, 19.88, 0.94269, 2, 8, 80.17, 18.58, 0.21402, 9, 32.04, 29.28, 0.78598, 2, 8, 55.21, 25.07, 0.78836, 9, 6.26, 28.42, 0.21164, 2, 7, 53.03, 26.22, 0.04539, 8, 19.11, 23.5, 0.95461, 3, 4, 29.35, -28.19, 0.00268, 7, 20.12, 19.19, 0.93318, 8, -14.5, 21.74, 0.06414, 2, 4, 20.02, -9.52, 0.42717, 7, 0.02, 13.57, 0.57283, 2, 4, 20.14, 2.61, 0.94537, 10, -17.79, -14.25, 0.05463, 2, 4, 19.46, 15.35, 0.36733, 10, -5.04, -14.32, 0.63267, 4, 4, 28.34, 41.51, 0, 10, 20.56, -24.71, 0.66751, 11, -9.02, -23.81, 0.32481, 12, -33.31, -31.23, 0.00768, 3, 10, 46.15, -30.5, 0.05595, 11, 15.7, -32.61, 0.68303, 12, -7.32, -34.82, 0.26102, 3, 10, 60.67, -35.04, 0.00156, 11, 29.57, -38.84, 0.53365, 12, 7.53, -38.11, 0.46478, 2, 11, 45.37, -24.31, 0.19174, 12, 20.05, -20.67, 0.80826, 1, 12, 32.81, -2.83, 1, 1, 12, 24.3, 14.38, 1, 3, 10, 59.21, 28.94, 0.01237, 11, 35.76, 24.85, 0.43246, 12, 0.65, 25.51, 0.55517, 3, 10, 29.77, 34.07, 0.3101, 11, 7.15, 33.46, 0.66945, 12, -29.12, 28.13, 0.02045, 3, 3, 7.98, 27.94, 0.02471, 10, 11.6, 22.03, 0.766, 11, -12.34, 23.67, 0.2093, 1, 10, 1.35, -0.4, 1, 3, 4, 2.71, 4.54, 0.67672, 3, 30.87, 4.78, 0.18707, 10, -14.85, 3.03, 0.13622, 3, 4, 5.58, -8.74, 0.2595, 3, 35.03, -8.15, 0.04601, 7, -3.5, -0.45, 0.69449, 2, 3, 41.8, -30.89, 0.00043, 7, 20.23, -0.45, 0.99957, 3, 7, 41.07, -0.82, 0.00325, 8, 3.04, -1.31, 0.99612, 9, -36.29, -11.68, 0.00063, 2, 8, 28.28, -0.38, 0.99868, 9, -12.34, -3.63, 0.00132, 2, 8, 52.18, -0.76, 0.09744, 9, 10.68, 2.79, 0.90256, 2, 8, 68.87, -1.18, 0.0611, 9, 26.8, 7.12, 0.9389, 3, 10, 52.67, -10.17, 0.00261, 11, 24.59, -13.2, 0.68032, 12, -2.55, -14.01, 0.31707, 3, 10, 31.93, -2.75, 0.05674, 11, 4.89, -3.36, 0.94186, 12, -23.84, -8.37, 0.00139, 2, 4, 4.59, 33.48, 6e-05, 10, 13.93, -0.54, 0.99994], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 46, 74, 74, 76, 76, 78], "width": 263, "height": 93}}}}], "animations": {"idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.75, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": -0.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.5}], "translate": [{"x": 0.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.6}]}, "bone5": {"rotate": [{"angle": -1.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.25}], "translate": [{"x": 1.1, "y": -0.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.54, "y": -0.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.1, "y": -0.05}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.53, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.27, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone13": {"rotate": [{"angle": -1.41, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -6.97, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -1.41}]}, "bone14": {"rotate": [{"angle": -4.39, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -1.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -6.97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -4.39}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.97, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone16": {"rotate": [{"angle": -1.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.98}]}, "bone17": {"rotate": [{"angle": 1.33, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 6.58, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 1.33}]}, "bone18": {"rotate": [{"angle": 4.15, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": 1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 6.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 4.15}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.58, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone20": {"rotate": [{"angle": 1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.58, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.87}]}, "bone21": {"rotate": [{"angle": 0.32}]}, "bone22": {"rotate": [{"angle": -1.07}]}, "bone23": {"rotate": [{"angle": -0.18}]}, "bone24": {"rotate": [{"angle": 0.53}]}, "bone25": {"rotate": [{"angle": 0.09}]}, "bone26": {"rotate": [{"angle": -0.51}]}, "bone27": {"rotate": [{"angle": -2.72}]}, "bone28": {"rotate": [{"angle": 6.31}]}, "bone29": {"translate": [{"x": 1.58, "y": -1.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.15, "y": -2.42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.58, "y": -1.21}]}, "bone30": {"rotate": [{"angle": 10.51, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 11.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 10.51}]}, "bone31": {"rotate": [{"angle": 10.51, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 11.64, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 10.51}]}, "bone32": {"translate": [{"x": 0.18, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.37, "y": 1.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.18, "y": 0.99}]}, "bone33": {"rotate": [{"angle": -7.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -7.97, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -7.2}]}, "bone34": {"rotate": [{"angle": -7.2, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -7.97, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -7.2}]}, "bone37": {"rotate": [{"angle": -2.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -11.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 20.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.33}]}, "bone38": {"rotate": [{"angle": 11.49, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -11.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 20.56, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 11.49}]}, "bone39": {"rotate": [{"angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.4, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 20.56}]}}}, "train": {"slots": {"tou1": {"attachment": [{"time": 0.2, "name": "tou1"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 13.18, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -36.91, "curve": "stepped"}, {"time": 0.3333, "angle": -36.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"curve": 0.314, "c3": 0.649, "c4": 0.35}, {"time": 0.1, "x": 2.07, "y": -32.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 23.32, "y": 111.78, "curve": "stepped"}, {"time": 0.3333, "x": 23.32, "y": 111.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 1.56, "curve": "stepped"}, {"time": 0.3333, "angle": 1.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": 0.55, "y": -11.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone4": {"rotate": [{"angle": 0.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "angle": 10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.56, "curve": "stepped"}, {"time": 0.3333, "angle": 1.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4667, "angle": 0.2}], "translate": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "x": -13.64, "y": -2.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}], "scale": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.038, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone5": {"rotate": [{"angle": 0.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "angle": 10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.56, "curve": "stepped"}, {"time": 0.3333, "angle": 1.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4667, "angle": 0.2}], "translate": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "x": -9.84, "y": -1.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}], "scale": [{"time": 0.0333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "x": 0.873, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.107, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone13": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -25.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone14": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -25.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone15": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -25.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone16": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -25.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone21": {"rotate": [{"angle": 0.32, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 38.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 70.47, "curve": "stepped"}, {"time": 0.3333, "angle": 70.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.32}]}, "bone22": {"rotate": [{"angle": -1.07, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -82.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -87.95, "curve": "stepped"}, {"time": 0.3333, "angle": -87.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.07}]}, "bone23": {"rotate": [{"angle": -0.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 30.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 53.46, "curve": "stepped"}, {"time": 0.3333, "angle": 53.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.18}]}, "bone24": {"rotate": [{"angle": 0.53}]}, "bone25": {"rotate": [{"angle": 0.09, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -78.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -33.39, "curve": "stepped"}, {"time": 0.3333, "angle": -33.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.09}]}, "bone26": {"rotate": [{"angle": -0.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": 95.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 78.98, "curve": "stepped"}, {"time": 0.3333, "angle": 78.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -0.51}]}, "bone27": {"rotate": [{"angle": -2.72, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -32.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -11.83, "curve": "stepped"}, {"time": 0.3333, "angle": -11.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -2.72}]}, "bone28": {"rotate": [{"angle": 6.31}]}, "bone35": {"rotate": [{"time": 0.1, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2, "angle": -12.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -92.08, "curve": "stepped"}, {"time": 0.3333, "angle": -92.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 52.37, "y": 127.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -89.8, "y": 203.73, "curve": 0.305, "c2": 0.23, "c3": 0.647, "c4": 0.6}, {"time": 0.3333, "x": -9.87, "y": 155.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone36": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -46.3, "y": 99.42, "curve": "stepped"}, {"time": 0.3333, "x": -46.3, "y": 99.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -58.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 84.16, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.2667, "angle": -99.43, "curve": "stepped"}, {"time": 0.4333, "angle": -99.43, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.4667}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -1, "curve": "stepped"}, {"time": 0.4333, "x": -1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone38": {"rotate": [{"angle": 5.39, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 24.04, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 5.39}]}, "bone39": {"rotate": [{"angle": 14.29, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -15.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 24.04, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.4667, "angle": 14.29}]}}}}}