import { Animation, Label, Node, UITransform, _decorator, instantiate, isValid, tween, v3 } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import FightManager, { scaleList } from "./FightManager";
import ResMgr from "../../../lib/common/ResMgr";
import { RecoverDetail } from "../FightDefine";
import Formate from "../../../lib/utils/Formate";
const { ccclass, property } = _decorator;

@ccclass("RecoverManager")
export class RecoverManager extends ManagerSection {
  public static sectionName(): string {
    return "RecoverManager";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("RecoverLabelRoot", FightManager.instance);
    this.root.getComponent(UITransform).setContentSize(FightManager.instance.getComponent(UITransform).contentSize);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
  }

  public callRecover(detail: RecoverDetail) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    let path = "resources?prefab/effectLab/recove_lab";
    ResMgr.loadPrefab(path, (prefab) => {
      if (isValid(this.root) == false) {
        return;
      }
      let node = instantiate(prefab);
      // node.layer = Layers.Enum["UILayer"];
      let lab = node.getChildByName("lab");
      lab.getComponent(Label).string = "+" + Formate.format(detail.recover);
      this.root.addChild(node);
      detail.goRole.getRecovePos(node);
      let layer = detail.goRole.layer;
      node.walk((child: Node) => (child.layer = layer));
      // 假设你已经有一个动画实例，这里命名为animation
      let animation = node.getComponent(Animation);
      animation.play("recove_lab");
      let defaultClip = animation.defaultClip;
      defaultClip.speed = scaleList[FightManager.instance.speed];
      // 监听动画结束事件
      animation.on(
        Animation.EventType.FINISHED,
        function () {
          // 动画播放结束时的处理逻辑
          node.removeFromParent();
          node.destroy();
        },
        this
      );
    });
  }
}
