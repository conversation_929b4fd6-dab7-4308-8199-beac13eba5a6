import { <PERSON><PERSON>, Tween, UITransform, Vec3, _decorator, tween, v3 } from "cc";
import GameObject from "../../../lib/object/GameObject";
import RenderSection from "../../../lib/object/RenderSection";
import { AnimationSection } from "../section/AnimationSection";
import { HurtSection } from "../section/HurtSection";
import { BattlePlayerRoundInfo, PlayerBackInfo } from "../FightDefine";
import HPSection from "../../../lib/object/HpSection";
import buffSection from "../../../lib/object/buffSection";
import StateSection, { STATE } from "../section/StateSection";
import FightManager, { scaleList } from "../manager/FightManager";
import { recoverSection } from "../section/recoverSection";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import ShakeSection from "../../../lib/object/ShakeSection";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import DirectSection from "../../../lib/object/DirectSection";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

@ccclass("GORole")
export class GORole extends GameObject {
  protected _startTween: Tween = null;
  protected _fightShowTime: boolean = false;
  public get fightShowTime() {
    return this._fightShowTime;
  }

  public set fightShowTime(bool: boolean) {
    this._fightShowTime = bool;
  }
  protected _detail: PlayerBackInfo;
  protected _roundDetail: BattlePlayerRoundInfo;
  protected _dir: number;
  protected _spineId: number;
  protected _dbScale: Vec3;
  protected _dbId: number;
  protected _isPlayer: boolean;
  protected _render: Node;

  protected _shakeSection: ShakeSection;
  protected _animationSection: AnimationSection;
  protected _stateSection: StateSection;
  protected _renderSection: RenderSection;
  protected _renderName: string;

  public get renderSection() {
    return this._renderSection;
  }
  /**
   * 初始化玩家细节信息
   * @param detail 玩家返回信息
   */
  public onInitDetail(detail: PlayerBackInfo, renderName: string = "") {
    /** 初始化角色 */
    this.onEnter();
    this._detail = detail;
    this._roundDetail = new BattlePlayerRoundInfo();
    this._roundDetail.a = detail.battlePlayerBackInfo.c;
    this._roundDetail.b = 1; // 创建时都不是眩晕状态所以写1
    this._roundDetail.c = detail.battlePlayerBackInfo.e;
    this._roundDetail.d = detail.battlePlayerBackInfo.f;
    this._isPlayer = detail.isPlayer;
    this._dbId = detail.dbId;
    this._dir = detail.dir;
    this._render = detail.render || null;

    this.setPosVec3(detail.pos);
  }

  public getDetail() {
    return this._detail;
  }

  protected onStart(): void {
    super.onStart();
  }

  protected getSceneScale(sceneId: number, db: any): Vec3 {
    return null;
  }

  /** 创建时执行 */
  public onEnter() {
    this.onMsg("OnAnimationStart", this.AnimationStart.bind(this));
    this.onMsg("OnAnimationEvent", this.PlayAnimation.bind(this));
    this.onMsg("OnAnimationCompleted", this.AnimationCompleted.bind(this));
  }

  /** 销毁时执行 */
  public onExit() {
    this.offMsg("OnAnimationStart", this.AnimationStart.bind(this));
    this.offMsg("OnAnimationEvent", this.PlayAnimation.bind(this));
    this.offMsg("OnAnimationCompleted", this.AnimationCompleted.bind(this));
  }

  protected async AnimationStart(detail) {
    if (detail.name === "hurt") {
      this._shakeSection.shake(1002);
      MsgMgr.emit(MsgEnum.ON_SHAKE_WORLD);
    }
  }

  protected async PlayAnimation(event) {
    if (event["data"].name === "atk") {
      // 处理攻击动画事件
    }
    if (event["data"].name === "audio") {
      // 处理音频事件
    }
  }

  protected async AnimationCompleted(data) {
    const animationName = data.animation.name;
    if (animationName === "boss_attack1" || animationName === "boss_attack2_2" || animationName === "boss_attack3") {
      this.emitMsg("OnSwitchState", STATE.IDLE);
    } else if (animationName === "hurt") {
      // 处理受伤动画完成事件
      this.emitMsg("OnSwitchState", STATE.IDLE);
    }
  }

  public async createAllSection() {
    this.createSection(DirectSection, this._dir);
    this._shakeSection = await this.createSection(ShakeSection);
    this._animationSection = await this.createSection(AnimationSection);
    this._animationSection.playAction(1, true);
    this._stateSection = await this.createSection(StateSection);
    this.createSection(HurtSection);
    this.createSection(recoverSection);
    this.createSection(buffSection);
  }

  protected async createRenderSection(): Promise<void> {
    return new Promise<void>(async (resolve) => {
      const param = {
        renderName: this._renderName,
        spineId: this._spineId,
        dbScale: this._dbScale,
        render: this._render,
        isBoss: false,
        callBack: resolve,
      };
      this._renderSection = await this.createSection(RenderSection, param);
    });
  }

  protected async createHPSection(): Promise<void> {
    return new Promise<void>((resolve) => {
      const param = {
        renderName: this._renderName,
        callBack: resolve,
        mirroring: false, // 默认值
      };
      this.createSection(HPSection, param);
      this.emitMsg("OnInitHP");
    });
  }

  /** 返回回合中的属性，战斗过程中会产生变化 */
  public getRoundDetail(): BattlePlayerRoundInfo {
    return this._roundDetail;
  }

  /** 回合初更新战斗的属性 */
  public roundStartUpDetail(detail: BattlePlayerRoundInfo) {
    this._roundDetail.a = detail.a || 0;
    this._roundDetail.b = detail.b || 0;
    this._roundDetail.c = detail.c || {};
    this._roundDetail.d = detail.d || 0;
    this.emitMsg("OnInitHP");
  }

  /** 返回位置索引 */
  public getDir() {
    return this._dir;
  }

  /** 返回形象Id */
  public getSpineId() {
    return this._spineId;
  }

  /** 返回是否是用户角色 */
  public getIsPlayerRole(): boolean {
    return this._isPlayer;
  }

  /** 返回配置的角色名称 */
  public getDbRoleName(): string {
    if (this._isPlayer) {
      return JsonMgr.instance.jsonList.c_leaderSkin[this._dbId].name;
    } else {
      let db1 = JsonMgr.instance.jsonList.c_leaderSkin[this._dbId];
      if (db1) {
        return db1.name;
      } else {
        return JsonMgr.instance.jsonList.c_monsterShow[this._dbId].name;
      }
    }
  }

  private getDot(labNode: Node) {
    const contentSize = labNode.getComponent(UITransform).contentSize;
    const crash = this._renderSection.getNode().getChildByName("crash");
    const pos = ToolExt.getRandomPoint(crash.getComponent(UITransform).getBoundingBox(), contentSize);
    const node = new Node();
    node.parent = this;
    node.setPosition(pos);
    const newPos = ToolExt.transferOfAxes(node, labNode.parent);
    labNode.setPosition(newPos);
    node.destroy();
    this.showDetail(labNode);
  }

  private showDetail(touchNode: Node) {
    const worldPosition = touchNode.getWorldPosition();
    const width = touchNode.getComponent(UITransform).width;
    const sceneWidth = FightManager.instance.getComponent(UITransform).width;

    if (this._dir === 1) {
      /** 左边 */
      const left = worldPosition.x - width / 2;
      if (left <= 0) {
        this.setTips(touchNode, worldPosition.x + Math.abs(left) + Math.random() * 20);
      }
    } else if (this._dir === 2) {
      /** 右边 */
      const right = worldPosition.x + width / 2 + 20;
      if (right >= sceneWidth) {
        this.setTips(touchNode, worldPosition.x - (right - sceneWidth) - Math.random() * 20);
      }
    }
  }

  private setTips(touchNode: Node, toWorldX: number) {
    const worldPosition = touchNode.getWorldPosition();
    tween(touchNode)
      .set({ worldPosition: new Vec3(toWorldX, worldPosition.y) })
      .start();
  }

  /** 伤害飘字坐标 */
  public getHurtPos(node: Node) {
    return this.getDot(node);
  }

  /** 暴击提示文字 */
  public getCritPos(node: Node) {
    return this.getDot(node);
  }

  /** 血量恢复数值 */
  public getRecovePos(node: Node) {
    return this.getDot(node);
  }

  /** 吸血提示文字 */
  public getSuckPos(node: Node) {
    return this.getDot(node);
  }

  /** 击晕提示文字 */
  public getStunPos(node: Node) {
    return this.getDot(node);
  }

  /** 闪避提示文字 */
  public getMissPos(node: Node) {
    return this.getDot(node);
  }

  /** 连击的提示文字 */
  public getDoublePos(node: Node) {
    return this.getDot(node);
  }

  /** 反击的提示文字 */
  public getBackPos(node: Node) {
    return this.getDot(node);
  }

  /** 闪避 */
  public async dodge() {
    await new Promise((res) => {
      const render = this._renderSection.getRender();
      tween(render)
        .to(0.1, { position: v3(render.getPosition().x, render.getPosition().y - 100, 0) })
        .to(0.1, { position: v3(render.getPosition().x, 0, 0) })
        .call(() => {
          res(1);
        })
        .start();
    });
  }

  /** 跳过战斗逻辑处理 */
  public fightSkip() {
    FightManager.instance.fightOver = true;
    this.emitMsg("OnSwitchState", STATE.IDLE);
    const render = this._renderSection.getRender();
    Tween.stopAllByTarget(render);
    render.setPosition(v3(0, 0, 1));
  }

  public setTimeScale() {
    this._renderSection.setTimeScale(scaleList[FightManager.instance.speed]);
  }

  /** 开始战斗进场动画执行 */
  public fightStartAni() {
    log.warn("需要重写进场动画，否则没有进场动画");
  }

  protected onRemove(): void {
    super.onRemove();
    this.onExit();
    Tween.stopAllByTarget(this);

    if (this._startTween) {
      this._startTween.stop();
      this._startTween = null;
    }
  }
}
