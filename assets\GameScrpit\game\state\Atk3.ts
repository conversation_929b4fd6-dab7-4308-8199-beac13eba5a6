import { _decorator, isValid } from "cc";
import { FSMState } from "../fight/FSM/FSMState";
import { FSMBoard, STATE } from "../fight/section/StateSection";
import { AnimationSection } from "../fight/section/AnimationSection";
import { CallSkillDetail } from "../fight/FightDefine";
import FightManager, { scaleList } from "../fight/manager/FightManager";
import SkillManager from "../fight/manager/SkillManager";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import HPSection from "../../lib/object/HpSection";
import { JsonMgr } from "../mgr/JsonMgr";
import { AudioMgr, AudioName } from "../../../platform/src/AudioHelper";
interface TimeHandle {
  time: number;
  handle: Function;
  isHandleOver: boolean;
  skillId: number;
}
const { ccclass, property } = _decorator;

@ccclass
export default class Atk3 extends FSMState {
  private timeHandle: Array<TimeHandle> = [];
  public async onEnter(board: FSMBoard) {
    //AudioMgr.instance.playEffect(AudioName.Effect.攻击1);
    let roundMovementInfo = board.roundMovementInfo;
    let detail: CallSkillDetail = {
      src: board.sub,
      target: roundMovementInfo.hurtRole,
      skillId: roundMovementInfo.skillId,
      actionType: roundMovementInfo.actionType,
      resolveBack: roundMovementInfo.resolveBack,
      movementInfo: roundMovementInfo.movementInfo,
    };
    if (FightManager.instance.fightOver == true) {
      return;
    }
    FightManager.instance.getSection(SkillManager).callSkill(detail);
    let skill_db = JsonMgr.instance.jsonList.c_skillShow[roundMovementInfo.skillId];
    if (skill_db["isHp"] == true) {
      board.sub.getSection(HPSection).setRenderActive(false);
    }
    board.sub.getSection(AnimationSection).playAction(5, false);
  }

  private playSound(board: FSMBoard) {}

  public update(board: FSMBoard, dt) {
    this.updateHandle(dt);
  }

  public onExit(board: any): void {}

  public updateHandle(dt) {}

  private getSkillObj(roleId: number) {}
}
