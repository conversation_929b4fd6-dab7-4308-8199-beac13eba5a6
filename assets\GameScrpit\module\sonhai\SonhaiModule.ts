import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { ActivityModule } from "../activity/ActivityModule";
import { SonhaiApi } from "./SonhaiApi";
import { SonhaiConfig } from "./SonhaiConfig";
import { SonhaiData } from "./SonhaiData";
import { SonhaiRoute } from "./SonhaiRoute";
import { SonhaiService } from "./SonhaiService";
import { SonhaiSubscriber } from "./SonhaiSubscriber";
import { SonhaiViewModel } from "./SonhaiViewModel";

export const sonhai_activityId: number = 11101;
export const sonhai_achieveId: number = 1110101;

export class SonhaiModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): SonhaiModule {
    if (!GameData.instance.SonhaiModule) {
      GameData.instance.SonhaiModule = new SonhaiModule();
    }
    return GameData.instance.SonhaiModule;
  }

  private _data = new SonhaiData();
  private _api = new SonhaiApi();
  private _config = new SonhaiConfig();
  private _service = new SonhaiService();
  private _viewModel = new SonhaiViewModel();
  private _route = new SonhaiRoute();
  private _subscriber = new SonhaiSubscriber();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public static get route() {
    return this.instance._route;
  }

  public init(data?: any) {
    this._data = new SonhaiData();
    this._api = new SonhaiApi();
    this._config = new SonhaiConfig();
    this._service = new SonhaiService();
    this._viewModel = new SonhaiViewModel();
    this._route = new SonhaiRoute();
    this._subscriber = new SonhaiSubscriber();

    // 初始化模块
    this._subscriber.register();
    this._route.init();
    this._service.init();

    // if (ActivityModule.service.checkActivityUnlock(sonhai_activityId)) {
    // }
    this._api.adventureInfo(sonhai_activityId);
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
