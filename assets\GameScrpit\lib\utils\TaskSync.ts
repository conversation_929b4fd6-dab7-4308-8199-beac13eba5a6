import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
/**
 * 同步任务
 * 用法：
 * let taskSync = new TaskSync(this.onEnd, this);
 * taskSync.addTask(this.task1, 1);
 */
export class TaskSync {
  private _target: any = null;

  private _taskArgs: any[] = [];

  private _taskList: Function[] = [];

  private _endFunc: Function = null;
  private _endFuncArgs: any = [];

  private _completedCount: number = 0;

  public constructor(endFunc: Function, target: any, ...args: any[]) {
    this._endFunc = endFunc.bind(target);
    this._target = target;
    this._endFuncArgs = args;
  }

  public addTask(task: Function, ...args: any[]) {
    this._taskList.push(task.bind(this._target));
    this._taskArgs.push(args);
  }

  public async start() {
    for (let i = 0; i < this._taskList.length; i++) {
      this._taskList[i](...this._taskArgs[i])
        .then(() => {
          this._completedCount++;

          if (this._completedCount >= this._taskList.length) {
            this._endFunc(this._target, this._endFuncArgs);
          }
        })
        .catch((error: any) => {
          log.error("TaskSync error:", error);
        });
    }
  }
}
