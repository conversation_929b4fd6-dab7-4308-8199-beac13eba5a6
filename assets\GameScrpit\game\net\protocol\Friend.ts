// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Friend.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface FriendChatRequest {
  chatMore: boolean;
}

/**  */
export interface FriendChatResponse {
  /**  */
  vitalityMessage:
    | FriendVitalityMessage
    | undefined;
  /** key:friendId value:谈心获得的friendShip */
  chatGetMap: { [key: number]: number };
  /** key:friendId value:friendShip最终值 */
  chatStockMap: { [key: number]: number };
}

export interface FriendChatResponse_ChatGetMapEntry {
  key: number;
  value: number;
}

export interface FriendChatResponse_ChatStockMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FriendCitySkillMessage {
  skillAdd: number;
  energyWashCount: number;
  itemWashCount: number;
  backUpSkillSkillAdd: number;
}

/**  */
export interface FriendCitySkillRequest {
  friendId: number;
  rank: number;
  consumeItem: boolean;
}

/**  */
export interface FriendGiftRequest {
  friendId: number;
  itemId: number;
  giftMore: boolean;
}

/**  */
export interface FriendGiftResponse {
  /** 友好度 (因果) */
  karma: number;
  /** 才华 (天命) */
  destiny: number;
}

/**  */
export interface FriendHeroSkillRequest {
  friendId: number;
  rank: number;
}

/**  */
export interface FriendHeroSkillResponse {
  level: number;
  friendShip: number;
}

/**  */
export interface FriendKarmaUpdateMessage {
  karmaUpdateMap: { [key: number]: number };
}

export interface FriendKarmaUpdateMessage_KarmaUpdateMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FriendLabelMessage {
  labelMessageMap: { [key: number]: FriendSubLabelMessage };
}

export interface FriendLabelMessage_LabelMessageMapEntry {
  key: number;
  value: FriendSubLabelMessage | undefined;
}

/**  */
export interface FriendMessage {
  /** 挚友模版ID */
  friendId: number;
  /** 友好度 (因果) */
  karma: number;
  /** 才华 (天命) */
  destiny: number;
  /** 缘分点 */
  friendShip: number;
  /** 美名等级 */
  fameLv: number;
  /** 据点技能 */
  citySkillList: FriendCitySkillMessage[];
  /** 门客技能 heroId : List<技能> */
  heroSkillList: number[];
  /** 获得时间 */
  timeStamp: number;
}

/**  */
export interface FriendStatisticsResponse {
  /** 仙友偶遇次数 */
  encounterMap: { [key: number]: number };
  /** VIP等级 */
  vipLevel: number;
  /** 主角等级 */
  level: number;
  /** 建筑总等级 */
  cityTotalLevel: number;
  /** 繁荣度(气运赚速) */
  energySpeed: number;
  /** 累计徒弟成年数 */
  adultPupilCnt: number;
  /** 累计获得灵兽数量 */
  petCnt: number;
  /** 累计徒弟结伴数量 */
  marryPupilCnt: number;
  /** 演武场累计胜利 */
  competeWinCnt: number;
}

export interface FriendStatisticsResponse_EncounterMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FriendSubLabelMessage {
  friendId: number;
  /** 完成的数量 */
  count: number;
  /** 是否已拥有 */
  take: boolean;
}

/**  */
export interface FriendUnlockCitySkillResponse {
  friendId: number;
  citySkillList: FriendCitySkillMessage[];
}

/**  */
export interface FriendVitalityMessage {
  vitality: number;
  lastUpdateTime: number;
}

/**  */
export interface TravelResUpdateMessage {
  /** -1表示无实际作用;标识要访问的地点 */
  placeId: number;
  /** 是否弹出对话 */
  isTalk: boolean;
  /** 体力值 */
  vitality:
    | TravelVitalityMessage
    | undefined;
  /** 游历获得奖励 如果是挚友，标识偶遇的次数 (注：如果要弹出仙友，索引0处为要弹出的仙友的ID) */
  resAddList: number[];
}

/**  */
export interface TravelVitalityMessage {
  vitality: number;
  vitalitySize: number;
  lastUpdateTime: number;
}

function createBaseFriendChatRequest(): FriendChatRequest {
  return { chatMore: false };
}

export const FriendChatRequest: MessageFns<FriendChatRequest> = {
  encode(message: FriendChatRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.chatMore !== false) {
      writer.uint32(8).bool(message.chatMore);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendChatRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendChatRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.chatMore = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendChatRequest>, I>>(base?: I): FriendChatRequest {
    return FriendChatRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendChatRequest>, I>>(object: I): FriendChatRequest {
    const message = createBaseFriendChatRequest();
    message.chatMore = object.chatMore ?? false;
    return message;
  },
};

function createBaseFriendChatResponse(): FriendChatResponse {
  return { vitalityMessage: undefined, chatGetMap: {}, chatStockMap: {} };
}

export const FriendChatResponse: MessageFns<FriendChatResponse> = {
  encode(message: FriendChatResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.vitalityMessage !== undefined) {
      FriendVitalityMessage.encode(message.vitalityMessage, writer.uint32(10).fork()).join();
    }
    Object.entries(message.chatGetMap).forEach(([key, value]) => {
      FriendChatResponse_ChatGetMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.chatStockMap).forEach(([key, value]) => {
      FriendChatResponse_ChatStockMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendChatResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendChatResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.vitalityMessage = FriendVitalityMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = FriendChatResponse_ChatGetMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.chatGetMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = FriendChatResponse_ChatStockMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.chatStockMap[entry3.key] = entry3.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendChatResponse>, I>>(base?: I): FriendChatResponse {
    return FriendChatResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendChatResponse>, I>>(object: I): FriendChatResponse {
    const message = createBaseFriendChatResponse();
    message.vitalityMessage = (object.vitalityMessage !== undefined && object.vitalityMessage !== null)
      ? FriendVitalityMessage.fromPartial(object.vitalityMessage)
      : undefined;
    message.chatGetMap = Object.entries(object.chatGetMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.chatStockMap = Object.entries(object.chatStockMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseFriendChatResponse_ChatGetMapEntry(): FriendChatResponse_ChatGetMapEntry {
  return { key: 0, value: 0 };
}

export const FriendChatResponse_ChatGetMapEntry: MessageFns<FriendChatResponse_ChatGetMapEntry> = {
  encode(message: FriendChatResponse_ChatGetMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendChatResponse_ChatGetMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendChatResponse_ChatGetMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendChatResponse_ChatGetMapEntry>, I>>(
    base?: I,
  ): FriendChatResponse_ChatGetMapEntry {
    return FriendChatResponse_ChatGetMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendChatResponse_ChatGetMapEntry>, I>>(
    object: I,
  ): FriendChatResponse_ChatGetMapEntry {
    const message = createBaseFriendChatResponse_ChatGetMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFriendChatResponse_ChatStockMapEntry(): FriendChatResponse_ChatStockMapEntry {
  return { key: 0, value: 0 };
}

export const FriendChatResponse_ChatStockMapEntry: MessageFns<FriendChatResponse_ChatStockMapEntry> = {
  encode(message: FriendChatResponse_ChatStockMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendChatResponse_ChatStockMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendChatResponse_ChatStockMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendChatResponse_ChatStockMapEntry>, I>>(
    base?: I,
  ): FriendChatResponse_ChatStockMapEntry {
    return FriendChatResponse_ChatStockMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendChatResponse_ChatStockMapEntry>, I>>(
    object: I,
  ): FriendChatResponse_ChatStockMapEntry {
    const message = createBaseFriendChatResponse_ChatStockMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFriendCitySkillMessage(): FriendCitySkillMessage {
  return { skillAdd: 0, energyWashCount: 0, itemWashCount: 0, backUpSkillSkillAdd: 0 };
}

export const FriendCitySkillMessage: MessageFns<FriendCitySkillMessage> = {
  encode(message: FriendCitySkillMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.skillAdd !== 0) {
      writer.uint32(9).double(message.skillAdd);
    }
    if (message.energyWashCount !== 0) {
      writer.uint32(16).int32(message.energyWashCount);
    }
    if (message.itemWashCount !== 0) {
      writer.uint32(24).int32(message.itemWashCount);
    }
    if (message.backUpSkillSkillAdd !== 0) {
      writer.uint32(33).double(message.backUpSkillSkillAdd);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendCitySkillMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendCitySkillMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.skillAdd = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.energyWashCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.itemWashCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.backUpSkillSkillAdd = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendCitySkillMessage>, I>>(base?: I): FriendCitySkillMessage {
    return FriendCitySkillMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendCitySkillMessage>, I>>(object: I): FriendCitySkillMessage {
    const message = createBaseFriendCitySkillMessage();
    message.skillAdd = object.skillAdd ?? 0;
    message.energyWashCount = object.energyWashCount ?? 0;
    message.itemWashCount = object.itemWashCount ?? 0;
    message.backUpSkillSkillAdd = object.backUpSkillSkillAdd ?? 0;
    return message;
  },
};

function createBaseFriendCitySkillRequest(): FriendCitySkillRequest {
  return { friendId: 0, rank: 0, consumeItem: false };
}

export const FriendCitySkillRequest: MessageFns<FriendCitySkillRequest> = {
  encode(message: FriendCitySkillRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.friendId !== 0) {
      writer.uint32(8).int64(message.friendId);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    if (message.consumeItem !== false) {
      writer.uint32(24).bool(message.consumeItem);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendCitySkillRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendCitySkillRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.friendId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.consumeItem = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendCitySkillRequest>, I>>(base?: I): FriendCitySkillRequest {
    return FriendCitySkillRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendCitySkillRequest>, I>>(object: I): FriendCitySkillRequest {
    const message = createBaseFriendCitySkillRequest();
    message.friendId = object.friendId ?? 0;
    message.rank = object.rank ?? 0;
    message.consumeItem = object.consumeItem ?? false;
    return message;
  },
};

function createBaseFriendGiftRequest(): FriendGiftRequest {
  return { friendId: 0, itemId: 0, giftMore: false };
}

export const FriendGiftRequest: MessageFns<FriendGiftRequest> = {
  encode(message: FriendGiftRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.friendId !== 0) {
      writer.uint32(8).int64(message.friendId);
    }
    if (message.itemId !== 0) {
      writer.uint32(16).int64(message.itemId);
    }
    if (message.giftMore !== false) {
      writer.uint32(24).bool(message.giftMore);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendGiftRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendGiftRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.friendId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.itemId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.giftMore = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendGiftRequest>, I>>(base?: I): FriendGiftRequest {
    return FriendGiftRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendGiftRequest>, I>>(object: I): FriendGiftRequest {
    const message = createBaseFriendGiftRequest();
    message.friendId = object.friendId ?? 0;
    message.itemId = object.itemId ?? 0;
    message.giftMore = object.giftMore ?? false;
    return message;
  },
};

function createBaseFriendGiftResponse(): FriendGiftResponse {
  return { karma: 0, destiny: 0 };
}

export const FriendGiftResponse: MessageFns<FriendGiftResponse> = {
  encode(message: FriendGiftResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.karma !== 0) {
      writer.uint32(8).int32(message.karma);
    }
    if (message.destiny !== 0) {
      writer.uint32(16).int32(message.destiny);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendGiftResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendGiftResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.karma = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.destiny = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendGiftResponse>, I>>(base?: I): FriendGiftResponse {
    return FriendGiftResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendGiftResponse>, I>>(object: I): FriendGiftResponse {
    const message = createBaseFriendGiftResponse();
    message.karma = object.karma ?? 0;
    message.destiny = object.destiny ?? 0;
    return message;
  },
};

function createBaseFriendHeroSkillRequest(): FriendHeroSkillRequest {
  return { friendId: 0, rank: 0 };
}

export const FriendHeroSkillRequest: MessageFns<FriendHeroSkillRequest> = {
  encode(message: FriendHeroSkillRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.friendId !== 0) {
      writer.uint32(8).int64(message.friendId);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendHeroSkillRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendHeroSkillRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.friendId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendHeroSkillRequest>, I>>(base?: I): FriendHeroSkillRequest {
    return FriendHeroSkillRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendHeroSkillRequest>, I>>(object: I): FriendHeroSkillRequest {
    const message = createBaseFriendHeroSkillRequest();
    message.friendId = object.friendId ?? 0;
    message.rank = object.rank ?? 0;
    return message;
  },
};

function createBaseFriendHeroSkillResponse(): FriendHeroSkillResponse {
  return { level: 0, friendShip: 0 };
}

export const FriendHeroSkillResponse: MessageFns<FriendHeroSkillResponse> = {
  encode(message: FriendHeroSkillResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.level !== 0) {
      writer.uint32(8).int32(message.level);
    }
    if (message.friendShip !== 0) {
      writer.uint32(16).int64(message.friendShip);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendHeroSkillResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendHeroSkillResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.friendShip = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendHeroSkillResponse>, I>>(base?: I): FriendHeroSkillResponse {
    return FriendHeroSkillResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendHeroSkillResponse>, I>>(object: I): FriendHeroSkillResponse {
    const message = createBaseFriendHeroSkillResponse();
    message.level = object.level ?? 0;
    message.friendShip = object.friendShip ?? 0;
    return message;
  },
};

function createBaseFriendKarmaUpdateMessage(): FriendKarmaUpdateMessage {
  return { karmaUpdateMap: {} };
}

export const FriendKarmaUpdateMessage: MessageFns<FriendKarmaUpdateMessage> = {
  encode(message: FriendKarmaUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.karmaUpdateMap).forEach(([key, value]) => {
      FriendKarmaUpdateMessage_KarmaUpdateMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendKarmaUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendKarmaUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = FriendKarmaUpdateMessage_KarmaUpdateMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.karmaUpdateMap[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendKarmaUpdateMessage>, I>>(base?: I): FriendKarmaUpdateMessage {
    return FriendKarmaUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendKarmaUpdateMessage>, I>>(object: I): FriendKarmaUpdateMessage {
    const message = createBaseFriendKarmaUpdateMessage();
    message.karmaUpdateMap = Object.entries(object.karmaUpdateMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseFriendKarmaUpdateMessage_KarmaUpdateMapEntry(): FriendKarmaUpdateMessage_KarmaUpdateMapEntry {
  return { key: 0, value: 0 };
}

export const FriendKarmaUpdateMessage_KarmaUpdateMapEntry: MessageFns<FriendKarmaUpdateMessage_KarmaUpdateMapEntry> = {
  encode(
    message: FriendKarmaUpdateMessage_KarmaUpdateMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendKarmaUpdateMessage_KarmaUpdateMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendKarmaUpdateMessage_KarmaUpdateMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendKarmaUpdateMessage_KarmaUpdateMapEntry>, I>>(
    base?: I,
  ): FriendKarmaUpdateMessage_KarmaUpdateMapEntry {
    return FriendKarmaUpdateMessage_KarmaUpdateMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendKarmaUpdateMessage_KarmaUpdateMapEntry>, I>>(
    object: I,
  ): FriendKarmaUpdateMessage_KarmaUpdateMapEntry {
    const message = createBaseFriendKarmaUpdateMessage_KarmaUpdateMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFriendLabelMessage(): FriendLabelMessage {
  return { labelMessageMap: {} };
}

export const FriendLabelMessage: MessageFns<FriendLabelMessage> = {
  encode(message: FriendLabelMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.labelMessageMap).forEach(([key, value]) => {
      FriendLabelMessage_LabelMessageMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendLabelMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendLabelMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = FriendLabelMessage_LabelMessageMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.labelMessageMap[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendLabelMessage>, I>>(base?: I): FriendLabelMessage {
    return FriendLabelMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendLabelMessage>, I>>(object: I): FriendLabelMessage {
    const message = createBaseFriendLabelMessage();
    message.labelMessageMap = Object.entries(object.labelMessageMap ?? {}).reduce<
      { [key: number]: FriendSubLabelMessage }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = FriendSubLabelMessage.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseFriendLabelMessage_LabelMessageMapEntry(): FriendLabelMessage_LabelMessageMapEntry {
  return { key: 0, value: undefined };
}

export const FriendLabelMessage_LabelMessageMapEntry: MessageFns<FriendLabelMessage_LabelMessageMapEntry> = {
  encode(message: FriendLabelMessage_LabelMessageMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      FriendSubLabelMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendLabelMessage_LabelMessageMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendLabelMessage_LabelMessageMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = FriendSubLabelMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendLabelMessage_LabelMessageMapEntry>, I>>(
    base?: I,
  ): FriendLabelMessage_LabelMessageMapEntry {
    return FriendLabelMessage_LabelMessageMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendLabelMessage_LabelMessageMapEntry>, I>>(
    object: I,
  ): FriendLabelMessage_LabelMessageMapEntry {
    const message = createBaseFriendLabelMessage_LabelMessageMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? FriendSubLabelMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseFriendMessage(): FriendMessage {
  return {
    friendId: 0,
    karma: 0,
    destiny: 0,
    friendShip: 0,
    fameLv: 0,
    citySkillList: [],
    heroSkillList: [],
    timeStamp: 0,
  };
}

export const FriendMessage: MessageFns<FriendMessage> = {
  encode(message: FriendMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.friendId !== 0) {
      writer.uint32(8).int64(message.friendId);
    }
    if (message.karma !== 0) {
      writer.uint32(16).int32(message.karma);
    }
    if (message.destiny !== 0) {
      writer.uint32(24).int32(message.destiny);
    }
    if (message.friendShip !== 0) {
      writer.uint32(32).int64(message.friendShip);
    }
    if (message.fameLv !== 0) {
      writer.uint32(40).int64(message.fameLv);
    }
    for (const v of message.citySkillList) {
      FriendCitySkillMessage.encode(v!, writer.uint32(50).fork()).join();
    }
    writer.uint32(58).fork();
    for (const v of message.heroSkillList) {
      writer.int32(v);
    }
    writer.join();
    if (message.timeStamp !== 0) {
      writer.uint32(64).int64(message.timeStamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.friendId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.karma = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.destiny = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.friendShip = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.fameLv = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.citySkillList.push(FriendCitySkillMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag === 56) {
            message.heroSkillList.push(reader.int32());

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.heroSkillList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.timeStamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendMessage>, I>>(base?: I): FriendMessage {
    return FriendMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendMessage>, I>>(object: I): FriendMessage {
    const message = createBaseFriendMessage();
    message.friendId = object.friendId ?? 0;
    message.karma = object.karma ?? 0;
    message.destiny = object.destiny ?? 0;
    message.friendShip = object.friendShip ?? 0;
    message.fameLv = object.fameLv ?? 0;
    message.citySkillList = object.citySkillList?.map((e) => FriendCitySkillMessage.fromPartial(e)) || [];
    message.heroSkillList = object.heroSkillList?.map((e) => e) || [];
    message.timeStamp = object.timeStamp ?? 0;
    return message;
  },
};

function createBaseFriendStatisticsResponse(): FriendStatisticsResponse {
  return {
    encounterMap: {},
    vipLevel: 0,
    level: 0,
    cityTotalLevel: 0,
    energySpeed: 0,
    adultPupilCnt: 0,
    petCnt: 0,
    marryPupilCnt: 0,
    competeWinCnt: 0,
  };
}

export const FriendStatisticsResponse: MessageFns<FriendStatisticsResponse> = {
  encode(message: FriendStatisticsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.encounterMap).forEach(([key, value]) => {
      FriendStatisticsResponse_EncounterMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    if (message.vipLevel !== 0) {
      writer.uint32(16).int32(message.vipLevel);
    }
    if (message.level !== 0) {
      writer.uint32(24).int32(message.level);
    }
    if (message.cityTotalLevel !== 0) {
      writer.uint32(32).int32(message.cityTotalLevel);
    }
    if (message.energySpeed !== 0) {
      writer.uint32(41).double(message.energySpeed);
    }
    if (message.adultPupilCnt !== 0) {
      writer.uint32(48).int32(message.adultPupilCnt);
    }
    if (message.petCnt !== 0) {
      writer.uint32(56).int32(message.petCnt);
    }
    if (message.marryPupilCnt !== 0) {
      writer.uint32(64).int32(message.marryPupilCnt);
    }
    if (message.competeWinCnt !== 0) {
      writer.uint32(72).int32(message.competeWinCnt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendStatisticsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendStatisticsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = FriendStatisticsResponse_EncounterMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.encounterMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.vipLevel = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.cityTotalLevel = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.energySpeed = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.adultPupilCnt = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.petCnt = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.marryPupilCnt = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.competeWinCnt = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendStatisticsResponse>, I>>(base?: I): FriendStatisticsResponse {
    return FriendStatisticsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendStatisticsResponse>, I>>(object: I): FriendStatisticsResponse {
    const message = createBaseFriendStatisticsResponse();
    message.encounterMap = Object.entries(object.encounterMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.vipLevel = object.vipLevel ?? 0;
    message.level = object.level ?? 0;
    message.cityTotalLevel = object.cityTotalLevel ?? 0;
    message.energySpeed = object.energySpeed ?? 0;
    message.adultPupilCnt = object.adultPupilCnt ?? 0;
    message.petCnt = object.petCnt ?? 0;
    message.marryPupilCnt = object.marryPupilCnt ?? 0;
    message.competeWinCnt = object.competeWinCnt ?? 0;
    return message;
  },
};

function createBaseFriendStatisticsResponse_EncounterMapEntry(): FriendStatisticsResponse_EncounterMapEntry {
  return { key: 0, value: 0 };
}

export const FriendStatisticsResponse_EncounterMapEntry: MessageFns<FriendStatisticsResponse_EncounterMapEntry> = {
  encode(message: FriendStatisticsResponse_EncounterMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendStatisticsResponse_EncounterMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendStatisticsResponse_EncounterMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendStatisticsResponse_EncounterMapEntry>, I>>(
    base?: I,
  ): FriendStatisticsResponse_EncounterMapEntry {
    return FriendStatisticsResponse_EncounterMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendStatisticsResponse_EncounterMapEntry>, I>>(
    object: I,
  ): FriendStatisticsResponse_EncounterMapEntry {
    const message = createBaseFriendStatisticsResponse_EncounterMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFriendSubLabelMessage(): FriendSubLabelMessage {
  return { friendId: 0, count: 0, take: false };
}

export const FriendSubLabelMessage: MessageFns<FriendSubLabelMessage> = {
  encode(message: FriendSubLabelMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.friendId !== 0) {
      writer.uint32(8).int64(message.friendId);
    }
    if (message.count !== 0) {
      writer.uint32(16).int64(message.count);
    }
    if (message.take !== false) {
      writer.uint32(24).bool(message.take);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendSubLabelMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendSubLabelMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.friendId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.count = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.take = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendSubLabelMessage>, I>>(base?: I): FriendSubLabelMessage {
    return FriendSubLabelMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendSubLabelMessage>, I>>(object: I): FriendSubLabelMessage {
    const message = createBaseFriendSubLabelMessage();
    message.friendId = object.friendId ?? 0;
    message.count = object.count ?? 0;
    message.take = object.take ?? false;
    return message;
  },
};

function createBaseFriendUnlockCitySkillResponse(): FriendUnlockCitySkillResponse {
  return { friendId: 0, citySkillList: [] };
}

export const FriendUnlockCitySkillResponse: MessageFns<FriendUnlockCitySkillResponse> = {
  encode(message: FriendUnlockCitySkillResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.friendId !== 0) {
      writer.uint32(8).int64(message.friendId);
    }
    for (const v of message.citySkillList) {
      FriendCitySkillMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendUnlockCitySkillResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendUnlockCitySkillResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.friendId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.citySkillList.push(FriendCitySkillMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendUnlockCitySkillResponse>, I>>(base?: I): FriendUnlockCitySkillResponse {
    return FriendUnlockCitySkillResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendUnlockCitySkillResponse>, I>>(
    object: I,
  ): FriendUnlockCitySkillResponse {
    const message = createBaseFriendUnlockCitySkillResponse();
    message.friendId = object.friendId ?? 0;
    message.citySkillList = object.citySkillList?.map((e) => FriendCitySkillMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFriendVitalityMessage(): FriendVitalityMessage {
  return { vitality: 0, lastUpdateTime: 0 };
}

export const FriendVitalityMessage: MessageFns<FriendVitalityMessage> = {
  encode(message: FriendVitalityMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.vitality !== 0) {
      writer.uint32(8).int32(message.vitality);
    }
    if (message.lastUpdateTime !== 0) {
      writer.uint32(16).int64(message.lastUpdateTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FriendVitalityMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendVitalityMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.vitality = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lastUpdateTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FriendVitalityMessage>, I>>(base?: I): FriendVitalityMessage {
    return FriendVitalityMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendVitalityMessage>, I>>(object: I): FriendVitalityMessage {
    const message = createBaseFriendVitalityMessage();
    message.vitality = object.vitality ?? 0;
    message.lastUpdateTime = object.lastUpdateTime ?? 0;
    return message;
  },
};

function createBaseTravelResUpdateMessage(): TravelResUpdateMessage {
  return { placeId: 0, isTalk: false, vitality: undefined, resAddList: [] };
}

export const TravelResUpdateMessage: MessageFns<TravelResUpdateMessage> = {
  encode(message: TravelResUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.placeId !== 0) {
      writer.uint32(8).int64(message.placeId);
    }
    if (message.isTalk !== false) {
      writer.uint32(16).bool(message.isTalk);
    }
    if (message.vitality !== undefined) {
      TravelVitalityMessage.encode(message.vitality, writer.uint32(26).fork()).join();
    }
    writer.uint32(34).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TravelResUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTravelResUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.placeId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isTalk = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.vitality = TravelVitalityMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag === 33) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TravelResUpdateMessage>, I>>(base?: I): TravelResUpdateMessage {
    return TravelResUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TravelResUpdateMessage>, I>>(object: I): TravelResUpdateMessage {
    const message = createBaseTravelResUpdateMessage();
    message.placeId = object.placeId ?? 0;
    message.isTalk = object.isTalk ?? false;
    message.vitality = (object.vitality !== undefined && object.vitality !== null)
      ? TravelVitalityMessage.fromPartial(object.vitality)
      : undefined;
    message.resAddList = object.resAddList?.map((e) => e) || [];
    return message;
  },
};

function createBaseTravelVitalityMessage(): TravelVitalityMessage {
  return { vitality: 0, vitalitySize: 0, lastUpdateTime: 0 };
}

export const TravelVitalityMessage: MessageFns<TravelVitalityMessage> = {
  encode(message: TravelVitalityMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.vitality !== 0) {
      writer.uint32(8).int32(message.vitality);
    }
    if (message.vitalitySize !== 0) {
      writer.uint32(16).int32(message.vitalitySize);
    }
    if (message.lastUpdateTime !== 0) {
      writer.uint32(24).int64(message.lastUpdateTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TravelVitalityMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTravelVitalityMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.vitality = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.vitalitySize = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lastUpdateTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TravelVitalityMessage>, I>>(base?: I): TravelVitalityMessage {
    return TravelVitalityMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TravelVitalityMessage>, I>>(object: I): TravelVitalityMessage {
    const message = createBaseTravelVitalityMessage();
    message.vitality = object.vitality ?? 0;
    message.vitalitySize = object.vitalitySize ?? 0;
    message.lastUpdateTime = object.lastUpdateTime ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
