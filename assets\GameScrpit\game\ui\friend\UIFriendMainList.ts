import {
  _decorator,
  EventTouch,
  instantiate,
  Label,
  Node,
  Prefab,
  Component,
  Sprite,
  tween,
  Animation,
  sp,
  v3,
} from "cc";
import { FriendModule } from "../../../module/friend/FriendModule";
import { UIFriendCard } from "./UIFriendCard";
import { FriendType, FriendSort } from "../../../module/friend/FriendConstant";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { FriendRouteItem } from "../../../module/friend/FriendRoute";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { times } from "../../../lib/utils/NumbersUtils";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { UseTypeEnum } from "../UIItemUse";
import TipMgr from "../../../lib/tips/TipMgr";
import { FriendChatResponse } from "../../net/protocol/Friend";
import MsgMgr from "../../../lib/event/MsgMgr";
import { FriendSubCmd } from "../../net/cmd/CmdData";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FriendAudioName } from "../../../module/friend/FriendConfig";
import { ScrollableView } from "../../../../platform/src/core/ui/components/ScrollableView";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIFriendHeartTalk } from "./UIFriendHeartTalk";
import { UIFriendHeartTalkOneKey } from "./UIFriendHeartTalkOneKey";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIFriendMainList")
export class UIFriendMainList extends Component {
  @property(Node)
  private friendViewHolder: Node;
  @property(Node)
  private scrollView: Node;

  @property(Node)
  private nodeAnchor: Node;

  @property(Node)
  private listContent: Node;
  @property(Node)
  private btnHeartTalk: Node;
  @property(Label)
  private lblMembers: Label;
  @property(Label)
  private lblTotalsFriendShip: Label;
  @property(Label)
  private lblTotalsTalent: Label;
  @property(Label)
  private lblPower: Label;
  @property(Node)
  private lblCd: Node;
  @property(Node)
  private checkBox: Node;
  @property(Label)
  private lblSortName: Label;
  @property(Node)
  private bgSortIndicator: Node;
  @property(Node)
  private nodeDivider: Node;
  @property(Node)
  private nodeBlank: Node;
  @property(Node)
  private nodeSkillAdds: Node;

  private _friendType: FriendType = FriendType.ALL;
  private _friendSort: FriendSort = FriendSort.DEFAULT;
  private _indexOwned: number = 0;
  private _indexUnowned: number = 0;
  private _friendNodeMap: Map<number, Node> = new Map();
  protected start(): void {
    MsgMgr.on(`${FriendSubCmd.addVitality}`, this.onAddVitality, this);
    this.refreshUI();
    FriendModule.api.getVitality(() => {
      this.refreshUI();
    });
    BadgeMgr.instance.setBadgeId(this.btnHeartTalk, BadgeType.UITerritory.btn_friend.heart_talk.id);
  }

  private onAddVitality() {
    this.refreshUI();
  }

  protected onDestroy(): void {
    MsgMgr.off(`${FriendSubCmd.addVitality}`, this.onAddVitality, this);
  }

  protected update(dt: number): void {
    // 刷新列表\
    let ownedIds = FriendModule.data.getFriendIds(true, this._friendSort, this._friendType);
    let unOwnedIds = FriendModule.data.getFriendIds(false, this._friendSort, this._friendType);

    if (this._indexOwned < ownedIds.length) {
      let friendId = ownedIds[this._indexOwned];
      let card = instantiate(this.friendViewHolder);
      card.active = true;

      card.name = "friend_card_" + friendId;
      card.getComponent(UIFriendCard).init(friendId);
      FriendModule.service.registerFriendListBadge(card.getChildByName("node_badge"), friendId);
      // card.getComponent(UIHeroCard).initInfo(hero, hero.remoteData.level, this.indexOwn++);
      card.on(Node.EventType.TOUCH_END, this.onOwnedItemClick, this);
      let index = this.listContent.getChildByName("division").getSiblingIndex();
      this.listContent.insertChild(card, index);

      tween(card)
        .set({ scale: v3(0.1, 0.1, 1) })
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .start();
      this._friendNodeMap.set(friendId, card);
      this._indexOwned++;
    } else if (this._indexUnowned < unOwnedIds.length) {
      // let hero = this.currentUnOwnedHero[this.indexUnOwned];
      let friendId = unOwnedIds[this._indexUnowned];
      let card = instantiate(this.friendViewHolder);
      card.active = true;
      card.name = "friend_card_" + friendId;
      card.getComponent(UIFriendCard).init(friendId);
      // card.getComponent(UIHeroCard).initInfo(hero, 0, this.indexUnOwned++);
      card.on(Node.EventType.TOUCH_END, this.showPreview, this);
      this.listContent.addChild(card);

      tween(card)
        .set({ scale: v3(0.1, 0.1, 1) })
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .start();
      this._friendNodeMap.set(friendId, card);
      this._indexUnowned++;
    }
  }
  private onOwnedItemClick(e: EventTouch) {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击仙友图标);
    let target = e.target;
    let ownedIds = FriendModule.data.getFriendIds(true, this._friendSort, this._friendType);
    let index = target.getSiblingIndex();
    UIMgr.instance.showDialog(FriendRouteItem.UIFriendFoster, [ownedIds, index]);
    log.log("onOwnedItemClick");
  }
  private showPreview(e: EventTouch) {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击仙友图标);
    let unOwnedIds = FriendModule.data.getFriendIds(false);
    let ownedIds = FriendModule.data.getFriendIds(true);
    let indexOffset = ownedIds.length + 1;
    UIMgr.instance.showDialog(FriendRouteItem.UIFriendPreview, [unOwnedIds, e.target.getSiblingIndex() - indexOffset]);
  }
  private refreshUI() {
    let ownedIds = FriendModule.data.getFriendIds(true, FriendSort.DEFAULT, FriendType.ALL);
    this.lblMembers.string = `仙友数量:${ownedIds.length}`;
    this.lblTotalsFriendShip.string = `总因果:${FriendModule.data.getTotalKarma()}`;
    this.lblTotalsTalent.string = `总天命:${FriendModule.data.getTotalDestiny()}`;
    let chatMax = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level).chatMax;
    if (FriendModule.data.vitality < 1) {
      this.lblCd.active = true;

      let lv = PlayerModule.data.getPlayerInfo().level;
      let chatTime = times(PlayerModule.data.getConfigLeaderData(lv).chatTime, 1000);
      let cd = (FriendModule.data.vitalityMessage?.lastUpdateTime ?? 0) + chatTime;
      FmUtils.setCd(this.lblCd, cd, true, () => {
        this.refreshUI();
      });
      this.lblPower.node.active = false;
    } else {
      this.lblCd.active = false;
      this.lblPower.node.active = true;
      this.lblPower.string = `精力:${FriendModule.data.vitality}/${chatMax}`;
    }
    // 校验一键互动条件
    let unlockFastList = Object.values(JsonMgr.instance.jsonList.c_friend)[0].unlockFastList;
    if (
      unlockFastList.length == 2 &&
      PlayerModule.data.playerDataMsg.level < unlockFastList[0] &&
      PlayerModule.data.playerDataMsg.vipLevel < unlockFastList[1]
    ) {
      FriendModule.viewModel.setting_ten_heart_talk = false;
    }
    this.checkBox.getChildByName("check").active = FriendModule.viewModel.setting_ten_heart_talk;
  }
  private refreshFriendList() {
    let ownedIds = FriendModule.data.getFriendIds(true, this._friendSort, this._friendType);
    let unOwnedIds = FriendModule.data.getFriendIds(false, this._friendSort, this._friendType);

    this.listContent.removeAllChildren();
    //插入分割条
    this.listContent.addChild(this.nodeDivider);
    ownedIds.forEach((id, i) => {
      let friendId = id;
      let friendNode = this._friendNodeMap.get(friendId);
      friendNode.getComponent(UIFriendCard).init(friendId);
      let index = this.nodeDivider.getSiblingIndex();
      this.listContent.insertChild(friendNode, index);
    });
    unOwnedIds.forEach((id, i) => {
      let friendId = id;
      let friendNode = this._friendNodeMap.get(friendId);
      friendNode.getComponent(UIFriendCard).init(friendId);
      this.listContent.addChild(friendNode);
    });
  }
  private onSelectSort(event: EventTouch) {
    AudioMgr.instance.playEffect(524);
    let target = event.target;
    switch (target.name) {
      case "select_1":
        this._friendSort = FriendSort.DEFAULT;
        this.lblSortName.string = "默认排序";
        break;
      case "select_2":
        this._friendSort = FriendSort.FRIENDSHIP;
        this.lblSortName.string = "因果排序";
        break;
      case "select_3":
        this._friendSort = FriendSort.TALENT;
        this.lblSortName.string = "天命排序";
        break;
    }
    this.refreshFriendList();
  }
  private onClickSort() {
    AudioMgr.instance.playEffect(524);
    let active = !this.nodeBlank.active;
    this.nodeBlank.active = active;
    this.bgSortIndicator.setScale(1, active ? 1 : -1);
  }
  private onClickBlank() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.nodeBlank.active = false;
    this.bgSortIndicator.setScale(1, this.nodeBlank.active ? 1 : -1);
    log.log("on_click_windows");
  }
  private onClickBelles() {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击美名录);
    UIMgr.instance.showDialog(FriendRouteItem.UIFriendRollOfBelles);
  }
  private onClickPowerAdd() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: 1004, type: UseTypeEnum.UIFriend });
  }
  private onClickHeartTalk() {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击互动按钮);
    if (FriendModule.data.vitality < 1) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: 1004, type: UseTypeEnum.UIFriend });
      return;
    }
    let curActive = this.checkBox.getChildByName("check").active;
    FriendModule.api.chat(
      curActive,
      (data: FriendChatResponse) => {
        this.refreshUI();
        if (Object.values(data.chatGetMap).length > 1) {
          //一键互动
          // log.log("一键互动", data.chatGetMap);
          RouteManager.uiRouteCtrl.showRoute(UIFriendHeartTalkOneKey, { payload: { chatList: data.chatGetMap } });
        } else {
          let keys = Object.keys(data.chatGetMap);
          this.hudongAnimate(Number(keys[0]), data.chatGetMap[keys[0]]);
        }
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  /**
   * 互动动画
   * @param friendId
   * @param chatAdd
   */
  private hudongAnimate(friendId: number, chatAdd: number) {
    let args = {
      friendId: friendId,
      chatAdd: chatAdd,
      keepMask: true,
    };
    let cardName = "friend_card_" + friendId;
    let card = this.listContent.getChildByName(cardName);
    tween(card)
      .call(() => {
        let offsetY = this.nodeAnchor.worldPosition.y - card.worldPosition.y;
        this.scrollView.getComponent(ScrollableView).scrollTo(0, offsetY, true);
      })
      .delay(0.3)
      .call(() => {
        AudioMgr.instance.playEffect(1074);
        RouteManager.uiRouteCtrl.showRoute(UIFriendHeartTalk, { payload: args });
        card.getComponent(Animation).play();
        let tx = card.getChildByName("tx_hudong");
        tx.active = true;
        tx.getComponentInChildren(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
          if (trackEntry.animation.name == "xy") {
            tx.active = false;
          }
        });
        tx.getComponentInChildren(sp.Skeleton).setAnimation(0, "xy", false);
      })
      .start();
  }

  private onClickSkillAddsBlank() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    log.log("onClickSkillAddsBlank");
    this.nodeSkillAdds.active = false;
  }
  private onClickSkillAdds() {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击技能加成);
    this.nodeSkillAdds.active = true;
  }

  private onClickWenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 7 });
  }
  private onClickQuickTalk() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let unlockFastList = Object.values(JsonMgr.instance.jsonList.c_friend)[0].unlockFastList;
    /* 打印出主角等级 贵族等级*/
    if (
      unlockFastList.length == 2 &&
      PlayerModule.data.playerDataMsg.level < unlockFastList[0] &&
      PlayerModule.data.playerDataMsg.vipLevel < unlockFastList[1]
    ) {
      TipMgr.showTip(`主角达到${unlockFastList[0]}级或者贵族${unlockFastList[1]}级解锁`);
      return;
    }
    let curActive = this.checkBox.getChildByName("check").active;
    this.checkBox.getChildByName("check").active = !curActive;
    this.checkBox.getComponent(Sprite).grayscale = curActive;
    FriendModule.viewModel.setting_ten_heart_talk = !curActive;
  }
}
