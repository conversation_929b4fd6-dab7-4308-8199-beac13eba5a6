import { _decorator, instantiate, isValid, Label, Node, tween, UIOpacity, UITransform, v2, v3, Vec3 } from "cc";
import FightManager, { scaleList } from "../../fight/manager/FightManager";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import Formate from "../../../lib/utils/Formate";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIFractureFightSuccess } from "./UIFractureFightSuccess";
import { UIFractureFightFaild } from "./UIFractureFightFaild";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;
const skipLabNum = 5;
@ccclass("UIFractureFight")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureFight",
  nextHop: [],
  exit: "",
})
export class UIFractureFight extends BaseCtrl {
  public playShowAni: boolean = true;
  private _args = null;

  private _round: number = 0;

  // 抖动cd
  private shakeCd: number = 0;

  private _maxRound = null;

  private _allHurt = 0;
  public init(args: RouteShowArgs): void {
    super.init(args);
    this._args = args.payload;
    this._maxRound = this._args.data.fightData.f;
  }
  protected start(): void {
    super.start();
    MsgMgr.on(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
    MsgMgr.on(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
    MsgMgr.on(MsgEnum.ON_SHAKE_WORLD, this.shakeWorld, this);
    MsgMgr.on(MsgEnum.ON_ClUB_FIGHT_DAMAGE, this.addHurt, this);
    this.onStart();
  }
  protected onDestroy(): void {
    super.onDestroy();
    MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
    MsgMgr.off(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
    MsgMgr.off(MsgEnum.ON_SHAKE_WORLD, this.shakeWorld, this);
    MsgMgr.off(MsgEnum.ON_ClUB_FIGHT_DAMAGE, this.addHurt, this);
  }
  protected update(dt: number): void {
    if (isValid(this.node, true) === false) {
      return;
    }
    FightManager.instance.tick(dt);
  }

  private addHurt(hurt: number) {
    if (isValid(this.node) == false) {
      return;
    }
    let node = instantiate(this.getNode("hurt"));
    this.getNode("add_hurt_layer").addChild(node);
    node.active = true;
    node.getComponent(Label).string = "+" + Formate.format(hurt);

    this._allHurt += hurt;
    this.setAllHurt();
    let op = node.getComponent(UIOpacity);

    tween(node)
      .by(0.3 / scaleList[FightManager.instance.speed], { position: v3(0, 5, 0) }, { easing: "sineOut" })
      .start();
    tween(op)
      .delay(0.3 / scaleList[FightManager.instance.speed])
      .to(0.3 / scaleList[FightManager.instance.speed], { opacity: 0 }, { easing: "sineOut" })
      .call(() => {
        node.removeFromParent();
        node.destroy();
      })
      .start();
  }

  private setAllHurt() {
    this.getNode("lbl_all_hurt").getComponent(Label).string = "伤害:" + Formate.format(this._allHurt);
  }

  private shakeWorld() {
    let ts = new Date().valueOf();
    if (this.shakeCd < ts) {
      // 0.2秒内不在抖动
      this.shakeCd = ts + 200;
      this.shake(this.getNode("world_bg"), v3(0, 0));
    }
  }

  /** 抖动具体方法 */
  private shake(node: Node, endPos: Vec3, amplitude: number = 20) {
    const tickTime = 0.05;

    // x轴震动
    let t1 = tween(node)
      .set({ position: endPos })
      .by(tickTime / 2, { position: v3(-amplitude / 2, 0) })
      .by(tickTime, { position: v3(+amplitude, 0) })
      .by(tickTime, { position: v3(-amplitude / 2, 0) });

    let t2 = tween(node)
      .delay(tickTime / 4)
      .by(tickTime / 2, { position: v3(0, amplitude / 2) })
      .by(tickTime, { position: v3(0, -amplitude) })
      .by(tickTime, { position: v3(0, +amplitude / 2) })
      .set({ position: endPos });
    tween(node).parallel(t1, t2).start();
  }

  private onStart(): void {
    this.getNode("btn_fightSkip").active = false;
    this.setAllHurt();
    this.setTimeScaleLab();

    let posMap = new Map([
      [1, v2(-200, -200)],
      [2, v2(200, -200)],
    ]);
    let contentSize = this.getNode("main").getComponent(UITransform);
    this.getNode("main").setScale(0, 0, 0);
    let nickname = PlayerModule.data.playerDataMsg.nickname;
    FightManager.instance.start({
      main: this.node,
      parent: this.getNode("fightPoint"),
      fight: this._args.data.fightData,
      posMap: posMap,
      playId: this._args.data.fightData.a,
      contentSize: contentSize,
      nameMap: { 1: nickname, 2: this._args.enemyName },
    });
  }

  /**更新回合显示 */
  private async setRoundShow(roundNumber: number) {
    this._round = roundNumber;

    this.getNode("roundLab").getComponent(Label).string = `第${this._round}/${this._maxRound}回合`;
    if (this._round >= skipLabNum) {
      this.getNode("btn_fightSkip").active = true;
      this.getNode("skipLab").active = false;
    } else {
      this.getNode("btn_fightSkip").active = false;
      this.getNode("skipLab").active = true;
      this.getNode("skipLab").getComponent(Label).string = skipLabNum - this._round + "回合可后跳过战斗";
    }
  }

  /**时间倍数按钮的显示 */
  private setTimeScaleLab() {
    this.getNode("scaleTimeLab").getComponent(Label).string = "x" + (PlayerModule.data.fightSpeed + 1);
  }

  /**战斗结束 */
  private fightEnd() {
    //胜利    失败
    log.log(this._args);
    if (this._args.data.win == false) {
      log.log("时空boss战斗结束-失败");
      RouteManager.uiRouteCtrl.showRoute(UIFractureFightFaild, {
        onCloseBack: () => {
          FightManager.instance.exit();
          this.closeBack();
        },
      });
    } else {
      log.log("时空boss战斗结束-成功");
      let args: RouteShowArgs = {
        payload: {
          enemyName: this._args.enemyName,
          fractureId: this._args.fractureId,
          rewardList: this._args.data.resAddList,
        },
        onCloseBack: () => {
          FightManager.instance.exit();
          this.closeBack();
        },
      };
      RouteManager.uiRouteCtrl.showRoute(UIFractureFightSuccess, args);
    }
    //this.closeBack();
  }

  private on_click_btn_fightSkip() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._round < skipLabNum) {
      TipMgr.showTip("第五回合后跳过战斗");
      return;
    }
    TipsMgr.setEnableTouch(false, 3, false);
    // if (FightManager.instance.fightOver == true) {
    //   log.error("结算中");
    //   return;
    // }
    FightManager.instance.fightOver = true;
    MsgMgr.emit(MsgEnum.ON_FIGHT_SKIP);
  }

  private on_click_btn_timeScale() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    PlayerModule.service.changeFightSpeed();
    this.setTimeScaleLab();
  }
}
