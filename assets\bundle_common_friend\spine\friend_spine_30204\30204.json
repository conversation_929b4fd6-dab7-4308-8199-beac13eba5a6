{"skeleton": {"hash": "V/oH4BQ6WgVgZkGUgtaRHYoEf5w", "spine": "3.8.75", "x": -468.81, "y": -791.65, "width": 933.98, "height": 1438.06, "images": "../images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 156.41, "rotation": 179.48, "x": 614.83, "y": -7.64}, {"name": "bone2", "parent": "bone", "length": 143.54, "rotation": -76.8, "x": 579.62, "y": 74.3}, {"name": "bone3", "parent": "bone2", "length": 186.26, "rotation": 21.36, "x": 143.54}, {"name": "bone4", "parent": "bone3", "length": 133.69, "rotation": -34.77, "x": 186.26}, {"name": "bone5", "parent": "bone", "length": 154.24, "rotation": 62.61, "x": 577.39, "y": 83.76}, {"name": "bone6", "parent": "bone5", "length": 150.29, "rotation": 20.41, "x": 154.24}, {"name": "bone7", "parent": "bone4", "length": 43.96, "rotation": 179.82, "x": 111.58, "y": 36.49}, {"name": "bone8", "parent": "bone4", "length": 99.54, "rotation": 143.81, "x": 149.69, "y": 57.3}, {"name": "bone11", "parent": "bone4", "length": 80.76, "rotation": -178.14, "x": 136.31, "y": 50.38}, {"name": "bone12", "parent": "bone11", "length": 68.29, "rotation": 30.58, "x": 81.31, "y": -0.01}, {"name": "bone13", "parent": "bone12", "length": 86.57, "rotation": -12.75, "x": 68.29}, {"name": "bone14", "parent": "bone13", "length": 53.5, "rotation": -11.89, "x": 86.57}, {"name": "bone15", "parent": "bone14", "length": 36.51, "rotation": -42.26, "x": 52.95, "y": 0.07}, {"name": "bone16", "parent": "bone12", "length": 63.98, "rotation": 4.96, "x": 64.22, "y": 5.2}, {"name": "bone17", "parent": "bone16", "length": 42.22, "rotation": 34.14, "x": 63.98}, {"name": "bone18", "parent": "bone17", "length": 27.78, "rotation": 58.19, "x": 42.22}, {"name": "bone9", "parent": "bone8", "length": 87.17, "rotation": -30.81, "x": 99.5, "y": -2.31}, {"name": "bone10", "parent": "bone9", "length": 60.14, "rotation": 29.89, "x": 87.17}, {"name": "bone19", "parent": "bone10", "length": 46.58, "rotation": 62.88, "x": 60.14}, {"name": "bone20", "parent": "bone4", "length": 104.73, "rotation": 147.96, "x": 116.79, "y": 64}, {"name": "bone21", "parent": "bone20", "length": 100.13, "rotation": -0.32, "x": 106.05, "y": -0.32}, {"name": "bone22", "parent": "bone21", "length": 70.45, "rotation": 32.08, "x": 100.13}, {"name": "bone23", "parent": "bone22", "length": 48.31, "rotation": 65.01, "x": 70.45}, {"name": "bone24", "parent": "bone4", "length": 104.67, "rotation": 16.33, "x": 154.31, "y": 53.96}, {"name": "bone25", "parent": "bone4", "length": 98.69, "rotation": -157.35, "x": 101.75, "y": -78.1}, {"name": "bone26", "parent": "bone25", "length": 85.96, "rotation": 34.63, "x": 98.69}, {"name": "bone27", "parent": "bone26", "length": 80.77, "rotation": 37.09, "x": 84.95, "y": 0.58}, {"name": "bone28", "parent": "bone27", "length": 58.27, "rotation": 9.72, "x": 80.7, "y": -1.04}, {"name": "bone29", "parent": "bone28", "length": 46.38, "rotation": -55.69, "x": 58.78, "y": -0.12}, {"name": "bone30", "parent": "bone25", "length": 47.82, "rotation": -45.52, "x": 39.58, "y": -34.21}, {"name": "bone31", "parent": "bone4", "length": 83.81, "rotation": -177.02, "x": 179.92, "y": -125.14}, {"name": "bone32", "parent": "bone31", "length": 50.89, "rotation": -8.65, "x": 83.81}, {"name": "bone33", "parent": "bone4", "length": 69.99, "rotation": 124.24, "x": 218.16, "y": -69.85}, {"name": "bone34", "parent": "bone33", "length": 30.95, "rotation": -7.45, "x": 69.99}, {"name": "bone35", "parent": "bone34", "length": 58.14, "rotation": 70.34, "x": 29.97, "y": 1.7}, {"name": "bone36", "parent": "bone35", "length": 53.13, "rotation": -23.45, "x": 58.14}, {"name": "bone37", "parent": "bone3", "x": 252.18, "y": 52.52}, {"name": "bone38", "parent": "bone3", "x": 122.33, "y": -71.01}, {"name": "bone39", "parent": "bone3", "length": 266.03, "rotation": 137.14, "x": 241.21, "y": 46.35}, {"name": "bone40", "parent": "bone39", "length": 131.61, "rotation": -32.77, "x": 263.16, "y": 0.13}, {"name": "bone41", "parent": "bone40", "length": 67.76, "rotation": -76.74, "x": 131.61}, {"name": "bone42", "parent": "bone41", "length": 31.94, "rotation": -12.44, "x": 72.21, "y": -22.54}, {"name": "bone43", "parent": "bone42", "length": 37.13, "rotation": -8.59, "x": 31.94}, {"name": "bone44", "parent": "bone41", "length": 18.72, "rotation": 21.36, "x": 79.84, "y": -8.2}, {"name": "bone45", "parent": "bone44", "length": 19.14, "rotation": 29.84, "x": 17.92, "y": -0.05}, {"name": "bone46", "parent": "bone45", "length": 24.23, "rotation": -5, "x": 19.45, "y": -0.13}, {"name": "bone47", "parent": "bone41", "length": 20.68, "rotation": 29.21, "x": 74.53, "y": 9.14}, {"name": "bone48", "parent": "bone47", "length": 19.77, "rotation": 58.1, "x": 20.68}, {"name": "bone49", "parent": "bone48", "length": 23.05, "rotation": -23.01, "x": 19.77}, {"name": "bone50", "parent": "bone41", "length": 17.82, "rotation": 49.46, "x": 69.03, "y": 15.81}, {"name": "bone51", "parent": "bone50", "length": 18.83, "rotation": 45.47, "x": 17.82}, {"name": "bone52", "parent": "bone51", "length": 17.62, "rotation": -6.68, "x": 18.83}, {"name": "bone53", "parent": "bone41", "length": 27.02, "rotation": 45.79, "x": 12.01, "y": 10.17}, {"name": "bone54", "parent": "bone53", "length": 24.75, "rotation": 5.88, "x": 27.02}, {"name": "bone55", "parent": "bone3", "length": 126.65, "rotation": 125.28, "x": 181, "y": 146.52}, {"name": "bone56", "parent": "bone3", "length": 110.61, "rotation": 100.46, "x": -10.16, "y": 266.33}, {"name": "bone57", "parent": "bone56", "length": 162.49, "rotation": 56.37, "x": 141.3, "y": 0.95}, {"name": "bone58", "parent": "bone57", "length": 145.56, "rotation": 42.94, "x": 162.49}, {"name": "bone59", "parent": "bone58", "length": 139.79, "rotation": -28.15, "x": 145.56}, {"name": "bone60", "parent": "bone59", "length": 144.06, "rotation": -9.15, "x": 139.79}, {"name": "bone61", "parent": "bone3", "length": 166.97, "rotation": -174.02, "x": -36, "y": -68.82}, {"name": "bone62", "parent": "bone61", "length": 69.59, "rotation": 16.95, "x": 167.53, "y": -0.21}, {"name": "bone63", "parent": "bone", "length": 139.81, "rotation": 55.05, "x": 690.01, "y": 119.87}, {"name": "bone64", "parent": "bone", "length": 425.31, "rotation": -160.97, "x": 688.45, "y": 244.18}, {"name": "bone65", "parent": "bone64", "length": 461.02, "rotation": -94.09, "x": 422.58, "y": -3.52}, {"name": "bone66", "parent": "bone65", "length": 188.95, "rotation": 36.83, "x": 462.77, "y": 1.46}, {"name": "bone67", "parent": "bone", "length": 374.9, "rotation": 148.98, "x": 546.12, "y": 215.66}, {"name": "bone68", "parent": "bone67", "length": 226.08, "rotation": -49.35, "x": 374.9}, {"name": "bone69", "parent": "bone4", "x": 129.36, "y": -68.66, "color": "abe323ff"}, {"name": "bone70", "parent": "bone4", "x": 160.76, "y": -84.35}, {"name": "bone71", "parent": "bone4", "x": 99.07, "y": -69.48, "color": "abe323ff"}, {"name": "target3", "parent": "root", "x": 422.6, "y": -631.88, "color": "ff3f00ff"}, {"name": "bone72", "parent": "root", "length": 132.33, "rotation": 98.2, "x": 346.86, "y": 32.11}, {"name": "bone73", "parent": "bone72", "length": 152.57, "rotation": 4.76, "x": 132.33}, {"name": "bone74", "parent": "bone73", "length": 95.9, "rotation": 5.7, "x": 152.57}, {"name": "bone75", "parent": "bone74", "length": 72.83, "rotation": 8.32, "x": 95.9}, {"name": "bone76", "parent": "bone75", "length": 76.95, "rotation": 8.66, "x": 72.83}, {"name": "bone78", "parent": "bone75", "length": 71.3, "rotation": 66.57, "x": 60.11, "y": 14.04}, {"name": "bone79", "parent": "bone74", "length": 86.05, "rotation": 43.91, "x": 37.51, "y": 11.88}, {"name": "bone80", "parent": "bone73", "length": 110.35, "rotation": 36.11, "x": 108.05, "y": 10.78}, {"name": "bone81", "parent": "bone72", "length": 75.52, "rotation": 56.1, "x": 66.72, "y": 27.55}, {"name": "bone82", "parent": "bone72", "length": 59.14, "rotation": 113.93, "x": 11.16, "y": 33.61}, {"name": "bone77", "parent": "bone35", "length": 31.98, "rotation": -2.19, "x": 61.94, "y": 0.42}, {"name": "bone83", "parent": "bone77", "length": 33.31, "rotation": -19.59, "x": 31.98}, {"name": "bone84", "parent": "bone3", "x": 68.38, "y": 320.72}, {"name": "bone85", "parent": "bone3", "x": 128.02, "y": -31.15}, {"name": "bone86", "parent": "bone3", "x": 52.2, "y": -58.57, "color": "abe323ff"}, {"name": "bone87", "parent": "root", "length": 403.69, "rotation": 132.03, "x": 341.62, "y": -724.17}, {"name": "bone88", "parent": "bone87", "length": 338.41, "rotation": 26.09, "x": 402.83, "y": -0.96}, {"name": "bone89", "parent": "bone88", "length": 342.15, "rotation": -36.34, "x": 338.41}, {"name": "bone90", "parent": "root", "length": 132.33, "rotation": 89.47, "x": -394.37, "y": -283.12, "scaleY": -1}, {"name": "bone91", "parent": "bone90", "length": 152.57, "rotation": 4.76, "x": 132.33}, {"name": "bone92", "parent": "bone91", "length": 95.9, "rotation": 5.7, "x": 152.57}, {"name": "bone93", "parent": "bone92", "length": 72.83, "rotation": 8.32, "x": 95.9}, {"name": "bone94", "parent": "bone93", "length": 76.95, "rotation": 8.66, "x": 72.83}, {"name": "bone95", "parent": "bone93", "length": 71.3, "rotation": 66.57, "x": 60.11, "y": 14.04}, {"name": "bone96", "parent": "bone92", "length": 86.05, "rotation": 43.91, "x": 37.51, "y": 11.88}, {"name": "bone97", "parent": "bone91", "length": 110.35, "rotation": 36.11, "x": 108.05, "y": 10.78}, {"name": "bone98", "parent": "bone90", "length": 75.52, "rotation": 56.1, "x": 66.72, "y": 27.55}, {"name": "bone99", "parent": "bone90", "length": 59.14, "rotation": 113.93, "x": 11.16, "y": 33.61}, {"name": "bone100", "parent": "root", "x": 115.44, "y": -355.84}, {"name": "bone101", "parent": "root", "x": 225.6, "y": -428.74}, {"name": "bone102", "parent": "root", "x": 304.17, "y": -536.47}, {"name": "bone103", "parent": "root", "x": 385.17, "y": -637.72}, {"name": "target2", "parent": "root", "x": 252.93, "y": -591.03, "color": "ff3f00ff"}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "bg_hua", "bone": "bone72", "attachment": "bg_hua"}, {"name": "bg_hua2", "bone": "bone90", "attachment": "bg_hua"}, {"name": "qunzi_hou", "bone": "root", "attachment": "qunzi_hou"}, {"name": "piaoda<PERSON>_zuo", "bone": "root", "attachment": "piaoda<PERSON>_zuo"}, {"name": "shu<PERSON>", "bone": "root", "attachment": "shu<PERSON>"}, {"name": "toufa_hou_you", "bone": "root", "attachment": "toufa_hou_you"}, {"name": "toufa_hou_zuo", "bone": "root", "attachment": "toufa_hou_zuo"}, {"name": "gebo_you", "bone": "root", "attachment": "gebo_you"}, {"name": "tui_hou", "bone": "root", "attachment": "tui_hou"}, {"name": "tui_qian_shang", "bone": "root"}, {"name": "a30204", "bone": "root", "attachment": "a30204"}, {"name": "tui_qian_xia", "bone": "root"}, {"name": "shenti", "bone": "root", "attachment": "shenti"}, {"name": "toufa_you", "bone": "root", "attachment": "toufa_you"}, {"name": "gebo_zuo", "bone": "root", "attachment": "gebo_zuo"}, {"name": "toufa_qian", "bone": "root", "attachment": "toufa_qian"}, {"name": "yanjing", "bone": "root", "attachment": "yanjing"}, {"name": "lian", "bone": "root", "attachment": "lian"}, {"name": "toufa_shang", "bone": "root", "attachment": "toufa_shang"}], "ik": [{"name": "target", "order": 1, "bones": ["bone64", "bone65"], "target": "target2", "bendPositive": false}, {"name": "target3", "bones": ["bone67", "bone68"], "target": "target3", "bendPositive": false}], "transform": [{"name": "bone71", "order": 3, "bones": ["bone69"], "target": "bone71", "x": 30.29, "y": 0.82, "rotateMix": 0, "translateMix": 0.126, "scaleMix": 0, "shearMix": 0}, {"name": "bone86", "order": 4, "bones": ["bone85"], "target": "bone86", "x": 75.83, "y": 27.42, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "lian", "order": 2, "bones": ["bone70"], "target": "bone71", "x": 61.69, "y": -14.88, "rotateMix": -1, "translateMix": -1, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"gebo_zuo": {"gebo_zuo": {"type": "mesh", "uvs": [0.73608, 0.00559, 0.65808, 0.06173, 0.61408, 0.1425, 0.62408, 0.22876, 0.55208, 0.27394, 0.59608, 0.35061, 0.54208, 0.42043, 0.46808, 0.48615, 0.39208, 0.57514, 0.40008, 0.64222, 0.32608, 0.68056, 0.27208, 0.70794, 0.19208, 0.66276, 0.12608, 0.61484, 0.07208, 0.58609, 0.06808, 0.64085, 0.12608, 0.6874, 0.16608, 0.70931, 0.06808, 0.72437, 0, 0.74354, 0, 0.79419, 0.06344, 0.7927, 0.10011, 0.76834, 0.13862, 0.75732, 0.18521, 0.76038, 0.14057, 0.76515, 0.11351, 0.77031, 0.08477, 0.79495, 0.04906, 0.82378, 0.00387, 0.85148, 0.03513, 0.87082, 0.08823, 0.85099, 0.07754, 0.90269, 0.11685, 0.88805, 0.14451, 0.8534, 0.17962, 0.82561, 0.25204, 0.84476, 0.19663, 0.8733, 0.1873, 0.9041, 0.27076, 0.88882, 0.30838, 0.88036, 0.37735, 0.88427, 0.44331, 0.87643, 0.49503, 0.91833, 0.49762, 0.98619, 0.5055, 0.99469, 0.59882, 0.96154, 0.65946, 0.90588, 0.7214, 0.82797, 0.76863, 0.75944, 0.80425, 0.68047, 0.84192, 0.58235, 0.8863, 0.499, 0.92499, 0.41477, 0.96026, 0.32831, 0.99554, 0.24963, 1, 0.17802, 0.99895, 0.12505, 0.93708, 0.06868, 0.86761, 0.01826, 0.80149, 0, 0.9329, 0.13915, 0.89356, 0.17697, 0.87682, 0.19588, 0.92871, 0.17869, 0.98646, 0.1403, 0.6503, 0.23673, 0.73488, 0.22682, 0.82057, 0.21006, 0.79961, 0.07086, 0.7514, 0.19886, 0.78792, 0.24986, 0.77477, 0.36786, 0.74994, 0.47386, 0.70027, 0.57386, 0.60094, 0.67686, 0.30878, 0.74286, 0.37441, 0.7738, 0.46439, 0.73296, 0.6913, 0.73898, 0.61404, 0.77715, 0.52308, 0.83004, 0.45363, 0.86753, 0.40962, 0.88025, 0.65023, 0.42421, 0.60499, 0.53067, 0.49613, 0.63906, 0.68425, 0.33315, 0.2629, 0.79754, 0.35094, 0.82617, 0.30366, 0.83986], "triangles": [43, 81, 47, 45, 44, 46, 44, 43, 46, 46, 43, 47, 55, 64, 56, 56, 64, 65, 65, 57, 56, 65, 61, 57, 17, 16, 12, 15, 13, 16, 16, 13, 12, 15, 14, 13, 24, 11, 76, 24, 17, 11, 76, 11, 10, 17, 12, 11, 23, 17, 24, 22, 18, 23, 23, 18, 17, 20, 19, 21, 21, 18, 22, 21, 19, 18, 30, 28, 31, 30, 29, 28, 26, 35, 27, 28, 27, 31, 35, 31, 27, 35, 26, 25, 35, 25, 24, 36, 35, 88, 33, 32, 31, 33, 31, 34, 34, 31, 35, 38, 37, 39, 37, 36, 39, 39, 36, 40, 36, 90, 40, 40, 90, 89, 36, 88, 90, 90, 88, 89, 89, 88, 77, 35, 24, 88, 88, 76, 77, 88, 24, 76, 47, 80, 48, 48, 79, 49, 42, 82, 43, 43, 82, 81, 81, 80, 47, 41, 83, 42, 83, 41, 89, 41, 40, 89, 42, 83, 82, 82, 83, 89, 81, 82, 77, 82, 89, 77, 77, 78, 81, 81, 78, 80, 80, 79, 48, 78, 75, 80, 80, 75, 79, 78, 77, 10, 77, 76, 10, 79, 75, 50, 10, 9, 78, 78, 86, 75, 78, 9, 86, 75, 86, 85, 9, 8, 86, 86, 8, 85, 85, 8, 7, 7, 6, 85, 85, 6, 84, 6, 5, 84, 84, 5, 87, 87, 4, 66, 66, 4, 3, 4, 87, 5, 49, 79, 50, 75, 74, 50, 50, 74, 51, 75, 85, 74, 74, 73, 51, 51, 73, 52, 74, 85, 73, 85, 84, 73, 52, 73, 53, 73, 72, 53, 73, 84, 72, 84, 87, 72, 53, 72, 54, 72, 71, 54, 72, 87, 71, 55, 54, 71, 71, 68, 63, 87, 67, 71, 87, 66, 67, 55, 71, 63, 55, 63, 64, 71, 67, 68, 67, 66, 70, 2, 70, 66, 67, 70, 68, 2, 66, 3, 63, 68, 62, 70, 2, 69, 62, 68, 70, 63, 62, 64, 62, 70, 69, 64, 61, 65, 64, 62, 61, 62, 69, 61, 61, 58, 57, 61, 69, 58, 69, 59, 58, 69, 60, 59, 69, 2, 1, 1, 0, 69, 69, 0, 60], "vertices": [2, 37, 27.78, -4, 0.99283, 39, -26.94, -27.95, 0.00717, 3, 37, 20.07, 31.27, 0.88615, 39, 2.71, -48.55, 0.09897, 55, -103.92, -38.25, 0.01488, 3, 37, -3.24, 63.99, 0.36731, 39, 42.05, -56.69, 0.51295, 55, -63.75, -38.13, 0.11974, 3, 37, -38.31, 83.88, 0.04976, 39, 81.29, -47.41, 0.3516, 55, -27.25, -20.99, 0.59864, 3, 37, -42.87, 114.7, 0.00209, 39, 105.6, -66.91, 0.00408, 55, 0.54, -35.07, 0.99383, 1, 55, 29.04, -9.35, 1, 1, 55, 65.56, -14.01, 1, 2, 55, 102.53, -25.32, 0.99981, 42, 18.81, -130.94, 0.00019, 5, 55, 149.88, -33.41, 0.69678, 40, 42.7, -86.79, 0.12413, 41, 64.07, -106.45, 0.00626, 42, 10.14, -83.7, 0.05282, 85, 53.3, -32.55, 0.12, 5, 55, 178.24, -20.01, 0.19418, 40, 64.41, -64.15, 0.21993, 41, 47.02, -80.13, 0.03385, 42, -12.19, -61.66, 0.11204, 85, 25.98, -17.14, 0.44, 5, 55, 203.28, -35.81, 0.02657, 40, 93.44, -69.98, 0.08371, 41, 59.35, -53.21, 0.05107, 42, -5.95, -32.73, 0.15066, 85, 24.42, 12.42, 0.688, 4, 55, 221.29, -47.44, 0.01489, 40, 114.41, -74.41, 0.0633, 41, 68.48, -33.82, 0.09534, 42, -1.21, -11.82, 0.82647, 2, 42, 31.87, -11.13, 0.49357, 43, 1.59, -11.02, 0.50643, 2, 42, 62.39, -14.33, 0, 43, 32.25, -9.61, 1, 2, 42, 84.19, -13.25, 0, 43, 53.64, -5.29, 1, 1, 43, 35.12, 12.32, 1, 3, 44, 27.48, -23.61, 0.01009, 42, 40.33, 11.31, 0.11783, 43, 6.61, 12.43, 0.87208, 3, 45, -11.2, -10.82, 0.07499, 44, 13.58, -15.01, 0.37917, 42, 24, 10.72, 0.54584, 2, 46, 2.3, -16.27, 0.55549, 45, 20.32, -16.54, 0.44451, 2, 46, 25.71, -14.45, 0.99394, 45, 43.8, -16.77, 0.00606, 1, 46, 32.97, 8.01, 1, 1, 46, 13.51, 13.57, 1, 3, 47, 29.07, -7.3, 0.0001, 46, -1.12, 6.37, 0.52465, 45, 18.9, 6.31, 0.47525, 4, 41, 95.08, 6.61, 0.00239, 47, 16.7, -12.23, 0.01951, 45, 5.58, 6.37, 0.74966, 44, 19.59, 8.25, 0.22844, 4, 41, 81.31, 0.83, 0.11273, 47, 1.86, -10.57, 0.38929, 45, -7.55, 13.47, 0.00287, 44, 4.66, 7.88, 0.4951, 4, 41, 92.8, 9.53, 0.00391, 47, 16.13, -8.58, 0.86798, 48, -9.68, -0.67, 0.07805, 44, 18.53, 11.8, 0.05006, 3, 47, 24.8, -6.31, 0.47343, 48, -3.18, -6.84, 0.52144, 44, 26.81, 15.22, 0.00513, 2, 52, -17.21, -23.45, 0.00173, 48, 11.38, -8.79, 0.99827, 2, 52, 0.13, -26.59, 0.17439, 48, 28.76, -11.64, 0.82561, 2, 52, 18.52, -32.62, 0.27377, 48, 47.25, -17.37, 0.72623, 2, 52, 21.33, -19.48, 0.28035, 48, 49.85, -4.18, 0.71965, 3, 51, 22.55, -9.93, 0.05789, 52, 4.85, -9.43, 0.5258, 48, 33.21, 5.59, 0.41631, 1, 52, 27.42, -0.33, 1, 3, 54, 52.42, -11.45, 0, 52, 15.24, 7.11, 1, 50, 38.44, 28.52, 0, 4, 54, 37.91, -22.76, 0.00018, 51, 16.47, 7, 0.72058, 52, -3.16, 6.68, 0.2518, 50, 24.38, 16.65, 0.02744, 3, 54, 22.48, -30.2, 0.01585, 51, 0.15, 12.15, 0.13742, 50, 9.25, 8.63, 0.84673, 4, 41, 43.91, 25.37, 0.19349, 53, 33.14, -12.27, 0.04133, 54, 4.83, -12.83, 0.56808, 50, -9.06, 25.3, 0.1971, 4, 56, 139.45, -78.35, 0, 41, 53.18, 45.45, 0.00121, 54, 26.33, -7.65, 0.99656, 50, 12.23, 31.31, 0.00222, 2, 56, 151.64, -70.21, 2e-05, 54, 34.76, 4.34, 0.99998, 3, 56, 127.68, -56.61, 0.06604, 53, 33.62, 9.11, 0.24206, 54, 7.5, 8.38, 0.6919, 4, 40, 166.88, -12.47, 0.2856, 56, 116.36, -51, 0.0427, 53, 20.99, 8.96, 0.22436, 54, -5.08, 9.53, 0.44734, 2, 40, 153.66, 5.21, 0.808, 53, 0.56, 17.32, 0.192, 5, 40, 136.97, 18.54, 0.74344, 56, 84.4, -22.11, 0.14741, 41, -16.81, 9.47, 0.00084, 53, -20.6, 20.18, 0.10801, 54, -45.3, 24.95, 0.00031, 1, 57, -28.41, 47.1, 1, 1, 57, 2.8, 41.92, 1, 1, 57, 7.16, 43.64, 1, 2, 56, 76.87, 40.96, 0.216, 57, -2.37, 75.8, 0.784, 2, 56, 44.89, 36.05, 0.392, 57, -24.17, 99.7, 0.608, 1, 56, 5.34, 24.04, 1, 2, 39, 318.67, 35.84, 0.29606, 56, -27.8, 11.85, 0.70394, 2, 39, 280.57, 41.46, 0.81889, 56, -61.71, -6.4, 0.18111, 2, 39, 233.55, 46.37, 0.9947, 56, -102.35, -30.55, 0.0053, 1, 39, 193.01, 54.45, 1, 1, 39, 152.33, 60.66, 1, 1, 39, 110.8, 65.65, 1, 2, 39, 72.85, 71.18, 0.008, 3, 139.39, 43.73, 0.992, 1, 3, 164.11, 21.62, 1, 1, 3, 186.86, 10.28, 1, 1, 39, -2.05, 50.17, 1, 2, 37, -0.63, -35.44, 0.22727, 39, -27.5, 14.42, 0.77273, 2, 37, 18.24, -22.74, 0.76542, 39, -32.69, -7.72, 0.23458, 1, 39, 34.18, 55.64, 1, 1, 39, 44.34, 33.87, 1, 1, 39, 53.86, 29.93, 1, 1, 39, 43.42, 45.07, 1, 1, 3, 183.2, 17.56, 1, 3, 37, -46.07, 79.03, 0.02832, 39, 83.69, -38.58, 0.57165, 55, -26.72, -11.85, 0.40003, 3, 37, -57.38, 54.11, 0.01586, 39, 75.01, -12.62, 0.87508, 55, -40.54, 11.78, 0.10906, 1, 39, 63.13, 13.21, 1, 3, 37, -8.76, -3.74, 0.18389, 39, 0.03, -3.29, 0.8161, 55, -115.84, 5.5, 0, 3, 37, -49.54, 42.43, 0.01996, 39, 61.33, -9.4, 0.93849, 55, -54.6, 12.12, 0.04154, 1, 39, 83.04, 5.74, 1, 1, 39, 138.03, 9.97, 1, 1, 39, 188.05, 9.67, 1, 2, 39, 236.52, 1.11, 0.99996, 56, -72.93, -65.08, 4e-05, 5, 55, 170.78, 45.64, 0.02081, 40, 34.01, -5.48, 0.79252, 41, -17.05, -96.26, 0.00016, 42, -71.28, -91.22, 0.00251, 85, -23.3, -61.15, 0.184, 4, 55, 232.39, -30.75, 0.02522, 40, 118.83, -54.86, 0.15981, 41, 50.46, -25.03, 0.65071, 42, -20.7, -7.12, 0.16427, 2, 40, 115.74, -29.63, 0.624, 42, -45.97, -9.85, 0.376, 1, 40, 82.46, -20.76, 1, 3, 39, 313.01, 10.01, 0.06026, 40, 36.57, 35.29, 0.79429, 56, -16.91, -12.25, 0.14545, 1, 40, 66.23, 28.63, 1, 1, 40, 103.91, 23.25, 1, 1, 40, 131.68, 18.25, 1, 5, 40, 145.43, 11.67, 0.9049, 56, 93.31, -28.38, 0.00978, 41, -8.19, 16.12, 0.00166, 53, -9.82, 18.64, 0.08366, 54, -34.73, 22.31, 0, 2, 39, 170.03, -25.29, 0.69156, 55, 55.05, 18.9, 0.30844, 3, 39, 221.26, -31.99, 0.75335, 55, 106.57, 22.86, 0.24485, 40, -17.84, -49.69, 0.0018, 6, 39, 276.48, -58.62, 0.00247, 55, 166.07, 8.15, 0.26298, 40, 42.99, -42.19, 0.35144, 41, 20.73, -95.94, 0.01111, 42, -34.45, -82.76, 0.052, 85, 10.03, -43.34, 0.32, 2, 39, 126.44, -21.03, 0.68473, 55, 11.52, 14.11, 0.31527, 5, 41, 51.29, 4.34, 0.87869, 53, 23.21, -32.21, 0.00614, 54, -7.09, -31.65, 0.02537, 50, -20.24, 6.03, 0.08657, 47, -22.64, 7.15, 0.00323, 3, 41, 20.23, 2.79, 0.86064, 53, 0.44, -11.03, 0.13924, 50, -41.6, 28.63, 0.00012, 3, 41, 30.49, 15.55, 0.30302, 53, 16.74, -9.49, 0.66803, 50, -25.24, 29.12, 0.02895], "hull": 61, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 0, 120, 116, 122, 122, 124, 124, 126, 126, 128, 128, 130, 114, 130, 6, 132, 132, 134, 134, 136, 136, 126, 138, 140, 142, 144, 144, 146, 146, 148, 148, 150, 22, 152, 152, 154, 154, 156, 158, 160, 160, 162, 162, 164, 166, 82, 168, 170, 170, 172, 168, 174, 178, 180, 180, 72, 90, 88, 88, 86, 86, 84, 84, 82, 78, 80, 80, 82], "width": 319, "height": 466}}, "tui_qian_shang": {"tui_qian_shang": {"type": "mesh", "uvs": [0, 0.48299, 0.0613, 0.34768, 0.16677, 0.23814, 0.28656, 0.17156, 0.43109, 0.09424, 0.54177, 0.05129, 0.66547, 0.01692, 0.76963, 0, 0.89203, 0, 1, 0.03625, 1, 0.20807, 1, 0.34338, 0.92458, 0.36701, 0.81521, 0.40567, 0.72406, 0.45292, 0.64463, 0.52809, 0.56521, 0.62689, 0.47927, 0.74931, 0.3738, 0.86314, 0.29698, 0.92972, 0.19411, 0.98342, 0.08083, 0.94046, 0.00922, 0.82878, 0, 0.68058], "triangles": [13, 8, 12, 11, 12, 10, 19, 20, 23, 23, 20, 21, 19, 2, 18, 1, 19, 23, 19, 1, 2, 2, 3, 18, 18, 3, 17, 22, 23, 21, 17, 3, 16, 16, 3, 4, 23, 0, 1, 16, 4, 15, 15, 4, 5, 14, 5, 6, 14, 15, 5, 13, 6, 7, 13, 14, 6, 13, 7, 8, 12, 8, 10, 8, 9, 10], "vertices": [1, 64, -12.08, 44.94, 1, 1, 64, 28.64, 72.46, 1, 1, 64, 86.93, 85.94, 1, 1, 64, 147.6, 85.35, 1, 1, 64, 220.52, 83.82, 1, 1, 64, 274.8, 78.17, 1, 1, 64, 334.17, 68.12, 1, 1, 64, 383.01, 56.34, 1, 1, 64, 438.5, 37.05, 1, 1, 64, 483.99, 10.06, 1, 2, 64, 463.25, -46.25, 0.72339, 65, 8.17, 56.35, 0.27661, 2, 64, 424.52, -91.74, 0.32075, 65, 61.56, 50.43, 0.67925, 2, 64, 378.6, -68.05, 0.09813, 65, 68.05, 4.37, 0.90187, 2, 64, 324.76, -26.18, 0.01099, 65, 66.23, -53.09, 0.98901, 2, 64, 301.15, -41.61, 0.65691, 65, 102.61, -62.53, 0.34309, 2, 64, 270.99, -63.76, 0.92987, 65, 145.55, -72.25, 0.07013, 2, 64, 229.78, -83.03, 0.99294, 65, 189.38, -90.93, 0.00706, 1, 64, 179.75, -103.83, 1, 1, 64, 121.05, -118.49, 1, 1, 64, 79.86, -124.67, 1, 1, 64, 28.09, -123.21, 1, 1, 64, -19.16, -93.55, 1, 1, 64, -40.95, -51.56, 1, 1, 64, -30.97, -9.37, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46], "width": 480, "height": 291}}, "toufa_hou_you": {"toufa_hou_you": {"type": "mesh", "uvs": [0.25947, 0.00425, 0.26405, 0.17416, 0.29456, 0.36295, 0.34949, 0.50049, 0.44713, 0.61646, 0.55393, 0.6731, 0.64548, 0.64613, 0.75533, 0.58949, 0.87586, 0.54634, 0.9735, 0.5868, 1, 0.68928, 1, 0.83492, 1, 0.99943, 0.92316, 0.96977, 0.86518, 0.96437, 0.68057, 0.96437, 0.51732, 0.88886, 0.37848, 0.7621, 0.26405, 0.5787, 0.20454, 0.63264, 0.09927, 0.68928, 0, 0.60298, 0, 0.4061, 0.01688, 0.24428, 0.11758, 0.12831, 0.18318, 0, 0.17556, 0.17146, 0.19844, 0.3198, 0.14809, 0.47352, 0.08554, 0.56792, 0.61344, 0.81873, 0.72786, 0.7594, 0.8606, 0.7648, 0.93841, 0.8538], "triangles": [21, 29, 20, 20, 29, 19, 29, 28, 19, 19, 28, 18, 21, 22, 29, 28, 27, 18, 29, 22, 28, 22, 23, 28, 28, 23, 27, 24, 27, 23, 13, 33, 12, 33, 11, 12, 13, 14, 33, 14, 32, 33, 11, 33, 10, 33, 32, 10, 32, 9, 10, 15, 31, 14, 31, 32, 14, 15, 30, 31, 31, 7, 32, 32, 8, 9, 32, 7, 8, 16, 5, 30, 30, 6, 31, 30, 5, 6, 31, 6, 7, 16, 30, 15, 17, 4, 16, 16, 4, 5, 18, 3, 17, 17, 3, 4, 18, 2, 3, 18, 27, 2, 27, 24, 26, 27, 1, 2, 27, 26, 1, 26, 0, 1, 26, 25, 0, 26, 24, 25], "vertices": [1, 25, -0.06, 24.83, 1, 1, 25, 31.74, 13.73, 1, 1, 25, 70.4, 9.65, 1, 2, 25, 102.85, 17.29, 0.11192, 26, 13.25, 11.86, 0.88808, 2, 26, 54.42, 11.51, 0.97919, 27, -17.76, 27.12, 0.02081, 1, 27, 18.82, 13.53, 1, 1, 27, 51.14, 16.79, 1, 2, 27, 90.23, 25.51, 0.57184, 28, 13.87, 24.56, 0.42816, 3, 27, 132.88, 31.32, 0.00195, 28, 56.89, 23.08, 0.86905, 29, -20.23, 11.52, 0.129, 2, 28, 88.28, 7.36, 0.00917, 29, 10.45, 28.58, 0.99083, 1, 29, 30.96, 19.81, 1, 2, 28, 85.9, -42.58, 0.00107, 29, 50.36, -1.53, 0.99893, 2, 28, 78.34, -74.27, 0.0257, 29, 72.27, -25.63, 0.9743, 2, 28, 53.54, -62.31, 0.20576, 29, 48.42, -39.38, 0.79424, 2, 28, 34.05, -56.57, 0.52804, 29, 32.69, -52.24, 0.47196, 4, 26, 160.55, -1.02, 0.05724, 27, 59.34, -46.88, 0.27366, 28, -28.8, -41.57, 0.66141, 29, -15.12, -95.7, 0.00769, 3, 26, 104.62, -20, 0.91172, 27, 3.28, -28.28, 0.07565, 28, -80.91, -13.77, 0.01263, 1, 26, 50.24, -25.8, 1, 3, 25, 106.02, -16.23, 0.46026, 26, -3.19, -17.53, 0.41097, 30, 33.73, 60, 0.12877, 3, 25, 108.14, -39.54, 0.41362, 26, -14.69, -37.91, 0.02763, 30, 51.84, 45.18, 0.55875, 2, 25, 104.75, -77.91, 0.0529, 30, 76.84, 15.88, 0.9471, 1, 30, 75.06, -22.8, 1, 1, 30, 39.33, -38.37, 1, 2, 25, 12.25, -71.69, 0.00026, 30, 7.6, -45.76, 0.99974, 2, 25, 4.14, -30.41, 0.45653, 30, -27.54, -22.63, 0.54347, 2, 25, -10.83, 0.39, 0.9999, 30, -60, -11.73, 0.0001, 2, 25, 19.66, -14.79, 0.72831, 30, -27.81, -0.61, 0.27169, 2, 25, 49.89, -18.35, 0.55939, 30, -4.09, 18.47, 0.44061, 2, 25, 71.53, -46.08, 0.15746, 30, 30.85, 14.48, 0.84254, 2, 25, 80.67, -73.38, 0.01236, 30, 56.73, 1.87, 0.98764, 3, 26, 125.06, 10.11, 0.14148, 27, 37.75, -16.59, 0.75123, 28, -44.96, -8.07, 0.10729, 3, 26, 152.03, 41.97, 0.00089, 28, -3.28, -5.94, 0.99649, 29, -30.18, -54.54, 0.00261, 2, 28, 41.66, -17.76, 0.70897, 29, 4.92, -24.08, 0.29103, 2, 28, 64.06, -41.22, 0.12333, 29, 36.92, -18.8, 0.87667], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66], "width": 350, "height": 198}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [348.06, -643.6, -345.94, -643.6, -345.94, 646.4, 348.06, 646.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 416, "height": 774}}, "qunzi_hou": {"qunzi_hou": {"type": "mesh", "uvs": [0.0659, 0.08177, 0.17115, 0.03879, 0.25358, 0.0096, 0.28063, 0, 0.32866, 0.02236, 0.38122, 0.0472, 0.43869, 0.09249, 0.49615, 0.13707, 0.56472, 0.1873, 0.63655, 0.23188, 0.71266, 0.26883, 0.80604, 0.31199, 0.8733, 0.35091, 0.91578, 0.41468, 0.92427, 0.49181, 0.92949, 0.55903, 0.94451, 0.63332, 0.95431, 0.69512, 0.98892, 0.74394, 1, 0.84937, 1, 0.94985, 1, 1, 0.95235, 1, 0.89946, 1, 0.81914, 0.94489, 0.76972, 0.88899, 0.73054, 0.82178, 0.69136, 0.74819, 0.66328, 0.67955, 0.6339, 0.61233, 0.59191, 0.54709, 0.556, 0.48624, 0.50833, 0.43034, 0.42996, 0.36539, 0.37445, 0.32506, 0.31372, 0.30808, 0.23732, 0.28615, 0.1681, 0.26209, 0.10868, 0.23591, 0.05709, 0.20336, 0.01138, 0.15737, 0, 0.11491, 0.03031, 0.08449, 0.10153, 0.11527, 0.27582, 0.10135, 0.39498, 0.17969, 0.56124, 0.25905, 0.67541, 0.33496, 0.75741, 0.39864, 0.80379, 0.51936, 0.83232, 0.64185, 0.8636, 0.74721, 0.89864, 0.87942, 0.75168, 0.69495, 0.69944, 0.54237, 0.60584, 0.3863, 0.45289, 0.29828, 0.29046, 0.22198, 0.14576, 0.18352], "triangles": [44, 2, 3, 44, 3, 4, 1, 2, 44, 43, 0, 1, 40, 0, 43, 39, 40, 43, 5, 44, 4, 38, 39, 58, 38, 58, 37, 36, 37, 57, 36, 57, 35, 52, 19, 20, 20, 22, 52, 18, 51, 17, 52, 18, 19, 24, 25, 52, 22, 23, 52, 24, 52, 23, 22, 20, 21, 13, 48, 12, 49, 13, 14, 49, 14, 15, 29, 30, 54, 50, 15, 16, 28, 29, 54, 17, 50, 16, 27, 28, 53, 26, 27, 53, 25, 26, 51, 46, 7, 8, 46, 8, 9, 47, 9, 10, 33, 34, 56, 48, 10, 11, 33, 56, 32, 31, 32, 55, 12, 48, 11, 30, 31, 55, 40, 41, 42, 45, 5, 6, 45, 6, 7, 0, 40, 42, 34, 35, 45, 18, 52, 51, 52, 25, 51, 48, 13, 49, 54, 48, 49, 50, 49, 15, 50, 53, 54, 50, 54, 49, 28, 54, 53, 17, 51, 50, 53, 50, 51, 26, 53, 51, 56, 7, 46, 47, 55, 46, 47, 46, 9, 47, 10, 48, 32, 56, 46, 32, 46, 55, 48, 55, 47, 54, 55, 48, 54, 30, 55, 58, 43, 1, 58, 1, 44, 39, 43, 58, 44, 45, 57, 45, 44, 5, 58, 44, 57, 37, 58, 57, 56, 45, 7, 35, 57, 45, 56, 34, 45], "vertices": [1, 0, 14.43, -317.39, 1, 1, 0, 50.42, -307.26, 1, 1, 0, 76.95, -296.53, 1, 1, 0, 94.95, -297.37, 1, 1, 0, 114.43, -310, 1, 1, 0, 136.25, -319.52, 1, 1, 0, 160.1, -336.86, 1, 1, 0, 183.94, -353.94, 1, 1, 0, 212.4, -373.18, 1, 1, 0, 252.96, -385.55, 1, 1, 0, 283.88, -396.34, 1, 1, 0, 313.22, -409.51, 1, 1, 0, 350.46, -421.8, 1, 1, 0, 367.39, -459.62, 1, 1, 0, 378.24, -492.46, 1, 1, 0, 373.8, -519.54, 1, 1, 0, 378.67, -549.97, 1, 1, 0, 379.34, -567.67, 1, 1, 0, 398.31, -590.98, 1, 1, 0, 403.57, -632.67, 1, 1, 0, 393.04, -665.23, 1, 1, 0, 393.04, -684.44, 1, 1, 0, 373.27, -684.44, 1, 1, 0, 351.31, -684.44, 1, 1, 0, 317.98, -663.33, 1, 1, 0, 297.47, -641.92, 1, 1, 0, 281.21, -616.18, 1, 1, 0, 264.95, -588, 1, 1, 0, 253.3, -561.71, 1, 1, 0, 241.11, -535.96, 1, 1, 0, 223.68, -510.98, 1, 1, 0, 208.78, -487.67, 1, 1, 0, 189, -466.26, 1, 1, 0, 156.47, -441.39, 1, 1, 0, 133.44, -425.94, 1, 1, 0, 108.23, -419.43, 1, 1, 0, 76.53, -411.03, 1, 1, 0, 47.8, -401.82, 1, 1, 0, 23.14, -391.79, 1, 1, 0, 1.73, -379.33, 1, 1, 0, -28.54, -362.62, 1, 1, 0, -37.33, -342.29, 1, 1, 0, -9.38, -323.85, 1, 1, 101, -90.48, 19.9, 1, 1, 101, -24.96, 18.34, 1, 2, 101, 26.52, -14.42, 0.8162, 102, -83.64, 58.48, 0.1838, 2, 101, 97.24, -44.06, 0.13391, 102, -12.92, 28.84, 0.86609, 2, 102, 40.12, 4.92, 0.82661, 103, -38.45, 112.65, 0.17339, 2, 102, 73.92, -22.12, 0.50965, 103, -4.65, 85.61, 0.49035, 3, 102, 95.76, -72.56, 0.14193, 103, 17.19, 35.17, 0.85438, 104, -63.81, 136.42, 0.00369, 3, 102, 104.08, -121.44, 0.00094, 103, 25.51, -13.71, 0.86464, 104, -55.49, 87.54, 0.13442, 2, 103, 35.39, -51.15, 0.55012, 104, -45.61, 50.1, 0.44988, 2, 103, 51.51, -104.19, 0.10954, 104, -29.49, -2.94, 0.89046, 3, 102, 67.16, -140.16, 0.00225, 103, -11.41, -32.43, 0.86903, 104, -92.41, 68.82, 0.12872, 2, 102, 47.4, -80.88, 0.25415, 103, -31.17, 26.85, 0.74585, 2, 102, 7.88, -17.44, 0.89939, 103, -70.69, 90.29, 0.10061, 2, 101, 50.96, -59.66, 0.3953, 102, -59.2, 13.24, 0.6047, 2, 101, -17.68, -29.5, 0.96241, 102, -127.84, 43.4, 0.03759, 1, 101, -74.88, -11.3, 1], "hull": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 0, 84], "width": 415, "height": 383}}, "gebo_you": {"gebo_you": {"type": "mesh", "uvs": [0.07653, 0.00281, 0.16632, 0.13442, 0.3038, 0.27471, 0.42445, 0.37853, 0.54791, 0.5076, 0.69662, 0.61702, 0.89022, 0.70962, 1, 0.7994, 1, 0.95933, 0.85094, 1, 0.66575, 1, 0.45812, 0.96495, 0.31222, 0.82465, 0.17474, 0.67033, 0.02603, 0.53846, 0, 0.38975, 0, 0.21298, 0, 0, 0.13851, 0.29188, 0.30511, 0.4711, 0.47928, 0.65285, 0.51715, 0.67305, 0.7519, 0.78916, 0.92103, 0.87751], "triangles": [22, 5, 6, 23, 6, 7, 22, 6, 23, 23, 7, 8, 11, 21, 22, 10, 11, 22, 9, 10, 22, 23, 9, 22, 9, 23, 8, 16, 17, 0, 16, 0, 1, 18, 16, 1, 18, 1, 2, 15, 16, 18, 19, 2, 3, 18, 2, 19, 14, 15, 18, 14, 18, 19, 4, 19, 3, 21, 20, 4, 20, 19, 4, 13, 14, 19, 13, 19, 20, 5, 21, 4, 21, 5, 22, 12, 13, 20, 21, 11, 12, 21, 12, 20], "vertices": [1, 61, -7.07, 22.51, 1, 1, 61, 25.59, 19.21, 1, 1, 61, 65.94, 22.27, 1, 1, 61, 98.3, 27.52, 1, 1, 61, 135.02, 29.86, 1, 2, 61, 171.99, 38.78, 0.6027, 62, 15.63, 36, 0.3973, 2, 61, 212.26, 57.02, 0.03746, 62, 59.47, 41.7, 0.96254, 2, 61, 240.98, 62.41, 0.00056, 62, 88.51, 38.49, 0.99944, 1, 62, 106.44, 10.85, 1, 1, 62, 85.24, -12.89, 1, 1, 62, 53.23, -33.65, 1, 2, 61, 195.2, -44.95, 0.01582, 62, 13.42, -50.87, 0.98418, 2, 61, 153.73, -49.34, 0.49817, 62, -27.52, -42.98, 0.50183, 2, 61, 111.18, -50.55, 0.94511, 62, -68.58, -31.72, 0.05489, 2, 61, 70.67, -56.5, 0.99983, 62, -109.07, -25.6, 0.00017, 1, 61, 43.77, -40.87, 1, 1, 61, 15.91, -17.43, 1, 1, 61, -17.66, 10.82, 1, 1, 61, 46.72, -6.06, 1, 2, 61, 97.06, -3.57, 0.99985, 62, -68.39, 17.33, 0.00015, 2, 61, 148.81, -0.23, 0.99941, 62, -17.91, 5.44, 0.00059, 1, 61, 157.02, 3.06, 1, 2, 61, 206.46, 24.67, 0.03772, 62, 44.49, 12.45, 0.96228, 2, 61, 242.81, 39.61, 7e-05, 62, 83.62, 16.14, 0.99993], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 36, 38, 38, 40, 42, 44, 44, 46], "width": 206, "height": 206}}, "tui_hou": {"tui_hou": {"type": "mesh", "uvs": [0.21021, 0, 0.38192, 0.05165, 0.51148, 0.14584, 0.63479, 0.24162, 0.75655, 0.35656, 0.8705, 0.45394, 0.96572, 0.54813, 0.97352, 0.71415, 0.98445, 0.83388, 1, 0.94881, 1, 1, 0.90952, 1, 0.75499, 1, 0.71909, 0.8658, 0.71596, 0.73969, 0.67382, 0.6934, 0.54113, 0.62954, 0.41782, 0.57207, 0.28201, 0.48906, 0.14621, 0.39966, 0.0666, 0.31665, 0.0104, 0.23683, 0, 0.12509, 0, 0.0325, 0.08221, 0, 0.05879, 0.0756, 0.18835, 0.2097, 0.39752, 0.31984, 0.60826, 0.41882, 0.7425, 0.51779, 0.8705, 0.61996, 0.86426, 0.76364, 0.89079, 0.89454], "triangles": [11, 9, 10, 12, 32, 11, 11, 32, 9, 12, 13, 32, 32, 8, 9, 13, 31, 32, 32, 31, 8, 13, 14, 31, 31, 7, 8, 31, 30, 7, 31, 14, 30, 14, 15, 30, 30, 6, 7, 30, 5, 6, 15, 29, 30, 15, 16, 29, 16, 28, 29, 16, 17, 28, 30, 29, 5, 18, 27, 17, 17, 27, 28, 29, 4, 5, 29, 28, 4, 18, 19, 27, 28, 3, 4, 28, 27, 3, 19, 26, 27, 19, 20, 26, 26, 1, 27, 27, 2, 3, 27, 1, 2, 20, 21, 26, 21, 25, 26, 21, 22, 25, 26, 25, 0, 26, 0, 1, 0, 25, 24, 22, 23, 25, 25, 23, 24], "vertices": [1, 67, -3.71, 84.37, 1, 1, 67, 74.22, 105.4, 1, 1, 67, 145.74, 100.5, 1, 1, 67, 215.23, 93.54, 1, 1, 67, 288.54, 79, 1, 2, 67, 354.79, 69.23, 0.95387, 68, -65.62, 29.85, 0.04613, 2, 67, 413.11, 56.24, 0.41402, 68, -17.77, 65.63, 0.58598, 2, 67, 454.36, -4.35, 0.00021, 68, 55.07, 57.45, 0.99979, 1, 68, 107.98, 53.9, 1, 1, 68, 159.14, 52.75, 1, 1, 68, 181.42, 49.16, 1, 1, 68, 174.92, 8.88, 1, 1, 68, 163.83, -59.93, 1, 2, 67, 391.44, -121.33, 0.01046, 68, 102.82, -66.49, 0.98954, 2, 67, 361.19, -74.64, 0.29372, 68, 47.69, -59.03, 0.70628, 2, 67, 334.32, -67.16, 0.69556, 68, 24.51, -74.54, 0.30444, 2, 67, 268.58, -74.4, 0.99219, 68, -12.82, -129.13, 0.00781, 1, 67, 207.92, -81.85, 1, 1, 67, 136.57, -82.62, 1, 1, 67, 63.74, -81, 1, 1, 67, 14.01, -68.54, 1, 1, 67, -25.99, -51.77, 1, 1, 67, -55.74, -12.19, 1, 1, 67, -77.07, 22.62, 1, 1, 67, -52.94, 54.21, 1, 1, 67, -44.53, 20.27, 1, 1, 67, 36.19, 0.36, 1, 1, 67, 142.01, 8.22, 1, 1, 67, 245.85, 20.65, 1, 1, 67, 320.28, 15.06, 1, 2, 67, 393.04, 6.79, 0.11116, 68, 6.66, 18.19, 0.88884, 1, 68, 68.77, 5.32, 1, 1, 68, 127.66, 7.94, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 451, "height": 441}}, "piaodai_zuo": {"piaodai_zuo": {"type": "mesh", "uvs": [0.29595, 0.00581, 0.36112, 0.07951, 0.33062, 0.1532, 0.32368, 0.20584, 0.37222, 0.26135, 0.4776, 0.34174, 0.56635, 0.41735, 0.65648, 0.51401, 0.66896, 0.60876, 0.65926, 0.66905, 0.77574, 0.71499, 0.86726, 0.77433, 0.94352, 0.84324, 0.89499, 0.91023, 0.82566, 0.95617, 0.7896, 1, 0.71056, 1, 0.64816, 1, 0.63568, 0.91406, 0.61072, 0.83079, 0.57051, 0.76284, 0.51643, 0.69107, 0.44987, 0.6279, 0.35419, 0.58579, 0.24283, 0.54476, 0.13597, 0.50022, 0.05415, 0.42461, 0.00423, 0.33847, 0, 0.23703, 0.00562, 0.14611, 0.02781, 0.06858, 0.06663, 0, 0.17341, 0, 0.19981, 0.04555, 0.14834, 0.11748, 0.1432, 0.22405, 0.22168, 0.32173, 0.29116, 0.41409, 0.39281, 0.46826, 0.51118, 0.53752, 0.5292, 0.5464, 0.56008, 0.59702, 0.63985, 0.73112, 0.7029, 0.79239, 0.76594, 0.86432, 0.77109, 0.9256], "triangles": [14, 15, 45, 15, 16, 45, 45, 16, 18, 16, 17, 18, 14, 45, 13, 18, 44, 45, 45, 44, 13, 44, 19, 43, 44, 18, 19, 13, 44, 12, 44, 11, 12, 44, 43, 11, 43, 20, 42, 43, 19, 20, 43, 10, 11, 43, 42, 10, 20, 21, 42, 42, 9, 10, 42, 21, 9, 21, 41, 9, 21, 22, 41, 9, 41, 8, 22, 40, 41, 22, 39, 40, 22, 23, 39, 41, 7, 8, 41, 40, 7, 40, 39, 7, 39, 6, 7, 23, 38, 39, 23, 24, 38, 24, 37, 38, 24, 25, 37, 39, 38, 6, 25, 26, 37, 38, 5, 6, 38, 37, 5, 37, 4, 5, 26, 36, 37, 26, 27, 36, 37, 36, 4, 27, 35, 36, 27, 28, 35, 36, 3, 4, 36, 35, 3, 28, 29, 35, 3, 35, 2, 35, 29, 34, 2, 35, 34, 29, 30, 34, 34, 33, 2, 2, 33, 1, 1, 33, 0, 34, 30, 33, 33, 31, 32, 31, 33, 30, 33, 32, 0], "vertices": [1, 56, 40.26, -18.82, 1, 1, 57, -19.68, 92.87, 1, 3, 56, 96.56, 60.6, 0.60472, 57, 24.89, 70.29, 0.39408, 58, -52.84, 145.19, 0.0012, 3, 56, 122.86, 82.87, 0.17639, 57, 58, 60.72, 0.78791, 58, -35.12, 115.63, 0.0357, 3, 56, 132.69, 123.98, 0.01679, 57, 97.67, 75.31, 0.71342, 58, 3.86, 99.28, 0.26979, 2, 57, 158.13, 111.94, 0.14832, 58, 73.07, 84.91, 0.85168, 3, 57, 214.1, 141.81, 0.00547, 58, 134.39, 68.65, 0.83798, 59, -42.24, 55.26, 0.15655, 2, 58, 204.31, 41.67, 0.04773, 59, 32.14, 64.46, 0.95227, 1, 59, 90.23, 42.72, 1, 2, 59, 123.76, 21.73, 0.89756, 60, -19.29, 18.9, 0.10244, 2, 59, 173.49, 55.97, 0.00762, 60, 24.36, 60.61, 0.99238, 1, 60, 73.18, 89.06, 1, 1, 60, 126.03, 109.16, 1, 1, 60, 161.67, 75.78, 1, 1, 60, 181.49, 37.33, 1, 1, 60, 204.26, 13.64, 1, 1, 60, 194.12, -20.45, 1, 1, 60, 186.12, -47.37, 1, 1, 60, 130.81, -36.79, 1, 1, 60, 75.57, -32.08, 1, 2, 59, 161.53, -40.78, 0.13238, 60, 27.95, -36.8, 0.86762, 3, 58, 221.49, -88.73, 3e-05, 59, 108.81, -42.4, 0.93389, 60, -23.85, -46.79, 0.06608, 2, 58, 173.01, -73.13, 0.09902, 59, 58.71, -51.52, 0.90098, 2, 58, 122.05, -76.34, 0.62853, 59, 15.29, -78.41, 0.37147, 3, 57, 268.08, -16.88, 0.00323, 58, 65.8, -84.29, 0.95286, 59, -30.55, -111.95, 0.04391, 3, 57, 230.45, -58.59, 0.15648, 58, 9.84, -89.19, 0.84328, 59, -77.58, -142.68, 0.00024, 2, 57, 175.07, -85.4, 0.66393, 58, -48.97, -71.09, 0.33607, 2, 57, 115.67, -96.82, 0.97215, 58, -100.23, -38.98, 0.02785, 1, 57, 50.37, -86.15, 1, 2, 56, 197.53, -45.29, 0.03798, 57, -7.36, -72.44, 0.96202, 2, 56, 154.96, -74.31, 0.37332, 57, -55.1, -53.06, 0.62668, 2, 56, 111.14, -93.92, 0.68498, 57, -95.69, -27.43, 0.31502, 2, 56, 76.9, -60.21, 0.90417, 57, -86.59, 19.75, 0.09583, 2, 56, 89.27, -30.71, 0.9525, 57, -55.18, 25.79, 0.0475, 2, 56, 138.67, -13.54, 0.21387, 57, -13.52, -5.84, 0.78613, 1, 57, 54.27, -21.28, 1, 2, 57, 123.5, 1.33, 0.99757, 58, -27.64, 27.54, 0.00243, 1, 58, 33.12, -2.66, 1, 2, 58, 90.89, -4.21, 0.99751, 59, -46.21, -29.5, 0.00249, 2, 58, 160.55, -9.27, 0.00755, 59, 17.59, -1.1, 0.99245, 1, 59, 26.32, 3.69, 1, 1, 59, 62.08, 1.89, 1, 2, 59, 156.43, -3.69, 0.00237, 60, 17.01, -1, 0.99763, 1, 60, 63.39, 14.81, 1, 1, 60, 116.43, 28.64, 1, 1, 60, 155.38, 19.48, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 84, 86, 86, 88, 88, 90], "width": 450, "height": 652}}, "yanjing": {"yanjing": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 69, -23.57, -31.26, 1, 1, 69, -24.6, 55.73, 1, 1, 69, 29.4, 56.37, 1, 1, 69, 30.43, -30.62, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 54}}, "a30204": {"a30204": {"type": "mesh", "uvs": [0, 0.17443, 0.0649, 0.11568, 0.13971, 0.08425, 0.22613, 0.04873, 0.33319, 0.02414, 0.44153, 0.00911, 0.54472, 0, 0.63758, 0.00091, 0.72271, 0.02824, 0.73561, 0.10338, 0.74206, 0.17853, 0.73948, 0.24274, 0.75237, 0.32608, 0.76914, 0.41762, 0.78204, 0.51189, 0.80526, 0.58021, 0.81945, 0.66491, 0.83234, 0.73596, 0.88007, 0.80837, 0.93166, 0.87805, 1, 0.94773, 0.98454, 0.99828, 0.93682, 1, 0.86072, 0.98735, 0.78978, 0.93817, 0.73174, 0.89445, 0.66209, 0.86849, 0.65177, 0.80701, 0.66854, 0.7182, 0.66209, 0.62939, 0.63113, 0.52146, 0.58986, 0.44358, 0.56148, 0.34794, 0.54729, 0.25777, 0.55632, 0.20448, 0.48409, 0.22498, 0.40928, 0.25777, 0.34995, 0.31515, 0.28159, 0.36434, 0.18228, 0.39986, 0.08167, 0.41762, 0.02879, 0.3821, 0, 0.32062, 0, 0.24957, 0.57066, 0.19271, 0.57752, 0.15918, 0.60026, 0.12124, 0.4923, 0.15105, 0.42115, 0.16733, 0.33914, 0.2112, 0.25278, 0.2984, 0.629, 0.15642, 0.61507, 0.25503, 0.63684, 0.38314, 0.69113, 0.49625, 0.64938, 0.073], "triangles": [20, 21, 22, 19, 20, 22, 19, 22, 23, 23, 24, 19, 24, 18, 19, 24, 25, 18, 18, 25, 17, 17, 25, 27, 27, 25, 26, 17, 27, 28, 28, 16, 17, 16, 29, 15, 14, 15, 54, 54, 15, 29, 28, 29, 16, 29, 30, 54, 30, 31, 54, 54, 13, 14, 31, 53, 54, 54, 53, 13, 31, 32, 53, 53, 12, 13, 32, 52, 53, 12, 52, 11, 12, 53, 52, 32, 33, 52, 33, 34, 52, 34, 44, 52, 52, 44, 51, 52, 51, 11, 51, 44, 45, 11, 51, 10, 44, 34, 45, 51, 9, 10, 51, 55, 9, 40, 41, 39, 39, 41, 50, 39, 50, 38, 1, 50, 43, 1, 2, 50, 42, 43, 50, 38, 50, 37, 50, 41, 42, 50, 49, 37, 37, 49, 36, 2, 3, 50, 50, 3, 49, 36, 48, 35, 36, 49, 48, 43, 0, 1, 35, 47, 34, 35, 48, 47, 3, 4, 49, 49, 4, 48, 45, 34, 47, 48, 5, 47, 48, 4, 5, 45, 46, 51, 45, 47, 46, 51, 46, 55, 47, 6, 46, 47, 5, 6, 55, 6, 7, 55, 46, 6, 55, 8, 9, 55, 7, 8], "vertices": [1, 64, -23.64, 39.9, 1, 1, 64, 29.47, 66.74, 1, 1, 64, 85.03, 74.47, 1, 1, 64, 149.09, 82.91, 1, 1, 64, 225.41, 80.95, 1, 1, 64, 301.12, 72.67, 1, 1, 64, 372.41, 61.45, 1, 1, 64, 435.15, 45.53, 1, 2, 64, 488.28, 13.89, 0.87626, 65, -22.05, 64.29, 0.12374, 2, 64, 481.3, -38.19, 0.22951, 65, 30.41, 61.04, 0.77049, 2, 64, 471.7, -88.77, 0.00153, 65, 81.53, 55.07, 0.99847, 1, 65, 123.19, 46.53, 1, 1, 65, 178.72, 47.04, 1, 1, 65, 239.99, 49.39, 1, 1, 65, 302.63, 48.8, 1, 2, 65, 349.49, 57.92, 0.97234, 66, -56.82, 113.09, 0.02766, 2, 65, 406.05, 59.18, 0.66827, 66, -10.79, 80.2, 0.33173, 2, 65, 453.59, 60.93, 0.07465, 66, 28.31, 53.1, 0.92535, 1, 66, 85.37, 42.32, 1, 1, 66, 143.1, 34.71, 1, 1, 66, 209.17, 35.26, 1, 1, 66, 224.73, 3.97, 1, 1, 66, 201.76, -20.09, 1, 1, 66, 158.06, -51.22, 1, 1, 66, 100.13, -62.67, 1, 2, 65, 545.89, -24.24, 0.03421, 66, 51.13, -70.4, 0.96579, 2, 65, 521.61, -69.58, 0.24102, 66, 4.51, -92.14, 0.75898, 2, 65, 480.55, -70.51, 0.51805, 66, -28.91, -68.27, 0.48195, 2, 65, 424.63, -50.05, 0.99711, 66, -61.4, -18.37, 0.00289, 1, 65, 366.23, -45.57, 1, 1, 65, 292.79, -56.04, 1, 1, 65, 238.31, -74.18, 1, 2, 64, 331.24, -171.06, 0.00861, 65, 170.88, -79.31, 0.99139, 2, 64, 330.86, -111.41, 0.2, 65, 112.78, -83.45, 0.8, 2, 64, 342.26, -76.11, 0.44748, 65, 78.13, -74.94, 0.55252, 2, 64, 294.82, -73.71, 0.8, 65, 79.12, -122.44, 0.2, 2, 64, 240.35, -81.14, 0.99716, 65, 90.41, -176.24, 0.00284, 1, 64, 191.39, -107.86, 1, 1, 64, 137.44, -128, 1, 1, 64, 64.65, -134.31, 1, 1, 64, -6.25, -129.05, 1, 1, 64, -36.52, -97.61, 1, 1, 64, -46.43, -53.55, 1, 1, 64, -35.35, -8.13, 1, 2, 64, 342.75, -54.59, 0.44529, 65, 80.31, -50.32, 0.55471, 2, 64, 355.62, -36.29, 0.54211, 65, 58.27, -46.51, 0.45789, 2, 64, 379.86, -19.43, 0.62304, 65, 32.66, -31.81, 0.37696, 2, 64, 307.88, -19.93, 0.80021, 65, 63.89, -96.66, 0.19979, 2, 64, 260.64, -22.81, 0.92871, 65, 86.71, -138.13, 0.07129, 2, 64, 199.73, -39, 0.98089, 65, 127.39, -186.26, 0.01911, 2, 64, 128.19, -81.07, 0.99935, 65, 196.02, -232.94, 0.00065, 2, 64, 384.06, -50.87, 0.40675, 65, 59.28, -14.57, 0.59325, 2, 64, 341.93, -99.27, 0.12452, 65, 121.04, -31.95, 0.87548, 2, 64, 303.64, -172.24, 0.00196, 65, 203.38, -35.35, 0.99804, 1, 65, 282.8, -12.21, 1, 2, 64, 426.08, -0.68, 0.76025, 65, -4.06, 1.95, 0.23975], "hull": 44, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 0, 86, 68, 88, 88, 90, 90, 92, 94, 96, 96, 98, 98, 100, 102, 104, 104, 106, 106, 108], "width": 697, "height": 658}}, "toufa_shang": {"toufa_shang": {"type": "mesh", "uvs": [0.43186, 0.59907, 0.44308, 0.72304, 0.44308, 0.83534, 0.42438, 0.88493, 0.34395, 0.91119, 0.38884, 0.97973, 0.46178, 1, 0.50778, 0.99712, 0.54293, 0.9585, 0.55343, 0.90973, 0.55904, 0.74492, 0.54782, 0.61657, 0.66939, 0.53781, 0.74421, 0.47072, 0.88636, 0.44593, 1, 0.49114, 1, 0.43426, 1, 0.33363, 0.96304, 0.26945, 0.84895, 0.1834, 0.68997, 0.1484, 0.53473, 0.15132, 0.50293, 0.05797, 0.39819, 0, 0.20741, 0, 0.12512, 0.06089, 0, 0.03026, 0, 0.15569, 0.0316, 0.29716, 0.01851, 0.40217, 0, 0.55386, 0.01664, 0.6822, 0.19993, 0.6895, 0.34395, 0.64866, 0.49358, 0.20382, 0.45243, 0.26362, 0.50293, 0.25487, 0.4543, 0.32925, 0.52725, 0.34092, 0.46365, 0.4328, 0.43186, 0.51448, 0.39819, 0.52469, 0.60206, 0.26362, 0.71428, 0.22716, 0.85082, 0.24758, 0.54782, 0.4926, 0.6563, 0.3978, 0.80219, 0.3205, 0.94434, 0.34384, 0.40754, 0.57719], "triangles": [8, 7, 6, 3, 8, 6, 8, 3, 9, 41, 33, 32, 32, 31, 30, 33, 49, 0, 33, 41, 49, 41, 32, 30, 30, 29, 41, 41, 29, 35, 35, 29, 28, 35, 28, 25, 35, 25, 24, 37, 41, 35, 24, 34, 35, 37, 35, 36, 28, 27, 25, 35, 34, 36, 34, 24, 23, 34, 22, 21, 34, 23, 22, 27, 26, 25, 3, 6, 5, 5, 4, 3, 3, 2, 9, 9, 2, 10, 2, 1, 10, 1, 11, 10, 1, 0, 11, 0, 40, 11, 0, 49, 40, 40, 45, 11, 11, 45, 12, 49, 41, 40, 41, 39, 40, 40, 39, 45, 39, 41, 37, 45, 39, 46, 39, 37, 38, 13, 12, 46, 12, 45, 46, 14, 16, 15, 14, 13, 47, 13, 46, 47, 14, 48, 16, 14, 47, 48, 48, 17, 16, 39, 38, 46, 38, 42, 46, 47, 42, 43, 47, 46, 42, 47, 44, 48, 48, 18, 17, 48, 44, 18, 37, 36, 38, 38, 36, 42, 47, 43, 44, 42, 34, 21, 42, 20, 43, 42, 21, 20, 42, 36, 34, 43, 19, 44, 18, 44, 19, 43, 20, 19], "vertices": [3, 34, 46.51, 14.58, 0.08931, 35, 17.7, -11.25, 0.74184, 24, -17.85, -27.09, 0.16885, 2, 35, 52.48, -12.91, 0.57916, 36, -1.75, -13.95, 0.42084, 1, 36, 29.26, -4.97, 1, 1, 36, 43.57, -4.81, 1, 1, 36, 55.6, -19.3, 1, 2, 36, 70.93, -4.48, 0.69745, 84, 37.27, -19.07, 0.30255, 2, 36, 71.69, 12.23, 0.07206, 84, 38.52, -2.39, 0.92794, 1, 84, 35.12, 6.98, 1, 1, 84, 22.8, 11.48, 1, 2, 83, 44.01, 6.44, 0.03732, 84, 9.17, 10.1, 0.96268, 2, 35, 60.09, 11.74, 0.32094, 83, -1.43, 11.04, 0.67906, 3, 33, 99.8, 26.31, 0.00084, 34, 26.15, 29.96, 0.0462, 35, 25.33, 13.1, 0.95296, 3, 33, 65.86, 22.65, 0.63949, 34, -7.03, 21.93, 0.24998, 35, 6.61, 41.65, 0.11052, 2, 33, 42.12, 16.1, 0.99979, 35, -10.04, 59.79, 0.00021, 1, 33, 12.74, 27.36, 1, 1, 33, -0.79, 51.37, 1, 1, 33, -9.5, 38.24, 1, 1, 33, -24.92, 15.01, 1, 2, 33, -28.09, -4.22, 0.99899, 24, 39.11, -162.21, 0.00101, 3, 33, -20.74, -37.7, 0.96239, 34, -85.07, -49.15, 0.00131, 24, 68.71, -144.91, 0.03629, 3, 33, 2.52, -64.77, 0.80197, 34, -58.5, -72.97, 0.03051, 24, 87.31, -114.46, 0.16752, 3, 33, 30.9, -82.64, 0.41933, 34, -28.03, -87.01, 0.06959, 24, 95.58, -81.95, 0.51107, 3, 33, 22.33, -107.98, 0.20904, 34, -33.25, -113.24, 0.03517, 24, 122.33, -82.32, 0.75579, 3, 33, 32.3, -133.87, 0.12825, 34, -20, -137.62, 0.01642, 24, 143.9, -64.86, 0.85533, 3, 33, 66.64, -156.65, 0.04285, 34, 17, -155.76, 0.001, 24, 155.02, -25.18, 0.95615, 2, 33, 90.78, -152.42, 0.01127, 24, 143.57, -3.52, 0.98873, 1, 24, 159.03, 20.22, 1, 1, 24, 125.58, 29.59, 1, 1, 24, 86, 33.59, 1, 1, 24, 58.76, 44.17, 1, 1, 24, 19.38, 59.35, 1, 1, 24, -15.83, 65.48, 1, 2, 35, 36.95, -63.84, 0.00022, 24, -28.45, 27.91, 0.99978, 3, 34, 69.6, 18.56, 0.0371, 35, 29.21, -31.66, 0.11607, 24, -25.96, -5.1, 0.84683, 3, 33, 46.35, -75.43, 0.38857, 34, -13.65, -77.86, 0.10022, 24, 83.97, -69.47, 0.51121, 3, 33, 62.91, -66.54, 0.31057, 34, 1.62, -66.9, 0.17115, 24, 70.42, -56.44, 0.51828, 3, 33, 52.49, -62.53, 0.4269, 34, -9.24, -64.27, 0.15245, 24, 69.81, -67.6, 0.42065, 3, 33, 72.63, -51.17, 0.27299, 34, 9.26, -50.39, 0.30773, 24, 52.81, -51.93, 0.41928, 3, 33, 61.29, -39.77, 0.46457, 34, -3.47, -40.56, 0.29164, 24, 45.44, -66.22, 0.24379, 3, 33, 86.81, -26.15, 0.10131, 34, 20.07, -23.75, 0.69074, 24, 24.64, -46.13, 0.20795, 3, 33, 105.04, -11.1, 1e-05, 34, 36.2, -6.46, 0.85143, 24, 4.71, -33.41, 0.14856, 3, 34, 43.97, -7.12, 0.61937, 35, -3.59, -16.15, 0.06004, 24, 3.95, -25.65, 0.32059, 3, 33, 35.98, -48.67, 0.67831, 34, -27.4, -52.67, 0.09631, 24, 61.7, -87.56, 0.22538, 3, 33, 10.2, -43.69, 0.86775, 34, -53.61, -51.07, 0.02259, 24, 64.89, -113.63, 0.10966, 3, 33, -11.25, -22.67, 0.98078, 34, -77.6, -33.01, 0.0003, 24, 51.48, -140.5, 0.01892, 3, 33, 80.82, -2.3, 0.00029, 34, 11.04, -0.87, 0.9983, 24, 3.79, -59.17, 0.00141, 3, 33, 46.77, -11.23, 0.93979, 34, -21.56, -14.14, 0.03605, 24, 22.75, -88.82, 0.02416, 3, 33, 8.68, -11.64, 0.98836, 34, -59.28, -19.5, 0.00076, 24, 34.87, -124.94, 0.01088, 1, 33, -13.33, 10.72, 1, 3, 34, 48.56, 6.83, 0.23256, 35, 11.09, -15.78, 0.44068, 24, -10.6, -23.67, 0.32676], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 42, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 84, 86, 86, 88, 90, 92, 92, 94, 94, 96, 82, 98, 18, 16, 12, 14, 16, 14], "width": 216, "height": 277}}, "lian": {"lian": {"type": "mesh", "uvs": [0.80897, 0, 0.92347, 0.09103, 1, 0.2429, 1, 0.42982, 0.99905, 0.56054, 0.99862, 0.62063, 0.95335, 0.64392, 0.90021, 0.67126, 0.83044, 0.82702, 0.71951, 0.92243, 0.61573, 1, 0.50123, 1, 0.3724, 0.87375, 0.27221, 0.75109, 0.23297, 0.68005, 0.20207, 0.58593, 0.1831, 0.58593, 0.20396, 0.60295, 0.21488, 0.63883, 0.21521, 0.7064, 0.21074, 0.78206, 0.19288, 0.8657, 0.1629, 0.89763, 0.11954, 0.82579, 0.08924, 0.76783, 0.09403, 0.68142, 0.1138, 0.60472, 0.13708, 0.57938, 0.08109, 0.5143, 0.03175, 0.42321, 0.0344, 0.33212, 0.09079, 0.26692, 0.18242, 0.28706, 0.25973, 0.17103, 0.36898, 0.07131, 0.47736, 0.02145, 0.58837, 0, 0.7082, 0, 0.21337, 0.48423, 0.20365, 0.3769, 0.13208, 0.38901, 0.15442, 0.57201, 0.1482, 0.5449, 0.16921, 0.54024, 0.17466, 0.57243, 0.97657, 0.52682, 0.96748, 0.56231, 0.97929, 0.58862, 0.97647, 0.61676, 0.94331, 0.52688, 0.92082, 0.50975, 0.83256, 0.49322, 0.79883, 0.51219, 0.77016, 0.54583, 0.76679, 0.5807, 0.80614, 0.6229, 0.95171, 0.54679, 0.96499, 0.54714, 0.92787, 0.63348, 0.9071, 0.62755, 0.89109, 0.64571, 0.8755, 0.69351, 0.84692, 0.64463, 0.87484, 0.64849, 0.88282, 0.63691, 0.78166, 0.57117, 0.81537, 0.54973, 0.84865, 0.53973, 0.87711, 0.55497, 0.9025, 0.57879, 0.89287, 0.6007, 0.80834, 0.5919, 0.84055, 0.6109, 0.87822, 0.61209, 0.817, 0.57339, 0.86154, 0.58044, 0.48222, 0.38307, 0.5189, 0.37557, 0.56686, 0.38307, 0.60543, 0.40764, 0.63364, 0.43971, 0.63396, 0.45814, 0.61358, 0.46394, 0.57752, 0.45848, 0.53206, 0.44551, 0.4935, 0.42197, 0.52266, 0.40491, 0.56906, 0.42538, 0.60793, 0.44108, 0.42604, 0.32534, 0.5032, 0.31249, 0.57488, 0.31846, 0.6436, 0.36434, 0.67775, 0.42721, 0.68239, 0.48732, 0.64486, 0.51484, 0.53988, 0.49465, 0.44291, 0.43041, 0.41803, 0.37306, 0.47693, 0.27139, 0.5423, 0.29347, 0.59564, 0.31719, 0.64373, 0.34908, 0.68806, 0.38424, 0.72788, 0.42676, 0.75192, 0.42513, 0.51299, 0.24523, 0.58587, 0.24523, 0.66101, 0.28625, 0.71811, 0.33767, 0.76094, 0.38262, 0.84038, 0.44231, 0.88471, 0.43903, 0.93505, 0.45047, 0.97111, 0.47253, 0.83033, 0.4869, 0.86233, 0.48666, 0.89033, 0.49361, 0.91838, 0.49855, 0.94216, 0.51172, 0.96681, 0.52287, 0.82187, 0.4677, 0.76126, 0.40919, 0.87289, 0.26359, 0.81142, 0.39071, 0.74339, 0.55311, 0.74997, 0.67251, 0.70388, 0.71908, 0.67278, 0.77507, 0.61859, 0.85825, 0.58279, 0.94669, 0.74346, 0.18341, 0.69183, 0.27008, 0.60577, 0.55327, 0.57673, 0.64691, 0.50681, 0.74875, 0.43259, 0.8342, 0.65095, 0.01947, 0.6047, 0.15058, 0.5735, 0.2173, 0.47131, 0.47835, 0.42506, 0.55209, 0.37558, 0.65393, 0.32502, 0.74523, 0.50789, 0.05575, 0.4057, 0.21964, 0.34869, 0.36596, 0.29383, 0.4959, 0.26801, 0.63285, 0.96165, 0.21952, 0.94598, 0.34284, 0.92308, 0.41893, 0.82423, 0.67733, 0.80133, 0.75997, 0.73986, 0.8308, 0.64101, 0.92, 0.77161, 0.56571, 0.7902, 0.53654, 0.82264, 0.51019, 0.86032, 0.50125, 0.88843, 0.50148], "triangles": [22, 23, 21, 21, 23, 20, 20, 23, 19, 17, 25, 16, 19, 23, 24, 18, 24, 25, 26, 16, 25, 17, 18, 25, 19, 24, 18, 11, 130, 10, 10, 155, 9, 10, 130, 155, 12, 136, 11, 11, 136, 130, 130, 136, 129, 130, 129, 155, 129, 136, 135, 9, 154, 8, 9, 155, 154, 155, 129, 154, 13, 143, 12, 12, 143, 136, 129, 128, 154, 129, 135, 128, 135, 136, 142, 128, 127, 154, 154, 153, 8, 154, 127, 153, 8, 61, 7, 8, 153, 61, 27, 16, 26, 16, 41, 44, 16, 27, 41, 135, 134, 128, 128, 134, 127, 153, 152, 61, 61, 62, 63, 61, 152, 62, 127, 126, 153, 153, 126, 152, 143, 13, 148, 136, 143, 142, 142, 141, 135, 135, 141, 134, 96, 141, 140, 141, 96, 134, 13, 14, 148, 143, 148, 142, 134, 133, 127, 127, 133, 126, 133, 125, 126, 125, 133, 95, 125, 95, 94, 60, 61, 63, 60, 7, 61, 15, 148, 14, 126, 55, 152, 152, 55, 62, 126, 54, 55, 126, 125, 54, 7, 58, 6, 58, 60, 59, 58, 7, 60, 141, 142, 147, 63, 64, 60, 63, 62, 64, 134, 96, 133, 60, 64, 59, 55, 72, 62, 62, 73, 64, 62, 72, 73, 6, 48, 5, 47, 48, 58, 48, 6, 58, 64, 73, 59, 46, 56, 57, 56, 46, 69, 69, 46, 58, 142, 148, 147, 147, 148, 15, 73, 70, 59, 70, 69, 59, 58, 59, 69, 55, 54, 71, 55, 71, 72, 71, 54, 65, 48, 47, 5, 5, 47, 4, 47, 58, 46, 72, 75, 73, 73, 75, 70, 71, 74, 72, 72, 74, 75, 70, 75, 69, 71, 65, 74, 47, 46, 4, 141, 147, 97, 97, 147, 146, 15, 38, 147, 38, 15, 43, 44, 43, 15, 54, 156, 65, 156, 54, 53, 74, 67, 75, 75, 68, 69, 75, 67, 68, 27, 42, 41, 27, 28, 42, 69, 49, 56, 69, 50, 49, 69, 68, 50, 65, 66, 74, 74, 66, 67, 41, 43, 44, 15, 16, 44, 41, 42, 43, 65, 157, 66, 65, 156, 157, 54, 125, 53, 156, 53, 157, 4, 46, 45, 46, 57, 45, 4, 45, 3, 67, 159, 68, 68, 160, 50, 68, 159, 160, 133, 96, 95, 94, 104, 125, 105, 125, 104, 125, 52, 53, 141, 97, 140, 98, 97, 146, 66, 158, 67, 66, 157, 158, 57, 56, 45, 56, 120, 45, 56, 49, 120, 157, 53, 52, 38, 43, 42, 40, 38, 42, 67, 158, 159, 157, 52, 158, 52, 124, 121, 50, 119, 49, 49, 119, 120, 120, 114, 45, 45, 114, 3, 120, 119, 114, 95, 96, 82, 96, 83, 82, 82, 81, 95, 95, 81, 94, 40, 42, 28, 28, 29, 40, 51, 158, 52, 52, 125, 124, 113, 114, 119, 158, 51, 159, 51, 52, 115, 115, 52, 121, 50, 160, 118, 160, 117, 118, 119, 50, 118, 117, 160, 116, 160, 159, 116, 159, 51, 116, 119, 118, 113, 118, 117, 113, 146, 147, 39, 96, 85, 84, 96, 84, 83, 116, 112, 117, 117, 112, 113, 51, 115, 116, 81, 93, 94, 94, 93, 104, 116, 115, 111, 115, 121, 111, 116, 111, 112, 40, 39, 38, 147, 38, 39, 85, 96, 97, 96, 140, 97, 114, 113, 3, 124, 125, 105, 124, 105, 122, 121, 124, 111, 83, 88, 82, 82, 88, 81, 84, 87, 83, 83, 87, 88, 88, 80, 81, 81, 80, 93, 112, 151, 113, 113, 151, 3, 85, 86, 84, 84, 86, 87, 111, 124, 112, 87, 79, 88, 88, 79, 80, 93, 80, 92, 112, 124, 151, 97, 76, 85, 97, 98, 76, 151, 150, 3, 150, 2, 3, 80, 79, 92, 93, 103, 104, 93, 92, 103, 122, 105, 104, 86, 78, 87, 87, 78, 79, 110, 122, 104, 29, 30, 40, 85, 76, 86, 151, 124, 150, 110, 104, 103, 122, 110, 124, 79, 78, 92, 76, 77, 86, 86, 77, 78, 124, 123, 150, 124, 110, 123, 30, 31, 40, 40, 32, 39, 40, 31, 32, 92, 102, 103, 103, 109, 110, 103, 102, 109, 78, 91, 92, 78, 77, 91, 98, 89, 76, 76, 90, 77, 76, 89, 90, 110, 109, 123, 39, 32, 146, 77, 90, 91, 98, 146, 89, 32, 33, 146, 146, 145, 89, 146, 33, 145, 91, 101, 92, 92, 101, 102, 102, 108, 109, 102, 101, 108, 150, 149, 2, 150, 123, 149, 108, 132, 109, 109, 131, 123, 109, 132, 131, 89, 99, 90, 89, 145, 99, 90, 100, 91, 91, 100, 101, 100, 107, 101, 101, 107, 108, 90, 99, 100, 99, 106, 100, 100, 106, 107, 108, 107, 132, 99, 145, 106, 131, 132, 138, 149, 123, 1, 106, 139, 107, 132, 107, 138, 107, 139, 138, 139, 106, 144, 33, 34, 145, 106, 145, 144, 144, 34, 35, 144, 145, 34, 123, 131, 1, 2, 149, 1, 139, 144, 138, 138, 137, 131, 137, 37, 131, 131, 0, 1, 131, 37, 0, 144, 36, 138, 138, 36, 137, 144, 35, 36, 137, 36, 37], "vertices": [3, 4, 214.14, -81.62, 0.624, 71, 115.08, -12.14, 0.176, 70, 53.39, 2.74, 0.2, 3, 4, 198.76, -102.98, 0.56, 71, 99.69, -33.51, 0.24, 70, 38, -18.63, 0.2, 2, 4, 173.11, -117.45, 0.8, 70, 12.35, -33.09, 0.2, 2, 4, 141.34, -117.82, 0.8, 70, -19.42, -33.47, 0.2, 2, 4, 119.11, -117.91, 0.8, 70, -41.64, -33.56, 0.2, 2, 4, 108.9, -117.95, 0.8, 70, -51.86, -33.6, 0.2, 3, 4, 104.84, -109.62, 0.7296, 71, 5.77, -40.15, 0.0704, 70, -55.92, -25.27, 0.2, 2, 4, 100.08, -99.85, 0.8, 70, -60.68, -15.5, 0.2, 2, 4, 73.45, -87.25, 0.8, 70, -87.31, -2.9, 0.2, 2, 4, 56.98, -66.93, 0.8, 70, -103.77, 17.43, 0.2, 2, 4, 43.57, -47.89, 0.8, 70, -117.18, 36.47, 0.2, 2, 4, 43.32, -26.7, 0.8, 70, -117.43, 57.65, 0.2, 3, 4, 64.5, -2.62, 0.6144, 71, -34.57, 66.86, 0.1856, 70, -96.26, 81.73, 0.2, 3, 4, 85.13, 16.16, 0.6528, 71, -13.94, 85.64, 0.1472, 70, -75.62, 100.52, 0.2, 2, 4, 97.12, 23.56, 0.8, 70, -63.63, 107.92, 0.2, 2, 4, 113.05, 29.47, 0.8, 70, -47.7, 113.82, 0.2, 1, 4, 113.01, 32.98, 1, 1, 7, 1.39, 7.41, 1, 1, 7, 7.46, 9.52, 1, 1, 7, 18.94, 9.75, 1, 1, 7, 31.82, 9.12, 1, 1, 7, 46.08, 6.02, 1, 1, 7, 51.59, 0.56, 1, 1, 7, 39.5, -7.64, 1, 1, 7, 29.73, -13.39, 1, 1, 7, 15.03, -12.73, 1, 1, 7, 1.94, -9.27, 1, 2, 4, 114.02, 41.51, 0.936, 7, -2.43, -5.02, 0.064, 2, 4, 124.96, 51.99, 0.8, 70, -35.79, 136.35, 0.2, 2, 4, 140.34, 61.3, 0.8, 70, -20.42, 145.66, 0.2, 2, 4, 155.83, 61, 0.8, 70, -4.93, 145.35, 0.2, 2, 4, 167.04, 50.7, 0.8, 70, 6.28, 135.05, 0.2, 2, 4, 163.81, 33.71, 0.8, 70, 3.06, 118.06, 0.2, 2, 4, 183.71, 19.64, 0.8, 70, 22.95, 103.99, 0.2, 2, 4, 200.9, -0.37, 0.8, 70, 40.14, 83.98, 0.2, 2, 4, 209.61, -20.32, 0.8, 70, 48.85, 64.03, 0.2, 2, 4, 213.5, -40.81, 0.8, 70, 52.74, 43.54, 0.2, 2, 4, 213.76, -62.98, 0.8, 70, 53.01, 21.37, 0.2, 1, 4, 130.36, 27.58, 1, 1, 4, 148.59, 29.6, 1, 1, 4, 146.37, 42.81, 1, 2, 4, 115.31, 38.31, 0.968, 7, -3.73, -1.83, 0.032, 1, 4, 119.91, 39.52, 1, 1, 4, 120.75, 35.64, 1, 1, 4, 115.29, 34.57, 1, 3, 4, 124.8, -113.68, 0.7232, 71, 25.73, -44.21, 0.0768, 70, -35.96, -29.33, 0.2, 2, 4, 118.74, -112.07, 0.928, 71, 19.67, -42.6, 0.072, 2, 4, 114.3, -114.31, 0.92, 71, 15.23, -44.83, 0.08, 2, 4, 109.51, -113.85, 0.872, 71, 10.44, -44.37, 0.128, 2, 4, 124.72, -107.53, 0.896, 71, 25.65, -38.05, 0.104, 2, 4, 127.58, -103.34, 0.85, 71, 28.51, -33.86, 0.15, 2, 4, 130.2, -86.98, 0.8, 71, 31.13, -17.5, 0.2, 2, 4, 126.9, -80.77, 0.8, 71, 27.83, -11.3, 0.2, 2, 4, 121.12, -75.54, 0.8, 71, 22.05, -6.06, 0.2, 2, 4, 115.18, -74.98, 0.8, 71, 16.11, -5.51, 0.2, 2, 4, 108.09, -82.35, 0.8, 71, 9.02, -12.87, 0.2, 2, 4, 121.35, -109.12, 0.896, 71, 22.28, -39.65, 0.104, 2, 4, 121.32, -111.58, 0.936, 71, 22.25, -42.11, 0.064, 2, 4, 106.56, -104.89, 0.848, 71, 7.49, -35.41, 0.152, 2, 4, 107.52, -101.03, 0.976, 71, 8.45, -31.56, 0.024, 2, 4, 104.4, -98.11, 0.92, 71, 5.33, -28.63, 0.08, 2, 4, 96.24, -95.32, 0.808, 71, -2.83, -25.85, 0.192, 2, 4, 104.49, -89.94, 0.824, 71, 5.42, -20.46, 0.176, 2, 4, 103.89, -95.11, 0.944, 71, 4.82, -25.63, 0.056, 2, 4, 105.88, -96.56, 0.92, 71, 6.81, -27.09, 0.08, 2, 4, 116.83, -77.72, 0.8, 71, 17.77, -8.24, 0.2, 2, 4, 120.55, -83.91, 0.8, 71, 21.48, -14.43, 0.2, 2, 4, 122.33, -90.05, 0.872, 71, 23.26, -20.57, 0.128, 2, 4, 119.8, -95.34, 0.914, 71, 20.73, -25.86, 0.086, 2, 4, 115.8, -100.09, 0.952, 71, 16.73, -30.61, 0.048, 2, 4, 112.06, -98.35, 0.952, 71, 12.99, -28.87, 0.048, 2, 4, 113.37, -82.69, 0.8, 71, 14.3, -13.22, 0.2, 2, 4, 110.21, -88.69, 0.85, 71, 11.14, -19.22, 0.15, 2, 4, 110.09, -95.66, 0.936, 71, 11.02, -26.19, 0.064, 2, 4, 116.54, -84.26, 0.8, 71, 17.47, -14.78, 0.2, 2, 4, 115.43, -92.51, 0.85, 71, 16.36, -23.04, 0.15, 2, 4, 148.15, -21.95, 0.85, 71, 49.08, 47.53, 0.15, 2, 4, 149.51, -28.72, 0.85, 71, 50.44, 40.76, 0.15, 2, 4, 148.34, -37.6, 0.85, 71, 49.27, 31.87, 0.15, 2, 4, 144.25, -44.79, 0.8, 71, 45.18, 24.69, 0.2, 2, 4, 138.86, -50.07, 0.78, 71, 39.79, 19.41, 0.22, 2, 4, 135.72, -50.17, 0.78, 71, 36.66, 19.31, 0.22, 2, 4, 134.69, -46.41, 0.8, 71, 35.63, 23.07, 0.2, 2, 4, 135.54, -39.73, 0.78, 71, 36.47, 29.75, 0.22, 2, 4, 137.65, -31.29, 0.85, 71, 38.58, 38.18, 0.15, 2, 4, 141.56, -24.11, 0.84, 71, 42.5, 45.36, 0.16, 2, 4, 144.53, -29.47, 0.85, 71, 45.46, 40.01, 0.15, 2, 4, 141.15, -38.09, 0.85, 71, 42.08, 31.38, 0.15, 2, 4, 138.57, -45.32, 0.8, 71, 39.5, 24.16, 0.2, 2, 4, 157.84, -11.44, 0.85, 71, 58.77, 58.04, 0.15, 2, 4, 160.19, -25.68, 0.85, 71, 61.13, 43.79, 0.15, 2, 4, 159.34, -38.96, 0.85, 71, 60.27, 30.52, 0.15, 2, 4, 151.69, -51.76, 0.8, 71, 52.62, 17.71, 0.2, 2, 4, 141.08, -58.21, 0.78, 71, 42.01, 11.27, 0.22, 2, 4, 130.87, -59.18, 0.75, 71, 31.8, 10.29, 0.25, 2, 4, 126.11, -52.3, 0.75, 71, 27.04, 17.18, 0.25, 2, 4, 129.31, -32.84, 0.85, 71, 30.24, 36.64, 0.15, 2, 4, 140.02, -14.77, 0.85, 71, 40.95, 54.71, 0.15, 2, 4, 149.71, -10.05, 0.85, 71, 50.64, 59.42, 0.15, 2, 4, 167.12, -20.74, 0.85, 71, 68.05, 48.73, 0.15, 2, 4, 163.51, -32.88, 0.85, 71, 64.44, 36.6, 0.15, 2, 4, 159.6, -42.79, 0.85, 71, 60.53, 26.68, 0.15, 2, 4, 154.28, -51.75, 0.8, 71, 55.22, 17.72, 0.2, 2, 4, 148.4, -60.03, 0.78, 71, 49.34, 9.45, 0.22, 2, 4, 141.26, -67.48, 0.75, 71, 42.2, 2, 0.25, 2, 4, 141.59, -71.92, 0.75, 71, 42.53, -2.45, 0.25, 2, 4, 171.65, -27.36, 0.85, 71, 72.58, 42.11, 0.15, 2, 4, 171.81, -40.84, 0.85, 71, 72.74, 28.63, 0.15, 2, 4, 165.02, -54.82, 0.8, 71, 65.96, 14.65, 0.2, 2, 4, 156.39, -65.49, 0.78, 71, 57.32, 3.98, 0.22, 2, 4, 148.84, -73.5, 0.75, 71, 49.77, -4.03, 0.25, 2, 4, 138.87, -88.32, 0.8, 71, 39.8, -18.84, 0.2, 2, 4, 139.52, -96.51, 0.85, 71, 40.45, -27.04, 0.15, 2, 4, 137.69, -105.85, 0.82, 71, 38.62, -36.37, 0.18, 2, 4, 134.01, -112.56, 0.928, 71, 34.94, -43.09, 0.072, 2, 4, 131.27, -86.55, 0.8, 71, 32.2, -17.07, 0.2, 2, 4, 131.38, -92.47, 0.8, 71, 32.31, -22.99, 0.2, 2, 4, 130.26, -97.66, 0.85, 71, 31.19, -28.19, 0.15, 2, 4, 129.48, -102.86, 0.85, 71, 30.41, -33.39, 0.15, 2, 4, 127.29, -107.29, 0.888, 71, 28.22, -37.81, 0.112, 2, 4, 125.45, -111.87, 0.968, 71, 26.38, -42.39, 0.032, 2, 4, 134.51, -84.95, 0.8, 71, 35.44, -15.47, 0.2, 2, 4, 144.33, -73.62, 0.75, 71, 45.26, -4.14, 0.25, 2, 4, 169.32, -93.97, 0.7, 71, 70.25, -24.5, 0.3, 2, 4, 147.58, -82.86, 0.7, 71, 48.51, -13.38, 0.3, 2, 4, 119.82, -70.6, 0.7, 71, 20.75, -1.12, 0.3, 2, 4, 99.54, -72.06, 0.7, 71, 0.47, -2.58, 0.3, 2, 4, 91.52, -63.63, 0.7, 71, -7.55, 5.85, 0.3, 2, 4, 81.93, -57.99, 0.7, 71, -17.14, 11.49, 0.3, 2, 4, 67.67, -48.13, 0.7, 71, -31.39, 21.35, 0.3, 2, 4, 52.56, -41.68, 0.7, 71, -46.51, 27.79, 0.3, 2, 4, 182.67, -69.87, 0.78, 71, 83.6, -0.39, 0.22, 1, 4, 167.83, -60.49, 1, 2, 4, 119.49, -45.14, 0.78, 71, 20.42, 24.33, 0.22, 2, 4, 103.51, -39.96, 0.78, 71, 4.44, 29.52, 0.22, 2, 4, 86.04, -27.23, 0.78, 71, -13.03, 42.25, 0.22, 2, 4, 71.36, -13.67, 0.78, 71, -27.71, 55.8, 0.22, 2, 4, 210.33, -52.43, 0.85, 71, 111.26, 17.05, 0.15, 2, 4, 187.94, -44.13, 0.85, 71, 88.87, 25.34, 0.15, 2, 4, 176.53, -38.5, 0.85, 71, 77.46, 30.98, 0.15, 2, 4, 131.93, -20.12, 0.85, 71, 32.86, 49.36, 0.15, 2, 4, 119.29, -11.71, 0.82, 71, 20.23, 57.76, 0.18, 2, 4, 101.87, -2.76, 0.82, 71, 2.81, 66.71, 0.18, 2, 4, 86.24, 6.41, 0.82, 71, -12.83, 75.88, 0.18, 1, 4, 203.85, -26.04, 1, 1, 4, 175.76, -7.46, 1, 1, 4, 150.77, 2.79, 1, 1, 4, 128.56, 12.68, 1, 1, 4, 105.22, 17.18, 1, 2, 4, 177, -110.31, 0.936, 71, 77.93, -40.83, 0.064, 2, 4, 156.01, -107.65, 0.874, 71, 56.94, -38.18, 0.126, 2, 4, 143.02, -103.57, 0.85, 71, 43.95, -34.09, 0.15, 2, 4, 98.88, -85.81, 0.888, 71, -0.19, -16.33, 0.112, 2, 4, 84.78, -81.74, 0.85, 71, -14.29, -12.26, 0.15, 2, 4, 72.6, -70.51, 0.85, 71, -26.46, -1.03, 0.15, 2, 4, 57.23, -52.4, 0.8, 71, -41.84, 17.08, 0.2, 2, 4, 117.74, -75.85, 0.8, 71, 18.67, -6.37, 0.2, 2, 4, 122.74, -79.23, 0.8, 71, 23.67, -9.75, 0.2, 2, 4, 127.29, -85.17, 0.8, 71, 28.22, -15.7, 0.2, 2, 4, 128.89, -92.13, 0.83435, 71, 29.83, -22.65, 0.16565, 2, 4, 128.92, -97.33, 0.85805, 71, 29.85, -27.85, 0.14195], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 30, 76, 76, 78, 78, 64, 54, 82, 82, 84, 84, 86, 86, 88, 88, 32, 6, 90, 90, 92, 6, 8, 8, 10, 92, 8, 8, 94, 94, 96, 10, 12, 12, 14, 96, 12, 98, 100, 102, 104, 104, 106, 106, 108, 108, 110, 98, 112, 112, 90, 112, 114, 114, 92, 12, 116, 116, 118, 118, 120, 120, 122, 110, 124, 124, 126, 126, 128, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 130, 142, 142, 144, 144, 146, 146, 140, 130, 148, 148, 150, 150, 138, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 152, 152, 172, 172, 174, 174, 176, 176, 162, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 178, 180, 178, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 198, 212, 212, 214, 214, 216, 216, 218, 218, 220, 222, 224, 224, 226, 226, 228, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 222, 242, 220, 244, 2, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 0, 262, 262, 264, 266, 268, 268, 270, 270, 272, 274, 276, 276, 278, 280, 282, 282, 284, 284, 286, 288, 290, 290, 292, 292, 294, 294, 296, 298, 300, 300, 302, 304, 306, 306, 308, 308, 310, 312, 314, 314, 316, 316, 318, 318, 320, 320, 100], "width": 185, "height": 170}}, "tui_qian_xia": {"tui_qian_xia": {"type": "mesh", "uvs": [0.73698, 0, 0.66369, 0.04696, 0.56658, 0.10005, 0.4768, 0.1641, 0.41817, 0.22056, 0.37419, 0.29894, 0.33755, 0.3731, 0.28808, 0.47001, 0.2441, 0.54754, 0.20379, 0.59305, 0.10851, 0.63181, 0.00957, 0.6512, 0, 0.70513, 0.02423, 0.75738, 0, 0.82396, 0, 0.89812, 0.00408, 0.94615, 0.07737, 0.99082, 0.14882, 1, 0.21845, 0.99503, 0.25693, 0.92087, 0.31189, 0.85851, 0.40534, 0.77002, 0.48229, 0.69418, 0.54642, 0.62844, 0.60689, 0.53153, 0.66918, 0.46411, 0.73698, 0.38405, 0.78462, 0.31916, 0.87073, 0.22815, 0.98433, 0.15399, 1, 0.09078, 1, 0.00482, 0.85791, 0], "triangles": [19, 18, 20, 20, 18, 17, 17, 16, 20, 16, 15, 20, 20, 15, 21, 15, 14, 21, 14, 13, 21, 21, 13, 22, 13, 10, 22, 22, 10, 23, 13, 12, 10, 10, 12, 11, 23, 10, 9, 23, 9, 24, 9, 8, 24, 24, 8, 25, 8, 7, 25, 25, 7, 26, 7, 6, 26, 26, 6, 27, 6, 5, 27, 27, 5, 28, 5, 4, 28, 28, 4, 29, 29, 4, 3, 29, 3, 2, 30, 29, 2, 30, 2, 1, 30, 1, 0, 31, 30, 0, 31, 0, 33, 33, 32, 31], "vertices": [1, 65, -34.66, -19.51, 1, 1, 65, -9.78, -49.49, 1, 1, 65, 17.22, -87.67, 1, 1, 65, 51.95, -125.52, 1, 1, 65, 84.17, -152.73, 1, 1, 65, 131.82, -179.28, 1, 1, 65, 177.31, -202.92, 1, 1, 65, 236.63, -234.28, 1, 1, 65, 283.73, -260.68, 1, 2, 65, 308.56, -279.72, 0.97605, 66, -256.06, -201.95, 0.02395, 2, 65, 279.33, -304.6, 0.54183, 66, -259.72, -218.18, 0.45817, 2, 65, 232.96, -332.43, 0.26031, 66, -289.1, -228.11, 0.73969, 2, 65, 238.73, -350, 0.10314, 66, -282.41, -242.59, 0.89686, 2, 65, 260.86, -360.65, 0.00908, 66, -264.67, -256.4, 0.99092, 1, 66, -243.64, -295.39, 1, 1, 66, -212.51, -334.5, 1, 1, 66, -191.36, -359.05, 1, 1, 66, -154.83, -368.45, 1, 1, 66, -133.65, -359.5, 1, 1, 66, -118.85, -343.44, 1, 1, 66, -140.64, -296.9, 1, 1, 66, -153.48, -253.4, 1, 2, 65, 352.65, -243.51, 0.05385, 66, -164.14, -192.04, 0.94615, 2, 65, 378.02, -206.71, 0.62312, 66, -162.1, -163.66, 0.37688, 2, 65, 359.13, -182.42, 0.96389, 66, -192.65, -133.35, 0.03611, 1, 65, 301.74, -149.15, 1, 1, 65, 262.66, -118.97, 1, 1, 65, 215.78, -84.99, 1, 1, 65, 177.2, -59.64, 1, 1, 65, 124.6, -18.29, 1, 1, 65, 85.15, 28.43, 1, 1, 65, 45.17, 43.9, 1, 1, 65, -10.88, 58.56, 1, 1, 65, -25.17, 16.76, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66], "width": 310, "height": 674}}, "toufa_qian": {"toufa_qian": {"type": "mesh", "uvs": [0.1952, 0.00618, 0.18788, 0.09757, 0.1952, 0.20509, 0.26103, 0.32335, 0.38539, 0.42729, 0.50974, 0.53301, 0.64873, 0.61007, 0.82063, 0.64411, 1, 0.61007, 1, 0.68174, 0.84989, 0.70145, 0.59021, 0.67637, 0.63044, 0.76417, 0.64507, 0.89319, 0.6085, 0.96487, 0.5134, 1, 0.41831, 1, 0.53535, 0.92007, 0.50609, 0.81614, 0.42928, 0.72117, 0.33418, 0.61007, 0.26835, 0.49896, 0.12936, 0.39324, 0.01232, 0.30364, 0, 0.18, 0, 0.07248, 0.03426, 0, 0.44025, 0.56347], "triangles": [7, 8, 9, 10, 11, 7, 11, 6, 7, 10, 7, 9, 11, 27, 6, 27, 5, 6, 27, 21, 5, 21, 4, 5, 14, 15, 17, 15, 16, 17, 14, 17, 13, 17, 18, 13, 18, 12, 13, 18, 19, 12, 19, 11, 12, 19, 20, 11, 20, 27, 11, 20, 21, 27, 21, 22, 4, 22, 3, 4, 22, 23, 3, 23, 2, 3, 23, 24, 2, 24, 1, 2, 24, 25, 1, 1, 25, 0, 0, 25, 26], "vertices": [1, 9, -2.85, 15.49, 1, 1, 9, 24.36, 13.86, 1, 2, 9, 56.41, 14.27, 0.96585, 10, -14.16, 24.96, 0.03415, 2, 9, 91.85, 23.16, 0.00762, 10, 20.86, 14.59, 0.99238, 2, 10, 56.75, 13.72, 0.34232, 14, -6.7, 9.13, 0.65768, 1, 14, 29.41, 4.85, 1, 3, 9, 178.43, 78, 0, 14, 59.95, 7.38, 0.71062, 15, 0.81, 8.37, 0.28938, 2, 15, 27.84, 7.01, 0.91909, 16, -1.62, 15.91, 0.08091, 1, 16, 25.11, 7.29, 1, 1, 16, 11.65, -9.29, 1, 2, 15, 37.48, -7.73, 0.99858, 16, -9.07, -0.05, 0.00142, 3, 11, 65.64, 14.93, 0.51259, 14, 70.68, -11.28, 0.16538, 15, -0.79, -13.09, 0.32203, 4, 9, 224.29, 74.38, 0, 11, 92.29, 11.96, 0.24801, 12, 3.14, 12.88, 0.75143, 15, 13.34, -35.89, 0.00055, 3, 9, 262.78, 75.73, 0, 12, 41.55, 10.23, 0.71083, 13, -15.27, -0.14, 0.28917, 2, 12, 62.09, 2.28, 0.47911, 13, 5.27, 7.78, 0.52089, 1, 13, 21.82, 2.45, 1, 2, 12, 69.03, -26.57, 4e-05, 13, 29.81, -8.9, 0.99996, 2, 12, 47.52, -6.66, 0.52624, 13, 0.5, -8.64, 0.47376, 1, 12, 16.25, -7.06, 1, 1, 11, 70.61, -11.64, 1, 2, 9, 177.49, 32.08, 0, 11, 34.78, -13.98, 1, 2, 10, 65.92, -12.05, 0.55258, 11, 0.35, -12.28, 0.44742, 2, 9, 112.27, 3.51, 0, 10, 28.45, -12.72, 1, 2, 9, 85.23, -13.02, 0.33495, 10, -3.24, -13.2, 0.66505, 1, 9, 48.35, -14.07, 1, 1, 9, 16.32, -13.41, 1, 1, 9, -5.17, -7.96, 1, 2, 11, 26.7, 5.19, 0.64779, 14, 30.62, -8.71, 0.35221], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 54, 22], "width": 146, "height": 298}}, "shenti": {"shenti": {"type": "mesh", "uvs": [0.24917, 0, 0.3024, 0.03089, 0.33788, 0.08198, 0.30979, 0.13521, 0.31127, 0.18098, 0.32942, 0.1919, 0.35797, 0.19434, 0.3766, 0.19452, 0.41337, 0.18947, 0.44652, 0.19487, 0.46805, 0.21298, 0.49055, 0.24434, 0.51157, 0.27645, 0.5553, 0.28527, 0.58021, 0.2957, 0.59943, 0.31315, 0.60535, 0.33444, 0.60091, 0.35808, 0.59233, 0.38511, 0.5876, 0.41056, 0.58967, 0.43121, 0.59938, 0.43735, 0.59994, 0.45547, 0.60357, 0.47982, 0.60413, 0.49552, 0.61811, 0.5078, 0.61308, 0.52215, 0.62761, 0.55275, 0.64047, 0.58234, 0.64075, 0.59885, 0.65194, 0.60891, 0.63293, 0.61233, 0.60161, 0.62018, 0.59366, 0.63021, 0.57296, 0.67279, 0.55966, 0.74624, 0.54487, 0.81011, 0.502, 0.87505, 0.45764, 0.92189, 0.41033, 0.95808, 0.37485, 1, 0.31423, 1, 0.261, 0.96127, 0.22108, 0.91231, 0.16194, 0.8676, 0.1028, 0.83779, 0.02592, 0.82076, 0, 0.79947, 0, 0.75157, 0.04662, 0.73666, 0.11611, 0.71857, 0.17968, 0.65895, 0.24769, 0.60466, 0.29353, 0.55889, 0.31127, 0.52376, 0.29057, 0.47905, 0.23439, 0.43327, 0.18116, 0.39389, 0.15011, 0.3215, 0.12942, 0.26828, 0.15455, 0.20334, 0.17377, 0.1565, 0.18116, 0.10221, 0.17525, 0.04473, 0.17377, 0.00427, 0.24819, 0.06576, 0.26638, 0.12045, 0.22893, 0.19517, 0.22144, 0.27451, 0.25568, 0.35386, 0.31666, 0.4201, 0.39049, 0.48712, 0.37658, 0.57956, 0.28136, 0.66892, 0.20219, 0.75905, 0.18079, 0.8271, 0.27173, 0.20453, 0.32094, 0.23843, 0.38407, 0.28388, 0.42577, 0.3659, 0.429, 0.41945, 0.46966, 0.47877, 0.48143, 0.55888, 0.43435, 0.64824, 0.36588, 0.72065, 0.32308, 0.8023, 0.31987, 0.87009, 0.43649, 0.23534, 0.47608, 0.26693, 0.4076, 0.2546, 0.50176, 0.2962, 0.52899, 0.37881, 0.54669, 0.45335, 0.57344, 0.53115, 0.57237, 0.59509, 0.50496, 0.63129, 0.47715, 0.69831, 0.44826, 0.77996, 0.4044, 0.86008, 0.3402, 0.93711, 0.50381, 0.27488, 0.4474, 0.26048, 0.40542, 0.24301, 0.38971, 0.24362, 0.37496, 0.2507, 0.34905, 0.24575, 0.43328, 0.24807, 0.39829, 0.22639, 0.36534, 0.21028, 0.33931, 0.19827, 0.26897, 0.32603, 0.32402, 0.29762, 0.43367, 0.28903, 0.47863, 0.29663, 0.51098, 0.29558, 0.53586, 0.29443, 0.57574, 0.29949, 0.32039, 0.49159, 0.32859, 0.46576, 0.34698, 0.44763, 0.37933, 0.42848, 0.47262, 0.41298, 0.49927, 0.39306, 0.55118, 0.39046, 0.56264, 0.40836, 0.57214, 0.41994, 0.58213, 0.4317, 0.22437, 0.40541, 0.28739, 0.40118, 0.29593, 0.35734, 0.37123, 0.37272, 0.48766, 0.3535, 0.51493, 0.33615, 0.55334, 0.35388, 0.59286, 0.3485], "triangles": [95, 94, 33, 94, 29, 32, 31, 32, 29, 31, 29, 30, 33, 94, 32, 29, 94, 28, 43, 44, 75, 44, 45, 75, 46, 49, 45, 75, 45, 74, 45, 49, 50, 75, 74, 85, 74, 45, 50, 46, 47, 49, 85, 74, 84, 47, 48, 49, 50, 51, 74, 74, 73, 84, 74, 51, 73, 51, 52, 73, 73, 72, 83, 72, 52, 53, 72, 73, 52, 53, 54, 72, 39, 40, 99, 40, 41, 99, 41, 42, 99, 42, 43, 99, 39, 99, 38, 43, 86, 99, 99, 98, 38, 99, 86, 98, 38, 98, 37, 86, 43, 75, 36, 37, 97, 86, 85, 98, 86, 75, 85, 37, 98, 97, 98, 85, 97, 85, 84, 97, 36, 97, 35, 97, 96, 35, 97, 84, 96, 35, 96, 34, 84, 83, 96, 84, 73, 83, 96, 95, 34, 96, 83, 95, 34, 95, 33, 95, 83, 82, 95, 82, 94, 28, 94, 27, 88, 90, 101, 103, 102, 89, 88, 11, 100, 12, 100, 11, 11, 88, 87, 101, 102, 106, 87, 88, 101, 101, 89, 102, 101, 106, 87, 87, 106, 107, 87, 10, 11, 103, 107, 102, 103, 108, 107, 106, 102, 107, 87, 107, 9, 87, 9, 10, 9, 107, 8, 108, 7, 107, 107, 7, 8, 108, 6, 7, 83, 72, 82, 94, 93, 27, 93, 26, 27, 93, 24, 26, 94, 82, 93, 72, 54, 71, 72, 71, 82, 71, 54, 117, 24, 92, 23, 23, 92, 22, 71, 81, 82, 82, 81, 93, 21, 22, 126, 126, 22, 92, 126, 20, 21, 81, 92, 93, 24, 93, 92, 54, 55, 117, 26, 24, 25, 117, 118, 71, 117, 55, 118, 118, 119, 71, 119, 120, 71, 71, 80, 81, 71, 120, 80, 55, 70, 118, 81, 121, 92, 81, 80, 121, 118, 70, 119, 121, 122, 92, 124, 92, 123, 92, 125, 126, 92, 124, 125, 91, 92, 122, 123, 92, 91, 119, 70, 120, 126, 125, 20, 125, 19, 20, 80, 120, 130, 125, 124, 19, 80, 79, 121, 121, 79, 122, 19, 124, 18, 124, 123, 18, 122, 131, 91, 18, 123, 133, 123, 91, 133, 17, 133, 134, 17, 18, 133, 91, 132, 133, 17, 134, 16, 116, 13, 14, 55, 56, 70, 70, 56, 128, 57, 127, 56, 56, 127, 128, 120, 70, 130, 130, 70, 129, 130, 79, 80, 128, 127, 69, 127, 57, 69, 129, 70, 69, 70, 128, 69, 57, 58, 69, 79, 131, 122, 131, 132, 91, 129, 111, 130, 130, 78, 79, 130, 111, 78, 131, 112, 113, 131, 79, 112, 79, 78, 112, 69, 110, 129, 129, 110, 111, 133, 132, 116, 134, 133, 15, 110, 69, 68, 132, 113, 90, 132, 131, 113, 134, 15, 16, 15, 133, 116, 90, 114, 132, 132, 114, 115, 69, 58, 68, 110, 68, 111, 58, 59, 68, 68, 77, 111, 111, 77, 78, 90, 113, 89, 101, 90, 89, 114, 90, 12, 90, 88, 100, 114, 12, 115, 12, 90, 100, 115, 12, 13, 78, 89, 112, 89, 104, 103, 89, 113, 112, 77, 105, 78, 105, 104, 78, 89, 78, 104, 59, 60, 68, 68, 76, 77, 68, 67, 76, 68, 60, 67, 104, 105, 103, 103, 105, 108, 105, 77, 108, 77, 76, 5, 77, 109, 108, 77, 5, 109, 5, 76, 4, 109, 6, 108, 76, 67, 4, 109, 5, 6, 116, 14, 15, 116, 132, 115, 115, 13, 116, 60, 61, 67, 4, 66, 3, 4, 67, 66, 67, 61, 66, 61, 62, 66, 3, 66, 2, 62, 65, 66, 66, 65, 2, 62, 63, 65, 65, 1, 2, 65, 0, 1, 65, 63, 0, 63, 64, 0], "vertices": [1, 4, 150.95, 5.26, 1, 1, 4, 127.36, -24.72, 1, 1, 4, 88, -44.99, 1, 3, 4, 46.57, -29.8, 0.98854, 3, 207.52, -51.04, 0.00941, 38, 85.19, 19.97, 0.00205, 3, 4, 11.11, -31.05, 0.43728, 3, 177.68, -31.84, 0.47479, 38, 55.35, 39.18, 0.08793, 5, 4, 2.76, -41.27, 0.28775, 3, 164.99, -35.48, 0.47763, 2, 310.12, 27.05, 0, 38, 42.67, 35.53, 0.20613, 87, 112.8, 23.09, 0.02848, 5, 4, 1.06, -57.23, 0.08066, 3, 154.5, -47.61, 0.43874, 2, 304.76, 11.93, 0, 38, 32.17, 23.4, 0.46409, 87, 102.3, 10.96, 0.01651, 5, 4, 1.05, -67.62, 0.0214, 3, 148.56, -56.14, 0.36124, 2, 302.34, 1.82, 0, 38, 26.23, 14.87, 0.59928, 87, 96.36, 2.42, 0.01807, 5, 4, 5.21, -88.09, 0.00689, 3, 140.3, -75.33, 0.12913, 2, 301.64, -19.06, 1e-05, 38, 17.97, -4.32, 0.84296, 87, 88.1, -16.76, 0.02101, 4, 3, 126.46, -88.3, 0.03568, 2, 293.48, -36.17, 0.0013, 38, 4.14, -17.29, 0.96039, 87, 74.27, -29.73, 0.00263, 4, 3, 108.1, -90.38, 0.08164, 2, 277.13, -44.8, 0.00309, 38, -14.22, -19.37, 0.90894, 87, 55.91, -31.81, 0.00632, 3, 3, 80.94, -87.16, 0.26821, 2, 250.67, -51.69, 0.0153, 38, -41.38, -16.14, 0.71649, 4, 3, 53.76, -82.92, 0.39091, 2, 223.8, -57.64, 0.06681, 38, -68.57, -11.91, 0.35738, 87, 1.56, -24.35, 0.18489, 4, 3, 34.41, -99.29, 0.3783, 2, 211.75, -79.94, 0.13033, 38, -87.91, -28.28, 0.22545, 87, -17.78, -40.72, 0.26592, 4, 3, 19.93, -106.27, 0.37559, 2, 200.81, -91.71, 0.178, 38, -102.39, -35.26, 0.19634, 87, -32.26, -47.71, 0.25007, 4, 3, 2.72, -107.57, 0.36463, 2, 185.25, -99.19, 0.23832, 38, -119.61, -36.56, 0.16591, 87, -49.48, -49, 0.23114, 4, 3, -12.8, -101.06, 0.32831, 2, 168.43, -98.77, 0.31879, 38, -135.12, -30.04, 0.13371, 87, -64.99, -42.49, 0.21919, 4, 3, -26.57, -88.74, 0.29429, 2, 151.11, -92.32, 0.40383, 38, -148.9, -17.73, 0.10038, 87, -78.77, -30.17, 0.20151, 4, 3, -41.25, -73.03, 0.2191, 2, 131.73, -83.04, 0.57466, 38, -163.57, -2.02, 0.06493, 87, -93.44, -14.46, 0.14131, 5, 3, -56.1, -59.79, 0.12961, 2, 113.07, -76.11, 0.78459, 38, -178.42, 11.22, 0.03662, 5, -142.76, -22.11, 1e-05, 87, -108.29, -1.22, 0.04917, 4, 3, -70, -51.77, 0.07153, 2, 97.21, -73.71, 0.90334, 38, -192.32, 19.24, 0.02385, 5, -129.15, -13.61, 0.00128, 5, 3, -76.97, -53.59, 0.05677, 2, 91.37, -77.95, 0.91383, 38, -199.3, 17.42, 0.01928, 5, -127.48, -6.6, 0.00326, 87, -129.17, 4.98, 0.00686, 4, 3, -88.77, -45.98, 0.01341, 2, 77.61, -75.16, 0.97012, 38, -211.1, 25.03, 0.00753, 5, -115.21, 0.24, 0.00893, 4, 3, -105.54, -37.08, 0.0062, 2, 58.75, -72.97, 0.93613, 38, -227.87, 33.94, 0.00379, 5, -99.47, 10.85, 0.05388, 4, 3, -115.79, -30.51, 0.00339, 2, 46.81, -70.59, 0.91159, 38, -238.12, 40.5, 0.00217, 5, -88.86, 16.81, 0.08285, 3, 2, 35.81, -76.11, 0.89122, 38, -250.37, 39.37, 0.00029, 5, -84.09, 28.16, 0.10849, 3, 2, 25.58, -70.91, 0.81671, 38, -258.01, 47.93, 0.00018, 5, -72.94, 30.87, 0.18311, 3, 2, 1.34, -70.58, 0.59988, 38, -280.46, 57.07, 3e-05, 5, -54.32, 46.39, 0.40009, 3, 2, -15.01, -66.12, 0.34059, 5, -39, 53.64, 0.48341, 64, 223.72, 100.88, 0.176, 3, 2, -28.77, -66.41, 0.07451, 5, -28.74, 62.82, 0.14149, 64, 222.46, 87.17, 0.784, 1, 64, 219.86, 76.38, 1, 1, 64, 206.65, 75.67, 1, 1, 64, 202.18, 73.13, 1, 1, 64, 195.27, 65.16, 1, 4, 2, -83.36, -23.35, 0.0001, 5, 40.73, 65.64, 0.66378, 6, -83.49, 101.1, 0.00812, 64, 173.5, 37.81, 0.328, 2, 5, 94.53, 85.67, 0.8075, 6, -26.08, 101.12, 0.1925, 2, 5, 142.14, 101.51, 0.45484, 6, 24.07, 99.36, 0.54516, 2, 5, 197.82, 103.88, 0.1184, 6, 77.07, 82.16, 0.8816, 2, 5, 241.47, 98.96, 0.01438, 6, 116.27, 62.32, 0.98562, 1, 6, 147.51, 39.78, 1, 1, 6, 182.29, 24.36, 1, 2, 6, 186.67, -9.18, 0.99975, 63, 227.96, 171.35, 0.00025, 2, 6, 160.76, -42.52, 0.96939, 63, 220.71, 129.76, 0.03061, 2, 6, 126.02, -69.53, 0.79224, 63, 202.69, 89.61, 0.20776, 2, 6, 95.94, -106.74, 0.3721, 63, 193.58, 42.64, 0.6279, 2, 6, 77.31, -142.46, 0.06893, 63, 193.88, 2.35, 0.93107, 2, 6, 69.78, -186.71, 0.00024, 63, 207.98, -40.26, 0.99976, 1, 63, 202.92, -61.61, 1, 1, 63, 172.66, -83.12, 1, 1, 63, 148.17, -68.61, 1, 1, 63, 114.27, -45.14, 1, 1, 63, 56.06, -43.01, 1, 3, 3, -74.35, 181.6, 0.00043, 2, 8.15, 142.05, 0.03058, 63, -0.22, -36.46, 0.96899, 4, 3, -59.3, 140.54, 0.01887, 2, 37.12, 109.28, 0.32967, 5, 35.55, -113.47, 0.00996, 63, -43.95, -36.18, 0.64149, 4, 3, -42.31, 117.08, 0.08898, 2, 61.49, 93.63, 0.58485, 5, 6.85, -117.44, 0.00279, 63, -71.87, -43.89, 0.32338, 4, 3, -7.14, 107.22, 0.30348, 2, 97.83, 97.25, 0.49489, 63, -93.42, -73.38, 0.09763, 87, -59.34, 165.79, 0.104, 4, 3, 39.81, 113.29, 0.56399, 2, 139.35, 120.01, 0.19113, 63, -104.16, -119.49, 0.02088, 87, -12.39, 171.86, 0.224, 4, 3, 81.74, 120.78, 0.60543, 2, 175.67, 142.25, 0.06162, 63, -111.83, -161.38, 0.00495, 87, 29.54, 179.35, 0.328, 4, 4, -98.85, 57.58, 0.00021, 3, 137.91, 103.68, 0.98833, 2, 234.21, 146.79, 0.01105, 63, -147.51, -208.01, 0.0004, 3, 4, -57.74, 69.62, 0.04198, 3, 178.54, 90.12, 0.95791, 2, 276.99, 148.96, 0.00011, 2, 4, -7.25, 56.19, 0.42748, 3, 212.35, 50.29, 0.57252, 2, 4, 29.17, 45.9, 0.91766, 3, 236.4, 21.06, 0.08234, 1, 4, 71.29, 42.27, 1, 1, 4, 115.8, 46.1, 1, 1, 4, 147.14, 47.29, 1, 1, 4, 99.99, 5.21, 1, 1, 4, 57.72, -5.44, 1, 2, 4, -0.43, 14.76, 0.55928, 3, 194.33, 12.37, 0.44072, 4, 4, -61.97, 18.22, 0.00366, 3, 145.75, 50.3, 0.89575, 2, 260.95, 99.94, 0.00059, 87, 93.55, 108.87, 0.1, 4, 3, 84.12, 68.95, 0.84636, 2, 196.76, 94.86, 0.04994, 63, -161.21, -145.47, 0.0037, 87, 31.92, 127.52, 0.1, 4, 3, 22.53, 69.55, 0.56121, 2, 139.18, 72.98, 0.31089, 63, -139.09, -87.99, 0.0279, 87, -29.67, 128.12, 0.1, 3, 3, -43.58, 64.56, 0.08415, 2, 79.44, 44.25, 0.82582, 63, -120.63, -24.32, 0.09003, 4, 3, -98.55, 111.14, 0.00545, 2, 11.27, 67.61, 0.39841, 5, 28.06, -65.01, 0.18506, 63, -57.75, 10.87, 0.41108, 3, 5, 114.11, -79.62, 0.04219, 6, -65.37, -60.63, 0.0008, 63, 29.48, 7.71, 0.95701, 3, 5, 196.51, -86.04, 0.00361, 6, 9.61, -95.38, 0.05783, 63, 112.01, 12.18, 0.93855, 2, 6, 63.46, -100.38, 0.26166, 63, 161.91, 33.02, 0.73834, 3, 4, -7.4, -9.2, 0.00071, 3, 174.93, -3.33, 0.99502, 38, 52.61, 67.68, 0.00427, 3, 3, 137.79, -11.35, 0.77883, 38, 15.46, 59.66, 0.06117, 87, 85.59, 47.22, 0.16, 3, 3, 88.87, -20.77, 0.61487, 38, -33.45, 50.24, 0.11313, 87, 36.68, 37.79, 0.272, 3, 3, 23.19, -4.41, 0.79894, 38, -99.14, 66.6, 0.00106, 87, -29.01, 54.16, 0.2, 4, 3, -12.19, 17.36, 0.10769, 2, 125.86, 11.73, 0.88096, 63, -175.83, -37.2, 0.00335, 87, -64.39, 75.93, 0.008, 2, 2, 76.02, -0.27, 1, 38, -185.3, 95.36, 0, 3, 2, 14.01, 7.01, 0.949, 5, -13.45, -20.77, 0.03926, 63, -104.72, 49.26, 0.01173, 3, 2, -47.75, 47.9, 0.0138, 5, 60.05, -11.64, 0.92456, 63, -33.06, 67.99, 0.06164, 3, 5, 127.52, -19.19, 0.84527, 6, -31.73, -8.67, 0.00463, 63, 34.82, 69.37, 0.1501, 3, 5, 194.62, -10.73, 0.00072, 6, 34.11, -24.14, 0.86851, 63, 100.23, 86.59, 0.13077, 2, 6, 86.43, -19.11, 0.94844, 63, 144.08, 115.57, 0.05156, 3, 3, 103.62, -66.08, 0.16524, 2, 264.11, -23.8, 0.00077, 38, -18.7, 4.93, 0.83399, 4, 3, 70.97, -70.65, 0.34935, 2, 235.37, -39.95, 0.01946, 38, -51.36, 0.36, 0.46318, 87, 18.77, -12.09, 0.168, 3, 3, 100.3, -44.37, 0.39837, 38, -22.03, 26.65, 0.42963, 87, 48.1, 14.2, 0.172, 4, 3, 44.15, -69.8, 0.40078, 2, 210.08, -48.93, 0.06326, 38, -78.17, 1.21, 0.23597, 87, -8.05, -11.23, 0.3, 3, 3, -17.38, -46.5, 0.36886, 2, 144.28, -49.64, 0.55832, 38, -139.71, 24.51, 0.07281, 4, 3, -70.76, -22.3, 0.01028, 2, 85.76, -46.54, 0.98266, 38, -193.08, 48.72, 0.00671, 5, -102.78, -26.8, 0.00035, 2, 2, 23.66, -47.8, 0.85378, 5, -56.44, 14.57, 0.14622, 3, 2, -24.54, -36.3, 0.13, 5, -12.36, 37.2, 0.366, 64, 193.02, 94.78, 0.504, 1, 5, 30.02, 17.06, 1, 2, 5, 83.2, 27.61, 0.9756, 6, -56.95, 50.65, 0.0244, 2, 5, 146.68, 42.93, 0.5064, 6, 7.89, 42.87, 0.4936, 2, 5, 213.01, 50.31, 0.02487, 6, 72.63, 26.65, 0.97513, 2, 6, 136.46, -1.13, 0.99962, 63, 179.84, 154.92, 0.00038, 4, 3, 57.19, -80.01, 0.39426, 2, 225.94, -53.69, 0.05357, 38, -65.14, -9, 0.38578, 87, 4.99, -21.44, 0.16639, 4, 3, 84.08, -60.2, 0.39457, 2, 243.77, -25.45, 0.01383, 38, -38.25, 10.81, 0.52309, 87, 31.88, -1.64, 0.06852, 5, 4, -36.34, -84.15, 0.00487, 3, 108.42, -48.4, 0.39587, 2, 262.14, -5.58, 0.00012, 38, -13.9, 22.62, 0.54983, 87, 56.22, 10.17, 0.04931, 5, 4, -36.92, -75.39, 0.00333, 3, 112.94, -40.87, 0.49396, 2, 263.61, 3.07, 8e-05, 38, -9.38, 30.14, 0.44603, 87, 60.74, 17.7, 0.05659, 5, 4, -42.5, -67.22, 0.00168, 3, 113.01, -30.98, 0.53758, 2, 260.07, 12.32, 4e-05, 38, -9.31, 40.04, 0.26194, 87, 60.81, 27.59, 0.19877, 5, 4, -38.83, -52.72, 0.0009, 3, 124.3, -21.16, 0.71883, 2, 267, 25.57, 2e-05, 38, 1.97, 49.86, 0.17708, 87, 72.1, 37.41, 0.10317, 5, 4, -40.07, -99.74, 0.00104, 3, 96.46, -59.07, 0.30094, 2, 254.89, -19.88, 0.006, 38, -25.86, 11.94, 0.60287, 87, 44.26, -0.5, 0.08915, 4, 4, -23.51, -80.02, 0.01083, 3, 121.31, -52.32, 0.37927, 2, 275.58, -4.54, 8e-05, 38, -1.01, 18.69, 0.60982, 5, 4, -11.24, -61.49, 0.01953, 3, 141.96, -44.09, 0.49366, 2, 291.81, 10.64, 1e-05, 38, 19.64, 26.92, 0.47079, 87, 89.76, 14.47, 0.016, 4, 4, -2.11, -46.85, 0.21534, 3, 157.81, -37.28, 0.50339, 2, 304.09, 22.76, 1e-05, 38, 35.49, 33.73, 0.28127, 6, 4, -101.57, -8.77, 0.00077, 3, 97.82, 50.72, 0.82951, 2, 216.17, 82.87, 0.03161, 38, -24.5, 121.74, 0.01983, 63, -183.08, -151.93, 0.00233, 87, 45.63, 109.29, 0.11595, 6, 4, -79.19, -39.23, 0.00032, 3, 98.84, 12.94, 0.6818, 2, 230.87, 48.05, 0.01303, 38, -23.49, 83.95, 0.0622, 63, -218.83, -139.65, 0.00096, 87, 46.64, 71.51, 0.2417, 4, 3, 70.05, -41.46, 0.55945, 2, 223.88, -13.09, 0.02665, 38, -52.28, 29.56, 0.17165, 87, 17.85, 17.11, 0.24225, 4, 3, 51.11, -58.93, 0.46227, 2, 212.6, -36.27, 0.05022, 38, -71.21, 12.08, 0.20808, 87, -1.09, -0.36, 0.27943, 4, 3, 41.66, -74.33, 0.40254, 2, 209.41, -54.05, 0.07805, 38, -80.66, -3.32, 0.23507, 87, -10.54, -15.76, 0.28435, 4, 3, 34.62, -86.33, 0.30999, 2, 207.22, -67.79, 0.09006, 38, -87.71, -15.32, 0.17624, 87, -17.58, -27.76, 0.4237, 4, 3, 18.9, -102.56, 0.34205, 2, 198.5, -88.63, 0.16204, 38, -103.42, -31.55, 0.17714, 87, -33.3, -43.99, 0.31877, 4, 3, -24.52, 98.89, 0.21637, 2, 84.69, 83.17, 0.62827, 5, -17.57, -124.59, 0.00066, 63, -95.14, -54.19, 0.1547, 4, 3, -10.5, 83.88, 0.33182, 2, 103.21, 74.29, 0.56295, 5, -37.4, -129.9, 0.00035, 63, -114.11, -62.06, 0.10487, 3, 3, -4.62, 67.5, 0.38815, 2, 114.65, 61.18, 0.55509, 63, -131.51, -61.84, 0.05676, 3, 3, -2.45, 44.24, 0.29724, 2, 125.14, 40.3, 0.67739, 63, -154.06, -55.73, 0.02537, 6, 3, -21.68, -5.6, 0.13663, 2, 125.38, -13.12, 0.81488, 38, -144.01, 65.41, 0.01432, 5, -111.12, -77.96, 6e-05, 63, -194.01, -20.27, 0.00211, 87, -73.88, 52.97, 0.032, 6, 3, -17.23, -26.58, 0.19744, 2, 137.17, -31.03, 0.55606, 38, -139.56, 44.44, 0.03042, 5, -131.72, -72.03, 3e-05, 63, -215.22, -17.1, 0.00097, 87, -69.43, 31.99, 0.21509, 4, 3, -31.8, -51.69, 0.26238, 2, 132.75, -59.72, 0.68022, 38, -154.13, 19.32, 0.0574, 5, -147.04, -47.36, 1e-05, 5, 3, -46.87, -49.21, 0.15294, 2, 117.81, -62.9, 0.73772, 38, -169.2, 21.81, 0.03726, 5, -137.76, -35.23, 7e-05, 87, -99.07, 9.36, 0.072, 4, 3, -57.28, -48.57, 0.11581, 2, 107.88, -66.1, 0.85243, 38, -179.6, 22.44, 0.03117, 5, -132.3, -26.34, 0.0006, 4, 3, -67.95, -48.08, 0.07185, 2, 97.77, -69.53, 0.90394, 38, -190.28, 22.93, 0.02314, 5, -126.85, -17.15, 0.00107, 4, 3, 60.83, 105.82, 0.63668, 2, 161.65, 120.7, 0.12865, 63, -118.52, -136.56, 0.01318, 87, 8.63, 164.38, 0.22149, 4, 3, 43.83, 74.86, 0.66913, 2, 157.09, 85.68, 0.22259, 63, -141.57, -109.8, 0.02009, 87, -8.36, 133.43, 0.08819, 5, 3, 69.29, 51.87, 0.824, 2, 189.18, 73.54, 0.04774, 38, -53.03, 122.88, 0.00026, 63, -172.02, -125.61, 0.00372, 87, 17.1, 110.43, 0.12428, 5, 3, 35.87, 23.75, 0.74563, 2, 168.29, 35.19, 0.08204, 38, -86.46, 94.77, 0.00068, 63, -186.66, -84.45, 0.00728, 87, -16.33, 82.32, 0.16436, 4, 3, 11.79, -38.4, 0.51018, 2, 168.5, -31.46, 0.27218, 38, -110.53, 32.62, 0.08928, 87, -40.41, 20.17, 0.12836, 4, 3, 14.39, -58.53, 0.38534, 2, 178.26, -49.27, 0.30269, 38, -107.94, 12.48, 0.15706, 87, -37.81, 0.04, 0.15491, 4, 3, -9, -68.58, 0.35784, 2, 160.13, -67.15, 0.41836, 38, -131.33, 2.43, 0.11248, 87, -61.2, -10.01, 0.11132, 4, 3, -17.91, -89.18, 0.31979, 2, 159.34, -89.58, 0.37539, 38, -140.24, -18.17, 0.11515, 87, -70.11, -30.62, 0.18968], "hull": 65, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 0, 128, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 142, 144, 144, 146, 146, 148, 148, 150, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 174, 176, 178, 180, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 176, 200, 176, 202, 202, 204, 204, 206, 206, 208, 208, 210, 202, 212, 212, 214, 214, 216, 216, 218, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 220, 222, 222, 156, 156, 224, 224, 226, 228, 230, 230, 232, 234, 236, 140, 238, 238, 142, 236, 238, 238, 240, 240, 160, 160, 242, 242, 244, 244, 182, 182, 246, 246, 248, 248, 250, 250, 252, 252, 40, 254, 256, 258, 260, 158, 262, 180, 264, 264, 182, 262, 264, 264, 266, 266, 268, 200, 24, 24, 26, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 558, "height": 775}}, "toufa_you": {"toufa_you": {"type": "mesh", "uvs": [0, 0.02381, 0.10122, 0.08211, 0.2406, 0.18643, 0.2598, 0.25007, 0.27439, 0.29843, 0.42221, 0.33985, 0.55736, 0.37821, 0.51935, 0.45952, 0.40954, 0.52856, 0.28283, 0.62214, 0.17725, 0.69885, 0.14346, 0.75868, 0.06321, 0.80011, 0.07235, 0.81893, 0.10122, 0.87835, 0.2406, 0.94739, 0.40109, 1, 0.5996, 0.99495, 0.86568, 1, 1, 0.9612, 1, 0.87682, 0.96705, 0.75408, 1, 0.66663, 1, 0.54697, 1, 0.45031, 0.91636, 0.33371, 0.84456, 0.21558, 0.67985, 0.13274, 0.49401, 0.0683, 0.34196, 0.02381, 0.16035, 0, 0, 0, 0.35307, 0.30464, 0.53567, 0.32861, 0.63467, 0.37976, 0.63027, 0.48205, 0.58187, 0.55317, 0.46087, 0.60272, 0.37727, 0.65626, 0.28707, 0.74097, 0.15507, 0.79372, 0.57207, 0.18931, 0.71278, 0.26265, 0.81678, 0.36931, 0.80454, 0.47709, 0.72195, 0.63931, 0.60878, 0.75709, 0.50478, 0.85709, 0.85042, 0.74042, 0.83513, 0.82376, 0.73419, 0.92042, 0.54454, 0.95042, 0.3396, 0.91709, 0.24478, 0.87376, 0.86572, 0.47153, 0.92995, 0.55265, 0.90242, 0.65487, 0.34157, 0.12006], "triangles": [0, 31, 30, 1, 0, 30, 1, 30, 29, 57, 1, 29, 57, 29, 28, 2, 1, 57, 32, 4, 3, 4, 32, 5, 27, 57, 28, 2, 57, 41, 3, 2, 41, 41, 32, 3, 5, 32, 33, 10, 9, 38, 39, 10, 38, 21, 48, 22, 46, 45, 48, 39, 38, 46, 11, 10, 39, 40, 11, 39, 12, 11, 40, 13, 12, 40, 49, 46, 48, 49, 48, 21, 47, 39, 46, 47, 46, 49, 40, 39, 47, 53, 40, 47, 13, 40, 53, 49, 21, 20, 14, 13, 53, 52, 53, 47, 50, 47, 49, 50, 49, 20, 15, 14, 53, 15, 53, 52, 51, 47, 50, 52, 47, 51, 50, 20, 19, 17, 51, 50, 16, 52, 51, 16, 51, 17, 15, 52, 16, 18, 50, 19, 17, 50, 18, 41, 57, 27, 41, 27, 26, 42, 41, 26, 41, 33, 32, 42, 33, 41, 42, 26, 25, 43, 42, 25, 33, 42, 43, 34, 6, 33, 5, 33, 6, 43, 34, 33, 43, 25, 24, 7, 6, 34, 54, 43, 24, 44, 34, 43, 44, 43, 54, 35, 7, 34, 44, 35, 34, 54, 24, 23, 55, 54, 23, 35, 8, 7, 36, 8, 35, 37, 8, 36, 9, 8, 37, 44, 36, 35, 44, 55, 45, 37, 36, 45, 44, 54, 55, 44, 45, 36, 56, 45, 55, 38, 9, 37, 56, 55, 23, 22, 56, 23, 48, 45, 56, 48, 56, 22, 45, 38, 37, 46, 38, 45], "vertices": [1, 4, 248.18, -61.55, 1, 1, 4, 234.64, -70.32, 1, 1, 4, 210.37, -82.45, 1, 1, 4, 195.5, -84.26, 1, 1, 4, 184.2, -85.64, 1, 1, 4, 174.65, -98.31, 1, 1, 31, 13.3, -15.94, 1, 2, 31, 32.18, -19.94, 0.99971, 32, -48.04, -27.47, 0.00029, 2, 31, 47.94, -29.91, 0.95193, 32, -30.96, -34.97, 0.04807, 2, 31, 69.39, -41.56, 0.62368, 32, -8, -43.25, 0.37632, 2, 31, 86.97, -51.25, 0.29107, 32, 10.83, -50.19, 0.70893, 2, 31, 100.84, -54.68, 0.12716, 32, 25.06, -51.5, 0.87284, 2, 31, 110.25, -61.89, 0.0598, 32, 35.45, -57.21, 0.9402, 2, 31, 114.68, -61.29, 0.04939, 32, 39.74, -55.95, 0.95061, 2, 31, 128.68, -59.39, 0.01715, 32, 53.29, -51.97, 0.98285, 2, 31, 145.29, -48.21, 0.00064, 32, 68.04, -38.41, 0.99936, 1, 32, 78.77, -23.5, 1, 1, 32, 75.73, -6.86, 1, 1, 32, 74.41, 15.75, 1, 1, 32, 64.12, 26.1, 1, 1, 32, 44.5, 23.92, 1, 2, 31, 102.58, 15.31, 0.01755, 32, 16.26, 17.96, 0.98245, 2, 31, 82.25, 18.93, 0.67945, 32, -4.39, 18.48, 0.32055, 1, 31, 54.27, 20.06, 1, 1, 31, 31.67, 20.97, 1, 1, 31, 4.12, 14.97, 1, 1, 31, -23.74, 9.98, 1, 1, 31, -43.68, -3.23, 1, 1, 4, 238.26, -103.67, 1, 1, 4, 248.52, -90.62, 1, 1, 4, 253.91, -75.12, 1, 1, 4, 253.75, -61.49, 1, 1, 4, 182.82, -92.34, 1, 1, 31, 1.63, -17.32, 1, 1, 31, 13.92, -9.39, 1, 2, 31, 37.83, -10.73, 0.99991, 32, -43.84, -17.52, 9e-05, 2, 31, 54.29, -15.51, 0.9754, 32, -26.85, -19.77, 0.0246, 2, 31, 65.46, -26.25, 0.81498, 32, -14.19, -28.71, 0.18502, 2, 31, 77.69, -33.86, 0.53866, 32, -0.95, -34.39, 0.46134, 2, 31, 97.19, -42.32, 0.19149, 32, 19.59, -39.82, 0.80851, 2, 31, 109.07, -54.02, 0.07444, 32, 33.1, -49.61, 0.92556, 1, 31, -30.82, -12.92, 1, 1, 31, -13.19, -1.66, 1, 1, 31, 12.1, 6.17, 1, 1, 31, 37.26, 4.12, 1, 2, 31, 74.91, -4.42, 0.95776, 32, -8.13, -5.71, 0.04224, 2, 31, 102.06, -15.14, 0.04848, 32, 20.32, -12.23, 0.95152, 2, 31, 125.09, -24.92, 0.00519, 32, 44.56, -18.43, 0.99481, 2, 31, 98.99, 5.54, 0.00641, 32, 14.18, 7.76, 0.99359, 1, 32, 33.7, 8.62, 1, 1, 32, 57.13, 2.59, 1, 1, 32, 65.89, -12.66, 1, 2, 31, 138.55, -39.51, 0.00131, 32, 60.06, -30.83, 0.99869, 2, 31, 128.09, -47.16, 0.0131, 32, 50.87, -39.96, 0.9869, 1, 31, 36.17, 9.37, 1, 1, 31, 55.36, 14.06, 1, 2, 31, 79.16, 10.76, 0.82582, 32, -6.21, 9.94, 0.17418, 1, 4, 226, -90.85, 1], "hull": 32, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 4, 6, 6, 8, 6, 64, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 24, 26, 26, 28, 80, 26, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110, 110, 112], "width": 85, "height": 234}}, "bg_hua2": {"bg_hua": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.90909, 0, 0.81818, 0, 0.72727, 0, 0.63636, 0, 0.54545, 0, 0.45455, 0, 0.36364, 0, 0.27273, 0, 0.18182, 0, 0.09091, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.09091, 1, 0.18182, 1, 0.27273, 1, 0.36364, 1, 0.45455, 1, 0.54545, 1, 0.63636, 1, 0.72727, 1, 0.81818, 1, 0.90909, 0.75, 0.90909, 0.5, 0.90909, 0.25, 0.90909, 0.75, 0.81818, 0.5, 0.81818, 0.25, 0.81818, 0.75, 0.72727, 0.5, 0.72727, 0.25, 0.72727, 0.75, 0.63636, 0.5, 0.63636, 0.25, 0.63636, 0.75, 0.54545, 0.5, 0.54545, 0.25, 0.54545, 0.75, 0.45455, 0.5, 0.45455, 0.25, 0.45455, 0.75, 0.36364, 0.5, 0.36364, 0.25, 0.36364, 0.75, 0.27273, 0.5, 0.27273, 0.25, 0.27273, 0.75, 0.18182, 0.5, 0.18182, 0.25, 0.18182, 0.75, 0.09091, 0.5, 0.09091, 0.25, 0.09091], "triangles": [14, 15, 16, 59, 14, 16, 59, 16, 17, 58, 59, 17, 56, 59, 58, 58, 17, 18, 13, 14, 59, 56, 13, 59, 12, 13, 56, 53, 12, 56, 55, 56, 58, 53, 56, 55, 57, 58, 18, 55, 58, 57, 54, 55, 57, 52, 55, 54, 57, 18, 19, 20, 57, 19, 54, 57, 20, 21, 54, 20, 11, 12, 53, 50, 11, 53, 10, 11, 50, 47, 10, 50, 9, 10, 47, 52, 53, 55, 50, 53, 52, 49, 50, 52, 47, 50, 49, 46, 47, 49, 44, 47, 46, 49, 52, 51, 46, 49, 48, 51, 52, 54, 48, 49, 51, 51, 54, 21, 22, 51, 21, 48, 51, 22, 23, 48, 22, 45, 48, 23, 45, 46, 48, 43, 46, 45, 42, 43, 45, 40, 43, 42, 24, 45, 23, 42, 45, 24, 25, 42, 24, 39, 42, 25, 26, 39, 25, 5, 6, 35, 32, 5, 35, 4, 5, 32, 3, 4, 32, 32, 35, 34, 31, 32, 34, 3, 32, 31, 2, 3, 31, 33, 34, 36, 31, 34, 33, 30, 31, 33, 2, 31, 30, 1, 2, 30, 30, 33, 28, 1, 30, 29, 0, 1, 29, 44, 9, 47, 8, 9, 44, 41, 8, 44, 7, 8, 41, 38, 7, 41, 6, 7, 38, 35, 6, 38, 43, 44, 46, 41, 44, 43, 40, 41, 43, 38, 41, 40, 37, 38, 40, 35, 38, 37, 34, 35, 37, 39, 40, 42, 37, 40, 39, 36, 37, 39, 34, 37, 36, 36, 39, 26, 33, 36, 27, 27, 36, 26, 28, 33, 27, 29, 30, 28], "vertices": [2, 91, -101.54, 8.38, 0.3584, 100, 22.66, 113.25, 0.6416, 2, 91, -92.31, 72.46, 0.1603, 100, 77.49, 78.81, 0.8397, 3, 91, -83.07, 136.55, 0.0111, 99, 6.95, 185.12, 0.03369, 100, 132.32, 44.36, 0.95521, 2, 99, 65.29, 213.19, 0.15318, 100, 187.15, 9.92, 0.84682, 2, 99, 123.64, 241.27, 0.20976, 100, 241.98, -24.53, 0.79024, 3, 99, 147.8, 191.05, 0.28794, 100, 212.33, -71.71, 0.7082, 97, -8.04, 398.47, 0.00386, 4, 99, 171.96, 140.83, 0.47708, 100, 182.69, -118.9, 0.49717, 92, -65.67, 255.15, 0.00019, 97, 17.64, 349.01, 0.02555, 4, 99, 196.12, 90.62, 0.6454, 100, 153.04, -166.09, 0.2551, 92, -11.36, 242.66, 0.01311, 97, 43.31, 299.55, 0.08639, 4, 99, 220.29, 40.4, 0.64791, 100, 123.4, -213.28, 0.08575, 92, 42.95, 230.16, 0.06139, 97, 68.98, 250.09, 0.20495, 4, 99, 244.45, -9.82, 0.49401, 100, 93.76, -260.47, 0.01578, 92, 97.25, 217.66, 0.11826, 97, 94.66, 200.63, 0.37196, 5, 99, 268.61, -60.04, 0.30575, 100, 64.11, -307.66, 0.00058, 92, 151.56, 205.16, 0.12759, 97, 120.33, 151.17, 0.55859, 96, 140.46, 176.69, 0.00749, 4, 99, 292.77, -110.25, 0.15021, 92, 205.87, 192.66, 0.07859, 97, 146.01, 101.71, 0.68669, 96, 137.01, 121.07, 0.08451, 4, 99, 316.93, -160.47, 0.04644, 92, 260.18, 180.16, 0.02109, 97, 171.68, 52.25, 0.54806, 96, 133.55, 65.45, 0.38441, 5, 99, 341.09, -210.69, 0.00519, 92, 314.48, 167.66, 0.00078, 97, 197.36, 2.79, 0.14329, 96, 130.1, 9.83, 0.82464, 95, 50.32, 131.24, 0.02611, 3, 97, 223.03, -46.67, 0.00063, 96, 126.64, -45.79, 0.68297, 95, 95.61, 98.77, 0.31641, 2, 96, 123.19, -101.41, 0.4436, 95, 140.9, 66.3, 0.5564, 2, 96, 58.56, -97.4, 0.15276, 95, 103.17, 13.68, 0.84724, 2, 94, 143.39, -28.65, 0.13681, 95, 65.45, -38.94, 0.86319, 3, 93, 221.21, -68.94, 0.01074, 94, 114.01, -86.35, 0.93724, 95, 27.72, -91.57, 0.05203, 2, 93, 200.5, -130.28, 0.03764, 94, 84.64, -144.05, 0.96236, 2, 93, 147.7, -112.46, 0.13061, 94, 34.97, -118.77, 0.86939, 2, 93, 94.9, -94.63, 0.43705, 94, -14.69, -93.49, 0.56295, 3, 92, 202.08, -72.24, 0.02513, 93, 42.1, -76.8, 0.82113, 94, -64.35, -68.2, 0.15374, 3, 92, 147.78, -59.74, 0.48876, 93, -10.7, -58.97, 0.50697, 94, -114.01, -42.92, 0.00427, 2, 92, 93.47, -47.24, 0.9866, 93, -63.5, -41.14, 0.0134, 2, 91, 174.24, -31.38, 0.0008, 92, 39.16, -34.74, 0.9992, 2, 91, 119.08, -23.42, 0.84977, 92, -15.15, -22.24, 0.15023, 1, 91, 63.93, -15.47, 1, 1, 91, 8.77, -7.52, 1, 2, 91, -46.39, 0.43, 0.51351, 100, -6.98, 66.06, 0.48649, 2, 91, -37.15, 64.51, 0.10571, 100, 47.85, 31.62, 0.89429, 3, 91, -27.91, 128.6, 0.00098, 99, 31.11, 134.9, 0.07829, 100, 102.68, -2.83, 0.92074, 3, 99, 89.45, 162.98, 0.23427, 100, 157.5, -37.27, 0.76395, 97, -65.51, 368.64, 0.00177, 2, 99, -3.08, 56.61, 0.15233, 100, 18.2, -15.57, 0.84767, 3, 99, 55.27, 84.69, 0.36776, 100, 73.03, -50.01, 0.6312, 97, -97.3, 289.35, 0.00104, 3, 99, 113.62, 112.76, 0.4587, 100, 127.86, -84.46, 0.52595, 97, -39.83, 319.18, 0.01535, 2, 99, 21.08, 6.4, 0.96265, 100, -11.44, -62.76, 0.03735, 3, 99, 79.43, 34.47, 0.81802, 100, 43.39, -97.2, 0.17498, 97, -71.63, 239.89, 0.007, 4, 99, 137.78, 62.54, 0.68913, 100, 98.22, -131.65, 0.24083, 92, -25.88, 179.56, 0.00935, 97, -14.16, 269.72, 0.06069, 4, 91, 128.32, 40.66, 0.20276, 99, 45.24, -43.82, 0.47179, 92, -0.62, 40.86, 0.32149, 97, -103.42, 160.6, 0.00395, 5, 91, 137.56, 104.75, 9e-05, 99, 103.59, -15.75, 0.80496, 100, 13.74, -144.39, 0.0057, 92, 13.9, 103.96, 0.12485, 97, -45.95, 190.43, 0.0644, 4, 99, 161.94, 12.33, 0.69411, 100, 68.57, -178.84, 0.06251, 92, 28.42, 167.06, 0.07652, 97, 11.51, 220.26, 0.16686, 3, 99, 69.4, -94.04, 0.11904, 92, 53.68, 28.36, 0.8519, 97, -77.75, 111.14, 0.02906, 3, 99, 127.75, -65.96, 0.40711, 92, 68.21, 91.46, 0.39273, 97, -20.28, 140.97, 0.20016, 4, 99, 186.1, -37.89, 0.48567, 100, 38.93, -226.02, 0.00799, 92, 82.73, 154.56, 0.17463, 97, 37.19, 170.8, 0.33172, 3, 99, 93.56, -144.26, 0.01978, 92, 107.99, 15.86, 0.92958, 97, -52.07, 61.67, 0.05064, 3, 99, 151.91, -116.18, 0.16658, 92, 122.51, 78.96, 0.42689, 97, 5.39, 91.51, 0.40653, 5, 99, 210.26, -88.11, 0.27273, 100, 9.28, -273.21, 5e-05, 92, 137.04, 142.06, 0.18959, 97, 62.86, 121.34, 0.53656, 96, 75.83, 180.7, 0.00107, 3, 99, 117.73, -194.47, 0.00047, 93, 10.02, 2.37, 0.97068, 97, -26.4, 12.21, 0.02885, 3, 99, 176.07, -166.4, 0.04833, 92, 176.82, 66.46, 0.17378, 97, 31.07, 42.05, 0.7779, 4, 99, 234.42, -138.33, 0.12096, 92, 191.34, 129.56, 0.1043, 97, 88.54, 71.88, 0.74998, 96, 72.38, 125.08, 0.02475, 2, 93, 62.82, -15.45, 0.96468, 94, -34.97, -10.5, 0.03532, 4, 93, 83.53, 45.89, 0.02608, 97, 56.74, -7.42, 0.92189, 94, -5.6, 47.2, 0.03648, 96, 4.3, 73.47, 0.01555, 4, 99, 258.58, -188.54, 0.02615, 92, 245.65, 117.06, 0.01582, 97, 114.21, 22.42, 0.74767, 96, 68.93, 69.46, 0.21036, 2, 93, 115.61, -33.28, 0.23516, 94, 14.69, -35.78, 0.76484, 3, 97, 82.42, -56.88, 0.08831, 94, 44.06, 21.92, 0.25565, 96, 0.85, 17.85, 0.65604, 3, 99, 282.74, -238.76, 0.00075, 97, 139.89, -27.04, 0.11039, 96, 65.47, 13.84, 0.88887, 2, 93, 168.41, -51.11, 0.05007, 94, 64.35, -61.07, 0.94993, 2, 94, 93.73, -3.36, 0.06112, 95, 20.16, -6.47, 0.93888, 2, 96, 62.02, -41.78, 0.51831, 95, 57.89, 46.15, 0.48169], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 259, "height": 613}}, "toufa_hou_zuo": {"toufa_hou_zuo": {"type": "mesh", "uvs": [0.79813, 0, 0.87279, 0.01935, 0.93659, 0.07761, 0.98003, 0.12728, 1, 0.20273, 1, 0.29155, 1, 0.36414, 0.92981, 0.43482, 0.84836, 0.41953, 0.75605, 0.40712, 0.69439, 0.45077, 0.65909, 0.53482, 0.65502, 0.63033, 0.64959, 0.72201, 0.62516, 0.7946, 0.58579, 0.87101, 0.54914, 0.96079, 0.50163, 1, 0.39168, 1, 0.2858, 1, 0.23286, 0.94646, 0.16634, 0.8328, 0.14734, 0.73348, 0.19349, 0.64943, 0.11747, 0.59594, 0.0496, 0.51572, 0.00209, 0.45077, 0, 0.37627, 0.08761, 0.35048, 0.25865, 0.30751, 0.42018, 0.28076, 0.54371, 0.23736, 0.6428, 0.17719, 0.70253, 0.1046, 0.74785, 0.00392, 0.21551, 0.60659, 0.15689, 0.51806, 0.19979, 0.4235, 0.31131, 0.35006, 0.35277, 0.33799, 0.53007, 0.33296, 0.35563, 0.37622, 0.26127, 0.41545, 0.25841, 0.4899, 0.3013, 0.48889, 0.43141, 0.39634, 0.39567, 0.5744, 0.31417, 0.66293, 0.27843, 0.76051, 0.31703, 0.82288, 0.42998, 0.84903, 0.57296, 0.83897, 0.22838, 0.77962, 0.27128, 0.86815, 0.37565, 0.91241, 0.45572, 0.92549, 0.51546, 0.93519, 0.34817, 0.66499, 0.40679, 0.77263, 0.51975, 0.80683, 0.58266, 0.44468, 0.52118, 0.53119, 0.48829, 0.61972, 0.52833, 0.72032, 0.83716, 0.08655, 0.73707, 0.22135, 0.61268, 0.2978, 0.87855, 0.14038, 0.8748, 0.22621, 0.87855, 0.3292, 0.78284, 0.31995, 0.10727, 0.37409, 0.05473, 0.43878], "triangles": [66, 31, 32, 32, 33, 65, 64, 65, 33, 33, 34, 64, 34, 0, 64, 64, 1, 2, 64, 0, 1, 44, 41, 45, 42, 38, 41, 38, 37, 29, 45, 41, 40, 38, 39, 41, 40, 39, 30, 40, 41, 39, 38, 29, 39, 39, 29, 30, 40, 31, 66, 40, 30, 31, 25, 72, 36, 36, 37, 43, 36, 72, 37, 25, 26, 72, 43, 42, 44, 43, 37, 42, 44, 42, 41, 26, 27, 72, 72, 71, 37, 71, 27, 28, 71, 72, 27, 38, 42, 37, 37, 71, 29, 71, 28, 29, 23, 35, 47, 23, 24, 35, 35, 43, 44, 24, 36, 35, 35, 36, 43, 24, 25, 36, 18, 55, 17, 17, 56, 16, 17, 55, 56, 19, 54, 18, 18, 54, 55, 19, 20, 54, 16, 56, 15, 20, 53, 54, 56, 55, 15, 54, 50, 55, 15, 50, 51, 15, 55, 50, 54, 49, 50, 15, 51, 14, 50, 59, 51, 50, 58, 59, 51, 59, 14, 59, 63, 14, 14, 63, 13, 20, 21, 53, 53, 49, 54, 21, 52, 53, 53, 52, 49, 49, 58, 50, 21, 22, 52, 52, 48, 49, 49, 48, 58, 59, 58, 63, 48, 57, 58, 48, 47, 57, 52, 22, 48, 58, 57, 63, 22, 23, 48, 48, 23, 47, 57, 62, 63, 57, 47, 46, 13, 63, 12, 63, 62, 12, 62, 57, 46, 47, 35, 46, 46, 35, 44, 62, 61, 12, 12, 61, 11, 62, 46, 61, 46, 44, 61, 61, 60, 11, 11, 60, 10, 44, 45, 61, 61, 45, 60, 45, 40, 60, 10, 60, 66, 60, 40, 66, 9, 10, 66, 6, 7, 69, 9, 70, 8, 7, 8, 69, 8, 70, 69, 9, 66, 70, 69, 5, 6, 5, 69, 68, 66, 65, 70, 69, 70, 68, 70, 65, 68, 66, 32, 65, 5, 68, 4, 68, 67, 4, 67, 3, 4, 68, 65, 67, 64, 67, 65, 67, 2, 3, 67, 64, 2], "vertices": [1, 8, -50.9, -32.78, 1, 1, 8, -69.66, 16.41, 1, 2, 20, -48.34, 19.07, 0.25399, 8, -19.08, 29.55, 0.74601, 2, 20, -16.57, 31.43, 0.91427, 8, 11.7, 44.18, 0.08573, 1, 20, 2.09, 49, 1, 1, 20, 27.05, 65.04, 1, 1, 20, 47.45, 78.14, 1, 2, 20, 76.23, 77.02, 0.99597, 21, -30.25, 77.17, 0.00403, 2, 20, 82.28, 58.16, 0.9716, 21, -24.1, 58.34, 0.0284, 2, 20, 90.51, 37.67, 0.7833, 21, -15.75, 37.9, 0.2167, 4, 20, 110.61, 33.35, 0.19889, 21, 4.38, 33.7, 0.80049, 22, -63.23, 79.41, 0.00027, 23, 15.51, 154.71, 0.00035, 3, 21, 32.43, 42.05, 0.95553, 22, -35.03, 71.59, 0.02972, 23, 20.33, 125.84, 0.01474, 3, 21, 59.7, 58.64, 0.71473, 22, -3.12, 71.16, 0.1878, 23, 33.42, 96.74, 0.09747, 3, 21, 86.06, 74.26, 0.36794, 22, 27.53, 70.39, 0.33551, 23, 45.67, 68.64, 0.29655, 3, 21, 109.52, 82.67, 0.15139, 22, 51.86, 65.06, 0.28491, 23, 51.12, 44.33, 0.56369, 3, 21, 135.95, 88.83, 0.02407, 22, 77.53, 56.23, 0.07226, 23, 53.96, 17.33, 0.90367, 1, 23, 59.33, -13.4, 1, 1, 23, 55.02, -30.06, 1, 1, 23, 31.79, -41.36, 1, 2, 22, 121.78, -13.54, 0.03898, 23, 9.41, -52.25, 0.96102, 3, 22, 104.11, -26.28, 0.21297, 23, -9.6, -41.61, 0.7863, 19, 149.88, -35.12, 0.00073, 3, 22, 66.42, -42.54, 0.8754, 23, -40.26, -14.31, 0.07337, 19, 108.87, -33.18, 0.05123, 2, 22, 33.32, -47.56, 0.79161, 19, 76.93, -23.15, 0.20839, 3, 21, 124.18, -28.81, 0.00997, 22, 5.07, -37.18, 0.29086, 19, 56.11, -1.42, 0.69916, 1, 19, 32.36, -10.02, 1, 2, 19, 1.33, -13.1, 0.73258, 18, 72.4, -4.79, 0.26742, 2, 19, -23.05, -14.01, 0.02415, 18, 62.1, -26.9, 0.97585, 1, 18, 42.74, -42.54, 1, 1, 18, 23.31, -31.54, 1, 2, 18, -12.66, -8.57, 0.09361, 17, 80.47, -13.74, 0.90639, 1, 17, 41.96, -7.59, 1, 1, 17, 9.6, -9.97, 1, 2, 17, -19.58, -19.72, 0.02928, 8, 72.59, -9.22, 0.97072, 1, 8, 44.77, -12.53, 1, 1, 8, -18.74, -29.55, 1, 4, 21, 109.36, -32.27, 0.10662, 22, -9.32, -32.24, 0.05708, 19, 45.35, 9.34, 0.82672, 18, 72.5, 44.62, 0.00958, 3, 21, 92.08, -59.94, 0.03606, 19, 12.73, 9.4, 0.74152, 18, 57.57, 15.62, 0.22243, 4, 21, 60.11, -68.71, 0.01762, 19, -11.6, 31.93, 0.01171, 18, 26.43, 4.23, 0.96653, 17, 107.98, 16.84, 0.00415, 4, 21, 25.26, -60.11, 0.02245, 19, -22.69, 66.06, 0.00025, 18, -9.01, 9.92, 0.0358, 17, 74.42, 4.11, 0.9415, 4, 21, 16.57, -54.14, 0.02806, 19, -22.21, 76.6, 3e-05, 18, -18.16, 15.15, 0.0092, 17, 63.87, 4.08, 0.96271, 4, 20, 98.38, -20.4, 0.24971, 21, -7.56, -20.13, 0.21171, 17, 24.69, 18.35, 0.50286, 8, 130.11, 0.81, 0.03572, 4, 21, 26.9, -46.61, 0.18388, 19, -10.36, 71.79, 0.00248, 18, -8.49, 23.5, 0.1031, 17, 68.1, 16.15, 0.71054, 4, 21, 49.98, -58.06, 0.10822, 19, -7.9, 46.15, 0.02453, 18, 15.46, 14, 0.74984, 17, 93.59, 19.84, 0.11741, 4, 21, 71.19, -45.07, 0.29726, 19, 14.33, 35, 0.24307, 18, 35.52, 28.71, 0.43448, 17, 103.66, 42.59, 0.02519, 4, 21, 65.41, -36.8, 0.45738, 19, 18.3, 44.27, 0.1606, 18, 29.08, 36.47, 0.32963, 17, 94.2, 46.11, 0.05239, 4, 21, 22.83, -28.02, 0.52854, 19, 3.28, 85.07, 0.0012, 18, -14.09, 41.69, 0.04157, 17, 54.18, 29.13, 0.42869, 4, 21, 77.26, -2.64, 0.98565, 19, 53.57, 52.24, 0.00936, 18, 38.06, 71.49, 0.00463, 17, 84.54, 80.95, 0.00036, 3, 21, 112.49, -2.57, 0.01315, 22, 9.11, -8.75, 0.88023, 19, 72.22, 22.35, 0.10662, 2, 22, 41.83, -16.6, 0.93861, 19, 98.18, 0.92, 0.06139, 3, 22, 62.51, -7.19, 0.98182, 23, -9.87, 4.16, 0.01261, 19, 120.89, 0.3, 0.00557, 3, 21, 149.76, 54.13, 0.00346, 22, 70.8, 19.5, 0.11439, 23, 17.83, 7.92, 0.88216, 3, 21, 128.63, 80.47, 0.05289, 22, 66.88, 53.04, 0.15311, 23, 46.57, 25.64, 0.794, 3, 22, 48.41, -28.26, 0.91123, 23, -34.92, 8.04, 0.00146, 19, 98.97, -12.44, 0.08731, 3, 22, 77.81, -17.68, 0.60448, 23, -12.92, -14.14, 0.38775, 19, 130.02, -15.85, 0.00778, 2, 22, 92.18, 7.09, 0.00563, 23, 15.6, -16.7, 0.99437, 1, 23, 34.43, -12.4, 1, 1, 23, 48.48, -9.17, 1, 3, 22, 9.66, -0.75, 0.99526, 19, 76.24, 29.29, 0.00474, 18, 68.81, 81.21, 0, 3, 21, 131.34, 35.65, 0.02083, 22, 45.38, 13.63, 0.8146, 23, 1.76, 28.48, 0.16457, 3, 21, 126.45, 64.13, 0.06859, 22, 56.36, 40.36, 0.29053, 23, 30.63, 29.82, 0.64087, 3, 21, 16.99, 10.58, 0.9997, 22, -64.83, 53.12, 0.0001, 23, -8.99, 145.06, 0.0002, 3, 21, 49.09, 14.22, 0.98164, 22, -35.7, 39.16, 0.01271, 23, -9.34, 112.75, 0.00565, 3, 21, 78.09, 23.86, 0.77832, 22, -6, 31.92, 0.18487, 23, -3.36, 82.78, 0.03681, 3, 21, 101.12, 50.07, 0.28297, 22, 27.43, 41.89, 0.46788, 23, 19.8, 56.69, 0.24915, 2, 20, -9.87, -4.17, 0.42862, 8, 20.97, 9.16, 0.57138, 1, 20, 40.72, 0.38, 1, 3, 20, 78, -10.42, 0.51413, 17, 2.27, 14.86, 0.08082, 8, 109.07, 9.29, 0.40505, 2, 20, 0, 13.73, 0.99653, 8, 29.52, 27.73, 0.00347, 1, 20, 24.59, 28.48, 1, 2, 20, 53.06, 47.82, 0.99628, 21, -53.26, 47.84, 0.00373, 2, 20, 62.62, 27.23, 0.98645, 21, -43.58, 27.3, 0.01355, 1, 18, 26.71, -23.06, 1, 2, 19, -21.43, -1.11, 0.00635, 18, 51.36, -19.58, 0.99365], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 70, 88, 88, 90, 90, 80, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 104, 106, 106, 108, 108, 110, 110, 112, 92, 114, 114, 116, 116, 118, 120, 122, 122, 124, 124, 126, 128, 130, 130, 132, 134, 136, 136, 138, 142, 144], "width": 235, "height": 334}}, "bg_hua": {"bg_hua": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.90909, 0, 0.81818, 0, 0.72727, 0, 0.63636, 0, 0.54545, 0, 0.45455, 0, 0.36364, 0, 0.27273, 0, 0.18182, 0, 0.09091, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.09091, 1, 0.18182, 1, 0.27273, 1, 0.36364, 1, 0.45455, 1, 0.54545, 1, 0.63636, 1, 0.72727, 1, 0.81818, 1, 0.90909, 0.75, 0.90909, 0.5, 0.90909, 0.25, 0.90909, 0.75, 0.81818, 0.5, 0.81818, 0.25, 0.81818, 0.75, 0.72727, 0.5, 0.72727, 0.25, 0.72727, 0.75, 0.63636, 0.5, 0.63636, 0.25, 0.63636, 0.75, 0.54545, 0.5, 0.54545, 0.25, 0.54545, 0.75, 0.45455, 0.5, 0.45455, 0.25, 0.45455, 0.75, 0.36364, 0.5, 0.36364, 0.25, 0.36364, 0.75, 0.27273, 0.5, 0.27273, 0.25, 0.27273, 0.75, 0.18182, 0.5, 0.18182, 0.25, 0.18182, 0.75, 0.09091, 0.5, 0.09091, 0.25, 0.09091], "triangles": [14, 15, 16, 59, 14, 16, 59, 16, 17, 58, 59, 17, 56, 59, 58, 58, 17, 18, 13, 14, 59, 56, 13, 59, 12, 13, 56, 53, 12, 56, 55, 56, 58, 53, 56, 55, 57, 58, 18, 55, 58, 57, 54, 55, 57, 52, 55, 54, 57, 18, 19, 20, 57, 19, 54, 57, 20, 21, 54, 20, 11, 12, 53, 50, 11, 53, 10, 11, 50, 47, 10, 50, 9, 10, 47, 52, 53, 55, 50, 53, 52, 49, 50, 52, 47, 50, 49, 46, 47, 49, 44, 47, 46, 49, 52, 51, 46, 49, 48, 51, 52, 54, 48, 49, 51, 51, 54, 21, 22, 51, 21, 48, 51, 22, 23, 48, 22, 45, 48, 23, 45, 46, 48, 43, 46, 45, 42, 43, 45, 40, 43, 42, 24, 45, 23, 42, 45, 24, 25, 42, 24, 39, 42, 25, 26, 39, 25, 5, 6, 35, 32, 5, 35, 4, 5, 32, 3, 4, 32, 32, 35, 34, 31, 32, 34, 3, 32, 31, 2, 3, 31, 33, 34, 36, 31, 34, 33, 30, 31, 33, 2, 31, 30, 1, 2, 30, 30, 33, 28, 1, 30, 29, 0, 1, 29, 44, 9, 47, 8, 9, 44, 41, 8, 44, 7, 8, 41, 38, 7, 41, 6, 7, 38, 35, 6, 38, 43, 44, 46, 41, 44, 43, 40, 41, 43, 38, 41, 40, 37, 38, 40, 35, 38, 37, 34, 35, 37, 39, 40, 42, 37, 40, 39, 36, 37, 39, 34, 37, 36, 36, 39, 26, 33, 36, 27, 27, 36, 26, 28, 33, 27, 29, 30, 28], "vertices": [2, 73, -101.54, 8.38, 0.3584, 82, 22.66, 113.25, 0.6416, 2, 73, -92.31, 72.46, 0.1603, 82, 77.49, 78.81, 0.8397, 3, 73, -83.07, 136.55, 0.0111, 81, 6.95, 185.12, 0.03369, 82, 132.32, 44.36, 0.95521, 2, 81, 65.29, 213.19, 0.15318, 82, 187.15, 9.92, 0.84682, 2, 81, 123.64, 241.27, 0.20976, 82, 241.98, -24.53, 0.79024, 3, 81, 147.8, 191.05, 0.28794, 82, 212.33, -71.71, 0.7082, 79, -8.04, 398.47, 0.00386, 4, 81, 171.96, 140.83, 0.47708, 82, 182.69, -118.9, 0.49717, 74, -65.67, 255.15, 0.00019, 79, 17.64, 349.01, 0.02555, 4, 81, 196.12, 90.62, 0.6454, 82, 153.04, -166.09, 0.2551, 74, -11.36, 242.66, 0.01311, 79, 43.31, 299.55, 0.08639, 4, 81, 220.29, 40.4, 0.64791, 82, 123.4, -213.28, 0.08575, 74, 42.95, 230.16, 0.06139, 79, 68.98, 250.09, 0.20495, 4, 81, 244.45, -9.82, 0.49401, 82, 93.76, -260.47, 0.01578, 74, 97.25, 217.66, 0.11826, 79, 94.66, 200.63, 0.37196, 5, 81, 268.61, -60.04, 0.30575, 82, 64.11, -307.66, 0.00058, 74, 151.56, 205.16, 0.12759, 79, 120.33, 151.17, 0.55859, 78, 140.46, 176.69, 0.00749, 4, 81, 292.77, -110.25, 0.15021, 74, 205.87, 192.66, 0.07859, 79, 146.01, 101.71, 0.68669, 78, 137.01, 121.07, 0.08451, 4, 81, 316.93, -160.47, 0.04644, 74, 260.18, 180.16, 0.02109, 79, 171.68, 52.25, 0.54806, 78, 133.55, 65.45, 0.38441, 5, 81, 341.09, -210.69, 0.00519, 74, 314.48, 167.66, 0.00078, 79, 197.36, 2.79, 0.14329, 78, 130.1, 9.83, 0.82464, 77, 50.32, 131.24, 0.02611, 3, 79, 223.03, -46.67, 0.00063, 78, 126.64, -45.79, 0.68297, 77, 95.61, 98.77, 0.31641, 2, 78, 123.19, -101.41, 0.4436, 77, 140.9, 66.3, 0.5564, 2, 78, 58.56, -97.4, 0.15276, 77, 103.17, 13.68, 0.84724, 2, 76, 143.39, -28.65, 0.13681, 77, 65.45, -38.94, 0.86319, 3, 75, 221.21, -68.94, 0.01074, 76, 114.01, -86.35, 0.93724, 77, 27.72, -91.57, 0.05203, 2, 75, 200.5, -130.28, 0.03764, 76, 84.64, -144.05, 0.96236, 2, 75, 147.7, -112.46, 0.13061, 76, 34.97, -118.77, 0.86939, 2, 75, 94.9, -94.63, 0.43705, 76, -14.69, -93.49, 0.56295, 3, 74, 202.08, -72.24, 0.02513, 75, 42.1, -76.8, 0.82113, 76, -64.35, -68.2, 0.15374, 3, 74, 147.78, -59.74, 0.48876, 75, -10.7, -58.97, 0.50697, 76, -114.01, -42.92, 0.00427, 2, 74, 93.47, -47.24, 0.9866, 75, -63.5, -41.14, 0.0134, 2, 73, 174.24, -31.38, 0.0008, 74, 39.16, -34.74, 0.9992, 2, 73, 119.08, -23.42, 0.84977, 74, -15.15, -22.24, 0.15023, 1, 73, 63.93, -15.47, 1, 1, 73, 8.77, -7.52, 1, 2, 73, -46.39, 0.43, 0.51351, 82, -6.98, 66.06, 0.48649, 2, 73, -37.15, 64.51, 0.10571, 82, 47.85, 31.62, 0.89429, 3, 73, -27.91, 128.6, 0.00098, 81, 31.11, 134.9, 0.07829, 82, 102.68, -2.83, 0.92074, 3, 81, 89.45, 162.98, 0.23427, 82, 157.5, -37.27, 0.76395, 79, -65.51, 368.64, 0.00177, 2, 81, -3.08, 56.61, 0.15233, 82, 18.2, -15.57, 0.84767, 3, 81, 55.27, 84.69, 0.36776, 82, 73.03, -50.01, 0.6312, 79, -97.3, 289.35, 0.00104, 3, 81, 113.62, 112.76, 0.4587, 82, 127.86, -84.46, 0.52595, 79, -39.83, 319.18, 0.01535, 2, 81, 21.08, 6.4, 0.96265, 82, -11.44, -62.76, 0.03735, 3, 81, 79.43, 34.47, 0.81802, 82, 43.39, -97.2, 0.17498, 79, -71.63, 239.89, 0.007, 4, 81, 137.78, 62.54, 0.68913, 82, 98.22, -131.65, 0.24083, 74, -25.88, 179.56, 0.00935, 79, -14.16, 269.72, 0.06069, 4, 73, 128.32, 40.66, 0.20276, 81, 45.24, -43.82, 0.47179, 74, -0.62, 40.86, 0.32149, 79, -103.42, 160.6, 0.00395, 5, 73, 137.56, 104.75, 9e-05, 81, 103.59, -15.75, 0.80496, 82, 13.74, -144.39, 0.0057, 74, 13.9, 103.96, 0.12485, 79, -45.95, 190.43, 0.0644, 4, 81, 161.94, 12.33, 0.69411, 82, 68.57, -178.84, 0.06251, 74, 28.42, 167.06, 0.07652, 79, 11.51, 220.26, 0.16686, 3, 81, 69.4, -94.04, 0.11904, 74, 53.68, 28.36, 0.8519, 79, -77.75, 111.14, 0.02906, 3, 81, 127.75, -65.96, 0.40711, 74, 68.21, 91.46, 0.39273, 79, -20.28, 140.97, 0.20016, 4, 81, 186.1, -37.89, 0.48567, 82, 38.93, -226.02, 0.00799, 74, 82.73, 154.56, 0.17463, 79, 37.19, 170.8, 0.33172, 3, 81, 93.56, -144.26, 0.01978, 74, 107.99, 15.86, 0.92958, 79, -52.07, 61.67, 0.05064, 3, 81, 151.91, -116.18, 0.16658, 74, 122.51, 78.96, 0.42689, 79, 5.39, 91.51, 0.40653, 5, 81, 210.26, -88.11, 0.27273, 82, 9.28, -273.21, 5e-05, 74, 137.04, 142.06, 0.18959, 79, 62.86, 121.34, 0.53656, 78, 75.83, 180.7, 0.00107, 3, 81, 117.73, -194.47, 0.00047, 75, 10.02, 2.37, 0.97068, 79, -26.4, 12.21, 0.02885, 3, 81, 176.07, -166.4, 0.04833, 74, 176.82, 66.46, 0.17378, 79, 31.07, 42.05, 0.7779, 4, 81, 234.42, -138.33, 0.12096, 74, 191.34, 129.56, 0.1043, 79, 88.54, 71.88, 0.74998, 78, 72.38, 125.08, 0.02475, 2, 75, 62.82, -15.45, 0.96468, 76, -34.97, -10.5, 0.03532, 4, 75, 83.53, 45.89, 0.02608, 79, 56.74, -7.42, 0.92189, 76, -5.6, 47.2, 0.03648, 78, 4.3, 73.47, 0.01555, 4, 81, 258.58, -188.54, 0.02615, 74, 245.65, 117.06, 0.01582, 79, 114.21, 22.42, 0.74767, 78, 68.93, 69.46, 0.21036, 2, 75, 115.61, -33.28, 0.23516, 76, 14.69, -35.78, 0.76484, 3, 79, 82.42, -56.88, 0.08831, 76, 44.06, 21.92, 0.25565, 78, 0.85, 17.85, 0.65604, 3, 81, 282.74, -238.76, 0.00075, 79, 139.89, -27.04, 0.11039, 78, 65.47, 13.84, 0.88887, 2, 75, 168.41, -51.11, 0.05007, 76, 64.35, -61.07, 0.94993, 2, 76, 93.73, -3.36, 0.06112, 77, 20.16, -6.47, 0.93888, 2, 78, 62.02, -41.78, 0.51831, 77, 57.89, 46.15, 0.48169], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 259, "height": 613}}, "shuzhi": {"shuzhi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 88, -48.86, -11.75, 1, 3, 88, 494.77, 591.42, 0.04925, 89, 343.08, 491.58, 0.39539, 90, -287.54, 398.74, 0.55536, 2, 89, 637.14, -240.57, 0.00092, 90, 383.19, -16.76, 0.99908, 3, 88, 537.22, -539.98, 0.37145, 89, -116.35, -543.21, 0.57058, 90, -44.42, -707.04, 0.05797], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 812, "height": 789}}}}], "animations": {"animation1": {"bones": {"target3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -22.11, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -22.11, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -22.11, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -22.11, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone82": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone81": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone80": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone79": {"rotate": [{"angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9}, {"time": 10.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -0.62}]}, "bone78": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.76}]}, "bone76": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.76}]}, "bone75": {"rotate": [{"angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -0.62}]}, "bone74": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone73": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone72": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone68": {"rotate": [{"angle": -0.25}]}, "bone67": {"rotate": [{"angle": 0.13}]}, "bone66": {"rotate": [{"angle": -1.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -8.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 33.98, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 8, "angle": -1.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -8.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -1.57}]}, "bone65": {"rotate": [{"angle": 0.08}]}, "bone64": {"rotate": [{"angle": -0.18}], "translate": [{}, {"time": 1.3333, "x": 0.04, "y": -4.75}, {"time": 2.6667}, {"time": 4, "x": 0.04, "y": -4.75}, {"time": 5.3333}, {"time": 6.6667, "x": 0.04, "y": -4.75}, {"time": 8}, {"time": 9.3333, "x": 0.04, "y": -4.75}, {"time": 10.6667}]}, "bone63": {"rotate": [{"angle": 1.51, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "angle": -1.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.0333, "angle": 3.76, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": 1.51, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 3.3667, "angle": -1.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.7, "angle": 3.76, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.3333, "angle": 1.51, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 6.0333, "angle": -1.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7.3667, "angle": 3.76, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 8, "angle": 1.51, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 8.7, "angle": -1.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 10.0333, "angle": 3.76, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 10.6667, "angle": 1.51}], "translate": [{"x": 2.24, "y": -0.87}]}, "bone6": {"rotate": [{"angle": -0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -2.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -2.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -2.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 8.5, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": -2.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 10.6667, "angle": -0.68}]}, "bone5": {"rotate": [{"angle": -0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -2.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -2.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": 7.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -2.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.37}], "translate": [{"x": 0.82, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.46, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.82, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 4.46, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.82, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 4.46, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.82, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 4.46, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 0.82, "y": 0.01}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone60": {"rotate": [{"angle": -10.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -10.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -10.4}], "translate": [{"y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 3, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 10.6667, "y": 0.64}], "scale": [{"x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 1.025, "y": 1.006}]}, "bone59": {"rotate": [{"angle": -5.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -6.38, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -5.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -6.38, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -6.38, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": -6.38, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -5.2}], "translate": [{"y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 3, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 10.6667, "y": 0.64}], "scale": [{"x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 1.025, "y": 1.006}]}, "bone58": {"rotate": [{"angle": 6.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 8.43, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 6.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 8.43, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 8.43, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 8.43, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": 6.88}], "translate": [{"y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 3, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 10.6667, "y": 0.64}], "scale": [{"x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 1.025, "y": 1.006}]}, "bone57": {"rotate": [{"angle": -2.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -2.66}], "scale": [{"x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 1.025, "y": 1.006}]}, "bone56": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone55": {"rotate": [{}, {"time": 1.3333, "angle": 6.09}, {"time": 2.6667}, {"time": 4, "angle": 6.09}, {"time": 5.3333}, {"time": 6.6667, "angle": 6.09}, {"time": 8}, {"time": 9.3333, "angle": 6.09}, {"time": 10.6667}]}, "bone54": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone53": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone52": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone51": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone50": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone49": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone48": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone47": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone46": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone45": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone44": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone43": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone42": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone41": {"rotate": [{"angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.83, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 11.83, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 11.83, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 11.83, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 8.29}]}, "bone40": {"rotate": [{"angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 1.77}]}, "bone39": {"rotate": [{"angle": 0.65, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.65, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.65, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.65, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 0.65}]}, "bone38": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 7.74, "y": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 7.74, "y": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 7.74, "y": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 7.74, "y": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone37": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.87, "y": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -1.87, "y": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -3.74, "y": -4.54, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -3.74, "y": -4.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone71": {"translate": [{}, {"time": 1.3333, "x": -3.26, "y": -6.14}, {"time": 2.6667}, {"time": 4, "x": -3.26, "y": -6.14}, {"time": 5.3333}, {"time": 6.6667, "x": -3.26, "y": -6.14}, {"time": 8}, {"time": 9.3333, "x": -3.26, "y": -6.14}, {"time": 10.6667}]}, "bone69": {"translate": [{"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.8667, "x": -1.79, "y": -3.81, "curve": "stepped"}, {"time": 9.7333, "x": -1.79, "y": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone36": {"rotate": [{"angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 1.73}]}, "bone35": {"rotate": [{"angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -2.95}]}, "bone34": {"rotate": [{"angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.46}]}, "bone32": {"rotate": [{"angle": -0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.76}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone29": {"rotate": [{"angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -4.71}], "scale": [{"x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": 0.981, "y": 0.981}]}, "bone28": {"rotate": [{"angle": -3.84, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -4.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.84, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -4.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.84, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -4.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -3.84, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9}, {"time": 10.3333, "angle": -4.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -3.84}], "scale": [{"x": 0.984, "y": 0.984, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 0.981, "y": 0.981, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 0.984, "y": 0.984, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.981, "y": 0.981, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 0.984, "y": 0.984, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.981, "y": 0.981, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "x": 0.984, "y": 0.984, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": 0.981, "y": 0.981, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "x": 0.984, "y": 0.984}]}, "bone27": {"rotate": [{"angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}, {"time": 10, "angle": -4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -2.35}], "scale": [{"x": 0.99, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.99, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.99, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 0.99, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "x": 0.99, "y": 0.99}]}, "bone26": {"rotate": [{"angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -4.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -4.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -4.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.87}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -6.89, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone24": {"rotate": [{}, {"time": 1.3333, "angle": -0.41}, {"time": 2.6667}, {"time": 4, "angle": -0.41}, {"time": 5.3333}, {"time": 6.6667, "angle": -0.41}, {"time": 8}, {"time": 9.3333, "angle": -0.41}, {"time": 10.6667}]}, "bone23": {"rotate": [{"angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.83, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 4.14}]}, "bone22": {"rotate": [{"angle": 3.37, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 3.37, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 4.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 3.37, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 5.59, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.6667, "angle": 4.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": 3.37, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": 4.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": 3.37}]}, "bone21": {"rotate": [{"angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 6.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3333, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 2.07}]}, "bone20": {"rotate": [{"angle": 0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": 4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": 4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 6.6667, "angle": 7.93}, {"time": 7, "angle": 4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": 4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": 0.76}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone15": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -0.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2.6667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4, "angle": -0.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 5.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": -0.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 9.3333, "angle": -0.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 10.6667}]}, "bone14": {"rotate": [{"angle": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 1.7, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2.6667, "angle": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4, "angle": 1.7, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 5.3333, "angle": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 1.7, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 8, "angle": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 9.3333, "angle": 1.7, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 10.6667, "angle": 2.5}]}, "bone13": {"rotate": [{"angle": 2.04, "curve": 0.317, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 1.3333, "angle": 1.23, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 2.6667, "angle": 2.04, "curve": 0.317, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 4, "angle": 1.23, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 5.3333, "angle": 2.04, "curve": 0.317, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 6.6667, "angle": 1.23, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 8, "angle": 2.04, "curve": 0.317, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 9.3333, "angle": 1.23, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 10.6667, "angle": 2.04}]}, "bone12": {"rotate": [{"angle": 1.25, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 1.3333, "angle": 0.44, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.6667, "angle": 1.25, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": 0.44, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 5.3333, "angle": 1.25, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 6.6667, "angle": 0.44, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 8, "angle": 1.25, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 9.3333, "angle": 0.44, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 10.6667, "angle": 1.25}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -0.07, "y": -2.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.07, "y": -2.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": -0.07, "y": -2.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": -0.07, "y": -2.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.6667}]}, "bone11": {"rotate": [{"angle": 0.46, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 1.3333, "angle": -0.35, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 2.6667, "angle": 0.46, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 4, "angle": -0.35, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 5.3333, "angle": 0.46, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 6.6667, "angle": -0.35, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 8, "angle": 0.46, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 9.3333, "angle": -0.35, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 10.6667, "angle": 0.46}]}, "bone19": {"rotate": [{"angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.83, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 4.68}]}, "bone10": {"rotate": [{"angle": 3.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": 4.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 3.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": 4.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 3.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 6.6667, "angle": 5.99}, {"time": 7.6667, "angle": 4.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": 3.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9}, {"time": 10.3333, "angle": 4.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": 3.81}]}, "bone9": {"rotate": [{"angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": 4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": 4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 6.6667, "angle": 7.16}, {"time": 7.3333, "angle": 4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}, {"time": 10, "angle": 4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 2.34}]}, "bone8": {"rotate": [{"angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 4.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": 8.64, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7, "angle": 4.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 4.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": 0.86}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 20.85, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -9.71, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "angle": -5.15, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.52, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bone3": {"rotate": [{"angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -0.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -0.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -0.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.1}], "translate": [{"x": 0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 5.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "x": 0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 5.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": "stepped"}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 5.61, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 10.6667, "x": 0.66}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 6.44, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.9, "angle": 3.4, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 8.9, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.81, "y": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 2.81, "y": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 2.81, "y": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone": {"translate": [{}, {"time": 1.3333, "x": -1.33, "y": -5.34}, {"time": 2.6667}, {"time": 4, "x": -1.33, "y": -5.34}, {"time": 5.3333}, {"time": 6.6667, "x": -1.33, "y": -5.34}, {"time": 8}, {"time": 9.3333, "x": -1.33, "y": -5.34}, {"time": 10.6667}]}, "bone83": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone77": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone84": {"translate": [{}, {"time": 1.3333, "x": -8.08, "y": -6.1}, {"time": 2.6667}, {"time": 4, "x": -8.08, "y": -6.1}, {"time": 5.3333}, {"time": 6.6667, "x": -8.08, "y": -6.1}, {"time": 8}, {"time": 9.3333, "x": -8.08, "y": -6.1}, {"time": 10.6667}]}, "bone86": {"translate": [{"x": 9.96, "y": -17.92, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "x": 2.63, "y": -1.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.3333, "x": 11.61, "y": -21.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 9.96, "y": -17.92, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "x": 2.63, "y": -1.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5, "x": 11.61, "y": -21.75, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 2.63, "y": -1.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7.6667, "x": 11.61, "y": -21.75, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 2.63, "y": -1.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 10.3333, "x": 11.61, "y": -21.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "x": 9.96, "y": -17.92}]}, "bone87": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone88": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone89": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone94": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.76}]}, "bone95": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.76}]}, "bone93": {"rotate": [{"angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -0.62}]}, "bone96": {"rotate": [{"angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9}, {"time": 10.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -0.62}]}, "bone92": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone97": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone91": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone98": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone99": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone90": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone103": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.75, "y": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.75, "y": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 1.75, "y": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 1.75, "y": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone102": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -10.03, "y": 33.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -10.03, "y": 33.7, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -10.03, "y": 33.7, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -10.03, "y": 33.7, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone101": {"translate": [{}, {"time": 1.3333, "x": 6.33, "y": 8.46}, {"time": 2.6667}, {"time": 4, "x": 6.33, "y": 8.46}, {"time": 5.3333}, {"time": 6.6667, "x": 6.33, "y": 8.46}, {"time": 8}, {"time": 9.3333, "x": 6.33, "y": 8.46}, {"time": 10.6667}]}, "bone100": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -9.27, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -9.27, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -9.27, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -9.27, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "target2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -39.96, "y": 44.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -39.96, "y": 44.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": -110.82, "y": 27.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.5, "x": 245, "y": 40.16, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -39.96, "y": 44.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.6667}]}}, "deform": {"default": {"lian": {"lian": [{"time": 7.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "offset": 20, "vertices": [-6.81619, 4.62778, -6.81607, 4.62777, 0, 0, 0, 0, 1.33939, -0.48419, 1.33939, -0.48419, 1.33932, -0.48421, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.53943, 1.00578, -6.53909, 1.0057, -3.52681, 2.29313, -3.52661, 2.29315, -0.19089, 1.42247, -0.19083, 1.42248, -7.72655, 2.6795, -7.7262, 2.67953, -10.29802, 2.04577, -10.29773, 2.04573, -2.14581, 0.01637, -2.146, 0.01632, -1.27499, 0.12571, -1.27505, 0.12571, 0, 0, 0, 0, 0.7074, -0.47935, 0.7074, -0.47939, 3.95152, -0.87202, 3.95154, -0.87208, -5.82449, 2.70837, -5.82419, 2.7084, -6.44966, 1.84789, -6.4494, 1.84777, 1.97136, -0.48897, 1.97137, -0.48899, 0.23581, -0.15978, 0.23578, -0.15979, 0, 0, 0, 0, 0, 0, 0, 0, 5.78917, -1.326, 5.78918, -1.32605, 4.2211, -1.8471, 4.22119, -1.84717, 2.84039, -0.49557, 2.84039, -0.49561, 0, 0, 0, 0, -4.49785, -0.63578, -4.49805, -0.6358, -7.76622, -0.29685, -7.76639, -0.29691, -6.50412, 0.24454, -6.5043, 0.2445, -4.34474, 0.0331, -4.34479, 0.03308, 0.30414, 0.79254, 0.30414, 0.79246, 2.58609, -0.79549, 2.58612, -0.79553, 4.68655, -1.44945, 4.68698, -1.4496, 2.91357, 0.05694, 2.91354, 0.05685, -0.86365, -0.81219, -0.86395, -0.81242, -1.34413, -0.1478, -1.34402, -0.14778, -3.29758, 1.1207, -3.29767, 1.12063, -7.95152, 2.3054, -7.95193, 2.3053, -10.10216, 2.39078, -10.10233, 2.39065, -7.93988, 2.874, -7.93982, 2.87393, -4.12091, 2.83486, -4.12103, 2.83478, -1.69276, 0.13113, -1.69284, 0.13105, 0, 0, 0, 0, 1.0849, -1.16959, 1.08496, -1.16966, 1.28377, -1.65845, 1.28378, -1.65849, 1.08839, -1.75751, 1.08844, -1.75764, -3.53568, 1.81228, -3.53577, 1.81226, -3.39215, 1.39011, -3.39224, 1.39009, -3.5011, 0.0177, -3.50085, 0.01747, -4.95802, 3.91902, -4.9581, 3.91892, -12.19438, 3.80249, -12.19464, 3.80241, -13.5804, 7.53439, -13.5806, 7.53431, -11.60376, 6.97083, -11.60391, 6.97075, -4.50647, 5.17867, -4.50656, 5.1786, 0, 0, 0, 0, 2.95187, 1.56838, 2.95184, 1.56836, 2.96426, -0.54657, 2.9642, -0.5466, 1.43239, -3.79858, 1.43225, -3.79866, -2.13557, -1.41743, -2.13577, -1.41759, -1.17668, 0.43898, -1.17673, 0.43897, -1.37616, 1.42649, -1.37619, 1.42651, -1.58403, 1.70608, -1.58405, 1.70611, -0.86557, 0.97662, -0.8656, 0.97662, -1.64226, 0.88551, -1.64236, 0.88552, -1.64453, 0.59452, -1.64459, 0.59453, -0.67453, 0.58714, -0.67453, 0.58714, -1.11517, 1.29848, -1.1152, 1.29851, -1.03273, 1.09088, -1.03271, 1.09089, -1.25362, 0.97954, -1.25366, 0.97957, -0.96333, 0.88033, -0.96341, 0.88036, -1.34682, 1.46529, -1.34689, 1.46529, -2.26848, -0.85275, -2.26843, -0.85276, -1.27499, 0.12571, -1.27505, 0.1257, -1.27104, 0.64767, -1.27106, 0.64768, -1.33211, 0.24215, -1.33218, 0.24214, -1.56367, 0.3019, -1.56369, 0.3019, -1.39104, 0.1266, -1.39108, 0.12659, -0.63751, 0.06286, -0.63754, 0.06285, -1.15991, 0.00884, -1.15997, 0.00882, -0.34749, 0.06065, -0.34753, 0.06064, -0.34663, 0.17664, -0.34665, 0.17664, -0.86949, 0.06461, -0.86951, 0.06462, -0.48131, 0.48867, -0.48132, 0.48868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.01915, -1.09303, -3.01941, -1.09303, -6.21597, -1.03017, -6.21564, -1.03028, -8.88065, -0.04435, -8.88, -0.04444, -10.47665, -0.64095, -10.47577, -0.64114], "curve": 0.25, "c3": 0.75}, {"time": 8.7}]}, "a30204": {"a30204": [{}, {"time": 6.6667, "offset": 82, "vertices": [0.3564, 2.18481, 8.18677, -2.98369, -2.83051, 8.23859, 6.70549, -3.8222, -1.24703, 7.61588, 0, 0, 0, 0, 2.54958, -2.85655, 0.61708, 3.77747]}, {"time": 10.6667}]}, "tui_qian_xia": {"tui_qian_xia": [{"curve": 0.25, "c3": 0.75}, {"time": 7.3667, "vertices": [-2.692, 6.84705, -19.23523, 11.52667, -26.28137, -0.3457, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.51985, -10.28198, -0.62677, 0.46399], "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "vertices": [-5.27672, 7.34051, -6.77614, 12.15465, -20.972, -0.27586, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.04027, -4.88676, -0.50015, 0.37025], "curve": 0.25, "c3": 0.75}, {"time": 8.7, "vertices": [1.12421, 1.82148, -5.06066, 9.07753, -15.66264, -0.20603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.76425, -3.64961, -0.37353, 0.27652], "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}}}}, "animation2": {"slots": {"bg": {"attachment": [{"name": null}]}, "bg_hua": {"attachment": [{"name": null}]}}, "bones": {"target3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -22.11, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -22.11, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -22.11, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -22.11, "y": 11.06, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone82": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone81": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone80": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone79": {"rotate": [{"angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9}, {"time": 10.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -0.62}]}, "bone78": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.76}]}, "bone76": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.76}]}, "bone75": {"rotate": [{"angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -0.62}]}, "bone74": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone73": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone72": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone68": {"rotate": [{"angle": -0.25}]}, "bone67": {"rotate": [{"angle": 0.13}]}, "bone66": {"rotate": [{"angle": -1.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -8.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 33.98, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 8, "angle": -1.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -8.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -1.57}]}, "bone65": {"rotate": [{"angle": 0.08}]}, "bone64": {"rotate": [{"angle": -0.18}], "translate": [{}, {"time": 1.3333, "x": 0.04, "y": -4.75}, {"time": 2.6667}, {"time": 4, "x": 0.04, "y": -4.75}, {"time": 5.3333}, {"time": 6.6667, "x": 0.04, "y": -4.75}, {"time": 8}, {"time": 9.3333, "x": 0.04, "y": -4.75}, {"time": 10.6667}]}, "bone63": {"rotate": [{"angle": 1.51, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "angle": -1.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.0333, "angle": 3.76, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": 1.51, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 3.3667, "angle": -1.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.7, "angle": 3.76, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.3333, "angle": 1.51, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 6.0333, "angle": -1.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7.3667, "angle": 3.76, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 8, "angle": 1.51, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 8.7, "angle": -1.07, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 10.0333, "angle": 3.76, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 10.6667, "angle": 1.51}], "translate": [{"x": 2.24, "y": -0.87}]}, "bone6": {"rotate": [{"angle": -0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -2.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -2.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -2.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -0.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 8.5, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": -2.03, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 10.6667, "angle": -0.68}]}, "bone5": {"rotate": [{"angle": -0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -2.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -2.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": 7.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -2.03, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.37}], "translate": [{"x": 0.82, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.46, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.82, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 4.46, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 0.82, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 4.46, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 0.82, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 4.46, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 0.82, "y": 0.01}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -17.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone60": {"rotate": [{"angle": -10.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -10.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -10.4}], "translate": [{"y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 3, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 10.6667, "y": 0.64}], "scale": [{"x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 1.025, "y": 1.006}]}, "bone59": {"rotate": [{"angle": -5.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -6.38, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -5.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -6.38, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -6.38, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": -6.38, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -5.2}], "translate": [{"y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 3, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 10.6667, "y": 0.64}], "scale": [{"x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 1.025, "y": 1.006}]}, "bone58": {"rotate": [{"angle": 6.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 8.43, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 6.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 8.43, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 8.43, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 8.43, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": 6.88}], "translate": [{"y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.64, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 3, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 10.6667, "y": 0.64}], "scale": [{"x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 1.025, "y": 1.006}]}, "bone57": {"rotate": [{"angle": -2.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -2.66}], "scale": [{"x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.025, "y": 1.006, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "x": 1.025, "y": 1.006}]}, "bone56": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 1.031, "y": 1.008, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone55": {"rotate": [{}, {"time": 1.3333, "angle": 6.09}, {"time": 2.6667}, {"time": 4, "angle": 6.09}, {"time": 5.3333}, {"time": 6.6667, "angle": 6.09}, {"time": 8}, {"time": 9.3333, "angle": 6.09}, {"time": 10.6667}]}, "bone54": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone53": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone52": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone51": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone50": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone49": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone48": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone47": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone46": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone45": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone44": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone43": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone42": {"rotate": [{"angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 13.75, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 3.54}]}, "bone41": {"rotate": [{"angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.83, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 11.83, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 11.83, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 8.29, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 11.83, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 8.29}]}, "bone40": {"rotate": [{"angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 1.77}]}, "bone39": {"rotate": [{"angle": 0.65, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.65, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.65, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 5.34, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 0.65, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 0.65}]}, "bone38": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 7.74, "y": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 7.74, "y": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 7.74, "y": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 7.74, "y": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone37": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.87, "y": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -1.87, "y": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -3.74, "y": -4.54, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -3.74, "y": -4.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone71": {"translate": [{}, {"time": 1.3333, "x": -3.26, "y": -6.14}, {"time": 2.6667}, {"time": 4, "x": -3.26, "y": -6.14}, {"time": 5.3333}, {"time": 6.6667, "x": -3.26, "y": -6.14}, {"time": 8}, {"time": 9.3333, "x": -3.26, "y": -6.14}, {"time": 10.6667}]}, "bone69": {"translate": [{"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.8667, "x": -1.79, "y": -3.81, "curve": "stepped"}, {"time": 9.7333, "x": -1.79, "y": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone36": {"rotate": [{"angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 1.73}]}, "bone35": {"rotate": [{"angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -6.72, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -2.95}]}, "bone34": {"rotate": [{"angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.46}]}, "bone32": {"rotate": [{"angle": -0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.76}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -3.72, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone29": {"rotate": [{"angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -4.71}], "scale": [{"x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": 0.981, "y": 0.981}]}, "bone28": {"rotate": [{"angle": -3.84, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -4.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.84, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -4.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.84, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -4.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -3.84, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9}, {"time": 10.3333, "angle": -4.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -3.84}], "scale": [{"x": 0.984, "y": 0.984, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 0.981, "y": 0.981, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 0.984, "y": 0.984, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 0.981, "y": 0.981, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 0.984, "y": 0.984, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.981, "y": 0.981, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "x": 0.984, "y": 0.984, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": 0.981, "y": 0.981, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "x": 0.984, "y": 0.984}]}, "bone27": {"rotate": [{"angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}, {"time": 10, "angle": -4.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -2.35}], "scale": [{"x": 0.99, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.99, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 0.99, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 0.99, "y": 0.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "x": 0.99, "y": 0.99}]}, "bone26": {"rotate": [{"angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -4.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -4.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -4.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.87}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -6.89, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone24": {"rotate": [{}, {"time": 1.3333, "angle": -0.41}, {"time": 2.6667}, {"time": 4, "angle": -0.41}, {"time": 5.3333}, {"time": 6.6667, "angle": -0.41}, {"time": 8}, {"time": 9.3333, "angle": -0.41}, {"time": 10.6667}]}, "bone23": {"rotate": [{"angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.83, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 4.14}]}, "bone22": {"rotate": [{"angle": 3.37, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 4.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 3.37, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 4.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 3.37, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 5.59, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.6667, "angle": 4.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": 3.37, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": 4.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": 3.37}]}, "bone21": {"rotate": [{"angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 6.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3333, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 2.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 4.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 2.07}]}, "bone20": {"rotate": [{"angle": 0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": 4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": 4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 6.6667, "angle": 7.93}, {"time": 7, "angle": 4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 0.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": 4.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": 0.76}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -4.64, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone15": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -0.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2.6667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4, "angle": -0.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 5.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": -0.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 9.3333, "angle": -0.81, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 10.6667}]}, "bone14": {"rotate": [{"angle": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 1.7, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2.6667, "angle": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4, "angle": 1.7, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 5.3333, "angle": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 1.7, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 8, "angle": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 9.3333, "angle": 1.7, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 10.6667, "angle": 2.5}]}, "bone13": {"rotate": [{"angle": 2.04, "curve": 0.317, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 1.3333, "angle": 1.23, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 2.6667, "angle": 2.04, "curve": 0.317, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 4, "angle": 1.23, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 5.3333, "angle": 2.04, "curve": 0.317, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 6.6667, "angle": 1.23, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 8, "angle": 2.04, "curve": 0.317, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 9.3333, "angle": 1.23, "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 10.6667, "angle": 2.04}]}, "bone12": {"rotate": [{"angle": 1.25, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 1.3333, "angle": 0.44, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.6667, "angle": 1.25, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": 0.44, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 5.3333, "angle": 1.25, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 6.6667, "angle": 0.44, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 8, "angle": 1.25, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 9.3333, "angle": 0.44, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 10.6667, "angle": 1.25}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -0.07, "y": -2.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.07, "y": -2.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": -0.07, "y": -2.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "x": -0.07, "y": -2.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.6667}]}, "bone11": {"rotate": [{"angle": 0.46, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 1.3333, "angle": -0.35, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 2.6667, "angle": 0.46, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 4, "angle": -0.35, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 5.3333, "angle": 0.46, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 6.6667, "angle": -0.35, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 8, "angle": 0.46, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 9.3333, "angle": -0.35, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 10.6667, "angle": 0.46}]}, "bone19": {"rotate": [{"angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.83, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 4.68}]}, "bone10": {"rotate": [{"angle": 3.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": 4.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 3.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": 4.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 3.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 6.6667, "angle": 5.99}, {"time": 7.6667, "angle": 4.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": 3.81, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9}, {"time": 10.3333, "angle": 4.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": 3.81}]}, "bone9": {"rotate": [{"angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": 4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": 4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 6.6667, "angle": 7.16}, {"time": 7.3333, "angle": 4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}, {"time": 10, "angle": 4.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 2.34}]}, "bone8": {"rotate": [{"angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 4.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": 8.64, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7, "angle": 4.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 4.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": 0.86}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 20.85, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "angle": -9.71, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 8.5667, "angle": -5.15, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.52, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bone3": {"rotate": [{"angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -0.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -0.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -0.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.1}], "translate": [{"x": 0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 5.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "x": 0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 5.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "curve": "stepped"}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 5.61, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 10.6667, "x": 0.66}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 6.44, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.9, "angle": 3.4, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 8.9, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.81, "y": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 2.81, "y": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 2.81, "y": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone": {"translate": [{}, {"time": 1.3333, "x": -1.33, "y": -5.34}, {"time": 2.6667}, {"time": 4, "x": -1.33, "y": -5.34}, {"time": 5.3333}, {"time": 6.6667, "x": -1.33, "y": -5.34}, {"time": 8}, {"time": 9.3333, "x": -1.33, "y": -5.34}, {"time": 10.6667}]}, "bone83": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone77": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone84": {"translate": [{}, {"time": 1.3333, "x": -8.08, "y": -6.1}, {"time": 2.6667}, {"time": 4, "x": -8.08, "y": -6.1}, {"time": 5.3333}, {"time": 6.6667, "x": -8.08, "y": -6.1}, {"time": 8}, {"time": 9.3333, "x": -8.08, "y": -6.1}, {"time": 10.6667}]}, "bone86": {"translate": [{"x": 9.96, "y": -17.92, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "x": 2.63, "y": -1.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.3333, "x": 11.61, "y": -21.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 9.96, "y": -17.92, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "x": 2.63, "y": -1.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5, "x": 11.61, "y": -21.75, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": 2.63, "y": -1.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7.6667, "x": 11.61, "y": -21.75, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 2.63, "y": -1.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 10.3333, "x": 11.61, "y": -21.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "x": 9.96, "y": -17.92}]}, "bone87": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone88": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone89": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone94": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.76}]}, "bone95": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -0.76}]}, "bone93": {"rotate": [{"angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -0.62}]}, "bone96": {"rotate": [{"angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -0.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 9}, {"time": 10.3333, "angle": -0.76, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 10.6667, "angle": -0.62}]}, "bone92": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone97": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}, {"time": 10, "angle": -0.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": -0.38}]}, "bone91": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone98": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone99": {"rotate": [{"angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -0.76, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -0.14}]}, "bone90": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone103": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.75, "y": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.75, "y": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 1.75, "y": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": 1.75, "y": 13.07, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone102": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -10.03, "y": 33.7, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -10.03, "y": 33.7, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -10.03, "y": 33.7, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -10.03, "y": 33.7, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone101": {"translate": [{}, {"time": 1.3333, "x": 6.33, "y": 8.46}, {"time": 2.6667}, {"time": 4, "x": 6.33, "y": 8.46}, {"time": 5.3333}, {"time": 6.6667, "x": 6.33, "y": 8.46}, {"time": 8}, {"time": 9.3333, "x": 6.33, "y": 8.46}, {"time": 10.6667}]}, "bone100": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -9.27, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -9.27, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -9.27, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -9.27, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "target2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -39.96, "y": 44.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -39.96, "y": 44.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "x": -110.82, "y": 27.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.5, "x": 245, "y": 40.16, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "x": -39.96, "y": 44.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.6667}]}}, "deform": {"default": {"lian": {"lian": [{"time": 7.3667, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "offset": 20, "vertices": [-6.81619, 4.62778, -6.81607, 4.62777, 0, 0, 0, 0, 1.33939, -0.48419, 1.33939, -0.48419, 1.33932, -0.48421, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.53943, 1.00578, -6.53909, 1.0057, -3.52681, 2.29313, -3.52661, 2.29315, -0.19089, 1.42247, -0.19083, 1.42248, -7.72655, 2.6795, -7.7262, 2.67953, -10.29802, 2.04577, -10.29773, 2.04573, -2.14581, 0.01637, -2.146, 0.01632, -1.27499, 0.12571, -1.27505, 0.12571, 0, 0, 0, 0, 0.7074, -0.47935, 0.7074, -0.47939, 3.95152, -0.87202, 3.95154, -0.87208, -5.82449, 2.70837, -5.82419, 2.7084, -6.44966, 1.84789, -6.4494, 1.84777, 1.97136, -0.48897, 1.97137, -0.48899, 0.23581, -0.15978, 0.23578, -0.15979, 0, 0, 0, 0, 0, 0, 0, 0, 5.78917, -1.326, 5.78918, -1.32605, 4.2211, -1.8471, 4.22119, -1.84717, 2.84039, -0.49557, 2.84039, -0.49561, 0, 0, 0, 0, -4.49785, -0.63578, -4.49805, -0.6358, -7.76622, -0.29685, -7.76639, -0.29691, -6.50412, 0.24454, -6.5043, 0.2445, -4.34474, 0.0331, -4.34479, 0.03308, 0.30414, 0.79254, 0.30414, 0.79246, 2.58609, -0.79549, 2.58612, -0.79553, 4.68655, -1.44945, 4.68698, -1.4496, 2.91357, 0.05694, 2.91354, 0.05685, -0.86365, -0.81219, -0.86395, -0.81242, -1.34413, -0.1478, -1.34402, -0.14778, -3.29758, 1.1207, -3.29767, 1.12063, -7.95152, 2.3054, -7.95193, 2.3053, -10.10216, 2.39078, -10.10233, 2.39065, -7.93988, 2.874, -7.93982, 2.87393, -4.12091, 2.83486, -4.12103, 2.83478, -1.69276, 0.13113, -1.69284, 0.13105, 0, 0, 0, 0, 1.0849, -1.16959, 1.08496, -1.16966, 1.28377, -1.65845, 1.28378, -1.65849, 1.08839, -1.75751, 1.08844, -1.75764, -3.53568, 1.81228, -3.53577, 1.81226, -3.39215, 1.39011, -3.39224, 1.39009, -3.5011, 0.0177, -3.50085, 0.01747, -4.95802, 3.91902, -4.9581, 3.91892, -12.19438, 3.80249, -12.19464, 3.80241, -13.5804, 7.53439, -13.5806, 7.53431, -11.60376, 6.97083, -11.60391, 6.97075, -4.50647, 5.17867, -4.50656, 5.1786, 0, 0, 0, 0, 2.95187, 1.56838, 2.95184, 1.56836, 2.96426, -0.54657, 2.9642, -0.5466, 1.43239, -3.79858, 1.43225, -3.79866, -2.13557, -1.41743, -2.13577, -1.41759, -1.17668, 0.43898, -1.17673, 0.43897, -1.37616, 1.42649, -1.37619, 1.42651, -1.58403, 1.70608, -1.58405, 1.70611, -0.86557, 0.97662, -0.8656, 0.97662, -1.64226, 0.88551, -1.64236, 0.88552, -1.64453, 0.59452, -1.64459, 0.59453, -0.67453, 0.58714, -0.67453, 0.58714, -1.11517, 1.29848, -1.1152, 1.29851, -1.03273, 1.09088, -1.03271, 1.09089, -1.25362, 0.97954, -1.25366, 0.97957, -0.96333, 0.88033, -0.96341, 0.88036, -1.34682, 1.46529, -1.34689, 1.46529, -2.26848, -0.85275, -2.26843, -0.85276, -1.27499, 0.12571, -1.27505, 0.1257, -1.27104, 0.64767, -1.27106, 0.64768, -1.33211, 0.24215, -1.33218, 0.24214, -1.56367, 0.3019, -1.56369, 0.3019, -1.39104, 0.1266, -1.39108, 0.12659, -0.63751, 0.06286, -0.63754, 0.06285, -1.15991, 0.00884, -1.15997, 0.00882, -0.34749, 0.06065, -0.34753, 0.06064, -0.34663, 0.17664, -0.34665, 0.17664, -0.86949, 0.06461, -0.86951, 0.06462, -0.48131, 0.48867, -0.48132, 0.48868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.01915, -1.09303, -3.01941, -1.09303, -6.21597, -1.03017, -6.21564, -1.03028, -8.88065, -0.04435, -8.88, -0.04444, -10.47665, -0.64095, -10.47577, -0.64114], "curve": 0.25, "c3": 0.75}, {"time": 8.7}]}, "a30204": {"a30204": [{}, {"time": 6.6667, "offset": 82, "vertices": [0.3564, 2.18481, 8.18677, -2.98369, -2.83051, 8.23859, 6.70549, -3.8222, -1.24703, 7.61588, 0, 0, 0, 0, 2.54958, -2.85655, 0.61708, 3.77747]}, {"time": 10.6667}]}, "tui_qian_xia": {"tui_qian_xia": [{"curve": 0.25, "c3": 0.75}, {"time": 7.3667, "vertices": [-2.692, 6.84705, -19.23523, 11.52667, -26.28137, -0.3457, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.51985, -10.28198, -0.62677, 0.46399], "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "vertices": [-5.27672, 7.34051, -6.77614, 12.15465, -20.972, -0.27586, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.04027, -4.88676, -0.50015, 0.37025], "curve": 0.25, "c3": 0.75}, {"time": 8.7, "vertices": [1.12421, 1.82148, -5.06066, 9.07753, -15.66264, -0.20603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.76425, -3.64961, -0.37353, 0.27652], "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}}}}}}