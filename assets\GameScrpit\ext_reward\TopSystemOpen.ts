import { _decorator, Component, math, Node, Sprite, tween, UIOpacity, UITransform, v3, Vec3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { TipsMgr } from "../../platform/src/TipsHelper";
import Tool from "../lib/common/Tool";
import { Sleep } from "../game/GameDefine";
import { AudioMgr } from "../../platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("TopSystemOpen")
export class TopSystemOpen extends BaseCtrl {
  // 起点
  fromPos: Vec3;

  // 最终位置
  toPos: Vec3;

  // 贝塞尔中间点
  midPos: Vec3;

  // 飞出动画时间
  time: number = 0.8;

  // 图标节点
  nodeIcon: Node;

  // 标题透明度
  titleOpacity: UIOpacity;

  // 要添加的图标
  nodeIconAdd: Node;

  init(args: any): void {
    this.toPos = args.toWorldPos;
    this.midPos = args.midPos;
    this.nodeIconAdd = args.nodeIconAdd;
  }

  async start() {
    TipsMgr.setEnableTouch(false, 2, false);
    super.start();

    // 播放音效
    tween(this.node)
      .delay(0.2)
      .call(() => {
        AudioMgr.instance.playEffect(507);
      })
      .start();

    // 数据准备
    this.nodeIcon = this.getNode("node_icon");

    // 添加图标
    this.nodeIcon.addChild(this.nodeIconAdd);
    this.nodeIconAdd.walk((child) => (child.layer = this.nodeIcon.layer));
    this.nodeIconAdd.setPosition(0, 0, 0);
    this.nodeIconAdd.active = true;

    // 标题透明度
    this.titleOpacity = this.getNode("bg_title").getComponent(UIOpacity);
    this.titleOpacity.opacity = 255;
    // 起始位置
    this.fromPos = this.nodeIcon.getPosition();
    this.toPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(this.toPos);

    // 中间坐标
    if (this.toPos.y > this.fromPos.y) {
      this.midPos = new Vec3(this.toPos.x, this.toPos.y + 200, this.toPos.z);
    } else {
      this.midPos = new Vec3(this.fromPos.x, this.fromPos.y + 200, this.fromPos.z);
    }

    // 出现动画
    tween(this.nodeIcon)
      .set({ scale: v3(0, 0, 0) })
      .to(0.5, { scale: v3(1.6, 1.6, 1) }, { easing: "backOut" })
      .start();

    await Sleep(1);

    // 飞到指定位置动画
    // 阴影变淡
    const maskOpacity = this.getNode("mask").getComponent(UIOpacity);
    tween(maskOpacity).to(this.time, { opacity: 0 }).start();

    // 动画隐藏标题
    this.titleOpacity.opacity = 255;
    tween(this.titleOpacity).to(0.3, { opacity: 0 }).start();

    this.getNode("spine_faguang").active = false;

    // 设置图标飞行
    tween(this.nodeIcon)
      .to(
        this.time,
        { scale: v3(1, 1, 1) },
        {
          onUpdate: (target, ratio) => {
            const vTemp = new Vec3();
            Tool.bezierCurve(ratio, this.fromPos, this.midPos, this.midPos, this.toPos, vTemp);
            this.nodeIcon.setPosition(vTemp);
          },
        }
      )
      .call(() => {
        this.closeBack();
      })
      .start();
  }
}
