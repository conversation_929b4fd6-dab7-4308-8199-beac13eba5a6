const { ExcelToJsonConverter, convertExcelToJson } = require("./excel-converter");

/**
 * Excel转JSON工具使用示例
 */

// 示例1: 快速转换（推荐）
async function quickConvert() {
  try {
    console.log("=== 快速转换示例 ===");

    // 转换指定目录下的所有Excel文件
    const results = await convertExcelToJson(
      "D:/feimeng/design_config/2.0", // 输入目录
      "D:/output" // 输出目录（可选，默认为 assets/bundle_common_json/json）
    );

    console.log("转换结果:", results);
  } catch (error) {
    console.error("转换失败:", error.message);
  }
}

// 示例2: 详细控制
async function detailedConvert() {
  try {
    console.log("=== 详细控制示例 ===");

    // 创建转换器实例
    const converter = new ExcelToJsonConverter("./input", "./output");

    // 可选：设置支持的文件扩展名
    converter.setSupportedExtensions([".xlsx", ".xls"]);

    // 可选：设置输出目录
    converter.setOutputDir("./custom-output");

    // 获取目录统计信息
    const stats = converter.getDirectoryStats();
    console.log("目录统计:", stats);

    // 转换所有文件
    const results = await converter.convertAll();
    console.log("转换结果:", results);
  } catch (error) {
    console.error("转换失败:", error.message);
  }
}

// 示例3: 转换单个文件
async function convertSingleFile() {
  try {
    console.log("=== 单文件转换示例 ===");

    const converter = new ExcelToJsonConverter("./input", "./output");

    // 转换单个文件
    const result = converter.convertSingle("example.xlsx");
    console.log("单文件转换结果:", result);
  } catch (error) {
    console.error("单文件转换失败:", error.message);
  }
}

// 示例4: 只转换特定扩展名的文件
async function convertSpecificExtensions() {
  try {
    console.log("=== 特定扩展名转换示例 ===");

    const results = await convertExcelToJson(
      "./input",
      "./output",
      [".xlsx"] // 只转换.xlsx文件
    );

    console.log("转换结果:", results);
  } catch (error) {
    console.error("转换失败:", error.message);
  }
}

// 运行示例
async function runExamples() {
  console.log("Excel转JSON工具 - 使用示例");
  console.log("================================");

  // 取消注释想要运行的示例
  await quickConvert();
  // await detailedConvert();
  // await convertSingleFile();
  // await convertSpecificExtensions();
}

// 如果直接运行此文件
if (require.main === module) {
  runExamples().catch(console.error);
}

module.exports = {
  quickConvert,
  detailedConvert,
  convertSingleFile,
  convertSpecificExtensions,
};
