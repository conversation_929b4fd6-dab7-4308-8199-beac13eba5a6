{"skeleton": {"hash": "c3RO3WLtVwVzUGDXTuefwaBdq/w=", "spine": "3.8.75", "x": -139.3, "y": -13.07, "width": 230.42, "height": 235.34, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/雷震子"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -43.75, "scaleX": 0.41, "scaleY": 0.41}, {"name": "bone2", "parent": "bone", "x": 8.86, "y": 266.81, "color": "ff0000ff"}, {"name": "bone3", "parent": "bone2", "length": 55.44, "rotation": 3.04, "color": "003dffff"}, {"name": "bone4", "parent": "bone3", "length": 30.8, "rotation": 90.26, "x": 0.26, "y": -1.33}, {"name": "bone5", "parent": "bone4", "length": 74.54, "rotation": 0.9, "x": 30.8}, {"name": "bone6", "parent": "bone5", "length": 22.43, "rotation": -5.94, "x": 74.16, "y": -0.28}, {"name": "bone7", "parent": "bone6", "length": 28.12, "rotation": -4.76, "x": 22.11, "y": 0.05}, {"name": "bone8", "parent": "bone7", "x": 7.56, "y": -12.01, "color": "abe323ff"}, {"name": "bone9", "parent": "bone7", "x": 47.73, "y": -13.46}, {"name": "bone18", "parent": "bone5", "x": 9.7, "y": -11.21, "color": "abe323ff"}, {"name": "bone19", "parent": "bone5", "x": 40.17, "y": -10.81, "color": "abe323ff"}, {"name": "bone44", "parent": "bone3", "length": 33.33, "rotation": -91.41, "x": 0.17, "y": -2.27}, {"name": "bone67", "parent": "bone44", "length": 50.57, "rotation": -81.77, "x": 22.4, "y": 16.5, "transform": "noRotationOrReflection", "color": "f3a9ffff"}, {"name": "bone68", "parent": "bone67", "length": 37.53, "rotation": -9.51, "x": 50.62, "y": 0.01, "color": "f3a9ffff"}, {"name": "bone69", "parent": "bone68", "length": 18.9, "rotation": -4.76, "x": 37.56, "y": 0.05, "color": "f3a9ffff"}, {"name": "bone70", "parent": "bone44", "length": 63.92, "rotation": -101.91, "x": 19.44, "y": -26.96, "transform": "noRotationOrReflection", "color": "f3a9ffff"}, {"name": "bone71", "parent": "bone70", "length": 36.21, "rotation": 6.14, "x": 63.99, "y": -0.03, "color": "f3a9ffff"}, {"name": "bone72", "parent": "bone71", "length": 20.44, "rotation": 4.04, "x": 34.95, "y": -0.12, "color": "f3a9ffff"}, {"name": "bone10", "parent": "bone5", "length": 50.35, "rotation": -164.61, "x": 43.4, "y": -42.5}, {"name": "bone11", "parent": "bone10", "length": 50.6, "rotation": 19.28, "x": 50.35}, {"name": "bone12", "parent": "bone11", "length": 26.87, "rotation": 0.05, "x": 50.6}, {"name": "bone13", "parent": "bone5", "length": 61.12, "rotation": 131.07, "x": 58.87, "y": 59.95}, {"name": "bone14", "parent": "bone13", "length": 55.93, "rotation": 72.59, "x": 61.12}, {"name": "bone15", "parent": "bone14", "length": 28.72, "rotation": 15.44, "x": 55.93}, {"name": "a2", "parent": "bone15", "length": 46.34, "rotation": 92.76, "x": 21.04, "y": 0.06}, {"name": "bone16", "parent": "bone7", "length": 24.75, "rotation": 49.52, "x": 85.17, "y": 50.64}, {"name": "bone17", "parent": "bone16", "length": 21.05, "rotation": -14.5, "x": 24.75}, {"name": "bone20", "parent": "bone7", "length": 23.35, "rotation": 9.46, "x": 92.68, "y": -33.07}, {"name": "bone21", "parent": "bone20", "length": 17.62, "rotation": 24.19, "x": 23.35}, {"name": "bone22", "parent": "bone5", "length": 66.03, "rotation": -2.37, "x": 52.92, "y": 25.51}, {"name": "bone23", "parent": "bone22", "length": 74.46, "rotation": -62.54, "x": 15.99, "y": -12.13}, {"name": "bone24", "parent": "bone23", "length": 90.36, "rotation": 40.9, "x": 74.46}, {"name": "bone25", "parent": "bone24", "length": 121.61, "rotation": -133.62, "x": 89.54, "y": -0.85}, {"name": "bone26", "parent": "bone25", "length": 83.13, "rotation": -0.98, "x": 121.61}, {"name": "bone27", "parent": "bone26", "length": 59.91, "rotation": 9.36, "x": 83.13}, {"name": "bone28", "parent": "bone22", "length": 72.99, "rotation": 56.78, "x": 7.61, "y": 23.3}, {"name": "bone29", "parent": "bone28", "length": 98.03, "rotation": -39.1, "x": 72.81, "y": -0.73}, {"name": "bone30", "parent": "bone29", "length": 143.8, "rotation": 129.31, "x": 99.41, "y": 2.31}, {"name": "bone31", "parent": "bone30", "length": 91.1, "rotation": 8.33, "x": 145.7, "y": -0.08}, {"name": "bone32", "parent": "bone31", "length": 65.37, "rotation": -13.28, "x": 90.9, "y": 0.49}, {"name": "bone45", "parent": "bone44", "length": 57.99, "rotation": 22.33, "x": 23.7, "y": 47.78}, {"name": "bone46", "parent": "bone45", "length": 37.94, "rotation": -5.88, "x": 57.99}, {"name": "bone47", "parent": "bone46", "length": 25.89, "rotation": -3.26, "x": 37.94}, {"name": "bone48", "parent": "bone44", "length": 52.69, "rotation": -42.67, "x": 7.04, "y": -51.39}, {"name": "bone49", "parent": "bone48", "length": 35.67, "rotation": -6.05, "x": 52.69}, {"name": "bone50", "parent": "bone49", "length": 25.27, "rotation": -0.86, "x": 35.67}, {"name": "bone51", "parent": "bone44", "length": 64.78, "rotation": -22.95, "x": 19.03, "y": -18.23}, {"name": "bone52", "parent": "bone51", "length": 43.31, "rotation": -3.29, "x": 64.78}, {"name": "bone53", "parent": "bone52", "length": 28.97, "rotation": -0.98, "x": 43.31}, {"name": "bone54", "parent": "bone44", "length": 66.66, "rotation": 0.58, "x": 14.42, "y": -10.2}, {"name": "bone55", "parent": "bone54", "length": 39.41, "rotation": -13.52, "x": 66.66}, {"name": "bone56", "parent": "bone55", "length": 28.7, "rotation": -11.31, "x": 39.41}, {"name": "bone57", "parent": "bone44", "length": 62.18, "rotation": 4.48, "x": 17.32, "y": 26.9}, {"name": "bone58", "parent": "bone57", "length": 44.24, "rotation": 2.01, "x": 63.64, "y": -0.16}, {"name": "bone59", "parent": "bone58", "length": 24.37, "rotation": 6.75, "x": 44.6, "y": -0.05}, {"name": "bone33", "parent": "bone", "x": -137.76, "y": 277.45}, {"name": "a21", "parent": "bone33", "length": 88.21, "rotation": 91.67, "x": 29.21, "y": 268.08}, {"name": "a20", "parent": "bone33", "length": 83.91, "rotation": 91.17, "x": -80.36, "y": 129.41}, {"name": "a22", "parent": "bone33", "length": 78.77, "rotation": 88.75, "x": 131.07, "y": 146.53}, {"name": "b1", "parent": "bone", "length": 300.28, "rotation": 90, "x": -20.66, "y": 318.25}, {"name": "dian", "parent": "bone", "x": -1.71, "y": 318.32, "scaleX": 2.1396, "scaleY": 2.1396}, {"name": "dianatk1", "parent": "bone", "rotation": 77.6, "x": 140.59, "y": 305.75, "scaleY": 2.0453}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "bone", "x": 1090.68, "y": 238.65, "scaleX": 1.9859, "scaleY": 1.9859}, {"name": "diandazhao2", "parent": "root", "x": 34.91, "y": 227.77}, {"name": "dianatk2", "parent": "bone", "rotation": 58.43, "x": 140.59, "y": 305.75, "scaleY": 2.0453}, {"name": "dianatk3", "parent": "bone", "rotation": 66.34, "x": 140.59, "y": 305.75, "scaleY": 2.491}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "root", "x": 423.15, "y": 6.26}, {"name": "shoujiji2", "parent": "root", "x": 506.6, "y": 18.37}, {"name": "shoujiji3", "parent": "root", "x": 486.41, "y": 107.21}, {"name": "bone34", "parent": "root", "x": 71.8, "y": 153.99}], "slots": [{"name": "a22", "bone": "a22", "attachment": "a22"}, {"name": "a43", "bone": "a22", "attachment": "pifu/lzz_a22"}, {"name": "a23", "bone": "a22", "color": "ff830000", "attachment": "a22", "blend": "additive"}, {"name": "a44", "bone": "a22", "color": "ff830000", "attachment": "pifu/lzz_a22", "blend": "additive"}, {"name": "a21", "bone": "a21", "attachment": "a21"}, {"name": "a39", "bone": "a21", "attachment": "pifu/lzz_a21"}, {"name": "a24", "bone": "a21", "color": "ff830000", "attachment": "a21", "blend": "additive"}, {"name": "a40", "bone": "a21", "color": "ff830000", "attachment": "pifu/lzz_a21", "blend": "additive"}, {"name": "a20", "bone": "a20", "attachment": "a20"}, {"name": "a42", "bone": "a20", "attachment": "pifu/lzz_a20"}, {"name": "a25", "bone": "a20", "color": "ff830000", "attachment": "a20", "blend": "additive"}, {"name": "a41", "bone": "a20", "color": "ff830000", "attachment": "pifu/lzz_a20", "blend": "additive"}, {"name": "a19", "bone": "bone23", "attachment": "a19"}, {"name": "a37", "bone": "bone23", "attachment": "pifu/lzz_a19"}, {"name": "a18", "bone": "bone28", "attachment": "a18"}, {"name": "a36", "bone": "bone28", "attachment": "pifu/lzz_a18"}, {"name": "a17", "bone": "bone45", "attachment": "a17"}, {"name": "a35", "bone": "bone45", "attachment": "pifu/lzz_a17"}, {"name": "a16", "bone": "bone10", "attachment": "a16"}, {"name": "a34", "bone": "bone10", "attachment": "pifu/lzz_a16"}, {"name": "a15", "bone": "bone12", "attachment": "a15"}, {"name": "a32", "bone": "bone12", "attachment": "pifu/lzz_a15"}, {"name": "a14", "bone": "bone12", "attachment": "a14"}, {"name": "a33", "bone": "bone12", "attachment": "pifu/lzz_a14"}, {"name": "a13", "bone": "bone67", "attachment": "a13"}, {"name": "a31", "bone": "bone67", "attachment": "pifu/lzz_a13"}, {"name": "a12", "bone": "bone68", "attachment": "a12"}, {"name": "a30", "bone": "bone68", "attachment": "pifu/lzz_a12"}, {"name": "a11", "bone": "bone70", "attachment": "a11"}, {"name": "a29", "bone": "bone70", "attachment": "pifu/lzz_a11"}, {"name": "a10", "bone": "bone71", "attachment": "a10"}, {"name": "a28", "bone": "bone71", "attachment": "pifu/lzz_a10"}, {"name": "a9", "bone": "bone51", "attachment": "a9"}, {"name": "a51", "bone": "bone51", "attachment": "pifu/lzz_a9"}, {"name": "a8", "bone": "bone45", "attachment": "a8"}, {"name": "a50", "bone": "bone45", "attachment": "pifu/lzz_a8"}, {"name": "a7", "bone": "bone57", "attachment": "a7"}, {"name": "a49", "bone": "bone57", "attachment": "pifu/lzz_a7"}, {"name": "a6", "bone": "bone5", "attachment": "a6"}, {"name": "a48", "bone": "bone5", "attachment": "pifu/lzz_a6"}, {"name": "a5", "bone": "bone7", "attachment": "a5"}, {"name": "a46", "bone": "bone7", "attachment": "pifu/lzz_a5"}, {"name": "a4", "bone": "bone7", "attachment": "a4"}, {"name": "a47", "bone": "bone7", "attachment": "pifu/lzz_a4"}, {"name": "a3", "bone": "bone13", "attachment": "a3"}, {"name": "a45", "bone": "bone13", "attachment": "pifu/lzz_a3"}, {"name": "a2", "bone": "a2", "attachment": "a2"}, {"name": "a38", "bone": "a2", "attachment": "pifu/lzz_a2"}, {"name": "a1", "bone": "bone14", "attachment": "a1"}, {"name": "a27", "bone": "bone14", "attachment": "pifu/lzz_a1"}, {"name": "a0", "bone": "bone15", "attachment": "a0"}, {"name": "a26", "bone": "bone15", "attachment": "pifu/lzz_a0"}, {"name": "b1", "bone": "b1", "color": "ffffff00", "attachment": "b1"}, {"name": "b2", "bone": "b1", "color": "ffffff00", "attachment": "pifu/lzz_b1"}, {"name": "dian1/chisongzi_sd_00000", "bone": "dian", "color": "dab6ffff", "attachment": "dian1/chisongzi_sd_00000", "blend": "additive"}, {"name": "dian2/qf_qing_jn3_qtgd_dsd_00", "bone": "dianatk1", "color": "ffc8f1ff", "blend": "additive"}, {"name": "dian2/qf_qing_jn3_qtgd_dsd_0", "bone": "dianatk2", "color": "ffc8f1ff", "blend": "additive"}, {"name": "dian2/qf_qing_jn3_qtgd_dsd_1", "bone": "dianatk3", "color": "ffc8f1ff", "blend": "additive"}, {"name": "dazhao/qf_qing_jn3_qtgd_xlsd_00", "bone": "diandazhao2", "blend": "additive"}, {"name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_00", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_0", "bone": "shoujiji2"}, {"name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_1", "bone": "shoujiji3"}, {"name": "dian2/qf_qing_jn3_qtgd_dsd_bz_00", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color": "ffe3f8ff"}], "transform": [{"name": "a1", "order": 2, "bones": ["bone13"], "target": "bone18", "rotation": 131.07, "x": 49.17, "y": 71.16, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "a2", "order": 3, "bones": ["bone10"], "target": "bone19", "rotation": -164.61, "x": 3.23, "y": -31.68, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "s01", "order": 1, "bones": ["bone19"], "target": "bone18", "x": 30.47, "y": 0.4, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "t01", "bones": ["bone9"], "target": "bone8", "x": 40.16, "y": -1.45, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"dian2/qf_qing_jn3_qtgd_dsd_bz_00": {"dian2/qf_qing_jn3_qtgd_dsd_bz_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200, -150, -200, -150, -200, 150, 200, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 180}, "dian2/qf_qing_jn3_qtgd_dsd_bz_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200, -150, -200, -150, -200, 150, 200, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 180}, "dian2/qf_qing_jn3_qtgd_dsd_bz_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200, -150, -200, -150, -200, 150, 200, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 180}, "dian2/qf_qing_jn3_qtgd_dsd_bz_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200, -150, -200, -150, -200, 150, 200, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 180}, "dian2/qf_qing_jn3_qtgd_dsd_bz_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200, -150, -200, -150, -200, 150, 200, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 180}, "dian2/qf_qing_jn3_qtgd_dsd_bz_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200, -150, -200, -150, -200, 150, 200, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 180}, "dian2/qf_qing_jn3_qtgd_dsd_bz_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [200, -150, -200, -150, -200, 150, 200, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 180}}, "dazhao/qf_qing_jn3_qtgd_xlsd_00": {"dazhao/qf_qing_jn3_qtgd_xlsd_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "dazhao/qf_qing_jn3_qtgd_xlsd_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162, -150, -161, -150, -161, 150, 162, 150], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}}, "dian2/qf_qing_jn3_qtgd_dsd_00": {"dian2/qf_qing_jn3_qtgd_dsd_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}}, "dian1/chisongzi_sd_00000": {"dian1/chisongzi_sd_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -93, -97, -93, -97, 94, 97, 94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}, "dian1/chisongzi_sd_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -92.28, -97, -92.28, -97, 94.72, 97, 94.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 130}}, "dazhao/qf_js_nu_jn2_ctj_mz_00": {"dazhao/qf_js_nu_jn2_ctj_mz_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}}, "dian2/qf_qing_jn3_qtgd_dsd_0": {"dian2/qf_qing_jn3_qtgd_dsd_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}}, "dian2/qf_qing_jn3_qtgd_dsd_1": {"dian2/qf_qing_jn3_qtgd_dsd_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}, "dian2/qf_qing_jn3_qtgd_dsd_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [150, -509.31, -150, -509.31, -150, 40.69, 150, 40.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 330}}, "dazhao/qf_js_nu_jn2_ctj_mz_1": {"dazhao/qf_js_nu_jn2_ctj_mz_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}}, "dazhao/qf_js_nu_jn2_ctj_mz_0": {"dazhao/qf_js_nu_jn2_ctj_mz_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}, "dazhao/qf_js_nu_jn2_ctj_mz_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 157}}}}, {"name": "<PERSON><PERSON><PERSON>", "attachments": {"a32": {"pifu/lzz_a15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [129.65, 26.15, 20.95, -108.43, -97.3, -12.92, 11.41, 121.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 138, "height": 122}}, "a33": {"pifu/lzz_a14": {"type": "mesh", "uvs": [0.29521, 0.01005, 0.49842, 0.01364, 0.68289, 0.08275, 0.81434, 0.22001, 0.9684, 0.49028, 1, 0.60961, 0.8477, 0.74836, 0.415, 0.99223, 0.19156, 1, 0.04742, 0.84414, 0.05014, 0.66729, 0.13734, 0.57539, 0.29148, 0.50222, 0.15014, 0.41073, 0.12327, 0.1253, 0.35695, 0.18354], "triangles": [15, 0, 1, 14, 0, 15, 13, 14, 15, 12, 13, 15, 6, 3, 4, 6, 4, 5, 15, 1, 2, 6, 12, 3, 3, 15, 2, 3, 12, 15, 7, 12, 6, 11, 12, 7, 9, 10, 11, 11, 8, 9, 7, 8, 11], "vertices": [1, 21, 2.81, 7.06, 1, 1, 21, 6.99, 12.05, 1, 1, 21, 12.42, 15.25, 1, 1, 21, 18.48, 15.76, 1, 1, 21, 28.3, 14.16, 1, 1, 21, 31.91, 12.55, 1, 1, 21, 32.3, 5.97, 1, 1, 21, 29.67, -9.71, 1, 1, 21, 25.37, -15.42, 1, 1, 21, 18.59, -15.88, 1, 1, 21, 14.25, -12.25, 1, 1, 21, 13.71, -8.24, 1, 1, 21, 14.99, -2.93, 1, 1, 21, 9.87, -4.61, 1, 1, 21, 2.22, 0.46, 1, 1, 21, 8.37, 5.11, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 0, 30, 24, 26, 26, 28, 20, 22, 22, 24, 16, 18], "width": 26, "height": 26}}, "a34": {"pifu/lzz_a16": {"type": "mesh", "uvs": [0.11349, 0.01466, 0.23583, 0, 0.37427, 0.02426, 0.47085, 0.11546, 0.50305, 0.22586, 0.51592, 0.32186, 0.49983, 0.39386, 0.58353, 0.44666, 0.62539, 0.53786, 0.69622, 0.57626, 0.74773, 0.65066, 0.81534, 0.70586, 0.90549, 0.74906, 0.97631, 0.78266, 0.99563, 0.84506, 0.99241, 0.91946, 0.87973, 0.97706, 0.75739, 1, 0.64792, 0.95546, 0.57066, 0.84506, 0.42256, 0.78506, 0.27446, 0.71066, 0.15212, 0.62666, 0.05553, 0.52346, 0.06197, 0.42746, 0, 0.34586, 0, 0.23306, 0, 0.11306, 0.05553, 0.05066, 0.18669, 0.15261, 0.22105, 0.28066, 0.27602, 0.41554, 0.38366, 0.547, 0.5394, 0.65968, 0.66995, 0.77578, 0.78446, 0.86456, 0.85546, 0.9209], "triangles": [35, 11, 12, 13, 14, 35, 35, 14, 36, 13, 35, 12, 15, 36, 14, 35, 18, 19, 16, 36, 15, 17, 18, 35, 17, 35, 36, 17, 36, 16, 32, 6, 7, 32, 7, 8, 33, 32, 8, 33, 8, 9, 33, 9, 10, 21, 22, 32, 21, 32, 33, 34, 33, 10, 34, 10, 11, 20, 21, 33, 19, 20, 33, 34, 19, 33, 35, 34, 11, 35, 19, 34, 29, 0, 1, 28, 0, 29, 27, 28, 29, 26, 27, 29, 30, 29, 4, 2, 29, 1, 3, 29, 2, 4, 29, 3, 26, 29, 30, 30, 4, 5, 25, 26, 30, 5, 31, 30, 6, 31, 5, 24, 25, 30, 31, 24, 30, 32, 31, 6, 31, 23, 24, 22, 31, 32, 22, 23, 31], "vertices": [1, 19, -19.3, -7.41, 1, 1, 19, -17.38, 4.22, 1, 1, 19, -10.01, 15.76, 1, 1, 19, 4.57, 20.91, 1, 1, 19, 19.6, 19.48, 1, 2, 19, 32.2, 16.88, 0.99785, 20, -11.56, 21.93, 0.00215, 2, 19, 40.85, 12.6, 0.86022, 20, -4.81, 15.03, 0.13978, 2, 19, 50.15, 18.09, 0.33338, 20, 5.78, 17.14, 0.66662, 2, 19, 63.04, 18.29, 0.0241, 20, 18.01, 13.08, 0.9759, 2, 19, 70.11, 23.18, 2e-05, 20, 26.3, 15.36, 0.99998, 2, 20, 37.31, 13.34, 0.97812, 21, -13.28, 13.36, 0.02188, 2, 20, 47.2, 14.06, 0.62544, 21, -3.39, 14.06, 0.37456, 2, 20, 57.15, 17.41, 0.1153, 21, 6.56, 17.4, 0.8847, 2, 20, 64.92, 20.06, 0.0119, 21, 14.34, 20.05, 0.9881, 1, 21, 22.16, 16.56, 1, 1, 21, 29.92, 10.44, 1, 1, 21, 29.42, -2.56, 1, 1, 21, 24.64, -13.55, 1, 2, 20, 64.03, -18.22, 0.03169, 21, 13.41, -18.23, 0.96831, 2, 20, 47.65, -15.29, 0.65745, 21, -2.96, -15.29, 0.34255, 3, 19, 88.17, -9.72, 0.00027, 20, 32.49, -21.66, 0.99754, 21, -18.13, -21.64, 0.00219, 2, 19, 74.13, -20.17, 0.09208, 20, 15.79, -26.89, 0.90792, 2, 19, 59.67, -27.92, 0.45958, 20, -0.43, -29.43, 0.54042, 2, 19, 43.56, -32.6, 0.82794, 20, -17.17, -28.53, 0.17206, 2, 19, 31.56, -28.25, 0.9675, 20, -27.06, -20.46, 0.0325, 2, 19, 19.27, -30.64, 0.99988, 20, -39.45, -18.66, 0.00012, 1, 19, 4.94, -26.22, 1, 1, 19, -10.31, -21.51, 1, 1, 19, -16.52, -14.05, 1, 1, 19, 0.5, -6.21, 1, 1, 19, 17.83, -8.13, 1, 2, 19, 36.67, -8.45, 0.99505, 20, -15.7, -3.46, 0.00495, 2, 19, 56.71, -3.89, 0.09913, 20, 4.72, -5.77, 0.90087, 1, 20, 25.97, -2.99, 1, 2, 20, 46.1, -2.37, 0.947, 21, -4.5, -2.37, 0.053, 1, 21, 11.76, -0.81, 1, 1, 21, 21.98, 0.06, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72], "width": 82, "height": 110}}, "a35": {"pifu/lzz_a17": {"type": "mesh", "uvs": [0.56479, 0, 0.64258, 0, 0.68223, 0.03143, 0.73181, 0.11142, 0.78497, 0.21074, 0.83797, 0.33562, 0.87701, 0.44826, 0.90917, 0.55892, 0.93717, 0.66687, 0.95856, 0.76023, 0.97678, 0.85227, 0.98748, 0.91564, 0.99752, 0.98144, 0.99242, 1, 0.96749, 0.96547, 0.93724, 0.9313, 0.89176, 0.88572, 0.83767, 0.85108, 0.75869, 0.8074, 0.65422, 0.7818, 0.52944, 0.75662, 0.38712, 0.74469, 0.27432, 0.7468, 0.15971, 0.7227, 0.07905, 0.68293, 0.01125, 0.67079, 0, 0.65884, 0.02273, 0.62111, 0.04518, 0.59485, 0.07814, 0.55151, 0.12466, 0.48448, 0.17417, 0.40753, 0.22304, 0.33784, 0.28351, 0.2588, 0.34461, 0.18856, 0.42012, 0.10966, 0.49873, 0.03795, 0.59783, 0.13584, 0.58085, 0.29689, 0.56532, 0.45503, 0.55104, 0.61576], "triangles": [25, 26, 27, 23, 24, 29, 28, 29, 24, 27, 28, 24, 25, 27, 24, 22, 23, 30, 23, 29, 30, 22, 31, 32, 31, 22, 30, 39, 34, 38, 39, 33, 34, 39, 21, 33, 40, 21, 39, 32, 33, 21, 32, 21, 22, 20, 21, 40, 37, 0, 1, 37, 1, 2, 36, 0, 37, 37, 35, 36, 38, 35, 37, 37, 2, 3, 3, 38, 37, 38, 34, 35, 3, 4, 38, 5, 38, 4, 6, 39, 5, 18, 19, 6, 7, 18, 6, 5, 39, 38, 6, 19, 39, 40, 39, 19, 20, 40, 19, 8, 18, 7, 17, 18, 8, 9, 17, 8, 16, 17, 9, 10, 16, 9, 15, 16, 10, 15, 10, 11, 14, 15, 11, 14, 11, 12, 13, 14, 12], "vertices": [1, 4, 10.09, 3.33, 1, 1, 4, 9.07, -14.3, 1, 1, 4, 3.91, -23.01, 1, 1, 4, -8.56, -33.57, 1, 1, 4, -23.93, -44.77, 1, 1, 41, 19.04, 2.16, 1, 1, 41, 37.87, 3.49, 1, 2, 42, -2.53, 3.27, 0.09463, 41, 55.81, 3.51, 0.90537, 1, 42, 14.63, 4.35, 1, 1, 42, 29.27, 4.68, 1, 2, 43, 5.31, 4.69, 0.98468, 42, 43.5, 4.38, 0.01532, 1, 43, 15, 4.64, 1, 1, 43, 24.99, 4.35, 1, 1, 43, 27.35, 2.53, 1, 1, 43, 20.96, -1.64, 1, 4, 43, 14.32, -6.98, 0.97513, 42, 51.84, -7.78, 0.02476, 44, 2.83, 177.25, 9e-05, 45, -68.28, 171, 1e-05, 5, 43, 5.15, -15.23, 0.51841, 42, 42.22, -15.5, 0.47046, 41, 98.4, -19.75, 0.00778, 44, 4.52, 165.03, 0.00282, 45, -65.31, 159.03, 0.00052, 6, 43, -2.94, -25.79, 0.12323, 42, 33.53, -25.58, 0.7842, 41, 88.73, -28.88, 0.0735, 4, -119.23, -51.25, 0.00037, 44, 8.71, 152.41, 0.01559, 45, -59.81, 146.92, 0.00312, 6, 43, -13.78, -41.47, 0.00532, 42, 21.82, -40.62, 0.62911, 41, 75.54, -42.64, 0.28118, 4, -111.74, -33.73, 0.00808, 44, 15.61, 134.64, 0.06244, 45, -51.08, 129.98, 0.01387, 5, 42, 10.86, -61.99, 0.33012, 41, 62.45, -62.77, 0.41752, 4, -106.6, -10.27, 0.03606, 44, 28.32, 114.26, 0.17084, 45, -36.29, 111.06, 0.04547, 5, 42, -1.47, -87.76, 0.14372, 41, 47.54, -87.14, 0.32958, 4, -101.24, 17.79, 0.0627, 44, 44.1, 90.45, 0.33505, 45, -18.08, 89.04, 0.12895, 6, 42, -13.18, -117.92, 0.04915, 41, 32.8, -115.95, 0.14949, 4, -97.62, 49.94, 0.03431, 44, 63.98, 64.92, 0.41613, 45, 4.38, 65.75, 0.35017, 46, -32.28, 65.28, 0.00075, 6, 42, -20.83, -142.36, 0.01676, 41, 22.69, -139.47, 0.05521, 4, -96.46, 75.53, 0.00581, 44, 81.03, 45.81, 0.2648, 45, 23.35, 48.55, 0.62064, 46, -13.05, 48.36, 0.03679, 5, 42, -32.3, -165.98, 0.00314, 41, 8.86, -161.8, 0.01062, 44, 95.42, 23.85, 0.05017, 45, 39.98, 28.22, 0.63622, 46, 3.88, 28.28, 0.29984, 5, 42, -43.58, -181.56, 0.00022, 41, -3.95, -176.14, 0.00075, 44, 103, 6.17, 0.00084, 45, 49.38, 11.44, 0.13839, 46, 13.53, 11.65, 0.85981, 4, 42, -50.06, -195.63, 0, 41, -11.85, -189.48, 0, 45, 59.43, -0.35, 1e-05, 46, 23.76, 0.01, 0.99999, 4, 42, -52.53, -197.51, 0, 41, -14.5, -191.09, 0, 45, 60.09, -3.38, 0, 46, 24.47, -3.02, 1, 1, 46, 16.9, -3.71, 1, 1, 46, 10.51, -3.18, 1, 2, 45, 36.29, -2.94, 0.36808, 46, 0.66, -2.93, 0.63192, 1, 45, 21.8, -3.02, 1, 1, 45, 5.81, -3.71, 1, 1, 44, 43.01, -2.7, 1, 4, 42, -88.84, -117.96, 1e-05, 41, -42.46, -108.24, 5e-05, 4, -24.47, 69.28, 0.00141, 44, 25.18, -0.03, 0.99854, 4, 42, -94.42, -101.54, 0.00027, 41, -46.33, -91.34, 0.00386, 4, -14.89, 54.84, 0.20052, 44, 8.23, 3.61, 0.79535, 1, 4, -4.22, 37.05, 1, 1, 4, 5.34, 18.63, 1, 1, 4, -10.42, -3, 1, 5, 42, -62.53, -55.54, 0.01741, 41, -9.9, -48.85, 0.29902, 4, -33.99, 2.22, 0.47284, 44, -14.88, 54.58, 0.19993, 45, -72.95, 47.15, 0.01081, 5, 42, -41.38, -66.16, 0.06464, 41, 10.06, -61.57, 0.37056, 4, -57.15, 7.09, 0.21704, 44, 5.09, 67.29, 0.30155, 45, -54.43, 61.89, 0.04622, 5, 42, -19.77, -76.62, 0.12826, 41, 30.48, -74.2, 0.35802, 4, -80.71, 11.7, 0.09313, 44, 25.16, 80.46, 0.32391, 45, -35.86, 77.11, 0.09668], "hull": 37, "edges": [0, 72, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 24, 26, 34, 36, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 60, 62, 62, 64, 68, 70, 70, 72, 54, 56, 56, 58, 58, 60, 64, 66, 66, 68, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 36, 38, 38, 40, 40, 42, 42, 44, 2, 74, 74, 76, 76, 78, 78, 80, 30, 32, 32, 34], "width": 182, "height": 118}}, "a26": {"pifu/lzz_a0": {"type": "mesh", "uvs": [0.43121, 0, 0.54843, 0.03946, 0.67216, 0.07493, 0.82276, 0.20317, 0.75434, 0.34137, 0.99584, 0.47807, 0.97807, 0.65783, 0.5167, 0.99747, 0.16261, 0.91924, 0.02186, 0.60986, 0.02154, 0.38772, 0.209, 0.1849, 0.30424, 0.32684], "triangles": [12, 11, 0, 12, 0, 1, 4, 2, 3, 10, 11, 12, 9, 10, 12, 6, 4, 5, 8, 9, 12, 12, 4, 7, 1, 4, 12, 2, 4, 1, 7, 4, 6, 8, 12, 7], "vertices": [2, 24, -1.53, 12.49, 0.34897, 23, 51.13, 11.63, 0.65103, 2, 24, 3.27, 15.22, 0.53021, 23, 55.02, 15.54, 0.46979, 2, 24, 8.15, 18.28, 0.689, 23, 58.91, 19.78, 0.311, 2, 24, 16.62, 19.6, 0.80627, 23, 66.73, 23.32, 0.19373, 2, 24, 18.63, 13.47, 0.90417, 23, 70.3, 17.95, 0.09583, 2, 24, 30.17, 17.54, 0.99704, 23, 80.33, 24.94, 0.00296, 2, 24, 34.98, 11.9, 0.99999, 23, 86.47, 20.79, 1e-05, 1, 24, 30.88, -12.76, 1, 2, 24, 17.61, -22.15, 0.99328, 23, 78.8, -16.66, 0.00672, 2, 24, 4.04, -18.06, 0.88949, 23, 64.63, -16.34, 0.11051, 2, 24, -2.6, -11.83, 0.69495, 23, 56.57, -12.09, 0.30505, 1, 23, 53.16, -0.75, 1, 1, 24, 4.31, -0.86, 1], "hull": 12, "edges": [0, 22, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 2, 2, 4, 24, 2, 8, 10], "width": 36, "height": 33}}, "a37": {"pifu/lzz_a19": {"type": "mesh", "uvs": [0.03681, 0.47632, 0.14027, 0.46726, 0.24772, 0.3893, 0.26961, 0.28776, 0.27557, 0.1826, 0.31935, 0.07382, 0.37705, 0, 0.51633, 0.0013, 0.63771, 0.08288, 0.73918, 0.20073, 0.81678, 0.36935, 0.87051, 0.5289, 0.92423, 0.70477, 0.96204, 0.84619, 1, 1, 0.87648, 0.97855, 0.73918, 0.95135, 0.58001, 0.90421, 0.46858, 0.80812, 0.34721, 0.8063, 0.20792, 0.80812, 0.0567, 0.72472, 0, 0.61412, 0, 0.53434, 0.245, 0.57485, 0.41473, 0.53552, 0.47514, 0.40446, 0.52117, 0.23931, 0.65926, 0.40446, 0.69953, 0.6063, 0.82611, 0.80291, 0.58734, 0.66921, 0.41473, 0.66397], "triangles": [30, 12, 13, 15, 30, 13, 16, 30, 15, 15, 13, 14, 11, 29, 28, 32, 25, 31, 29, 31, 25, 29, 11, 12, 30, 29, 12, 31, 29, 30, 18, 32, 31, 17, 18, 31, 17, 31, 30, 16, 17, 30, 27, 7, 8, 27, 8, 9, 28, 27, 9, 28, 9, 10, 26, 27, 28, 11, 28, 10, 28, 25, 26, 29, 25, 28, 5, 6, 7, 5, 7, 27, 27, 4, 5, 27, 3, 4, 26, 3, 27, 2, 3, 26, 25, 2, 26, 24, 1, 2, 24, 2, 25, 24, 22, 23, 24, 25, 32, 24, 23, 1, 1, 23, 0, 21, 22, 24, 19, 24, 32, 20, 21, 24, 19, 20, 24, 19, 32, 18], "vertices": [1, 31, 5.37, 20.57, 1, 1, 31, 28.06, 10.56, 1, 4, 31, 60.41, 15.82, 0.73683, 32, -0.26, 21.16, 0.26315, 33, 46.02, -80.2, 2e-05, 34, -74.21, -81.47, 0, 4, 31, 77.98, 36.45, 0.04068, 32, 26.52, 25.26, 0.95905, 33, 24.58, -63.63, 0.00027, 34, -95.93, -65.28, 0, 3, 32, 52.91, 33.25, 0.99997, 33, 0.59, -50.05, 3e-05, 34, -120.15, -52.1, 0, 3, 32, 83.25, 33.08, 1, 33, -20.22, -27.96, 0, 34, -141.33, -30.38, 0, 2, 32, 106.1, 26.67, 1, 34, -152.82, -9.61, 0, 2, 32, 117.05, -4.71, 0.71144, 33, -16.19, 22.57, 0.28856, 2, 32, 106.76, -39.2, 0.04723, 33, 15.87, 38.92, 0.95277, 1, 33, 54.3, 46.78, 1, 2, 33, 102.08, 43.59, 0.87637, 34, -20.27, 43.25, 0.12363, 2, 33, 145.18, 36.37, 0.15157, 34, 22.95, 36.77, 0.84843, 2, 34, 70.02, 28.44, 0.8919, 35, -8.31, 30.19, 0.1081, 2, 34, 107.32, 20.58, 0.02252, 35, 27.22, 16.37, 0.97748, 1, 35, 65.42, 0.72, 1, 3, 31, 115.77, -192.16, 0.00061, 34, 129.76, -12.8, 0.00383, 35, 43.93, -20.22, 0.99556, 3, 31, 90.68, -169.92, 0.02152, 34, 109.19, -39.27, 0.27243, 35, 19.32, -42.99, 0.70605, 5, 31, 63.59, -140.57, 0.11733, 32, -100.25, -99.14, 2e-05, 33, 202.08, -69.58, 0.00033, 34, 81.65, -68.2, 0.66238, 35, -12.55, -67.05, 0.21993, 5, 31, 52.72, -105.61, 0.36008, 32, -85.58, -65.59, 0.00518, 33, 167.69, -82.11, 0.02072, 34, 47.47, -81.31, 0.57819, 35, -48.41, -74.43, 0.03583, 5, 31, 27.69, -91.03, 0.65243, 32, -94.95, -38.19, 0.00201, 33, 154.31, -107.8, 0.01783, 34, 34.54, -107.23, 0.32543, 35, -65.39, -97.89, 0.00228, 3, 31, -1.53, -75.18, 0.83585, 33, 139.87, -137.74, 0.00287, 34, 20.61, -137.41, 0.16128, 2, 31, -22.31, -38.48, 0.95166, 34, -14.67, -160.52, 0.04834, 2, 31, -19.94, -6.61, 0.99317, 34, -46.64, -160.21, 0.00683, 1, 31, -9.72, 11.61, 1, 4, 31, 36.08, -26.24, 0.92445, 32, -46.2, 5.3, 0.00128, 33, 89.19, -102.51, 0.01321, 34, -30.66, -103.04, 0.06107, 4, 31, 76.44, -37.07, 0.47585, 32, -22.78, -29.31, 0.1911, 33, 98.09, -61.68, 0.19375, 34, -22.46, -62.07, 0.13929, 4, 31, 105.8, -14.19, 0.02804, 32, 14.39, -31.24, 0.50449, 33, 73.84, -33.43, 0.45999, 34, -47.19, -34.24, 0.00748, 2, 32, 58.8, -26.92, 0.07233, 33, 40.07, -4.27, 0.92767, 1, 33, 93.49, 5.86, 1, 4, 31, 126.64, -86.49, 0.01532, 32, -17.19, -99.53, 0.00299, 33, 145.06, -9.18, 0.00272, 34, 23.61, -8.78, 0.97897, 3, 31, 127.79, -146.16, 0.00132, 34, 83.09, -3.78, 0.52824, 35, -0.65, -3.72, 0.47045, 5, 31, 95.23, -87.76, 0.16616, 32, -41.76, -79.93, 0.02186, 33, 147.83, -40.49, 0.06843, 34, 26.91, -40.04, 0.73765, 35, -61.99, -30.36, 0.0059, 5, 31, 59.98, -66.41, 0.5336, 32, -54.43, -40.71, 0.03587, 33, 128.18, -76.72, 0.08026, 34, 7.88, -76.6, 0.34923, 35, -86.71, -63.33, 0.00104], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46], "width": 195, "height": 214}}, "a38": {"pifu/lzz_a2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [45.42, -188.4, -137.79, 1.68, 46.53, 179.33, 229.74, -10.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 211, "height": 205}}, "a39": {"pifu/lzz_a21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-93.38, -74.95, -87.96, 110.97, 93.96, 105.67, 88.54, -80.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 74, "height": 73}}, "b2": {"pifu/lzz_b1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-243.41, -242.91, -243.41, 319.09, 330.59, 319.09, 330.59, -242.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 134, "height": 138}}, "a36": {"pifu/lzz_a18": {"type": "mesh", "uvs": [0.60193, 0.00376, 0.62516, 0.02419, 0.69491, 0.12754, 0.69845, 0.21407, 0.70174, 0.29458, 0.72365, 0.35145, 0.74987, 0.41947, 0.79808, 0.45557, 0.83016, 0.4796, 0.90794, 0.48614, 0.95516, 0.49011, 0.98449, 0.51212, 0.99932, 0.56362, 0.9904, 0.61638, 0.9562, 0.65821, 0.91629, 0.70261, 0.85728, 0.74862, 0.77495, 0.76902, 0.71145, 0.80104, 0.69514, 0.74321, 0.62862, 0.79152, 0.60566, 0.79043, 0.59202, 0.75312, 0.59399, 0.69235, 0.55165, 0.75391, 0.49755, 0.80314, 0.47255, 0.79752, 0.4761, 0.73687, 0.42819, 0.79205, 0.38264, 0.84044, 0.32848, 0.8883, 0.28393, 0.9171, 0.288, 0.85142, 0.24159, 0.89383, 0.2018, 0.93018, 0.15125, 0.96076, 0.14992, 0.93149, 0.18992, 0.8533, 0.14437, 0.89498, 0.10368, 0.93221, 0.07584, 0.95768, 0.04447, 0.97876, 0.01287, 1, 0.00386, 1, 0.00096, 0.9903, 0.01592, 0.96663, 0.03412, 0.93782, 0.05058, 0.89539, 0.07013, 0.84503, 0.08638, 0.79182, 0.10465, 0.732, 0.12476, 0.66616, 0.14036, 0.61508, 0.15695, 0.56074, 0.12796, 0.56252, 0.15435, 0.49005, 0.19001, 0.40605, 0.23154, 0.32486, 0.23253, 0.29725, 0.28389, 0.21255, 0.34907, 0.12667, 0.43189, 0.0579, 0.51399, 0.0187, 0.57585, 0.00202, 0.23491, 0.78317, 0.27719, 0.71031, 0.31574, 0.6363, 0.35305, 0.55547, 0.54704, 0.13364, 0.53088, 0.26229, 0.55948, 0.4103, 0.64653, 0.52984, 0.90145, 0.5788, 0.77959, 0.58791], "triangles": [37, 49, 64, 45, 41, 42, 45, 42, 44, 44, 42, 43, 45, 46, 41, 41, 46, 40, 35, 36, 34, 40, 46, 39, 46, 47, 39, 39, 47, 38, 34, 36, 37, 34, 37, 33, 47, 48, 38, 38, 48, 37, 48, 49, 37, 55, 56, 67, 31, 32, 30, 33, 37, 32, 30, 32, 29, 37, 64, 32, 29, 32, 65, 28, 29, 65, 24, 25, 27, 25, 26, 27, 65, 32, 64, 28, 65, 27, 49, 50, 64, 65, 64, 51, 65, 66, 27, 23, 27, 67, 64, 50, 51, 51, 52, 65, 65, 52, 66, 27, 66, 67, 52, 53, 66, 66, 53, 67, 54, 55, 53, 53, 55, 67, 56, 57, 67, 67, 57, 70, 70, 57, 58, 69, 58, 59, 58, 69, 70, 68, 0, 1, 59, 60, 69, 69, 60, 68, 68, 60, 61, 68, 63, 0, 61, 62, 68, 68, 62, 63, 70, 69, 4, 70, 4, 5, 3, 68, 2, 4, 69, 3, 68, 1, 2, 3, 69, 68, 18, 19, 17, 19, 20, 22, 20, 21, 22, 16, 17, 73, 22, 23, 19, 23, 24, 27, 17, 19, 73, 15, 73, 72, 15, 16, 73, 19, 23, 73, 15, 72, 14, 23, 71, 73, 23, 67, 71, 14, 72, 13, 13, 72, 12, 73, 71, 7, 73, 8, 72, 73, 7, 8, 7, 71, 6, 72, 11, 12, 72, 10, 11, 72, 9, 10, 72, 8, 9, 67, 70, 71, 71, 70, 6, 70, 5, 6], "vertices": [3, 36, 157.19, -91.8, 0.00162, 37, 122.93, -17.46, 0.41539, 38, -30.2, -5.67, 0.583, 2, 37, 115.12, -21.37, 0.6565, 38, -28.28, 2.85, 0.3435, 2, 37, 79.98, -28.97, 0.86186, 38, -11.9, 34.86, 0.13814, 2, 37, 55.51, -21.32, 0.96067, 38, 9.52, 48.94, 0.03933, 2, 36, 89.27, -32.41, 0.08068, 37, 32.75, -14.21, 0.91932, 2, 36, 75.43, -21.13, 0.27237, 37, 14.9, -14.18, 0.72763, 2, 36, 58.88, -7.65, 0.57517, 37, -6.45, -14.16, 0.42483, 2, 36, 42.16, -5.33, 0.82772, 37, -20.88, -22.9, 0.17228, 2, 36, 31.03, -3.79, 0.96937, 37, -30.49, -28.72, 0.03063, 2, 36, 12.03, -13.11, 0.9999, 37, -39.35, -47.95, 0.0001, 2, 36, 0.5, -18.77, 0.9997, 39, -79.36, 164.07, 0.0003, 3, 36, -9.68, -17.35, 0.99571, 38, 44.86, 160.93, 0.00077, 39, -76.44, 173.93, 0.00352, 2, 36, -21.05, -6.43, 0.99839, 39, -63.95, 183.55, 0.00161, 2, 36, -27.12, 8.16, 0.99529, 39, -48.63, 187.38, 0.00471, 3, 36, -25.66, 23.55, 0.98749, 38, 85.83, 176.76, 0.00017, 39, -33.62, 183.65, 0.01234, 3, 36, -23.27, 40.41, 0.9721, 38, 102.67, 174.31, 0.00132, 39, -17.3, 178.79, 0.02658, 3, 36, -16.72, 60.36, 0.9496, 38, 122.61, 167.68, 0.00393, 39, 1.46, 169.34, 0.04648, 3, 36, -0.82, 77.14, 0.92044, 38, 139.32, 151.72, 0.009, 39, 15.68, 151.12, 0.07056, 3, 36, 8.94, 94.19, 0.88628, 38, 156.34, 141.9, 0.0164, 39, 31.1, 138.95, 0.09733, 3, 36, 21.63, 81.88, 0.85017, 38, 143.99, 129.26, 0.02523, 39, 17.04, 128.23, 0.1246, 3, 36, 29.57, 103.48, 0.81698, 38, 165.55, 121.24, 0.03345, 39, 37.22, 117.17, 0.14957, 3, 36, 35.05, 106.44, 0.78224, 38, 168.5, 115.75, 0.04282, 39, 39.34, 111.31, 0.17494, 3, 36, 43.95, 98.94, 0.71935, 38, 160.96, 106.87, 0.05437, 39, 30.6, 103.61, 0.22628, 3, 36, 52.87, 83.31, 0.62177, 38, 145.29, 98.01, 0.06753, 39, 13.81, 97.12, 0.3107, 3, 36, 53.17, 104.84, 0.50948, 38, 166.83, 97.63, 0.0759, 39, 35.06, 93.62, 0.41462, 3, 36, 58.1, 124.91, 0.42543, 38, 186.88, 92.63, 0.07843, 39, 54.18, 85.77, 0.49613, 4, 36, 64.75, 127.02, 0.35727, 38, 188.97, 85.97, 0.07192, 39, 55.28, 78.88, 0.57066, 40, -52.67, 68.12, 0.00015, 4, 36, 73.28, 111.2, 0.2835, 38, 173.11, 77.5, 0.05795, 39, 38.36, 72.8, 0.65568, 40, -67.74, 58.31, 0.00287, 4, 36, 75.85, 131.9, 0.18775, 38, 193.81, 74.85, 0.03611, 39, 58.46, 67.17, 0.76274, 40, -46.89, 57.45, 0.0134, 4, 36, 78.93, 150.56, 0.10627, 38, 212.45, 71.71, 0.01665, 39, 76.45, 61.36, 0.84533, 40, -28.04, 55.93, 0.03175, 4, 36, 84.08, 170.3, 0.05512, 38, 232.17, 66.48, 0.00435, 39, 95.2, 53.34, 0.87881, 40, -7.95, 52.43, 0.06172, 4, 36, 89.94, 183.86, 0.03365, 38, 245.71, 60.57, 0.00066, 39, 107.75, 45.52, 0.82563, 40, 6.06, 47.7, 0.14006, 4, 36, 99.13, 166.69, 0.02157, 38, 228.51, 51.45, 2e-05, 39, 89.4, 38.99, 0.68996, 40, -10.3, 37.13, 0.28846, 4, 36, 103.33, 183.96, 0.0114, 38, 245.76, 47.18, 0, 39, 105.86, 32.27, 0.48998, 40, 7.26, 34.37, 0.49862, 4, 36, 106.93, 198.76, 0.00426, 38, 260.55, 43.53, 0, 39, 119.96, 26.51, 0.3156, 40, 22.31, 32.01, 0.68014, 3, 36, 113.91, 213.62, 0.00082, 39, 133.62, 17.4, 0.22511, 40, 37.69, 26.28, 0.77407, 3, 36, 118.73, 206.42, 0.00139, 39, 125.78, 13.71, 0.25349, 40, 30.91, 20.88, 0.74511, 4, 36, 121.53, 181.01, 0.00497, 38, 242.75, 28.99, 0.00028, 39, 100.24, 14.71, 0.3526, 40, 5.83, 15.99, 0.64214, 3, 36, 125.64, 197.98, 0.00135, 39, 116.4, 8.12, 0.15256, 40, 23.07, 13.29, 0.84608, 3, 36, 129.31, 213.12, 0.00022, 39, 130.84, 2.24, 0.03426, 40, 38.47, 10.88, 0.96552, 1, 40, 49.01, 9.24, 1, 1, 40, 59.06, 6.05, 1, 1, 40, 69.19, 2.84, 1, 1, 40, 70.63, 0.87, 1, 1, 40, 68.77, -1.46, 1, 1, 40, 60.72, -2.32, 1, 2, 39, 139.69, -14.48, 0.00157, 40, 50.93, -3.36, 0.99843, 2, 39, 126.38, -15.25, 0.04246, 40, 38.15, -7.16, 0.95754, 2, 39, 110.59, -16.16, 0.1845, 40, 22.99, -11.68, 0.8155, 2, 39, 94.36, -18.22, 0.43452, 40, 7.67, -17.41, 0.56548, 2, 39, 76.13, -20.53, 0.71699, 40, -9.55, -23.85, 0.28301, 3, 38, 204.5, -14.79, 0.00466, 39, 56.05, -23.07, 0.90363, 40, -28.5, -30.93, 0.09171, 3, 38, 189.38, -19, 0.01568, 39, 40.48, -25.05, 0.97435, 40, -43.21, -36.43, 0.00997, 2, 38, 173.29, -23.48, 0.06161, 39, 23.91, -27.15, 0.93839, 2, 38, 177.8, -29.93, 0.19245, 39, 27.44, -34.19, 0.80755, 2, 38, 155.75, -34.92, 0.42127, 39, 4.9, -35.93, 0.57873, 2, 38, 129.47, -39.53, 0.69136, 39, -21.77, -36.68, 0.30864, 2, 38, 103.08, -42.34, 0.88877, 39, -48.29, -35.64, 0.11123, 2, 38, 95.95, -46.34, 0.98224, 39, -55.92, -38.56, 0.01776, 2, 38, 67.3, -47.41, 0.99955, 39, -84.43, -35.47, 0.00045, 3, 37, 111.52, 59.28, 0.0033, 38, 36.41, -45.46, 0.99668, 39, -114.71, -29.07, 2e-05, 2, 37, 123.21, 31.33, 0.04288, 38, 7.37, -36.8, 0.95712, 2, 37, 126.72, 6.48, 0.1341, 38, -14.08, -23.77, 0.8659, 2, 37, 125.77, -10.97, 0.25877, 38, -26.98, -11.98, 0.74123, 4, 36, 121.93, 156.94, 0.01782, 38, 218.68, 28.68, 0.00614, 39, 76.37, 17.89, 0.67359, 40, -18.13, 13.6, 0.30245, 4, 36, 123.38, 132.56, 0.03698, 38, 194.29, 27.31, 0.05779, 39, 52.05, 20.07, 0.81667, 40, -42.31, 10.14, 0.08855, 4, 36, 125.88, 108.42, 0.05704, 38, 170.14, 24.91, 0.13432, 39, 27.81, 21.19, 0.80268, 40, -66.16, 5.66, 0.00596, 3, 36, 129.71, 82.73, 0.06811, 38, 144.44, 21.17, 0.19283, 39, 1.83, 21.22, 0.73905, 4, 36, 149.87, -51.24, 0.05925, 37, 91.66, 9.4, 0.29317, 38, 10.39, 1.51, 0.646, 39, -133.65, 21.18, 0.00158, 4, 36, 133.77, -16.45, 0.20939, 37, 57.22, 26.25, 0.21934, 38, 45.24, 17.47, 0.56037, 39, -96.85, 31.93, 0.0109, 4, 36, 104.34, 16.91, 0.46152, 37, 13.34, 33.57, 0.13575, 38, 78.71, 46.79, 0.37872, 39, -59.49, 56.08, 0.02401, 4, 36, 65.77, 34.83, 0.71449, 37, -27.89, 23.15, 0.07069, 38, 96.77, 85.29, 0.18048, 39, -36.04, 91.56, 0.03434, 3, 36, -0.75, 11.22, 0.97121, 38, 73.4, 151.9, 0.01238, 39, -49.52, 160.85, 0.0164, 4, 36, 26.04, 30.72, 0.89246, 37, -56.13, -5.1, 0.01655, 38, 92.81, 125.04, 0.06177, 39, -34.21, 131.46, 0.02921], "hull": 64, "edges": [0, 126, 0, 2, 2, 4, 20, 22, 22, 24, 24, 26, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 68, 70, 70, 72, 72, 74, 84, 86, 86, 88, 106, 108, 108, 110, 114, 116, 120, 122, 122, 124, 124, 126, 92, 94, 94, 96, 80, 82, 82, 84, 88, 90, 90, 92, 78, 80, 74, 76, 76, 78, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 110, 112, 112, 114, 116, 118, 118, 120, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 54, 56, 56, 58, 64, 66, 66, 68, 74, 128, 128, 130, 130, 132, 132, 134, 0, 136, 136, 138, 138, 140, 140, 142, 22, 144, 144, 146, 146, 142], "width": 217, "height": 237}}, "a40": {"pifu/lzz_a21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-93.38, -74.95, -87.96, 110.97, 93.96, 105.67, 88.54, -80.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 74, "height": 73}}, "a41": {"pifu/lzz_a20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-96.2, -83.26, -92.41, 102.7, 89.55, 98.99, 85.76, -86.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 74, "height": 73}}, "a42": {"pifu/lzz_a20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-96.2, -83.26, -92.41, 102.7, 89.55, 98.99, 85.76, -86.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 74, "height": 73}}, "a43": {"pifu/lzz_a22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-82.93, -77.74, -86.41, 82.23, 69.55, 85.62, 73.03, -74.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 62}}, "a44": {"pifu/lzz_a22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-82.93, -77.74, -86.41, 82.23, 69.55, 85.62, 73.03, -74.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 62}}, "a45": {"pifu/lzz_a3": {"type": "mesh", "uvs": [0.70324, 1e-05, 0.81434, 0.0674, 0.94514, 0.24841, 0.99177, 0.46516, 1, 0.68334, 0.94732, 0.8168, 0.73034, 0.9028, 0.52146, 0.98559, 0.24581, 1, 0.04386, 0.93131, 0, 0.88778, 0.03579, 0.65535, 0.16179, 0.3678, 0.4042, 0.11365, 0.6338, 0.00717, 0.77843, 0.41371, 0.17398, 0.76791, 0.42385, 0.60443], "triangles": [15, 0, 1, 15, 1, 2, 15, 2, 3, 15, 17, 13, 12, 13, 17, 16, 11, 12, 17, 16, 12, 14, 0, 15, 15, 13, 14, 4, 5, 15, 4, 15, 3, 10, 11, 16, 6, 17, 15, 6, 15, 5, 9, 10, 16, 7, 17, 6, 8, 16, 17, 7, 8, 17, 9, 16, 8], "vertices": [1, 22, -10.09, -20.81, 1, 1, 22, -12.96, -11.31, 1, 1, 22, -11.36, 4.82, 1, 1, 22, -3.33, 17.97, 1, 1, 22, 6.91, 29.02, 1, 2, 23, 17.67, 52.43, 0.00012, 22, 16.38, 32.55, 0.99988, 2, 23, 14.91, 34.5, 0.01165, 22, 32.66, 24.55, 0.98835, 2, 23, 12.25, 17.24, 0.106, 22, 48.33, 16.85, 0.894, 1, 23, 2.95, -2.48, 1, 2, 23, -8.69, -14.37, 0.0302, 22, 72.23, -12.59, 0.9698, 2, 23, -12.97, -16.03, 0.0126, 22, 72.53, -17.17, 0.9874, 1, 22, 59.15, -26.45, 1, 1, 22, 38.05, -33.34, 1, 1, 22, 12.11, -32.08, 1, 1, 22, -5.87, -24.36, 1, 1, 22, 6.01, 3.5, 1, 1, 22, 56.98, -13.23, 1, 1, 22, 35.08, -7.14, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 10, 12, 12, 14, 4, 30, 30, 0, 30, 10, 18, 32, 32, 34], "width": 63, "height": 55}}, "a46": {"pifu/lzz_a5": {"type": "mesh", "uvs": [0.03355, 0.00245, 0.04618, 0.06395, 0.08552, 0.09753, 0.14049, 0.12351, 0.19201, 0.13868, 0.22187, 0.11607, 0.27952, 0.13859, 0.33023, 0.17286, 0.37905, 0.21226, 0.42486, 0.2606, 0.50201, 0.24137, 0.58335, 0.23032, 0.67343, 0.23704, 0.74457, 0.26641, 0.77186, 0.23512, 0.78376, 0.18799, 0.78666, 0.13439, 0.79729, 0.10232, 0.78812, 0.05591, 0.76147, 0.01931, 0.79722, 0.01594, 0.82522, 0.04404, 0.85625, 0.07585, 0.885, 0.11674, 0.91213, 0.1618, 0.93526, 0.21298, 0.9507, 0.26233, 0.95949, 0.31176, 0.9948, 0.3126, 0.99084, 0.43944, 0.98243, 0.59267, 0.89004, 0.71742, 0.8849, 0.79221, 0.95116, 0.8526, 0.96997, 0.94381, 0.94455, 0.99378, 0.88525, 1, 0.79985, 0.9281, 0.76358, 0.98641, 0.73826, 0.98847, 0.60248, 0.99952, 0.46601, 0.92759, 0.33644, 0.8593, 0.26946, 0.92517, 0.19266, 0.93926, 0.15944, 0.89403, 0.16765, 0.82814, 0.19329, 0.78491, 0.24407, 0.73389, 0.21312, 0.64529, 0.16544, 0.5296, 0.20252, 0.50512, 0.18436, 0.44323, 0.16485, 0.38494, 0.11006, 0.34407, 0.06341, 0.3031, 0.14597, 0.29744, 0.10421, 0.24836, 0.06802, 0.1974, 0.03711, 0.13994, 0.02151, 0.09447, 0.01296, 0.05581, 0.01481, 0.00593, 0.68871, 0.38848, 0.71789, 0.55889, 0.70677, 0.72698, 0.74428, 0.83131, 0.74567, 0.91245, 0.56937, 0.52391, 0.56236, 0.64476, 0.55681, 0.73866, 0.57626, 0.8314, 0.61932, 0.91023, 0.39221, 0.49505, 0.39528, 0.63324, 0.41067, 0.73788, 0.43806, 0.83239, 0.81616, 0.40836, 0.82728, 0.51965, 0.81477, 0.63905, 0.79671, 0.72367, 0.80505, 0.78975, 0.8245, 0.83148, 0.33845, 0.7852, 0.31483, 0.72261, 0.31882, 0.40098, 0.2688, 0.32447, 0.22712, 0.25607, 0.17155, 0.19115, 0.53466, 0.3948, 0.27389, 0.61124, 0.81138, 0.32322, 0.83775, 0.26122, 0.84853, 0.19622, 0.84254, 0.13322, 0.71216, 0.64542], "triangles": [93, 14, 15, 92, 93, 25, 94, 15, 16, 15, 94, 93, 93, 24, 25, 24, 94, 23, 24, 93, 94, 16, 17, 94, 94, 22, 23, 94, 17, 22, 22, 18, 21, 22, 17, 18, 18, 20, 21, 18, 19, 20, 3, 58, 59, 3, 59, 2, 59, 60, 2, 60, 1, 2, 60, 61, 1, 1, 61, 0, 61, 62, 0, 8, 87, 7, 56, 57, 87, 57, 88, 87, 87, 88, 6, 6, 88, 4, 87, 6, 7, 6, 4, 5, 57, 58, 88, 58, 3, 88, 88, 3, 4, 54, 55, 56, 56, 87, 86, 92, 14, 93, 92, 25, 26, 54, 56, 53, 92, 26, 91, 27, 77, 26, 29, 27, 28, 26, 77, 91, 91, 13, 92, 53, 56, 86, 86, 8, 9, 30, 78, 29, 64, 77, 78, 64, 63, 77, 89, 11, 63, 89, 10, 11, 51, 85, 73, 51, 52, 85, 73, 85, 89, 9, 10, 89, 34, 35, 36, 40, 72, 39, 40, 41, 72, 34, 36, 37, 33, 37, 82, 37, 33, 34, 39, 67, 38, 39, 72, 67, 38, 67, 37, 44, 45, 43, 82, 32, 33, 37, 67, 82, 42, 76, 41, 41, 71, 72, 41, 76, 71, 42, 43, 46, 72, 66, 67, 67, 66, 82, 72, 71, 66, 43, 45, 46, 46, 47, 42, 42, 47, 83, 83, 47, 48, 42, 83, 76, 83, 48, 84, 83, 75, 76, 76, 70, 71, 76, 75, 70, 66, 81, 82, 82, 81, 32, 71, 65, 66, 71, 70, 65, 81, 65, 80, 81, 66, 65, 32, 81, 31, 81, 80, 31, 83, 84, 75, 70, 69, 65, 70, 75, 69, 84, 74, 75, 75, 74, 69, 48, 49, 84, 65, 95, 80, 65, 69, 95, 80, 79, 31, 80, 95, 79, 49, 90, 84, 84, 90, 74, 31, 79, 30, 30, 79, 78, 95, 64, 79, 95, 69, 64, 49, 50, 90, 69, 68, 64, 69, 74, 68, 79, 64, 78, 90, 73, 74, 74, 73, 68, 50, 51, 90, 90, 51, 73, 68, 63, 64, 73, 89, 68, 68, 89, 63, 52, 53, 85, 53, 86, 85, 85, 9, 89, 85, 86, 9, 27, 29, 77, 78, 77, 29, 63, 91, 77, 11, 12, 63, 63, 13, 91, 63, 12, 13, 92, 13, 14, 8, 86, 87], "vertices": [2, 27, 24.16, -4.19, 0.9, 9, 75.7, 93.36, 0.1, 3, 7, 114.01, 77.16, 0, 27, 14.89, -1.03, 0.9, 9, 66.29, 90.62, 0.1, 4, 7, 109.36, 71.44, 0, 26, 31.53, -4.89, 0.18796, 27, 7.79, -3.04, 0.71204, 9, 61.63, 84.9, 0.1, 5, 7, 106.12, 63.83, 0, 26, 23.63, -7.38, 0.48146, 27, 0.77, -7.42, 0.41853, 28, 29.18, 93.37, 1e-05, 9, 58.4, 77.29, 0.1, 5, 7, 104.52, 56.85, 0, 26, 17.29, -10.68, 0.78139, 27, -4.54, -12.21, 0.11853, 28, 26.45, 86.75, 8e-05, 9, 56.79, 70.31, 0.1, 5, 7, 108.49, 53.37, 0.00013, 26, 17.21, -15.97, 0.89185, 27, -3.29, -17.34, 0.00649, 28, 29.8, 82.66, 0.00153, 9, 60.76, 66.83, 0.1, 4, 7, 105.83, 45.46, 0.0037, 26, 9.48, -19.07, 0.88821, 28, 25.88, 75.3, 0.00809, 9, 58.1, 58.92, 0.1, 4, 7, 101.24, 38.25, 0.0864, 26, 1.01, -20.26, 0.7909, 28, 20.16, 68.95, 0.02271, 9, 53.51, 51.71, 0.1, 4, 7, 95.81, 31.2, 0.55187, 26, -7.88, -20.71, 0.33373, 28, 13.65, 62.88, 0.0144, 9, 48.09, 44.66, 0.1, 2, 7, 88.95, 24.38, 0.9, 9, 41.23, 37.84, 0.1, 2, 7, 93.1, 14.68, 0.9, 9, 45.37, 28.14, 0.1, 1, 7, 96.03, 4.29, 1, 2, 7, 96.31, -7.56, 0.9, 9, 48.59, 5.9, 0.1, 2, 7, 92.79, -17.34, 0.9, 9, 45.06, -3.88, 0.1, 5, 7, 98.07, -20.33, 0.70041, 26, -45.61, -55.88, 0.00205, 29, -9.75, 17.18, 0.00543, 28, 7.41, 11.68, 0.19212, 9, 50.35, -6.88, 0.1, 5, 7, 105.6, -21.05, 0.11241, 26, -41.26, -62.07, 0.005, 29, -3.88, 12.42, 0.18102, 28, 14.72, 9.74, 0.60157, 9, 57.88, -7.59, 0.1, 5, 7, 114, -20.47, 0.0003, 26, -35.37, -68.09, 0.00071, 29, 3.43, 8.24, 0.46515, 28, 23.1, 8.92, 0.43383, 9, 66.28, -7.01, 0.1, 3, 29, 7.28, 4.7, 0.75187, 28, 28.06, 7.27, 0.14813, 9, 71.44, -7.83, 0.1, 3, 29, 14.31, 2.45, 0.8841, 28, 35.4, 8.09, 0.0159, 9, 78.54, -5.81, 0.1, 3, 29, 21.02, 2.93, 0.89997, 28, 41.32, 11.28, 3e-05, 9, 83.86, -1.69, 0.1, 2, 29, 19.35, -1.48, 0.9, 9, 84.91, -6.28, 0.1, 2, 29, 13.75, -2.73, 0.9, 9, 80.95, -10.43, 0.1, 3, 29, 7.45, -4.07, 0.77782, 28, 31.81, -0.66, 0.12218, 9, 76.44, -15.03, 0.1, 3, 29, 0.02, -4.49, 0.47782, 28, 25.21, -4.09, 0.42218, 9, 70.49, -19.5, 0.1, 3, 29, -7.9, -4.42, 0.17782, 28, 17.96, -7.27, 0.72218, 9, 63.86, -23.83, 0.1, 3, 7, 103.95, -41.21, 0.11386, 28, 9.78, -9.88, 0.78614, 9, 56.22, -27.75, 0.1, 3, 7, 96.48, -44.1, 0.67306, 28, 1.93, -11.5, 0.22694, 9, 48.75, -30.64, 0.1, 2, 7, 88.9, -46.12, 0.9, 9, 41.17, -32.66, 0.1, 2, 7, 89.29, -50.73, 0.9, 9, 41.57, -37.27, 0.1, 2, 7, 69.45, -52.47, 0.9, 9, 21.72, -39.01, 0.1, 2, 7, 45.42, -54.1, 0.9, 9, -2.31, -40.64, 0.1, 2, 7, 24.59, -44.29, 0.9, 9, -23.14, -30.83, 0.1, 2, 7, 12.85, -44.95, 0.9, 9, -34.88, -31.49, 0.1, 2, 7, 4.41, -54.64, 0.9, 9, -43.32, -41.18, 0.1, 2, 7, -9.54, -58.71, 0.9, 9, -57.27, -45.25, 0.1, 2, 7, -17.71, -56.29, 0.9, 9, -65.44, -42.83, 0.1, 2, 7, -19.56, -48.68, 0.9, 9, -67.29, -35.22, 0.1, 3, 7, -9.61, -36.29, 0.82594, 9, -57.34, -22.83, 0.11246, 8, -17.18, -24.29, 0.0616, 3, 7, -19.24, -32.61, 0.7848, 9, -66.97, -19.15, 0.0872, 8, -26.81, -20.6, 0.128, 3, 7, -19.94, -29.35, 0.7992, 9, -67.67, -15.89, 0.0888, 8, -27.51, -17.34, 0.112, 3, 7, -23.68, -11.87, 0.7992, 9, -71.4, 1.59, 0.0888, 8, -31.24, 0.13, 0.112, 3, 7, -14.48, 7.17, 0.8064, 9, -62.21, 20.63, 0.0896, 8, -22.05, 19.18, 0.104, 2, 7, -5.75, 25.25, 0.9, 9, -53.48, 38.71, 0.1, 2, 7, -17.02, 32.8, 0.9, 9, -64.74, 46.26, 0.1, 2, 7, -20.35, 42.54, 0.9, 9, -68.08, 56, 0.1, 2, 7, -13.79, 47.67, 0.9, 9, -61.52, 61.13, 0.1, 2, 7, -3.39, 47.77, 0.9, 9, -51.12, 61.23, 0.1, 2, 7, 3.73, 45.2, 0.9, 9, -43.99, 58.66, 0.1, 2, 7, 12.44, 39.5, 0.9, 9, -35.28, 52.96, 0.1, 2, 7, 25.81, 45.1, 0.9, 9, -21.92, 58.56, 0.1, 2, 7, 43.14, 53.36, 0.9, 9, -4.58, 66.82, 0.1, 2, 7, 47.51, 48.97, 0.9, 9, -0.21, 62.43, 0.1, 2, 7, 56.9, 52.43, 0.9, 9, 9.17, 65.89, 0.1, 2, 7, 65.7, 56.01, 0.9, 9, 17.98, 69.47, 0.1, 4, 7, 71.27, 63.87, 0.56617, 26, 1.04, 19.16, 0.33382, 28, -5.19, 99.14, 2e-05, 9, 23.54, 77.33, 0.1, 4, 7, 76.96, 70.67, 0.55139, 26, 9.91, 19.24, 0.34859, 28, 1.55, 104.91, 1e-05, 9, 29.24, 84.13, 0.1, 3, 7, 79.07, 60.02, 0.51895, 26, 3.18, 10.73, 0.38105, 9, 31.35, 73.48, 0.1, 4, 7, 86.11, 66.33, 0.08363, 26, 12.55, 9.47, 0.78774, 27, -14.18, 6.12, 0.02864, 9, 38.38, 79.79, 0.1, 3, 26, 21.63, 7.48, 0.57448, 27, -4.89, 6.46, 0.32552, 9, 45.8, 85.41, 0.1, 3, 26, 30.99, 4.29, 0.27448, 27, 4.97, 5.71, 0.62552, 9, 54.3, 90.45, 0.1, 3, 26, 37.6, 0.91, 0.00312, 27, 12.22, 4.1, 0.89688, 9, 61.16, 93.29, 0.1, 2, 27, 18.09, 2.18, 0.9, 9, 67.07, 95.09, 0.1, 2, 27, 24.85, -1.77, 0.9, 9, 74.87, 95.73, 0.1, 2, 7, 72.92, -12.24, 0.72, 8, 65.35, -0.23, 0.28, 2, 7, 46.77, -19.06, 0.72, 8, 39.2, -7.06, 0.28, 2, 7, 20.38, -20.6, 0.72, 8, 12.82, -8.6, 0.28, 2, 7, 4.66, -27.34, 0.72, 8, -2.9, -15.33, 0.28, 2, 7, -7.97, -28.96, 0.816, 8, -15.54, -16.96, 0.184, 2, 7, 50.02, 0.89, 0.688, 8, 42.46, 12.89, 0.312, 2, 7, 31.07, -0.35, 0.688, 8, 23.5, 11.66, 0.312, 2, 7, 16.34, -1.29, 0.704, 8, 8.77, 10.71, 0.296, 2, 7, 2.16, -5.47, 0.712, 8, -5.41, 6.54, 0.288, 2, 7, -9.5, -12.48, 0.832, 8, -17.06, -0.47, 0.168, 2, 7, 51.9, 24.46, 0.752, 8, 44.33, 36.47, 0.248, 2, 7, 30.39, 21.61, 0.824, 8, 22.82, 33.61, 0.176, 2, 7, 14.29, 17.74, 0.824, 8, 6.73, 29.75, 0.176, 2, 7, -0.05, 12.5, 0.824, 8, -7.61, 24.5, 0.176, 2, 7, 71.7, -29.18, 0.72, 8, 64.14, -17.17, 0.28, 2, 7, 54.51, -32.6, 0.72, 8, 46.95, -20.6, 0.28, 2, 7, 35.7, -33.1, 0.744, 8, 28.13, -21.09, 0.256, 2, 7, 22.23, -32.25, 0.792, 8, 14.67, -20.24, 0.208, 2, 7, 12.05, -34.51, 0.872, 8, 4.48, -22.5, 0.128, 1, 7, 5.83, -37.78, 1, 2, 7, 5.84, 26.3, 0.952, 8, -1.73, 38.31, 0.048, 2, 7, 15.25, 30.49, 0.952, 8, 7.69, 42.5, 0.048, 2, 7, 65.48, 35.69, 0.784, 8, 57.92, 47.69, 0.216, 2, 7, 76.68, 43.55, 0.832, 8, 69.11, 55.56, 0.168, 4, 7, 86.73, 50.19, 0.56917, 26, 0.68, -1.47, 0.3586, 28, 7.81, 83.11, 0.00023, 8, 79.16, 62.2, 0.072, 4, 7, 96.03, 58.58, 0, 26, 13.09, -3.1, 0.95939, 28, 18.37, 89.85, 0.00061, 8, 88.47, 70.59, 0.04, 2, 7, 69.65, 7.7, 0.688, 8, 62.08, 19.71, 0.312, 2, 7, 32.02, 37.8, 0.952, 8, 24.45, 49.8, 0.048, 2, 7, 84.92, -27.04, 0.872, 8, 77.35, -15.04, 0.128, 4, 7, 94.98, -29.37, 0.79417, 26, -54.49, -59.4, 0.00054, 29, -17.34, 11.37, 0.00015, 28, 2.87, 3.27, 0.20514, 4, 7, 105.28, -29.62, 0.09283, 26, -48, -67.4, 0.00151, 29, -8.9, 5.46, 0.1515, 28, 12.99, 1.33, 0.75416, 4, 7, 115.02, -27.72, 4e-05, 26, -40.23, -73.57, 0.00015, 29, 0.26, 1.64, 0.22724, 28, 22.91, 1.6, 0.77257, 2, 7, 33.18, -19.86, 0.72, 8, 25.62, -7.85, 0.28], "hull": 63, "edges": [0, 124, 0, 2, 2, 4, 8, 10, 24, 26, 38, 40, 54, 56, 60, 62, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 100, 102, 106, 108, 108, 110, 110, 112, 120, 122, 122, 124, 118, 120, 116, 118, 112, 114, 114, 116, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60, 20, 22, 22, 24, 22, 126, 126, 128, 130, 132, 132, 134, 76, 78, 78, 80, 134, 78, 136, 138, 138, 140, 140, 142, 142, 144, 80, 82, 82, 84, 136, 146, 146, 148, 148, 150, 150, 152, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 84, 166, 166, 168, 96, 98, 98, 100, 146, 170, 170, 172, 172, 174, 174, 176, 102, 104, 104, 106, 34, 36, 36, 38, 136, 178, 100, 180, 62, 64, 64, 66, 154, 182, 182, 184, 184, 186, 186, 188, 128, 190, 190, 130], "width": 105, "height": 126}}, "a47": {"pifu/lzz_a4": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 7, 2, 3, 7, 2, 7, 8, 1, 2, 8, 1, 8, 9, 0, 1, 9], "vertices": [3, 7, 4.87, -95.25, 0.49144, 8, -2.7, -83.24, 0.04456, 9, -42.86, -81.79, 0.464, 3, 7, 0.08, -53.27, 0.66823, 8, -7.48, -41.27, 0.13444, 9, -47.64, -39.81, 0.19733, 3, 7, -4.7, -11.29, 0.71083, 8, -12.26, 0.71, 0.23851, 9, -52.42, 2.17, 0.05067, 2, 7, -9.48, 30.69, 0.77333, 8, -17.04, 42.69, 0.22667, 2, 7, -14.26, 72.66, 0.79733, 8, -21.82, 84.67, 0.20267, 2, 7, 72.18, 82.51, 0.808, 8, 64.62, 94.52, 0.192, 2, 7, 76.96, 40.53, 0.784, 8, 69.4, 52.54, 0.216, 3, 7, 81.74, -1.45, 0.72149, 8, 74.18, 10.56, 0.22784, 9, 34.02, 12.01, 0.05067, 3, 7, 86.53, -43.43, 0.66823, 8, 78.96, -31.42, 0.13444, 9, 38.8, -29.97, 0.19733, 3, 7, 91.31, -85.4, 0.49144, 8, 83.74, -73.4, 0.04456, 9, 43.58, -71.94, 0.464], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 135, "height": 70}}, "a48": {"pifu/lzz_a6": {"type": "mesh", "uvs": [0.44243, 0.00131, 0.49907, 0.00755, 0.57563, 0.05488, 0.6396, 0.12434, 0.6263, 0.1619, 0.64293, 0.19845, 0.72425, 0.22697, 0.80734, 0.25552, 0.88532, 0.3016, 0.92903, 0.40407, 0.92417, 0.48042, 0.84511, 0.55744, 0.8029, 0.62203, 0.86799, 0.63461, 0.95728, 0.72829, 0.95774, 0.78296, 0.93766, 0.81578, 0.85243, 0.84951, 0.90851, 0.89216, 0.98459, 0.95001, 0.9891, 0.98213, 0.90258, 0.99737, 0.84699, 0.99649, 0.77102, 0.96105, 0.72146, 0.92488, 0.63497, 0.92378, 0.54608, 0.86852, 0.49081, 0.91873, 0.42499, 0.96432, 0.33609, 1, 0.22851, 1, 0.09504, 0.95619, 0.08538, 0.92727, 0.14588, 0.88705, 0.21037, 0.83935, 0.13729, 0.79357, 0.13299, 0.72205, 0.18586, 0.61985, 0.06196, 0.49654, 0, 0.34694, 0.0075, 0.2742, 0.04555, 0.21744, 0.14862, 0.19023, 0.25716, 0.17444, 0.32136, 0.14697, 0.38784, 0.11391, 0.39844, 0.07331, 0.40383, 0.02677, 0.53448, 0.12778, 0.58408, 0.24236, 0.639, 0.36708, 0.66026, 0.49036, 0.67115, 0.6035, 0.66229, 0.72967, 0.66229, 0.82539, 0.16913, 0.32079, 0.26284, 0.46807, 0.32773, 0.60322, 0.34931, 0.71695, 0.35793, 0.82943, 0.28888, 0.9293, 0.79494, 0.39133, 0.77587, 0.50659, 0.7533, 0.61104, 0.74403, 0.70585, 0.36226, 0.28236, 0.44729, 0.41434, 0.49512, 0.55212, 0.51992, 0.65799, 0.53586, 0.76966, 0.86217, 0.75853], "triangles": [48, 1, 2, 48, 2, 3, 0, 1, 48, 47, 0, 48, 4, 48, 3, 46, 47, 48, 45, 46, 48, 49, 48, 4, 70, 13, 14, 70, 64, 13, 70, 14, 15, 58, 35, 36, 16, 70, 15, 54, 53, 64, 69, 53, 54, 59, 58, 69, 34, 58, 59, 34, 35, 58, 70, 54, 64, 17, 70, 16, 17, 54, 70, 26, 69, 54, 59, 69, 26, 27, 59, 26, 25, 26, 54, 24, 54, 17, 25, 54, 24, 60, 34, 59, 28, 60, 59, 33, 34, 60, 31, 32, 33, 23, 24, 17, 23, 17, 18, 27, 28, 59, 22, 23, 18, 21, 22, 18, 19, 21, 18, 20, 21, 19, 30, 33, 60, 31, 33, 30, 29, 60, 28, 30, 60, 29, 36, 37, 58, 64, 63, 12, 13, 64, 12, 48, 65, 45, 49, 4, 5, 65, 44, 45, 43, 44, 65, 55, 43, 65, 6, 50, 49, 6, 49, 5, 7, 50, 6, 61, 7, 8, 61, 50, 7, 61, 8, 9, 65, 48, 49, 66, 49, 50, 66, 65, 49, 56, 55, 65, 56, 65, 66, 10, 61, 9, 51, 50, 61, 66, 50, 51, 38, 55, 56, 62, 51, 61, 62, 61, 10, 67, 66, 51, 56, 66, 67, 11, 62, 10, 57, 56, 67, 52, 51, 62, 63, 52, 62, 67, 51, 52, 11, 63, 62, 37, 38, 56, 37, 56, 57, 12, 63, 11, 68, 67, 52, 57, 67, 68, 64, 52, 63, 58, 57, 68, 53, 68, 52, 58, 37, 57, 53, 52, 64, 69, 68, 53, 58, 68, 69, 41, 42, 55, 40, 41, 55, 39, 40, 55, 55, 42, 43, 38, 39, 55], "vertices": [3, 5, 114.88, 9.01, 0.1143, 6, 39.54, 13.46, 0.7857, 11, 74.71, 19.82, 0.1, 3, 5, 113.23, 1.18, 0.0258, 6, 38.71, 5.5, 0.8742, 11, 73.07, 11.99, 0.1, 3, 5, 104.38, -8.92, 0.0544, 6, 30.95, -5.46, 0.8456, 11, 64.21, 1.9, 0.1, 3, 5, 91.88, -16.98, 0.2088, 6, 19.35, -14.77, 0.6912, 11, 51.71, -6.17, 0.1, 3, 5, 85.61, -14.65, 0.4632, 6, 12.87, -13.11, 0.4368, 11, 45.44, -3.84, 0.1, 3, 5, 79.2, -16.51, 0.7088, 6, 6.69, -15.62, 0.1912, 11, 39.04, -5.7, 0.1, 3, 5, 73.5, -27.51, 0.8544, 6, 2.16, -27.15, 0.0456, 11, 33.34, -16.7, 0.1, 2, 5, 67.78, -38.75, 0.9, 11, 27.62, -27.94, 0.1, 2, 5, 59.13, -49.07, 0.9, 11, 18.96, -38.25, 0.1, 2, 5, 41.2, -53.89, 0.9, 11, 1.04, -43.07, 0.1, 2, 5, 28.23, -52.25, 0.9, 11, -11.94, -41.44, 0.1, 2, 5, 15.91, -40.25, 0.9, 11, -24.26, -29.43, 0.1, 3, 5, 5.32, -33.54, 0.8, 4, 36.65, -33.46, 0.1, 11, -34.84, -22.73, 0.1, 3, 5, 2.51, -42.47, 0.6, 4, 33.97, -42.43, 0.3, 11, -37.65, -31.66, 0.1, 3, 5, -14.38, -53.77, 0.3, 4, 17.26, -53.99, 0.6, 11, -54.55, -42.96, 0.1, 3, 5, -23.71, -53.15, 0.075, 4, 7.92, -53.51, 0.825, 11, -63.87, -42.34, 0.1, 2, 4, 2.48, -50.38, 0.9, 11, -69.27, -39.12, 0.1, 2, 4, -2.59, -38.14, 0.9, 11, -74.14, -26.8, 0.1, 2, 4, -10.32, -45.56, 0.9, 11, -81.99, -34.09, 0.1, 2, 4, -20.81, -55.62, 0.9, 11, -92.64, -43.99, 0.1, 2, 4, -26.33, -55.93, 0.9, 11, -98.16, -44.22, 0.1, 2, 4, -28.24, -43.69, 0.9, 11, -99.87, -31.95, 0.1, 1, 4, -27.64, -35.93, 1, 2, 4, -20.97, -25.66, 0.9, 11, -92.33, -14.03, 0.1, 2, 4, -14.4, -19.09, 0.9, 11, -85.65, -7.57, 0.1, 2, 4, -13.51, -7.01, 0.9, 11, -84.58, 4.49, 0.1, 2, 4, -3.36, 4.87, 0.9, 11, -74.24, 16.21, 0.1, 2, 4, -11.49, 13.09, 0.9, 11, -82.24, 24.56, 0.1, 2, 4, -18.74, 22.73, 0.9, 11, -89.34, 34.32, 0.1, 2, 4, -24.12, 35.51, 0.9, 11, -94.51, 47.18, 0.1, 2, 4, -23.25, 50.55, 0.9, 11, -93.41, 62.2, 0.1, 2, 4, -14.69, 68.77, 0.9, 11, -84.57, 80.29, 0.1, 2, 4, -9.68, 69.84, 0.9, 11, -79.54, 81.27, 0.1, 2, 4, -3.3, 60.98, 0.9, 11, -73.3, 72.32, 0.1, 2, 4, 4.32, 51.5, 0.9, 11, -65.83, 62.72, 0.1, 3, 5, -17.11, 61.54, 0.1, 4, 12.73, 61.26, 0.8, 11, -57.27, 72.35, 0.1, 3, 5, -4.86, 61.24, 0.3, 4, 24.97, 61.16, 0.6, 11, -45.03, 72.06, 0.1, 3, 5, 12.02, 52.58, 0.6, 4, 41.99, 52.76, 0.3, 11, -28.14, 63.39, 0.1, 3, 5, 34.32, 68.34, 0.8, 4, 64.04, 68.87, 0.1, 11, -5.84, 79.15, 0.1, 2, 5, 60.47, 75.11, 0.9, 11, 20.31, 85.93, 0.1, 2, 5, 72.8, 73.16, 0.9, 11, 32.63, 83.97, 0.1, 2, 5, 82.09, 67.13, 0.9, 11, 41.92, 77.94, 0.1, 2, 5, 85.67, 52.4, 0.9, 11, 45.51, 63.21, 0.1, 3, 5, 87.25, 37.05, 0.8658, 6, 9.16, 38.48, 0.0342, 11, 47.09, 47.86, 0.1, 3, 5, 91.28, 27.74, 0.7088, 6, 14.13, 29.64, 0.1912, 11, 51.11, 38.55, 0.1, 3, 5, 96.24, 18.04, 0.4632, 6, 20.06, 20.51, 0.4368, 11, 56.07, 28.86, 0.1, 3, 5, 103.05, 16.05, 0.2346, 6, 27.05, 19.24, 0.6654, 11, 62.89, 26.87, 0.1, 3, 5, 110.93, 14.72, 0.1551, 6, 35.02, 18.72, 0.7449, 11, 70.77, 25.53, 0.1, 3, 5, 92.37, -2.26, 0.29912, 6, 18.31, -0.08, 0.62887, 10, 82.67, 8.95, 0.072, 3, 5, 72.32, -7.75, 0.49122, 6, -1.06, -7.62, 0.20478, 10, 62.62, 3.46, 0.304, 3, 5, 50.48, -13.85, 0.59335, 6, -22.14, -15.95, 0.04665, 10, 40.79, -2.64, 0.36, 3, 5, 29.24, -15.28, 0.56889, 4, 60.27, -14.82, 0.07111, 10, 19.54, -4.07, 0.36, 3, 5, 9.84, -15.38, 0.464, 4, 40.87, -15.23, 0.232, 10, 0.14, -4.17, 0.304, 3, 5, -11.59, -12.56, 0.232, 4, 19.4, -12.74, 0.464, 10, -21.29, -1.35, 0.304, 3, 5, -27.92, -11.37, 0.12667, 4, 3.06, -11.8, 0.63333, 10, -37.61, -0.15, 0.24, 2, 5, 63.2, 51.17, 0.808, 10, 53.5, 62.38, 0.192, 3, 5, 37.12, 39.93, 0.71822, 4, 67.28, 40.51, 0.08978, 10, 27.42, 51.14, 0.192, 3, 5, 13.4, 32.57, 0.53867, 4, 43.69, 32.77, 0.26933, 10, 3.71, 43.78, 0.192, 3, 5, -6.21, 30.98, 0.26933, 4, 24.1, 30.87, 0.53867, 10, -15.91, 42.19, 0.192, 3, 5, -25.48, 31.18, 0.08978, 4, 4.83, 30.78, 0.71822, 10, -35.18, 42.39, 0.192, 1, 4, -11.67, 41.41, 1, 2, 5, 44.75, -35.32, 0.72, 10, 35.05, -24.11, 0.28, 3, 5, 25.29, -31.22, 0.61156, 4, 56.57, -30.82, 0.07644, 10, 15.59, -20.01, 0.312, 3, 5, 7.71, -26.76, 0.52, 4, 38.92, -26.63, 0.2, 10, -1.99, -15.55, 0.28, 3, 5, -8.37, -24.27, 0.42933, 4, 22.81, -24.4, 0.30667, 10, -18.07, -13.06, 0.264, 2, 5, 67.77, 23.73, 0.696, 10, 58.07, 34.94, 0.304, 2, 5, 44.39, 13.51, 0.696, 10, 34.69, 24.72, 0.304, 3, 5, 20.4, 8.55, 0.61867, 4, 51.06, 8.87, 0.07733, 10, 10.7, 19.76, 0.304, 3, 5, 2.09, 6.42, 0.50267, 4, 32.79, 6.45, 0.19333, 10, -7.61, 17.63, 0.304, 3, 5, -17.11, 5.59, 0.406, 4, 13.6, 5.32, 0.29, 10, -26.81, 16.8, 0.304, 1, 4, 12.86, -40.4, 1], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 56, 58, 58, 60, 60, 62, 62, 64, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 90, 92, 92, 94, 82, 84, 84, 86, 10, 12, 12, 14, 0, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 34, 36, 36, 38, 44, 46, 46, 48, 52, 54, 54, 56, 82, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 64, 66, 66, 68, 18, 122, 122, 124, 124, 126, 126, 128, 86, 130, 130, 132, 132, 134, 134, 136, 136, 138, 30, 140, 86, 88, 88, 90, 6, 8, 8, 10, 20, 22, 22, 24, 94, 96], "width": 112, "height": 137}}, "a49": {"pifu/lzz_a7": {"type": "mesh", "uvs": [0.72326, 0.08009, 0.78147, 0.1941, 0.79567, 0.29659, 0.80775, 0.38376, 0.83949, 0.47974, 0.87282, 0.57817, 0.88324, 0.67163, 0.90206, 0.756, 0.92776, 0.82843, 0.95458, 0.88919, 0.9854, 0.93279, 0.99208, 0.95855, 0.78088, 0.99474, 0.55918, 0.99475, 0.32355, 0.93925, 0.10511, 0.95717, 0.16096, 0.89371, 0.20858, 0.83138, 0.25301, 0.7644, 0.28931, 0.68627, 0.30992, 0.60563, 0.31797, 0.50948, 0.31585, 0.42311, 0.28974, 0.3307, 0.2402, 0.23058, 0.15002, 0.11821, 0.00032, 0.04363, 0.0517, 0.03307, 0.6402, 0.0014, 0.57576, 0.90876, 0.58344, 0.82267], "triangles": [30, 19, 6, 13, 14, 29, 30, 29, 14, 13, 29, 12, 12, 29, 11, 11, 29, 10, 9, 29, 8, 29, 30, 8, 29, 9, 10, 30, 7, 8, 30, 6, 7, 6, 20, 5, 5, 21, 4, 4, 22, 3, 23, 2, 3, 17, 18, 30, 15, 16, 14, 14, 16, 17, 30, 14, 17, 18, 19, 30, 6, 19, 20, 5, 20, 21, 4, 21, 22, 22, 23, 3, 2, 23, 1, 24, 0, 1, 0, 25, 28, 0, 24, 25, 26, 27, 25, 25, 27, 28, 23, 24, 1], "vertices": [2, 4, 13.29, -26.96, 0.85185, 53, -32.25, 2.25, 0.14815, 2, 4, -8.42, -31.13, 0.62963, 53, -10.36, 5.35, 0.37037, 3, 4, -27.73, -31.34, 0.37037, 53, 8.94, 4.61, 0.61585, 54, -54.5, 6.69, 0.01378, 3, 4, -44.16, -31.51, 0.14815, 53, 25.35, 3.98, 0.77347, 54, -38.12, 5.48, 0.07838, 3, 4, -62.34, -33.42, 0.03704, 53, 43.61, 5, 0.73213, 54, -19.84, 5.85, 0.23084, 3, 53, 62.34, 6.11, 0.53316, 54, -1.08, 6.3, 0.43456, 55, -44.62, 11.68, 0.03228, 3, 53, 79.91, 5.2, 0.28768, 54, 16.45, 4.78, 0.57845, 55, -27.39, 8.11, 0.13387, 3, 53, 95.87, 5.25, 0.1068, 54, 32.4, 4.27, 0.55138, 55, -11.61, 5.72, 0.34182, 3, 53, 109.66, 6.17, 0.02326, 54, 46.22, 4.71, 0.38043, 55, 2.16, 4.54, 0.59632, 4, 51, 43.2, 64.63, 0.0018, 52, -8.96, 64.12, 0.01281, 54, 57.88, 5.56, 0.17689, 55, 13.84, 4.02, 0.8085, 4, 51, 50.67, 69.05, 0.01028, 52, -2.49, 69.92, 0.05449, 54, 66.4, 7.24, 0.05459, 55, 22.5, 4.68, 0.88063, 4, 51, 55.3, 70.61, 0.02831, 52, 1.74, 72.36, 0.16221, 54, 71.28, 7.17, 0.01316, 55, 27.34, 4.04, 0.79632, 4, 51, 65.82, 52.69, 0.02143, 52, 15.57, 56.84, 0.24689, 54, 75.23, -13.23, 0.00656, 55, 28.87, -16.69, 0.72513, 4, 51, 69.87, 32.47, 0.029, 52, 23.51, 37.81, 0.39221, 54, 72.32, -33.65, 0.00959, 55, 23.58, -36.62, 0.5692, 4, 51, 63.94, 8.93, 0.01176, 52, 22.3, 13.57, 0.65885, 54, 58.89, -53.86, 0.00328, 55, 7.87, -55.12, 0.32611, 4, 51, 71.22, -10.33, 0.0399, 52, 33.23, -3.88, 0.82118, 54, 59.36, -74.45, 0.00019, 55, 5.91, -75.62, 0.13873, 3, 51, 58.51, -7.57, 0.1483, 52, 20.22, -3.68, 0.81067, 55, -4.28, -67.54, 0.04103, 4, 50, 110.23, -16.17, 0.00947, 51, 46.15, -5.53, 0.35936, 52, 7.7, -4.1, 0.62523, 55, -14.47, -60.25, 0.00594, 4, 50, 97.81, -11.55, 0.06543, 51, 32.99, -3.95, 0.56184, 52, -5.52, -5.13, 0.37272, 55, -25.58, -53.02, 0, 3, 50, 83.26, -7.61, 0.20494, 51, 17.92, -3.52, 0.6444, 52, -20.38, -7.66, 0.15066, 3, 50, 68.19, -5.11, 0.43663, 51, 2.68, -4.61, 0.52496, 52, -35.11, -11.72, 0.03841, 4, 4, -65.13, 15.32, 0.03704, 50, 50.15, -3.66, 0.64939, 51, -15.19, -7.42, 0.31304, 52, -52.08, -17.98, 0.00054, 3, 4, -48.91, 14.58, 0.14815, 50, 33.92, -3.23, 0.7321, 51, -31.07, -10.8, 0.11975, 3, 4, -31.42, 16.01, 0.37037, 50, 16.47, -4.99, 0.60206, 51, -47.63, -16.59, 0.02757, 2, 4, -12.37, 19.52, 0.62963, 50, -2.52, -8.86, 0.37037, 2, 4, 9.21, 26.68, 0.85185, 50, -23.95, -16.43, 0.14815, 2, 4, 24.01, 39.77, 0.96296, 50, -38.5, -29.8, 0.03704, 1, 4, 25.71, 34.88, 1, 2, 4, 28.5, -20.1, 0.96296, 53, -47.78, -3.85, 0.03704, 4, 51, 53.71, 30.81, 0.06135, 52, 7.99, 33.02, 0.25122, 54, 56.53, -29.83, 0.02474, 55, 8.35, -30.98, 0.66269, 4, 51, 37.7, 28.34, 0.09668, 52, -7.22, 27.45, 0.24784, 54, 40.61, -26.84, 0.04022, 55, -7.1, -26.13, 0.61525], "hull": 29, "edges": [0, 56, 0, 2, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 48, 50, 50, 52, 52, 54, 54, 56, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 10, 12, 12, 14, 6, 8, 8, 10, 44, 46, 46, 48, 2, 4, 4, 6, 14, 16, 16, 18, 26, 58, 58, 60, 58, 22], "width": 74, "height": 150}}, "a28": {"pifu/lzz_a10": {"type": "mesh", "uvs": [0.27379, 0.00034, 0.51744, 0.05524, 0.72661, 0.08504, 0.72718, 0.13265, 0.67223, 0.1927, 0.6288, 0.27087, 0.61088, 0.32508, 0.61614, 0.35763, 0.62422, 0.39319, 0.6399, 0.41691, 0.69055, 0.35904, 0.8409, 0.36163, 0.96003, 0.44075, 0.98422, 0.46783, 0.98423, 0.57843, 0.88644, 0.63511, 0.87644, 0.55302, 0.67758, 0.56557, 0.6219, 0.67498, 0.506, 0.73729, 0.44419, 0.8386, 0.2371, 1, 0.15972, 0.96579, 0.15135, 0.84502, 0.07689, 0.89453, 0.00484, 0.77868, 0.0121, 0.69485, 0.06937, 0.60641, 0.28096, 0.55797, 0.3491, 0.50867, 0.35827, 0.42623, 0.36238, 0.37956, 0.35354, 0.33862, 0.2456, 0.11434, 0.24157, 0.02105, 0.48622, 0.15457, 0.49258, 0.38092], "triangles": [9, 17, 30, 30, 17, 29, 30, 31, 36, 29, 17, 18, 19, 29, 18, 19, 28, 29, 19, 23, 28, 23, 27, 28, 20, 23, 19, 26, 27, 23, 25, 26, 23, 24, 25, 23, 21, 22, 23, 20, 21, 23, 33, 34, 0, 33, 0, 1, 35, 33, 1, 3, 35, 1, 3, 1, 2, 4, 35, 3, 5, 35, 4, 5, 32, 35, 32, 33, 35, 6, 32, 5, 36, 31, 32, 6, 36, 32, 7, 36, 6, 36, 7, 8, 16, 11, 12, 16, 12, 13, 9, 16, 17, 11, 9, 10, 16, 9, 11, 16, 13, 14, 15, 16, 14, 9, 36, 8, 9, 30, 36], "vertices": [1, 17, -1.83, -17.74, 1, 1, 17, 1.79, -2.2, 1, 1, 17, 3.27, 10.99, 1, 1, 17, 7.72, 11.47, 1, 1, 17, 13.68, 8.65, 1, 1, 17, 21.26, 6.71, 1, 1, 17, 26.44, 6.12, 1, 1, 17, 29.45, 6.75, 1, 2, 17, 32.73, 7.59, 0.96017, 18, -1.68, 7.84, 0.03983, 2, 17, 34.85, 8.78, 0.8169, 18, 0.52, 8.88, 0.1831, 2, 17, 29.12, 11.36, 0.87605, 18, -5.01, 11.86, 0.12395, 2, 17, 28.42, 20.65, 0.89265, 18, -5.05, 21.18, 0.10735, 2, 17, 35.08, 28.75, 0.88032, 18, 2.16, 28.79, 0.11968, 2, 17, 37.46, 30.5, 0.87837, 18, 4.66, 30.37, 0.12163, 2, 17, 47.81, 31.55, 0.87668, 18, 15.05, 30.68, 0.12332, 2, 17, 53.72, 26.05, 0.88079, 18, 20.56, 24.78, 0.11921, 2, 17, 46.1, 24.66, 0.83267, 18, 12.87, 23.93, 0.16733, 2, 17, 48.52, 12.51, 0.28993, 18, 14.42, 11.64, 0.71007, 2, 17, 59.1, 10.11, 0.00652, 18, 24.8, 8.5, 0.99348, 1, 18, 30.87, 1.5, 1, 1, 18, 40.51, -2.05, 1, 1, 18, 56.06, -14.42, 1, 1, 18, 52.99, -19.31, 1, 1, 18, 41.66, -20.17, 1, 1, 18, 46.45, -24.65, 1, 1, 18, 35.7, -29.44, 1, 1, 18, 27.81, -29.23, 1, 1, 18, 19.4, -25.93, 1, 1, 18, 14.45, -12.96, 1, 1, 18, 9.69, -8.88, 1, 2, 17, 37.48, -8.51, 0.01706, 18, 1.93, -8.54, 0.98294, 2, 17, 33.09, -8.69, 0.27828, 18, -2.47, -8.42, 0.72172, 2, 17, 29.31, -9.63, 0.64483, 18, -6.3, -9.09, 0.35517, 1, 17, 9.01, -18.41, 1, 1, 17, 0.31, -19.54, 1, 1, 17, 11.27, -3.18, 1, 2, 17, 32.4, -0.65, 0.92954, 18, -2.58, -0.35, 0.07046], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 64, 66, 66, 68, 56, 58, 58, 60, 60, 62, 62, 64, 20, 22, 8, 10, 2, 70, 18, 20, 14, 72, 10, 12, 12, 14, 14, 16, 16, 18], "width": 50, "height": 75}}, "a29": {"pifu/lzz_a11": {"type": "mesh", "uvs": [0.92821, 0.06171, 0.98881, 0.15348, 0.98883, 0.42789, 0.87468, 0.83531, 0.81338, 0.97172, 0.69034, 0.9984, 0.53451, 0.99194, 0.331, 0.9467, 0.18446, 0.86791, 0.0651, 0.68604, 1e-05, 0.49915, 0.0469, 0.42464, 0.27293, 0.24, 0.4994, 0.09272, 0.70408, 0.00925, 0.81479, 0.00833, 0.36952, 0.72119, 0.74168, 0.25128], "triangles": [17, 1, 2, 17, 0, 1, 17, 15, 0, 17, 14, 15, 4, 5, 16, 16, 5, 6, 3, 4, 16, 16, 6, 7, 7, 8, 16, 8, 9, 16, 3, 16, 2, 16, 11, 12, 16, 17, 2, 17, 12, 13, 17, 16, 12, 9, 10, 16, 11, 16, 10, 13, 14, 17], "vertices": [1, 4, 9.17, -1.1, 1, 2, 16, -26.69, 27.41, 0.00659, 4, -1.04, -5.85, 0.99341, 2, 16, 2.31, 33.53, 0.68617, 4, -30.62, -4.14, 0.31383, 3, 16, 47.44, 32.79, 0.99294, 4, -73.97, 8.42, 0.0014, 17, -12.95, 34.4, 0.00567, 2, 16, 62.97, 30.55, 0.9421, 17, 2.25, 30.51, 0.0579, 2, 16, 68.02, 20.55, 0.84231, 17, 6.21, 20.03, 0.15769, 2, 16, 70.17, 6.99, 0.3147, 17, 6.89, 6.32, 0.6853, 2, 16, 69.09, -11.55, 0.5188, 17, 3.83, -11.99, 0.4812, 2, 16, 63.42, -25.92, 0.8644, 17, -3.34, -25.68, 0.1356, 2, 16, 46.37, -40.25, 0.98727, 17, -21.82, -38.1, 0.01273, 2, 16, 27.81, -50.02, 0.99999, 17, -41.33, -45.83, 1e-05, 1, 16, 19.08, -47.65, 1, 2, 16, -4.54, -32.3, 0.9898, 4, -6.74, 57.58, 0.0102, 2, 16, -24.22, -16.09, 0.70877, 4, 8, 36.77, 0.29123, 2, 16, -36.75, -0.32, 0.24372, 4, 15.96, 18.27, 0.75628, 2, 16, -38.86, 9.19, 0.09246, 4, 15.5, 8.54, 0.90754, 2, 16, 44.56, -13.26, 0.99233, 17, -20.74, -11.07, 0.00767, 2, 16, -11.86, 8.31, 0.58298, 4, -10.33, 16.47, 0.41702], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 16, 32, 30, 34, 32, 8, 18, 20, 32, 20], "width": 70, "height": 86}}, "a27": {"pifu/lzz_a1": {"type": "mesh", "uvs": [0.21737, 0.00967, 0.49886, 0.05955, 0.62025, 0.12959, 0.74082, 0.27153, 0.91139, 0.59667, 0.9009, 0.68082, 0.97721, 0.70329, 0.98479, 0.78513, 0.82715, 0.87308, 0.70313, 0.99882, 0.67286, 1, 0.58768, 0.9497, 0.57397, 0.91478, 0.31017, 0.78014, 0.28533, 0.69489, 0.1587, 0.58404, 0.09491, 0.47198, 0.06351, 0.36214, 0.01561, 0.29192, 0.01536, 0.18062, 0.10375, 0.02645, 0.72591, 0.76866, 0.23589, 0.18369, 0.47093, 0.47286], "triangles": [2, 22, 1, 20, 0, 22, 22, 0, 1, 10, 11, 9, 9, 11, 12, 12, 21, 8, 12, 13, 21, 8, 21, 5, 13, 14, 21, 14, 23, 21, 5, 21, 4, 14, 15, 23, 4, 21, 23, 23, 3, 4, 15, 16, 23, 23, 17, 22, 23, 2, 3, 2, 23, 22, 23, 16, 17, 17, 18, 22, 18, 19, 22, 19, 20, 22, 8, 9, 12, 7, 5, 6, 7, 8, 5], "vertices": [2, 23, -15.59, -4.81, 0.25516, 22, 61.04, -16.31, 0.74484, 1, 22, 51.11, -1.19, 1, 2, 23, 3.9, 13.6, 0.38473, 22, 49.31, 7.79, 0.61527, 2, 23, 16.49, 15.54, 0.88251, 22, 51.22, 20.38, 0.11749, 2, 24, -9.47, 17.24, 0.09594, 23, 42.21, 14.1, 0.90406, 2, 24, -5.51, 12.61, 0.31407, 23, 47.26, 10.69, 0.68593, 2, 24, -1.04, 15, 0.58688, 23, 50.93, 14.18, 0.41312, 2, 24, 3.58, 11.3, 0.70388, 23, 56.37, 11.85, 0.29612, 1, 24, 1.38, -0.27, 1, 2, 24, 2.61, -12.16, 0.40867, 23, 61.68, -11.03, 0.59133, 2, 24, 1.36, -13.61, 0.39604, 23, 60.86, -12.75, 0.60396, 2, 24, -4.95, -15.03, 0.26862, 23, 55.15, -15.81, 0.73138, 2, 24, -7.38, -13.93, 0.19609, 23, 52.53, -15.39, 0.80391, 2, 24, -25.83, -19.38, 0.00361, 23, 36.19, -25.56, 0.99639, 2, 24, -31.37, -16.31, 0.00027, 23, 30.03, -24.08, 0.99973, 2, 23, 19.25, -27.4, 0.99868, 22, 93.02, 10.17, 0.00132, 2, 23, 10.24, -27.18, 0.97114, 22, 90.12, 1.63, 0.02886, 2, 23, 2.32, -25.24, 0.874, 22, 85.89, -5.34, 0.126, 2, 23, -3.56, -25.54, 0.77143, 22, 84.42, -11.04, 0.22857, 2, 23, -10.65, -21.81, 0.62446, 22, 78.74, -16.69, 0.37554, 2, 23, -17.86, -11.7, 0.36704, 22, 66.94, -20.55, 0.63296, 1, 23, 47.7, -2.02, 1, 2, 23, -3.96, -9.63, 0.598, 22, 69.12, -6.67, 0.402, 1, 23, 21.36, -6.27, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 10, 12, 6, 8, 8, 10, 10, 42, 40, 44, 44, 46], "width": 50, "height": 58}}, "a50": {"pifu/lzz_a8": {"type": "mesh", "uvs": [0, 0.02338, 0.07225, 0.00306, 0.11705, 0.01092, 0.24312, 0.06428, 0.41509, 0.16075, 0.58968, 0.30453, 0.69518, 0.42395, 0.78597, 0.5308, 0.85242, 0.61877, 0.9068, 0.69978, 0.95549, 0.78047, 0.98212, 0.84477, 1, 0.89753, 0.88173, 0.93273, 0.73697, 0.96342, 0.58303, 0.98598, 0.41873, 0.99879, 0.41318, 0.96533, 0.4056, 0.92368, 0.39309, 0.86038, 0.38491, 0.78144, 0.37077, 0.68764, 0.34059, 0.5796, 0.30547, 0.46265, 0.24443, 0.31232, 0.17122, 0.21583, 0.08219, 0.0985, 0, 0.02528], "triangles": [25, 26, 3, 27, 0, 1, 2, 27, 1, 26, 2, 3, 26, 27, 2, 20, 9, 10, 19, 20, 10, 19, 10, 11, 19, 14, 18, 11, 13, 19, 12, 13, 11, 13, 14, 19, 15, 17, 18, 14, 15, 18, 16, 17, 15, 7, 22, 23, 8, 21, 22, 8, 22, 7, 21, 8, 9, 20, 21, 9, 25, 3, 4, 24, 25, 4, 5, 24, 4, 23, 24, 5, 23, 5, 6, 7, 23, 6], "vertices": [1, 12, 1.65, 28.89, 1, 1, 12, -1.38, 33.75, 1, 2, 41, -26.21, -1.25, 0.11111, 12, -0.07, 36.67, 0.88889, 2, 41, -15.23, 2.98, 0.33333, 12, 8.48, 44.75, 0.66667, 2, 41, 3.14, 7.23, 0.66667, 12, 23.85, 55.66, 0.33333, 3, 41, 28.31, 8.66, 0.77875, 42, -30.41, 5.57, 0.11014, 12, 46.6, 66.54, 0.11111, 2, 41, 48.17, 7.45, 0.6686, 42, -10.54, 6.41, 0.3314, 3, 41, 65.83, 6.16, 0.33624, 42, 7.17, 6.93, 0.65243, 43, -31.11, 5.17, 0.01133, 3, 41, 80.15, 4.59, 0.11305, 42, 21.58, 6.84, 0.75318, 43, -16.72, 5.9, 0.13377, 3, 41, 93.16, 2.74, 0.00097, 42, 34.7, 6.33, 0.63171, 43, -3.59, 6.13, 0.36733, 2, 42, 47.67, 5.47, 0.31067, 43, 9.4, 6.02, 0.68933, 2, 42, 57.75, 4.03, 0.09978, 43, 19.55, 5.15, 0.90022, 2, 42, 65.94, 2.6, 4e-05, 43, 27.81, 4.19, 0.99996, 2, 42, 68.73, -6.53, 0.00068, 43, 31.12, -4.77, 0.99932, 2, 42, 70.32, -17.09, 0.0023, 43, 33.3, -15.23, 0.9977, 2, 42, 70.51, -27.84, 0.00581, 43, 34.1, -25.95, 0.99419, 2, 42, 69.05, -38.77, 0.01457, 43, 33.26, -36.94, 0.98543, 2, 42, 63.97, -37.5, 0.04283, 43, 28.12, -35.96, 0.95717, 2, 42, 57.64, -35.96, 0.12249, 43, 21.71, -34.78, 0.87751, 3, 41, 102.28, -38.42, 0.00176, 42, 47.99, -33.68, 0.28463, 43, 11.95, -33.05, 0.71361, 3, 41, 90.81, -33.91, 0.03406, 42, 36.12, -30.37, 0.48511, 43, -0.09, -30.42, 0.48083, 3, 41, 77.06, -28.82, 0.16571, 42, 21.92, -26.72, 0.58869, 43, -14.47, -27.58, 0.2456, 3, 41, 60.85, -23.8, 0.40494, 42, 5.28, -23.38, 0.50917, 43, -31.27, -25.19, 0.08589, 3, 41, 43.24, -18.5, 0.6942, 42, -12.78, -19.92, 0.29059, 43, -49.51, -22.77, 0.01521, 4, 41, 20.17, -12.66, 0.78478, 42, -36.32, -16.47, 0.1038, 43, -73.21, -20.66, 0.00032, 12, 47.17, 43.73, 0.11111, 3, 41, 4.45, -10.96, 0.65489, 42, -52.13, -16.39, 0.01177, 12, 31.98, 39.33, 0.33333, 2, 41, -14.66, -8.9, 0.33333, 12, 13.52, 33.98, 0.66667, 2, 41, -27.3, -9.22, 0.11111, 12, 1.95, 28.88, 0.88889], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 44, 46, 46, 48, 52, 54, 38, 40, 48, 50, 50, 52, 10, 12, 12, 14, 16, 18, 18, 20, 40, 42, 42, 44, 32, 34, 34, 36], "width": 53, "height": 125}}, "a51": {"pifu/lzz_a9": {"type": "mesh", "uvs": [0.05392, 0.65345, 0.151, 0.57226, 0.23476, 0.50878, 0.29376, 0.45269, 0.37752, 0.38478, 0.47269, 0.31097, 0.56977, 0.23274, 0.67066, 0.15155, 0.77535, 0.06297, 0.85149, 0.0054, 0.94286, 0, 1, 0, 1, 0.08217, 0.97522, 0.18107, 0.95047, 0.29031, 0.9105, 0.39069, 0.8591, 0.49697, 0.79058, 0.61507, 0.71444, 0.72135, 0.65162, 0.83207, 0.59071, 0.93835, 0.54122, 1, 0.40036, 0.97526, 0.2576, 0.90293, 0.1491, 0.81878, 0.09009, 0.73021, 0.88949, 0.08254, 0.82685, 0.15339, 0.72635, 0.23031, 0.63891, 0.32444, 0.5319, 0.44083, 0.39878, 0.53395, 0.20171, 0.65338, 0.82293, 0.2493, 0.77856, 0.36266, 0.68068, 0.475, 0.59846, 0.61973, 0.4823, 0.76851], "triangles": [23, 24, 37, 24, 32, 37, 24, 25, 32, 32, 31, 37, 25, 0, 32, 0, 1, 32, 32, 1, 2, 37, 31, 36, 32, 2, 31, 31, 30, 36, 2, 3, 31, 3, 4, 31, 36, 30, 35, 31, 4, 30, 30, 29, 35, 4, 5, 30, 30, 5, 29, 29, 28, 34, 5, 6, 29, 29, 6, 28, 33, 28, 27, 6, 7, 28, 28, 7, 27, 7, 8, 27, 21, 22, 20, 20, 22, 37, 22, 23, 37, 20, 37, 19, 19, 37, 18, 37, 36, 18, 18, 36, 17, 36, 35, 17, 17, 35, 16, 35, 34, 16, 16, 34, 15, 35, 29, 34, 15, 34, 14, 34, 33, 14, 34, 28, 33, 14, 33, 13, 33, 27, 13, 27, 26, 13, 13, 26, 12, 27, 8, 26, 8, 9, 26, 26, 10, 12, 26, 9, 10, 10, 11, 12], "vertices": [1, 46, 25.34, -6.97, 1, 2, 45, 41.04, -8.83, 0.1083, 46, 5.5, -8.75, 0.8917, 2, 45, 24.7, -9.24, 0.99757, 46, -10.83, -9.4, 0.00243, 2, 44, 63.26, -12.21, 0.02322, 45, 11.8, -11.03, 0.97678, 2, 44, 46.37, -11.42, 0.83369, 45, -5.08, -12.02, 0.16631, 1, 44, 27.64, -10.12, 1, 1, 44, 8.13, -9.14, 1, 2, 47, -12.27, -38.75, 0.13602, 44, -12.14, -8.11, 0.86398, 2, 47, -32.43, -30.94, 0.49323, 44, -33.75, -7.56, 0.50677, 2, 47, -45.94, -24.81, 0.60055, 44, -48.54, -6.34, 0.39945, 2, 47, -51.48, -13.28, 0.64577, 44, -57.64, 2.63, 0.35423, 2, 47, -54.38, -5.86, 0.65675, 44, -62.87, 8.65, 0.34325, 2, 47, -40.61, -0.48, 0.6798, 44, -51.72, 18.35, 0.3202, 2, 47, -22.77, 2.77, 0.79455, 44, -36.03, 27.43, 0.20545, 2, 47, -3.21, 6.7, 0.99678, 44, -18.94, 37.73, 0.00322, 1, 47, 15.65, 8.07, 1, 1, 47, 36.07, 8.34, 1, 2, 47, 59.34, 7.15, 0.93465, 48, -5.85, 6.83, 0.06535, 1, 48, 15.96, 5.13, 1, 2, 48, 37.73, 5.46, 0.98616, 49, -5.68, 5.36, 0.01384, 1, 49, 15.24, 5.95, 1, 2, 49, 28.23, 4.52, 0.99983, 46, 16.61, 84.87, 0.00017, 3, 49, 32.7, -15.13, 0.89902, 45, 64.92, 67.97, 0.00028, 46, 28.23, 68.4, 0.10071, 4, 48, 72.21, -39.22, 0.01669, 49, 29.57, -38.72, 0.56523, 45, 70.65, 44.87, 0.00567, 46, 34.3, 45.39, 0.41242, 4, 48, 64.75, -59.29, 0.01511, 49, 22.45, -58.91, 0.24945, 45, 71.43, 23.48, 0.00097, 46, 35.4, 24.01, 0.73448, 3, 48, 53.69, -73.41, 0.00283, 49, 11.64, -73.22, 0.04933, 46, 30.85, 6.66, 0.94784, 2, 47, -34.94, -14.82, 0.62294, 44, -41.55, 6.77, 0.37706, 2, 47, -19.88, -18.33, 0.5457, 44, -26.19, 8.54, 0.4543, 2, 47, -1.89, -26.36, 0.2339, 44, -6.55, 7.05, 0.7661, 2, 47, 18.32, -31.57, 0.14841, 44, 14.24, 8.97, 0.85159, 4, 47, 43.26, -37.87, 0.13796, 48, -19.32, -39.04, 0.01784, 44, 39.84, 11.45, 0.79855, 45, -13.99, 10.03, 0.04565, 6, 47, 65.62, -49.08, 0.02287, 48, 3.65, -48.95, 0.0611, 49, -38.82, -49.63, 0.00329, 44, 64.67, 8.44, 0.03455, 45, 11.02, 9.66, 0.87427, 46, -24.79, 9.29, 0.00392, 4, 48, 34.63, -65.01, 0.0135, 49, -7.56, -65.15, 0.02471, 45, 45.79, 6.68, 0.02819, 46, 10.02, 6.83, 0.9336, 2, 47, -3.61, -12.57, 0.69535, 44, -12.82, 19.46, 0.30465, 2, 47, 17.64, -10.92, 0.79234, 44, 6.63, 28.18, 0.20766, 4, 47, 41.44, -16.29, 0.70114, 48, -22.37, -17.61, 0.01046, 44, 30.85, 31.15, 0.26914, 45, -25.01, 28.67, 0.01927, 6, 47, 69.87, -17.51, 0.20655, 48, 6.08, -17.19, 0.56202, 49, -36.93, -17.83, 0.00299, 44, 58.02, 39.59, 0.06256, 45, 1.13, 39.93, 0.15744, 46, -35.14, 39.41, 0.00844, 5, 48, 37.17, -20.78, 0.47042, 49, -5.79, -20.89, 0.29425, 44, 88.85, 44.94, 2e-05, 45, 31.22, 48.51, 0.11046, 46, -5.18, 48.43, 0.12485], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 20, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 54, 66, 66, 68, 68, 70, 70, 72, 72, 74], "width": 114, "height": 147}}, "a30": {"pifu/lzz_a12": {"type": "mesh", "uvs": [0.60389, 0.02868, 0.85046, 0.00197, 0.87611, 0.01737, 0.86678, 0.10179, 0.7932, 0.18422, 0.73025, 0.29225, 0.69612, 0.33987, 0.69081, 0.36845, 0.69206, 0.39139, 0.69337, 0.42892, 0.68591, 0.5104, 0.74095, 0.57323, 0.83607, 0.6065, 0.94624, 0.6181, 0.98363, 0.70163, 0.98365, 0.78741, 0.91783, 0.8718, 0.72042, 0.99775, 0.53325, 0.83, 0.47205, 0.7058, 0.37176, 0.64263, 0.33292, 0.53221, 0.07461, 0.57041, 0.01586, 0.53238, 0.01845, 0.40386, 0.18362, 0.31509, 0.33735, 0.32683, 0.40523, 0.39332, 0.41667, 0.38257, 0.41842, 0.35296, 0.43296, 0.32169, 0.42287, 0.23193, 0.38575, 0.14992, 0.34364, 0.05687, 0.55845, 0.36572, 0.543, 0.42462, 0.62264, 0.1389], "triangles": [3, 1, 2, 0, 1, 3, 36, 0, 3, 32, 33, 0, 36, 32, 0, 4, 36, 3, 31, 32, 36, 5, 36, 4, 31, 36, 5, 34, 30, 31, 31, 5, 34, 6, 34, 5, 29, 30, 34, 7, 34, 6, 28, 29, 34, 8, 34, 7, 35, 28, 34, 9, 35, 8, 8, 35, 34, 10, 35, 9, 27, 21, 25, 27, 25, 26, 24, 25, 21, 23, 24, 21, 22, 23, 21, 35, 20, 21, 35, 27, 28, 10, 20, 35, 19, 10, 11, 27, 35, 21, 10, 19, 20, 12, 18, 19, 12, 19, 11, 18, 12, 15, 14, 12, 13, 15, 12, 14, 16, 18, 15, 17, 18, 16], "vertices": [1, 14, 4.83, 0.88, 1, 1, 14, 1.99, 15.61, 1, 1, 14, 3.4, 17.18, 1, 1, 14, 11.35, 16.8, 1, 1, 14, 19.19, 12.56, 1, 2, 15, -8.85, 8.26, 0.00086, 14, 29.43, 9.01, 0.99914, 2, 15, -4.18, 6.69, 0.10875, 14, 33.95, 7.06, 0.89125, 2, 15, -1.47, 6.66, 0.37124, 14, 36.65, 6.81, 0.62876, 2, 15, 0.66, 6.96, 0.6383, 14, 38.8, 6.93, 0.3617, 2, 15, 4.16, 7.41, 0.92156, 14, 42.33, 7.09, 0.07844, 1, 15, 11.83, 7.77, 1, 1, 15, 17.35, 11.68, 1, 1, 15, 19.86, 17.68, 1, 1, 15, 20.25, 24.37, 1, 1, 15, 27.82, 27.43, 1, 1, 15, 35.84, 28.27, 1, 1, 15, 44.14, 25.18, 1, 1, 15, 57.16, 14.65, 1, 1, 15, 42.66, 1.82, 1, 1, 15, 31.44, -3.06, 1, 1, 15, 26.17, -9.67, 1, 1, 15, 16.09, -13.08, 1, 1, 15, 21.3, -28.11, 1, 1, 15, 18.11, -31.99, 1, 1, 15, 6.08, -33.11, 1, 1, 15, -3.26, -24.13, 1, 2, 15, -3.13, -14.84, 0.99078, 14, 33.21, -14.48, 0.00922, 2, 15, 2.65, -10.14, 0.85936, 14, 39.37, -10.27, 0.14064, 2, 15, 1.58, -9.56, 0.69918, 14, 38.34, -9.61, 0.30082, 2, 15, -1.2, -9.75, 0.40286, 14, 35.55, -9.57, 0.59714, 2, 15, -4.22, -9.19, 0.15572, 14, 32.6, -8.76, 0.84428, 2, 15, -12.54, -10.68, 4e-05, 14, 24.18, -9.55, 0.99996, 1, 14, 16.52, -11.95, 1, 1, 14, 7.83, -14.67, 1, 2, 15, -0.89, -1.27, 0.17661, 14, 36.57, -1.14, 0.82339, 2, 15, 4.71, -1.61, 0.99225, 14, 42.12, -1.94, 0.00775, 1, 14, 15.16, 2.23, 1], "hull": 34, "edges": [2, 4, 4, 6, 6, 8, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 60, 62, 50, 52, 8, 10, 14, 68, 52, 54, 68, 70, 18, 20, 20, 22, 2, 0, 0, 66, 0, 72, 62, 64, 64, 66, 72, 64, 58, 60, 10, 12, 12, 14, 14, 16, 16, 18, 54, 56, 56, 58], "width": 48, "height": 75}}, "a31": {"pifu/lzz_a13": {"type": "mesh", "uvs": [0.50602, 0.10634, 0.72911, 0.2805, 0.91864, 0.47649, 0.99405, 0.60115, 0.92902, 0.73448, 0.78679, 0.90019, 0.61176, 0.99036, 0.28011, 0.99056, 0.08521, 0.92991, 0.05393, 0.8503, 0.02679, 0.68209, 0, 0.45821, 0.0115, 0.21623, 0.04184, 0.11008, 0.1651, 0.0094, 0.34866, 0.0093, 0.53684, 0.80433, 0.25604, 0.23637], "triangles": [6, 16, 5, 6, 7, 16, 7, 8, 16, 8, 9, 16, 5, 16, 4, 9, 10, 16, 16, 11, 17, 16, 17, 1, 4, 16, 3, 16, 1, 2, 1, 17, 0, 3, 16, 2, 16, 10, 11, 11, 12, 17, 12, 13, 17, 13, 14, 17, 17, 15, 0, 17, 14, 15], "vertices": [1, 13, -30.63, 10.29, 1, 2, 13, -10.17, 26.49, 0.99993, 14, -64.33, 16.08, 7e-05, 2, 13, 12.12, 39.55, 0.97595, 14, -44.51, 32.64, 0.02405, 2, 13, 25.74, 44.05, 0.94042, 14, -31.81, 39.33, 0.05958, 2, 13, 38.54, 36.62, 0.86527, 14, -17.96, 34.11, 0.13473, 2, 13, 53.71, 22.21, 0.51184, 14, -0.63, 22.41, 0.48816, 2, 13, 60.77, 6.16, 0.32726, 14, 8.99, 7.74, 0.67274, 2, 13, 56.76, -21.75, 0.80909, 14, 9.64, -20.44, 0.19091, 2, 13, 48.2, -37.25, 0.96332, 14, 3.77, -37.15, 0.03668, 2, 13, 39.71, -38.71, 0.98403, 14, -4.37, -39.99, 0.01597, 1, 13, 22.23, -38.51, 1, 1, 13, -0.92, -37.46, 1, 1, 13, -25.45, -32.93, 1, 1, 13, -35.9, -28.81, 1, 1, 13, -44.66, -16.96, 1, 1, 13, -42.44, -1.52, 1, 2, 13, 40.89, 2.6, 0.656, 14, -10.02, 0.94, 0.344, 1, 13, -20.42, -12.65, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 10, 32, 28, 34, 32, 6, 32, 16, 18, 20, 20, 22], "width": 68, "height": 82}}}}, {"name": "yuanpi", "attachments": {"a10": {"a10": {"type": "mesh", "uvs": [0.27379, 0.00034, 0.51744, 0.05524, 0.72661, 0.08504, 0.72718, 0.13265, 0.67223, 0.1927, 0.6288, 0.27087, 0.61088, 0.32508, 0.61614, 0.35763, 0.62422, 0.39319, 0.6399, 0.41691, 0.69055, 0.35904, 0.8409, 0.36163, 0.96003, 0.44075, 0.98422, 0.46783, 0.98423, 0.57843, 0.88644, 0.63511, 0.87644, 0.55302, 0.67758, 0.56557, 0.6219, 0.67498, 0.506, 0.73729, 0.44419, 0.8386, 0.2371, 1, 0.15972, 0.96579, 0.15135, 0.84502, 0.07689, 0.89453, 0.00484, 0.77868, 0.0121, 0.69485, 0.06937, 0.60641, 0.28096, 0.55797, 0.3491, 0.50867, 0.35827, 0.42623, 0.36238, 0.37956, 0.35354, 0.33862, 0.2456, 0.11434, 0.24157, 0.02105, 0.48622, 0.15457, 0.49258, 0.38092], "triangles": [9, 17, 30, 30, 17, 29, 30, 31, 36, 29, 17, 18, 19, 29, 18, 19, 28, 29, 19, 23, 28, 23, 27, 28, 20, 23, 19, 26, 27, 23, 25, 26, 23, 24, 25, 23, 21, 22, 23, 20, 21, 23, 33, 34, 0, 33, 0, 1, 35, 33, 1, 3, 35, 1, 3, 1, 2, 4, 35, 3, 5, 35, 4, 5, 32, 35, 32, 33, 35, 6, 32, 5, 36, 31, 32, 6, 36, 32, 7, 36, 6, 36, 7, 8, 16, 11, 12, 16, 12, 13, 9, 16, 17, 11, 9, 10, 16, 9, 11, 16, 13, 14, 15, 16, 14, 9, 36, 8, 9, 30, 36], "vertices": [1, 17, -1.83, -17.74, 1, 1, 17, 1.79, -2.2, 1, 1, 17, 3.27, 10.99, 1, 1, 17, 7.72, 11.47, 1, 1, 17, 13.68, 8.65, 1, 1, 17, 21.26, 6.71, 1, 1, 17, 26.44, 6.12, 1, 1, 17, 29.45, 6.75, 1, 2, 17, 32.73, 7.59, 0.96017, 18, -1.68, 7.84, 0.03983, 2, 17, 34.85, 8.78, 0.8169, 18, 0.52, 8.88, 0.1831, 2, 17, 29.12, 11.36, 0.87605, 18, -5.01, 11.86, 0.12395, 2, 17, 28.42, 20.65, 0.89265, 18, -5.05, 21.18, 0.10735, 2, 17, 35.08, 28.75, 0.88032, 18, 2.16, 28.79, 0.11968, 2, 17, 37.46, 30.5, 0.87837, 18, 4.66, 30.37, 0.12163, 2, 17, 47.81, 31.55, 0.87668, 18, 15.05, 30.68, 0.12332, 2, 17, 53.72, 26.05, 0.88079, 18, 20.56, 24.78, 0.11921, 2, 17, 46.1, 24.66, 0.83267, 18, 12.87, 23.93, 0.16733, 2, 17, 48.52, 12.51, 0.28993, 18, 14.42, 11.64, 0.71007, 2, 17, 59.1, 10.11, 0.00652, 18, 24.8, 8.5, 0.99348, 1, 18, 30.87, 1.5, 1, 1, 18, 40.51, -2.05, 1, 1, 18, 56.06, -14.42, 1, 1, 18, 52.99, -19.31, 1, 1, 18, 41.66, -20.17, 1, 1, 18, 46.45, -24.65, 1, 1, 18, 35.7, -29.44, 1, 1, 18, 27.81, -29.23, 1, 1, 18, 19.4, -25.93, 1, 1, 18, 14.45, -12.96, 1, 1, 18, 9.69, -8.88, 1, 2, 17, 37.48, -8.51, 0.01706, 18, 1.93, -8.54, 0.98294, 2, 17, 33.09, -8.69, 0.27828, 18, -2.47, -8.42, 0.72172, 2, 17, 29.31, -9.63, 0.64483, 18, -6.3, -9.09, 0.35517, 1, 17, 9.01, -18.41, 1, 1, 17, 0.31, -19.54, 1, 1, 17, 11.27, -3.18, 1, 2, 17, 32.4, -0.65, 0.92954, 18, -2.58, -0.35, 0.07046], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 64, 66, 66, 68, 56, 58, 58, 60, 60, 62, 62, 64, 20, 22, 8, 10, 2, 70, 18, 20, 14, 72, 10, 12, 12, 14, 14, 16, 16, 18], "width": 62, "height": 94}}, "a11": {"a11": {"type": "mesh", "uvs": [0.92821, 0.06171, 0.98881, 0.15348, 0.98883, 0.42789, 0.87468, 0.83531, 0.81338, 0.97172, 0.69034, 0.9984, 0.53451, 0.99194, 0.331, 0.9467, 0.18446, 0.86791, 0.0651, 0.68604, 1e-05, 0.49915, 0.0469, 0.42464, 0.27293, 0.24, 0.4994, 0.09272, 0.70408, 0.00925, 0.81479, 0.00833, 0.36952, 0.72119, 0.74168, 0.25128], "triangles": [17, 1, 2, 17, 0, 1, 17, 15, 0, 17, 14, 15, 4, 5, 16, 16, 5, 6, 3, 4, 16, 16, 6, 7, 7, 8, 16, 8, 9, 16, 3, 16, 2, 16, 11, 12, 16, 17, 2, 17, 12, 13, 17, 16, 12, 9, 10, 16, 11, 16, 10, 13, 14, 17], "vertices": [1, 4, 9.17, -1.1, 1, 2, 16, -26.69, 27.41, 0.00659, 4, -1.04, -5.85, 0.99341, 2, 16, 2.31, 33.53, 0.68617, 4, -30.62, -4.14, 0.31383, 3, 16, 47.44, 32.79, 0.99294, 4, -73.97, 8.42, 0.0014, 17, -12.95, 34.4, 0.00567, 2, 16, 62.97, 30.55, 0.9421, 17, 2.25, 30.51, 0.0579, 2, 16, 68.02, 20.55, 0.84231, 17, 6.21, 20.03, 0.15769, 2, 16, 70.17, 6.99, 0.3147, 17, 6.89, 6.32, 0.6853, 2, 16, 69.09, -11.55, 0.5188, 17, 3.83, -11.99, 0.4812, 2, 16, 63.42, -25.92, 0.8644, 17, -3.34, -25.68, 0.1356, 2, 16, 46.37, -40.25, 0.98727, 17, -21.82, -38.1, 0.01273, 2, 16, 27.81, -50.02, 0.99999, 17, -41.33, -45.83, 1e-05, 1, 16, 19.08, -47.65, 1, 2, 16, -4.54, -32.3, 0.9898, 4, -6.74, 57.58, 0.0102, 2, 16, -24.22, -16.09, 0.70877, 4, 8, 36.77, 0.29123, 2, 16, -36.75, -0.32, 0.24372, 4, 15.96, 18.27, 0.75628, 2, 16, -38.86, 9.19, 0.09246, 4, 15.5, 8.54, 0.90754, 2, 16, 44.56, -13.26, 0.99233, 17, -20.74, -11.07, 0.00767, 2, 16, -11.86, 8.31, 0.58298, 4, -10.33, 16.47, 0.41702], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 16, 32, 30, 34, 32, 8, 18, 20, 32, 20], "width": 88, "height": 108}}, "a12": {"a12": {"type": "mesh", "uvs": [0.60389, 0.02868, 0.85046, 0.00197, 0.87611, 0.01737, 0.86678, 0.10179, 0.7932, 0.18422, 0.73025, 0.29225, 0.69612, 0.33987, 0.69081, 0.36845, 0.69206, 0.39139, 0.69337, 0.42892, 0.68591, 0.5104, 0.74095, 0.57323, 0.83607, 0.6065, 0.94624, 0.6181, 0.98363, 0.70163, 0.98365, 0.78741, 0.91783, 0.8718, 0.72042, 0.99775, 0.53325, 0.83, 0.47205, 0.7058, 0.37176, 0.64263, 0.33292, 0.53221, 0.07461, 0.57041, 0.01586, 0.53238, 0.01845, 0.40386, 0.18362, 0.31509, 0.33735, 0.32683, 0.40523, 0.39332, 0.41667, 0.38257, 0.41842, 0.35296, 0.43296, 0.32169, 0.42287, 0.23193, 0.38575, 0.14992, 0.34364, 0.05687, 0.55845, 0.36572, 0.543, 0.42462, 0.62264, 0.1389], "triangles": [3, 1, 2, 0, 1, 3, 36, 0, 3, 32, 33, 0, 36, 32, 0, 4, 36, 3, 31, 32, 36, 5, 36, 4, 31, 36, 5, 34, 30, 31, 31, 5, 34, 6, 34, 5, 29, 30, 34, 7, 34, 6, 28, 29, 34, 8, 34, 7, 35, 28, 34, 9, 35, 8, 8, 35, 34, 10, 35, 9, 27, 21, 25, 27, 25, 26, 24, 25, 21, 23, 24, 21, 22, 23, 21, 35, 20, 21, 35, 27, 28, 10, 20, 35, 19, 10, 11, 27, 35, 21, 10, 19, 20, 12, 18, 19, 12, 19, 11, 18, 12, 15, 14, 12, 13, 15, 12, 14, 16, 18, 15, 17, 18, 16], "vertices": [1, 14, 4.83, 0.88, 1, 1, 14, 1.99, 15.61, 1, 1, 14, 3.4, 17.18, 1, 1, 14, 11.35, 16.8, 1, 1, 14, 19.19, 12.56, 1, 2, 15, -8.85, 8.26, 0.00086, 14, 29.43, 9.01, 0.99914, 2, 15, -4.18, 6.69, 0.10875, 14, 33.95, 7.06, 0.89125, 2, 15, -1.47, 6.66, 0.37124, 14, 36.65, 6.81, 0.62876, 2, 15, 0.66, 6.96, 0.6383, 14, 38.8, 6.93, 0.3617, 2, 15, 4.16, 7.41, 0.92156, 14, 42.33, 7.09, 0.07844, 1, 15, 11.83, 7.77, 1, 1, 15, 17.35, 11.68, 1, 1, 15, 19.86, 17.68, 1, 1, 15, 20.25, 24.37, 1, 1, 15, 27.82, 27.43, 1, 1, 15, 35.84, 28.27, 1, 1, 15, 44.14, 25.18, 1, 1, 15, 57.16, 14.65, 1, 1, 15, 42.66, 1.82, 1, 1, 15, 31.44, -3.06, 1, 1, 15, 26.17, -9.67, 1, 1, 15, 16.09, -13.08, 1, 1, 15, 21.3, -28.11, 1, 1, 15, 18.11, -31.99, 1, 1, 15, 6.08, -33.11, 1, 1, 15, -3.26, -24.13, 1, 2, 15, -3.13, -14.84, 0.99078, 14, 33.21, -14.48, 0.00922, 2, 15, 2.65, -10.14, 0.85936, 14, 39.37, -10.27, 0.14064, 2, 15, 1.58, -9.56, 0.69918, 14, 38.34, -9.61, 0.30082, 2, 15, -1.2, -9.75, 0.40286, 14, 35.55, -9.57, 0.59714, 2, 15, -4.22, -9.19, 0.15572, 14, 32.6, -8.76, 0.84428, 2, 15, -12.54, -10.68, 4e-05, 14, 24.18, -9.55, 0.99996, 1, 14, 16.52, -11.95, 1, 1, 14, 7.83, -14.67, 1, 2, 15, -0.89, -1.27, 0.17661, 14, 36.57, -1.14, 0.82339, 2, 15, 4.71, -1.61, 0.99225, 14, 42.12, -1.94, 0.00775, 1, 14, 15.16, 2.23, 1], "hull": 34, "edges": [2, 4, 4, 6, 6, 8, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 60, 62, 50, 52, 8, 10, 14, 68, 52, 54, 68, 70, 18, 20, 20, 22, 2, 0, 0, 66, 0, 72, 62, 64, 64, 66, 72, 64, 58, 60, 10, 12, 12, 14, 14, 16, 16, 18, 54, 56, 56, 58], "width": 60, "height": 94}}, "a13": {"a13": {"type": "mesh", "uvs": [0.50602, 0.10634, 0.72911, 0.2805, 0.91864, 0.47649, 0.99405, 0.60115, 0.92902, 0.73448, 0.78679, 0.90019, 0.61176, 0.99036, 0.28011, 0.99056, 0.08521, 0.92991, 0.05393, 0.8503, 0.02679, 0.68209, 0, 0.45821, 0.0115, 0.21623, 0.04184, 0.11008, 0.1651, 0.0094, 0.34866, 0.0093, 0.53684, 0.80433, 0.25604, 0.23637], "triangles": [6, 16, 5, 6, 7, 16, 7, 8, 16, 8, 9, 16, 5, 16, 4, 9, 10, 16, 16, 11, 17, 16, 17, 1, 4, 16, 3, 16, 1, 2, 1, 17, 0, 3, 16, 2, 16, 10, 11, 11, 12, 17, 12, 13, 17, 13, 14, 17, 17, 15, 0, 17, 14, 15], "vertices": [1, 13, -30.63, 10.29, 1, 2, 13, -10.17, 26.49, 0.99993, 14, -64.33, 16.08, 7e-05, 2, 13, 12.12, 39.55, 0.97595, 14, -44.51, 32.64, 0.02405, 2, 13, 25.74, 44.05, 0.94042, 14, -31.81, 39.33, 0.05958, 2, 13, 38.54, 36.62, 0.86527, 14, -17.96, 34.11, 0.13473, 2, 13, 53.71, 22.21, 0.51184, 14, -0.63, 22.41, 0.48816, 2, 13, 60.77, 6.16, 0.32726, 14, 8.99, 7.74, 0.67274, 2, 13, 56.76, -21.75, 0.80909, 14, 9.64, -20.44, 0.19091, 2, 13, 48.2, -37.25, 0.96332, 14, 3.77, -37.15, 0.03668, 2, 13, 39.71, -38.71, 0.98403, 14, -4.37, -39.99, 0.01597, 1, 13, 22.23, -38.51, 1, 1, 13, -0.92, -37.46, 1, 1, 13, -25.45, -32.93, 1, 1, 13, -35.9, -28.81, 1, 1, 13, -44.66, -16.96, 1, 1, 13, -42.44, -1.52, 1, 2, 13, 40.89, 2.6, 0.656, 14, -10.02, 0.94, 0.344, 1, 13, -20.42, -12.65, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 10, 32, 28, 34, 32, 6, 32, 16, 18, 20, 20, 22], "width": 85, "height": 103}}, "a14": {"a14": {"type": "mesh", "uvs": [0.29521, 0.01005, 0.49842, 0.01364, 0.68289, 0.08275, 0.81434, 0.22001, 0.9684, 0.49028, 1, 0.60961, 0.8477, 0.74836, 0.415, 0.99223, 0.19156, 1, 0.04742, 0.84414, 0.05014, 0.66729, 0.13734, 0.57539, 0.29148, 0.50222, 0.15014, 0.41073, 0.12327, 0.1253, 0.35695, 0.18354], "triangles": [15, 0, 1, 14, 0, 15, 13, 14, 15, 12, 13, 15, 6, 3, 4, 6, 4, 5, 15, 1, 2, 6, 12, 3, 3, 15, 2, 3, 12, 15, 7, 12, 6, 11, 12, 7, 9, 10, 11, 11, 8, 9, 7, 8, 11], "vertices": [1, 21, 2.81, 7.06, 1, 1, 21, 6.99, 12.05, 1, 1, 21, 12.42, 15.25, 1, 1, 21, 18.48, 15.76, 1, 1, 21, 28.3, 14.16, 1, 1, 21, 31.91, 12.55, 1, 1, 21, 32.3, 5.97, 1, 1, 21, 29.67, -9.71, 1, 1, 21, 25.37, -15.42, 1, 1, 21, 18.59, -15.88, 1, 1, 21, 14.25, -12.25, 1, 1, 21, 13.71, -8.24, 1, 1, 21, 14.99, -2.93, 1, 1, 21, 9.87, -4.61, 1, 1, 21, 2.22, 0.46, 1, 1, 21, 8.37, 5.11, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 0, 30, 24, 26, 26, 28, 20, 22, 22, 24, 16, 18], "width": 32, "height": 32}}, "a15": {"a15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [129.65, 26.15, 20.95, -108.43, -97.3, -12.92, 11.41, 121.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 173, "height": 152}}, "a16": {"a16": {"type": "mesh", "uvs": [0.26878, 0, 0.34518, 0.01504, 0.40024, 0.06829, 0.47188, 0.17462, 0.52039, 0.39798, 0.58341, 0.47688, 0.61761, 0.56507, 0.6701, 0.56204, 0.71803, 0.60871, 0.74479, 0.68223, 0.77909, 0.69206, 0.80765, 0.72167, 0.912, 0.75545, 0.96779, 0.78004, 0.99672, 0.91514, 0.80534, 1, 0.7234, 0.99556, 0.63137, 0.92171, 0.61132, 0.85973, 0.57363, 0.83749, 0.5598, 0.8076, 0.5141, 0.79741, 0.38725, 0.76587, 0.36789, 0.72459, 0.16747, 0.61172, 0.1552, 0.56533, 0.09191, 0.51796, 0.07261, 0.4912, 0.06553, 0.4343, 0.00255, 0.29856, 0, 0.17704, 0.03642, 0.08896, 0.17085, 0, 0.21975, 0.17613, 0.3216, 0.44219, 0.65946, 0.74654, 0.6982, 0.78224, 0.50915, 0.64225], "triangles": [12, 14, 15, 16, 17, 15, 14, 12, 13, 36, 12, 15, 12, 36, 11, 15, 17, 36, 17, 18, 36, 18, 19, 36, 19, 20, 36, 20, 35, 36, 20, 21, 35, 35, 21, 37, 36, 10, 11, 36, 9, 10, 36, 35, 9, 21, 22, 37, 22, 23, 37, 35, 37, 8, 35, 8, 9, 8, 6, 7, 6, 8, 37, 23, 24, 37, 24, 34, 37, 37, 5, 6, 5, 37, 34, 5, 34, 4, 24, 25, 34, 25, 26, 34, 26, 27, 34, 27, 28, 34, 28, 33, 34, 34, 3, 4, 34, 33, 3, 28, 29, 33, 29, 30, 33, 30, 31, 33, 31, 32, 33, 33, 2, 3, 33, 1, 2, 33, 0, 1, 33, 32, 0], "vertices": [1, 19, -19.09, 11.19, 1, 1, 19, -14.5, 17.91, 1, 1, 19, -5.67, 20.79, 1, 1, 19, 10.63, 22.82, 1, 2, 19, 41.34, 17.19, 0.76559, 20, -2.83, 19.2, 0.23441, 2, 19, 53.77, 19.65, 0.20532, 20, 9.72, 17.42, 0.79468, 2, 19, 66.42, 18.89, 0.00501, 20, 21.41, 12.52, 0.99499, 2, 19, 67.84, 24.12, 0, 20, 24.47, 17, 1, 2, 20, 32.59, 16.8, 0.99982, 21, -18, 16.81, 0.00018, 2, 20, 42.21, 12.58, 0.88227, 21, -8.38, 12.58, 0.11773, 2, 20, 45.49, 14.47, 0.71433, 21, -5.1, 14.48, 0.28567, 2, 20, 50.52, 14.2, 0.41763, 21, -0.07, 14.2, 0.58237, 2, 20, 60.89, 19.64, 0.02598, 21, 10.31, 19.63, 0.97402, 2, 20, 67.14, 21.99, 0.00074, 21, 16.56, 21.97, 0.99926, 1, 21, 32.93, 12.57, 1, 1, 21, 29.66, -10.12, 1, 1, 21, 23.88, -16.3, 1, 2, 20, 60.61, -17.26, 0.03257, 21, 9.99, -17.27, 0.96743, 2, 20, 52.66, -13.5, 0.32561, 21, 2.04, -13.5, 0.67439, 2, 20, 47.83, -14.6, 0.6363, 21, -2.78, -14.59, 0.3637, 2, 20, 43.73, -13.12, 0.8604, 21, -6.89, -13.11, 0.1396, 2, 20, 39.68, -15.9, 0.97689, 21, -10.94, -15.89, 0.02311, 1, 20, 28.09, -23.34, 1, 2, 19, 78.54, -12.72, 0.00382, 20, 22.4, -21.32, 0.99618, 2, 19, 56.94, -26.95, 0.41988, 20, -2.68, -27.61, 0.58012, 2, 19, 50.48, -25.99, 0.60758, 20, -8.46, -24.58, 0.39242, 2, 19, 42.14, -29.94, 0.84403, 20, -17.64, -25.55, 0.15597, 2, 19, 37.99, -30.58, 0.89321, 20, -21.76, -24.78, 0.10679, 2, 19, 30.35, -28.63, 0.96373, 20, -28.33, -20.42, 0.03627, 1, 19, 10.53, -28.46, 1, 1, 19, -5.36, -23.08, 1, 1, 19, -15.55, -15.47, 1, 1, 19, -22.47, 1.69, 1, 1, 19, 2.11, -1.72, 1, 1, 19, 40.22, -4.15, 1, 2, 20, 43.61, 0.16, 0.99986, 21, -6.99, 0.17, 0.00014, 2, 20, 49.95, 0.18, 0.70453, 21, -0.65, 0.18, 0.29547, 1, 20, 22.69, -2.86, 1], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66, 66, 68, 18, 70, 38, 40, 34, 36, 36, 38, 18, 20, 20, 22, 22, 24, 22, 72, 40, 42, 42, 44, 16, 18, 12, 74, 58, 60, 14, 16], "width": 103, "height": 138}}, "a17": {"a17": {"type": "mesh", "uvs": [0.56479, 0, 0.64258, 0, 0.68223, 0.03143, 0.73181, 0.11142, 0.78497, 0.21074, 0.83797, 0.33562, 0.87701, 0.44826, 0.90917, 0.55892, 0.93717, 0.66687, 0.95856, 0.76023, 0.97678, 0.85227, 0.98748, 0.91564, 0.99752, 0.98144, 0.99242, 1, 0.96749, 0.96547, 0.93724, 0.9313, 0.89176, 0.88572, 0.83767, 0.85108, 0.75869, 0.8074, 0.65422, 0.7818, 0.52944, 0.75662, 0.38712, 0.74469, 0.27432, 0.7468, 0.15971, 0.7227, 0.07905, 0.68293, 0.01125, 0.67079, 0, 0.65884, 0.02273, 0.62111, 0.04518, 0.59485, 0.07814, 0.55151, 0.12466, 0.48448, 0.17417, 0.40753, 0.22304, 0.33784, 0.28351, 0.2588, 0.34461, 0.18856, 0.42012, 0.10966, 0.49873, 0.03795, 0.59783, 0.13584, 0.58085, 0.29689, 0.56532, 0.45503, 0.55104, 0.61576], "triangles": [25, 26, 27, 23, 24, 29, 28, 29, 24, 27, 28, 24, 25, 27, 24, 22, 23, 30, 23, 29, 30, 22, 31, 32, 31, 22, 30, 39, 34, 38, 39, 33, 34, 39, 21, 33, 40, 21, 39, 32, 33, 21, 32, 21, 22, 20, 21, 40, 37, 0, 1, 37, 1, 2, 36, 0, 37, 37, 35, 36, 38, 35, 37, 37, 2, 3, 3, 38, 37, 38, 34, 35, 3, 4, 38, 5, 38, 4, 6, 39, 5, 18, 19, 6, 7, 18, 6, 5, 39, 38, 6, 19, 39, 40, 39, 19, 20, 40, 19, 8, 18, 7, 17, 18, 8, 9, 17, 8, 16, 17, 9, 10, 16, 9, 15, 16, 10, 15, 10, 11, 14, 15, 11, 14, 11, 12, 13, 14, 12], "vertices": [1, 4, 10.09, 3.33, 1, 1, 4, 9.07, -14.3, 1, 1, 4, 3.91, -23.01, 1, 1, 4, -8.56, -33.57, 1, 1, 4, -23.93, -44.77, 1, 1, 41, 19.04, 2.16, 1, 1, 41, 37.87, 3.49, 1, 2, 42, -2.53, 3.27, 0.09463, 41, 55.81, 3.51, 0.90537, 1, 42, 14.63, 4.35, 1, 1, 42, 29.27, 4.68, 1, 2, 43, 5.31, 4.69, 0.98468, 42, 43.5, 4.38, 0.01532, 1, 43, 15, 4.64, 1, 1, 43, 24.99, 4.35, 1, 1, 43, 27.35, 2.53, 1, 1, 43, 20.96, -1.64, 1, 4, 43, 14.32, -6.98, 0.97513, 42, 51.84, -7.78, 0.02476, 44, 2.83, 177.25, 9e-05, 45, -68.28, 171, 1e-05, 5, 43, 5.15, -15.23, 0.51841, 42, 42.22, -15.5, 0.47046, 41, 98.4, -19.75, 0.00778, 44, 4.52, 165.03, 0.00282, 45, -65.31, 159.03, 0.00052, 6, 43, -2.94, -25.79, 0.12323, 42, 33.53, -25.58, 0.7842, 41, 88.73, -28.88, 0.0735, 4, -119.23, -51.25, 0.00037, 44, 8.71, 152.41, 0.01559, 45, -59.81, 146.92, 0.00312, 6, 43, -13.78, -41.47, 0.00532, 42, 21.82, -40.62, 0.62911, 41, 75.54, -42.64, 0.28118, 4, -111.74, -33.73, 0.00808, 44, 15.61, 134.64, 0.06244, 45, -51.08, 129.98, 0.01387, 5, 42, 10.86, -61.99, 0.33012, 41, 62.45, -62.77, 0.41752, 4, -106.6, -10.27, 0.03606, 44, 28.32, 114.26, 0.17084, 45, -36.29, 111.06, 0.04547, 5, 42, -1.47, -87.76, 0.14372, 41, 47.54, -87.14, 0.32958, 4, -101.24, 17.79, 0.0627, 44, 44.1, 90.45, 0.33505, 45, -18.08, 89.04, 0.12895, 6, 42, -13.18, -117.92, 0.04915, 41, 32.8, -115.95, 0.14949, 4, -97.62, 49.94, 0.03431, 44, 63.98, 64.92, 0.41613, 45, 4.38, 65.75, 0.35017, 46, -32.28, 65.28, 0.00075, 6, 42, -20.83, -142.36, 0.01676, 41, 22.69, -139.47, 0.05521, 4, -96.46, 75.53, 0.00581, 44, 81.03, 45.81, 0.2648, 45, 23.35, 48.55, 0.62064, 46, -13.05, 48.36, 0.03679, 5, 42, -32.3, -165.98, 0.00314, 41, 8.86, -161.8, 0.01062, 44, 95.42, 23.85, 0.05017, 45, 39.98, 28.22, 0.63622, 46, 3.88, 28.28, 0.29984, 5, 42, -43.58, -181.56, 0.00022, 41, -3.95, -176.14, 0.00075, 44, 103, 6.17, 0.00084, 45, 49.38, 11.44, 0.13839, 46, 13.53, 11.65, 0.85981, 4, 42, -50.06, -195.63, 0, 41, -11.85, -189.48, 0, 45, 59.43, -0.35, 1e-05, 46, 23.76, 0.01, 0.99999, 4, 42, -52.53, -197.51, 0, 41, -14.5, -191.09, 0, 45, 60.09, -3.38, 0, 46, 24.47, -3.02, 1, 1, 46, 16.9, -3.71, 1, 1, 46, 10.51, -3.18, 1, 2, 45, 36.29, -2.94, 0.36808, 46, 0.66, -2.93, 0.63192, 1, 45, 21.8, -3.02, 1, 1, 45, 5.81, -3.71, 1, 1, 44, 43.01, -2.7, 1, 4, 42, -88.84, -117.96, 1e-05, 41, -42.46, -108.24, 5e-05, 4, -24.47, 69.28, 0.00141, 44, 25.18, -0.03, 0.99854, 4, 42, -94.42, -101.54, 0.00027, 41, -46.33, -91.34, 0.00386, 4, -14.89, 54.84, 0.20052, 44, 8.23, 3.61, 0.79535, 1, 4, -4.22, 37.05, 1, 1, 4, 5.34, 18.63, 1, 1, 4, -10.42, -3, 1, 5, 42, -62.53, -55.54, 0.01741, 41, -9.9, -48.85, 0.29902, 4, -33.99, 2.22, 0.47284, 44, -14.88, 54.58, 0.19993, 45, -72.95, 47.15, 0.01081, 5, 42, -41.38, -66.16, 0.06464, 41, 10.06, -61.57, 0.37056, 4, -57.15, 7.09, 0.21704, 44, 5.09, 67.29, 0.30155, 45, -54.43, 61.89, 0.04622, 5, 42, -19.77, -76.62, 0.12826, 41, 30.48, -74.2, 0.35802, 4, -80.71, 11.7, 0.09313, 44, 25.16, 80.46, 0.32391, 45, -35.86, 77.11, 0.09668], "hull": 37, "edges": [0, 72, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 24, 26, 34, 36, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 60, 62, 62, 64, 68, 70, 70, 72, 54, 56, 56, 58, 58, 60, 64, 66, 66, 68, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 36, 38, 38, 40, 40, 42, 42, 44, 2, 74, 74, 76, 76, 78, 78, 80, 30, 32, 32, 34], "width": 227, "height": 148}}, "a18": {"a18": {"type": "mesh", "uvs": [0.60193, 0.00376, 0.62516, 0.02419, 0.69491, 0.12754, 0.69845, 0.21407, 0.70174, 0.29458, 0.72365, 0.35145, 0.74987, 0.41947, 0.79808, 0.45557, 0.83016, 0.4796, 0.90794, 0.48614, 0.95516, 0.49011, 0.98449, 0.51212, 0.99932, 0.56362, 0.9904, 0.61638, 0.9562, 0.65821, 0.91629, 0.70261, 0.85728, 0.74862, 0.77495, 0.76902, 0.71145, 0.80104, 0.69514, 0.74321, 0.62862, 0.79152, 0.60566, 0.79043, 0.59202, 0.75312, 0.59399, 0.69235, 0.55165, 0.75391, 0.49755, 0.80314, 0.47255, 0.79752, 0.4761, 0.73687, 0.42819, 0.79205, 0.38264, 0.84044, 0.32848, 0.8883, 0.28393, 0.9171, 0.288, 0.85142, 0.24159, 0.89383, 0.2018, 0.93018, 0.15125, 0.96076, 0.14992, 0.93149, 0.18992, 0.8533, 0.14437, 0.89498, 0.10368, 0.93221, 0.07584, 0.95768, 0.04447, 0.97876, 0.01287, 1, 0.00386, 1, 0.00096, 0.9903, 0.01592, 0.96663, 0.03412, 0.93782, 0.05058, 0.89539, 0.07013, 0.84503, 0.08638, 0.79182, 0.10465, 0.732, 0.12476, 0.66616, 0.14036, 0.61508, 0.15695, 0.56074, 0.12796, 0.56252, 0.15435, 0.49005, 0.19001, 0.40605, 0.23154, 0.32486, 0.23253, 0.29725, 0.28389, 0.21255, 0.34907, 0.12667, 0.43189, 0.0579, 0.51399, 0.0187, 0.57585, 0.00202, 0.23491, 0.78317, 0.27719, 0.71031, 0.31574, 0.6363, 0.35305, 0.55547, 0.54704, 0.13364, 0.53088, 0.26229, 0.55948, 0.4103, 0.64653, 0.52984, 0.90145, 0.5788, 0.77959, 0.58791], "triangles": [37, 49, 64, 45, 41, 42, 45, 42, 44, 44, 42, 43, 45, 46, 41, 41, 46, 40, 35, 36, 34, 40, 46, 39, 46, 47, 39, 39, 47, 38, 34, 36, 37, 34, 37, 33, 47, 48, 38, 38, 48, 37, 48, 49, 37, 55, 56, 67, 31, 32, 30, 33, 37, 32, 30, 32, 29, 37, 64, 32, 29, 32, 65, 28, 29, 65, 24, 25, 27, 25, 26, 27, 65, 32, 64, 28, 65, 27, 49, 50, 64, 65, 64, 51, 65, 66, 27, 23, 27, 67, 64, 50, 51, 51, 52, 65, 65, 52, 66, 27, 66, 67, 52, 53, 66, 66, 53, 67, 54, 55, 53, 53, 55, 67, 56, 57, 67, 67, 57, 70, 70, 57, 58, 69, 58, 59, 58, 69, 70, 68, 0, 1, 59, 60, 69, 69, 60, 68, 68, 60, 61, 68, 63, 0, 61, 62, 68, 68, 62, 63, 70, 69, 4, 70, 4, 5, 3, 68, 2, 4, 69, 3, 68, 1, 2, 3, 69, 68, 18, 19, 17, 19, 20, 22, 20, 21, 22, 16, 17, 73, 22, 23, 19, 23, 24, 27, 17, 19, 73, 15, 73, 72, 15, 16, 73, 19, 23, 73, 15, 72, 14, 23, 71, 73, 23, 67, 71, 14, 72, 13, 13, 72, 12, 73, 71, 7, 73, 8, 72, 73, 7, 8, 7, 71, 6, 72, 11, 12, 72, 10, 11, 72, 9, 10, 72, 8, 9, 67, 70, 71, 71, 70, 6, 70, 5, 6], "vertices": [3, 36, 157.19, -91.8, 0.00162, 37, 122.93, -17.46, 0.41539, 38, -30.2, -5.67, 0.583, 2, 37, 115.12, -21.37, 0.6565, 38, -28.28, 2.85, 0.3435, 2, 37, 79.98, -28.97, 0.86186, 38, -11.9, 34.86, 0.13814, 2, 37, 55.51, -21.32, 0.96067, 38, 9.52, 48.94, 0.03933, 2, 36, 89.27, -32.41, 0.08068, 37, 32.75, -14.21, 0.91932, 2, 36, 75.43, -21.13, 0.27237, 37, 14.9, -14.18, 0.72763, 2, 36, 58.88, -7.65, 0.57517, 37, -6.45, -14.16, 0.42483, 2, 36, 42.16, -5.33, 0.82772, 37, -20.88, -22.9, 0.17228, 2, 36, 31.03, -3.79, 0.96937, 37, -30.49, -28.72, 0.03063, 2, 36, 12.03, -13.11, 0.9999, 37, -39.35, -47.95, 0.0001, 2, 36, 0.5, -18.77, 0.9997, 39, -79.36, 164.07, 0.0003, 3, 36, -9.68, -17.35, 0.99571, 38, 44.86, 160.93, 0.00077, 39, -76.44, 173.93, 0.00352, 2, 36, -21.05, -6.43, 0.99839, 39, -63.95, 183.55, 0.00161, 2, 36, -27.12, 8.16, 0.99529, 39, -48.63, 187.38, 0.00471, 3, 36, -25.66, 23.55, 0.98749, 38, 85.83, 176.76, 0.00017, 39, -33.62, 183.65, 0.01234, 3, 36, -23.27, 40.41, 0.9721, 38, 102.67, 174.31, 0.00132, 39, -17.3, 178.79, 0.02658, 3, 36, -16.72, 60.36, 0.9496, 38, 122.61, 167.68, 0.00393, 39, 1.46, 169.34, 0.04648, 3, 36, -0.82, 77.14, 0.92044, 38, 139.32, 151.72, 0.009, 39, 15.68, 151.12, 0.07056, 3, 36, 8.94, 94.19, 0.88628, 38, 156.34, 141.9, 0.0164, 39, 31.1, 138.95, 0.09733, 3, 36, 21.63, 81.88, 0.85017, 38, 143.99, 129.26, 0.02523, 39, 17.04, 128.23, 0.1246, 3, 36, 29.57, 103.48, 0.81698, 38, 165.55, 121.24, 0.03345, 39, 37.22, 117.17, 0.14957, 3, 36, 35.05, 106.44, 0.78224, 38, 168.5, 115.75, 0.04282, 39, 39.34, 111.31, 0.17494, 3, 36, 43.95, 98.94, 0.71935, 38, 160.96, 106.87, 0.05437, 39, 30.6, 103.61, 0.22628, 3, 36, 52.87, 83.31, 0.62177, 38, 145.29, 98.01, 0.06753, 39, 13.81, 97.12, 0.3107, 3, 36, 53.17, 104.84, 0.50948, 38, 166.83, 97.63, 0.0759, 39, 35.06, 93.62, 0.41462, 3, 36, 58.1, 124.91, 0.42543, 38, 186.88, 92.63, 0.07843, 39, 54.18, 85.77, 0.49613, 4, 36, 64.75, 127.02, 0.35727, 38, 188.97, 85.97, 0.07192, 39, 55.28, 78.88, 0.57066, 40, -52.67, 68.12, 0.00015, 4, 36, 73.28, 111.2, 0.2835, 38, 173.11, 77.5, 0.05795, 39, 38.36, 72.8, 0.65568, 40, -67.74, 58.31, 0.00287, 4, 36, 75.85, 131.9, 0.18775, 38, 193.81, 74.85, 0.03611, 39, 58.46, 67.17, 0.76274, 40, -46.89, 57.45, 0.0134, 4, 36, 78.93, 150.56, 0.10627, 38, 212.45, 71.71, 0.01665, 39, 76.45, 61.36, 0.84533, 40, -28.04, 55.93, 0.03175, 4, 36, 84.08, 170.3, 0.05512, 38, 232.17, 66.48, 0.00435, 39, 95.2, 53.34, 0.87881, 40, -7.95, 52.43, 0.06172, 4, 36, 89.94, 183.86, 0.03365, 38, 245.71, 60.57, 0.00066, 39, 107.75, 45.52, 0.82563, 40, 6.06, 47.7, 0.14006, 4, 36, 99.13, 166.69, 0.02157, 38, 228.51, 51.45, 2e-05, 39, 89.4, 38.99, 0.68996, 40, -10.3, 37.13, 0.28846, 4, 36, 103.33, 183.96, 0.0114, 38, 245.76, 47.18, 0, 39, 105.86, 32.27, 0.48998, 40, 7.26, 34.37, 0.49862, 4, 36, 106.93, 198.76, 0.00426, 38, 260.55, 43.53, 0, 39, 119.96, 26.51, 0.3156, 40, 22.31, 32.01, 0.68014, 3, 36, 113.91, 213.62, 0.00082, 39, 133.62, 17.4, 0.22511, 40, 37.69, 26.28, 0.77407, 3, 36, 118.73, 206.42, 0.00139, 39, 125.78, 13.71, 0.25349, 40, 30.91, 20.88, 0.74511, 4, 36, 121.53, 181.01, 0.00497, 38, 242.75, 28.99, 0.00028, 39, 100.24, 14.71, 0.3526, 40, 5.83, 15.99, 0.64214, 3, 36, 125.64, 197.98, 0.00135, 39, 116.4, 8.12, 0.15256, 40, 23.07, 13.29, 0.84608, 3, 36, 129.31, 213.12, 0.00022, 39, 130.84, 2.24, 0.03426, 40, 38.47, 10.88, 0.96552, 1, 40, 49.01, 9.24, 1, 1, 40, 59.06, 6.05, 1, 1, 40, 69.19, 2.84, 1, 1, 40, 70.63, 0.87, 1, 1, 40, 68.77, -1.46, 1, 1, 40, 60.72, -2.32, 1, 2, 39, 139.69, -14.48, 0.00157, 40, 50.93, -3.36, 0.99843, 2, 39, 126.38, -15.25, 0.04246, 40, 38.15, -7.16, 0.95754, 2, 39, 110.59, -16.16, 0.1845, 40, 22.99, -11.68, 0.8155, 2, 39, 94.36, -18.22, 0.43452, 40, 7.67, -17.41, 0.56548, 2, 39, 76.13, -20.53, 0.71699, 40, -9.55, -23.85, 0.28301, 3, 38, 204.5, -14.79, 0.00466, 39, 56.05, -23.07, 0.90363, 40, -28.5, -30.93, 0.09171, 3, 38, 189.38, -19, 0.01568, 39, 40.48, -25.05, 0.97435, 40, -43.21, -36.43, 0.00997, 2, 38, 173.29, -23.48, 0.06161, 39, 23.91, -27.15, 0.93839, 2, 38, 177.8, -29.93, 0.19245, 39, 27.44, -34.19, 0.80755, 2, 38, 155.75, -34.92, 0.42127, 39, 4.9, -35.93, 0.57873, 2, 38, 129.47, -39.53, 0.69136, 39, -21.77, -36.68, 0.30864, 2, 38, 103.08, -42.34, 0.88877, 39, -48.29, -35.64, 0.11123, 2, 38, 95.95, -46.34, 0.98224, 39, -55.92, -38.56, 0.01776, 2, 38, 67.3, -47.41, 0.99955, 39, -84.43, -35.47, 0.00045, 3, 37, 111.52, 59.28, 0.0033, 38, 36.41, -45.46, 0.99668, 39, -114.71, -29.07, 2e-05, 2, 37, 123.21, 31.33, 0.04288, 38, 7.37, -36.8, 0.95712, 2, 37, 126.72, 6.48, 0.1341, 38, -14.08, -23.77, 0.8659, 2, 37, 125.77, -10.97, 0.25877, 38, -26.98, -11.98, 0.74123, 4, 36, 121.93, 156.94, 0.01782, 38, 218.68, 28.68, 0.00614, 39, 76.37, 17.89, 0.67359, 40, -18.13, 13.6, 0.30245, 4, 36, 123.38, 132.56, 0.03698, 38, 194.29, 27.31, 0.05779, 39, 52.05, 20.07, 0.81667, 40, -42.31, 10.14, 0.08855, 4, 36, 125.88, 108.42, 0.05704, 38, 170.14, 24.91, 0.13432, 39, 27.81, 21.19, 0.80268, 40, -66.16, 5.66, 0.00596, 3, 36, 129.71, 82.73, 0.06811, 38, 144.44, 21.17, 0.19283, 39, 1.83, 21.22, 0.73905, 4, 36, 149.87, -51.24, 0.05925, 37, 91.66, 9.4, 0.29317, 38, 10.39, 1.51, 0.646, 39, -133.65, 21.18, 0.00158, 4, 36, 133.77, -16.45, 0.20939, 37, 57.22, 26.25, 0.21934, 38, 45.24, 17.47, 0.56037, 39, -96.85, 31.93, 0.0109, 4, 36, 104.34, 16.91, 0.46152, 37, 13.34, 33.57, 0.13575, 38, 78.71, 46.79, 0.37872, 39, -59.49, 56.08, 0.02401, 4, 36, 65.77, 34.83, 0.71449, 37, -27.89, 23.15, 0.07069, 38, 96.77, 85.29, 0.18048, 39, -36.04, 91.56, 0.03434, 3, 36, -0.75, 11.22, 0.97121, 38, 73.4, 151.9, 0.01238, 39, -49.52, 160.85, 0.0164, 4, 36, 26.04, 30.72, 0.89246, 37, -56.13, -5.1, 0.01655, 38, 92.81, 125.04, 0.06177, 39, -34.21, 131.46, 0.02921], "hull": 64, "edges": [0, 126, 0, 2, 2, 4, 20, 22, 22, 24, 24, 26, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 68, 70, 70, 72, 72, 74, 84, 86, 86, 88, 106, 108, 108, 110, 114, 116, 120, 122, 122, 124, 124, 126, 92, 94, 94, 96, 80, 82, 82, 84, 88, 90, 90, 92, 78, 80, 74, 76, 76, 78, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 110, 112, 112, 114, 116, 118, 118, 120, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 54, 56, 56, 58, 64, 66, 66, 68, 74, 128, 128, 130, 130, 132, 132, 134, 0, 136, 136, 138, 138, 140, 140, 142, 22, 144, 144, 146, 146, 142], "width": 271, "height": 296}}, "a19": {"a19": {"type": "mesh", "uvs": [0.43635, 0.00348, 0.50923, 0.02735, 0.59746, 0.07737, 0.67645, 0.15731, 0.7375, 0.24223, 0.77959, 0.32442, 0.80344, 0.39528, 0.83321, 0.45857, 0.86016, 0.53178, 0.87439, 0.5597, 0.84161, 0.57182, 0.86872, 0.64589, 0.89604, 0.72249, 0.91844, 0.79893, 0.93744, 0.86296, 0.95741, 0.91299, 0.97856, 0.95569, 1, 0.98683, 0.99594, 1, 0.95853, 0.98262, 0.91633, 0.95071, 0.8665, 0.90786, 0.81109, 0.85368, 0.84849, 0.92876, 0.8487, 0.9527, 0.83262, 0.95102, 0.77614, 0.91209, 0.70825, 0.84879, 0.71439, 0.90698, 0.70014, 0.90704, 0.62833, 0.84967, 0.57547, 0.79253, 0.52084, 0.73348, 0.52076, 0.79779, 0.50078, 0.79784, 0.45418, 0.7572, 0.41493, 0.70281, 0.39241, 0.80089, 0.30947, 0.74764, 0.28932, 0.79538, 0.25687, 0.78492, 0.21815, 0.76335, 0.13734, 0.74771, 0.07453, 0.69505, 0.0036, 0.6088, 0.00363, 0.53988, 0.03252, 0.48728, 0.14664, 0.48597, 0.24382, 0.4247, 0.29775, 0.29131, 0.30384, 0.12971, 0.3512, 0.0455, 0.39669, 0, 0.45267, 0.14698, 0.45591, 0.29974, 0.41372, 0.45696, 0.30173, 0.56374, 0.14431, 0.60972, 0.75804, 0.76391, 0.70285, 0.66751, 0.6493, 0.55479, 0.58925, 0.4302, 0.79073, 0.48988], "triangles": [18, 19, 17, 19, 16, 17, 19, 20, 16, 20, 15, 16, 25, 23, 24, 25, 26, 23, 20, 21, 15, 21, 14, 15, 23, 26, 22, 21, 22, 14, 22, 13, 14, 60, 61, 62, 22, 26, 27, 29, 27, 28, 29, 30, 27, 27, 58, 22, 22, 58, 13, 27, 30, 58, 58, 30, 31, 58, 12, 13, 33, 34, 32, 32, 34, 35, 31, 59, 58, 31, 32, 59, 58, 11, 12, 58, 59, 11, 59, 32, 60, 32, 36, 60, 59, 10, 11, 10, 60, 62, 10, 59, 60, 10, 8, 9, 10, 62, 8, 62, 7, 8, 60, 55, 61, 62, 6, 7, 62, 61, 6, 61, 5, 6, 61, 4, 5, 61, 54, 4, 54, 3, 4, 3, 53, 2, 48, 49, 55, 55, 54, 61, 55, 49, 54, 49, 53, 54, 3, 54, 53, 49, 50, 53, 50, 51, 53, 51, 52, 53, 52, 0, 53, 53, 1, 2, 53, 0, 1, 37, 38, 36, 39, 40, 38, 40, 41, 38, 38, 41, 57, 35, 36, 32, 57, 41, 42, 42, 43, 57, 57, 56, 38, 38, 56, 36, 56, 55, 36, 60, 36, 55, 43, 44, 57, 44, 45, 57, 57, 45, 47, 57, 47, 56, 47, 45, 46, 47, 48, 56, 56, 48, 55], "vertices": [2, 32, 114.46, 20.09, 0.84209, 33, -32.35, 3.58, 0.15791, 2, 32, 114.49, 1.2, 0.59941, 33, -18.7, 16.64, 0.40059, 2, 32, 109.22, -23.57, 0.29721, 33, 2.87, 29.92, 0.70279, 3, 32, 95.67, -48.94, 0.09573, 33, 30.58, 37.61, 0.90401, 34, -91.65, 36.06, 0.00026, 3, 32, 79.39, -70.64, 0.0052, 33, 57.52, 40.8, 0.98477, 34, -64.77, 39.7, 0.01003, 2, 33, 81.74, 40.17, 0.9359, 34, -40.54, 39.48, 0.0641, 2, 33, 101.27, 36.91, 0.78998, 34, -20.96, 36.56, 0.21002, 2, 33, 119.63, 35.85, 0.56432, 34, -2.58, 35.81, 0.43568, 2, 33, 140.06, 32.99, 0.3233, 34, 17.89, 33.3, 0.6767, 2, 33, 148.28, 32.76, 0.19312, 34, 26.11, 33.21, 0.80688, 3, 33, 147.6, 24.16, 0.14839, 34, 25.57, 24.6, 0.84945, 35, -52.79, 33.64, 0.00216, 3, 33, 168.24, 21.23, 0.06415, 34, 46.27, 22.03, 0.84819, 35, -32.79, 27.73, 0.08766, 3, 33, 189.52, 18.05, 0.00717, 34, 67.59, 19.21, 0.70927, 35, -12.2, 21.48, 0.28356, 2, 34, 88.36, 15.32, 0.41519, 35, 7.65, 14.26, 0.58481, 2, 34, 105.78, 12.12, 0.16664, 35, 24.32, 8.27, 0.83336, 2, 34, 119.94, 10.74, 0.0292, 35, 38.06, 4.61, 0.9708, 1, 35, 50.36, 2.31, 1, 1, 35, 60.17, 1.84, 1, 1, 35, 62.49, -0.99, 1, 1, 35, 53.46, -5.81, 1, 3, 31, 126.23, -187.75, 0.00019, 34, 124.69, -2.65, 0.04586, 35, 40.58, -9.37, 0.95394, 3, 31, 121.22, -171.82, 0.00133, 34, 109.12, -8.67, 0.19494, 35, 24.23, -12.79, 0.80373, 5, 31, 116.51, -152.59, 0.00526, 32, -68.12, -142.87, 7e-05, 33, 211.58, -16.16, 0.00045, 34, 90.23, -14.62, 0.44231, 35, 4.63, -15.58, 0.55192, 3, 31, 114.66, -174.54, 0.00133, 34, 112.25, -15.05, 0.41501, 35, 26.29, -19.58, 0.58366, 3, 31, 111.58, -180.14, 0.00037, 34, 118.04, -17.76, 0.40716, 35, 31.56, -23.2, 0.59247, 3, 31, 108.37, -177.83, 0.00328, 34, 115.94, -21.11, 0.46828, 35, 28.94, -26.16, 0.52845, 3, 31, 101.44, -162.02, 0.0097, 34, 100.61, -29.04, 0.59073, 35, 12.53, -31.5, 0.39957, 3, 31, 95.26, -139.18, 0.01909, 34, 78.22, -36.69, 0.7537, 35, -10.81, -35.4, 0.2272, 4, 31, 88.97, -153.46, 0.02882, 33, 213.75, -43.62, 5e-05, 34, 92.88, -42.05, 0.86839, 35, 2.78, -43.07, 0.10274, 5, 31, 85.93, -151.77, 0.04636, 32, -90.7, -122.23, 2e-05, 33, 212.21, -46.74, 0.00151, 34, 91.39, -45.19, 0.9155, 35, 0.8, -45.93, 0.03661, 5, 31, 78.14, -129.84, 0.09577, 32, -82.23, -100.55, 0.00043, 33, 190.68, -55.56, 0.0091, 34, 70.01, -54.38, 0.87581, 35, -21.79, -51.52, 0.0189, 5, 31, 74.36, -110.22, 0.18167, 32, -72.25, -83.25, 0.00083, 33, 171.26, -60.27, 0.02213, 34, 50.68, -59.42, 0.78718, 35, -41.69, -53.35, 0.00819, 5, 31, 70.44, -89.95, 0.28796, 32, -61.93, -65.36, 0.00119, 33, 151.2, -65.15, 0.03788, 34, 30.7, -64.63, 0.6711, 35, -62.25, -55.24, 0.00187, 5, 31, 62.03, -104.92, 0.37081, 32, -78.09, -71.16, 0.00097, 33, 166.55, -72.84, 0.04759, 34, 46.18, -72.07, 0.5806, 35, -48.18, -65.1, 2e-05, 4, 31, 57.77, -102.54, 0.44104, 32, -79.76, -66.58, 0.00123, 33, 164.38, -77.21, 0.05209, 34, 44.08, -76.47, 0.50563, 4, 31, 53.16, -87.52, 0.52412, 32, -73.4, -52.2, 0.00152, 33, 149.59, -82.53, 0.04904, 34, 29.38, -82.04, 0.42532, 4, 31, 51.91, -70.17, 0.64296, 32, -62.99, -38.27, 0.0018, 33, 132.32, -84.6, 0.04092, 34, 12.15, -84.4, 0.31431, 4, 31, 34.31, -90.32, 0.76312, 32, -89.49, -41.98, 0.00114, 33, 153.28, -101.22, 0.02709, 34, 33.4, -100.67, 0.20866, 4, 31, 23.61, -68.02, 0.85066, 32, -82.97, -18.12, 0.00048, 33, 131.51, -112.97, 0.01486, 34, 11.83, -112.78, 0.134, 3, 31, 13.09, -76.73, 0.89793, 33, 140.72, -123.06, 0.00657, 34, 21.21, -122.72, 0.0955, 3, 31, 7.55, -70.42, 0.92342, 33, 134.68, -128.9, 0.00328, 34, 15.27, -128.66, 0.0733, 3, 31, 2.13, -60.77, 0.94507, 33, 125.3, -134.77, 0.00148, 34, 5.99, -134.69, 0.05345, 3, 31, -13.03, -47.49, 0.96634, 33, 112.75, -150.54, 0.00061, 34, -6.29, -150.67, 0.03305, 3, 31, -19.51, -27.72, 0.98402, 33, 93.32, -157.96, 0.00014, 34, -25.59, -158.42, 0.01584, 2, 31, -23.34, 0.83, 0.99482, 34, -53.84, -164.08, 0.00518, 2, 31, -14.33, 16.87, 0.99892, 34, -70.43, -156.13, 0.00108, 3, 31, -1.31, 25.67, 0.9874, 32, -40.47, 69.02, 0.01259, 34, -80.05, -143.7, 1e-05, 2, 31, 23.14, 12.35, 0.86697, 32, -30.7, 42.94, 0.13303, 2, 31, 51.83, 15.02, 0.63542, 32, -7.28, 26.17, 0.36458, 2, 31, 80.73, 39.64, 0.31795, 32, 30.69, 25.86, 0.68205, 2, 31, 103.14, 76.55, 0.10505, 32, 71.79, 39.09, 0.89495, 3, 31, 124.21, 90.5, 0.00327, 32, 96.86, 35.84, 0.99661, 33, -31.61, -20.02, 0.00012, 2, 32, 112.05, 29.51, 0.96319, 33, -37.51, -4.66, 0.03681, 4, 31, 132.55, 54.76, 0.03515, 32, 79.76, 3.36, 0.90183, 33, 3.7, -9.99, 0.06056, 34, -117.72, -12, 0.00245, 4, 31, 113.29, 18.8, 0.17425, 32, 41.65, -11.21, 0.71582, 33, 40.53, -27.52, 0.09989, 34, -80.59, -28.9, 0.01004, 4, 31, 83.77, -12.77, 0.4236, 32, -1.33, -15.75, 0.46462, 33, 73.47, -55.51, 0.09329, 34, -47.18, -56.32, 0.01848, 4, 31, 45.99, -24.27, 0.70974, 32, -37.41, 0.3, 0.20631, 33, 86.75, -92.7, 0.05893, 34, -33.27, -93.28, 0.02502, 4, 31, 6.48, -16.18, 0.85597, 32, -61.98, 32.28, 0.08867, 33, 80.56, -132.54, 0.02921, 34, -38.79, -133.22, 0.02615, 5, 31, 116.94, -125.35, 0.01745, 32, -49.96, -122.56, 0.00213, 33, 184.35, -17.01, 0.04637, 34, 63.02, -15.94, 0.68343, 35, -22.43, -12.46, 0.25062, 5, 31, 117.8, -96.32, 0.02815, 32, -30.31, -101.17, 0.01551, 33, 155.3, -17.55, 0.18907, 34, 33.99, -16.97, 0.69977, 35, -51.24, -8.75, 0.0675, 5, 31, 121.12, -63.68, 0.03449, 32, -6.42, -78.68, 0.03536, 33, 122.54, -15.77, 0.40163, 34, 1.2, -15.76, 0.5273, 35, -83.4, -2.22, 0.00122, 4, 31, 124.62, -27.5, 0.03551, 32, 19.91, -53.62, 0.04998, 33, 86.24, -14, 0.53379, 34, -35.13, -14.6, 0.38073, 2, 33, 122.47, 22.84, 0.22865, 34, 0.48, 22.85, 0.77135], "hull": 53, "edges": [0, 104, 0, 2, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 44, 116, 116, 118, 118, 120, 120, 122, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 40, 42, 42, 44, 60, 62, 62, 64, 10, 12, 12, 14, 2, 4, 20, 124], "width": 244, "height": 267}}, "b1": {"b1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-243.41, -242.91, -243.41, 319.09, 330.59, 319.09, 330.59, -242.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 168, "height": 172}}, "a20": {"a20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-96.2, -83.26, -92.41, 102.7, 89.55, 98.99, 85.76, -86.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 91}}, "a21": {"a21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-93.38, -74.95, -87.96, 110.97, 93.96, 105.67, 88.54, -80.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 91}}, "a22": {"a22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-82.93, -77.74, -86.41, 82.23, 69.55, 85.62, 73.03, -74.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 78}}, "a23": {"a22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-82.93, -77.74, -86.41, 82.23, 69.55, 85.62, 73.03, -74.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 78}}, "a24": {"a21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-93.38, -74.95, -87.96, 110.97, 93.96, 105.67, 88.54, -80.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 91}}, "a25": {"a20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-96.2, -83.26, -92.41, 102.7, 89.55, 98.99, 85.76, -86.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 91}}, "a0": {"a0": {"type": "mesh", "uvs": [0.43121, 0, 0.54843, 0.03946, 0.67216, 0.07493, 0.82276, 0.20317, 0.75434, 0.34137, 0.99584, 0.47807, 0.97807, 0.65783, 0.5167, 0.99747, 0.16261, 0.91924, 0.02186, 0.60986, 0.02154, 0.38772, 0.209, 0.1849, 0.30424, 0.32684], "triangles": [12, 11, 0, 12, 0, 1, 4, 2, 3, 10, 11, 12, 9, 10, 12, 6, 4, 5, 8, 9, 12, 12, 4, 7, 1, 4, 12, 2, 4, 1, 7, 4, 6, 8, 12, 7], "vertices": [2, 24, -1.53, 12.49, 0.34897, 23, 51.13, 11.63, 0.65103, 2, 24, 3.27, 15.22, 0.53021, 23, 55.02, 15.54, 0.46979, 2, 24, 8.15, 18.28, 0.689, 23, 58.91, 19.78, 0.311, 2, 24, 16.62, 19.6, 0.80627, 23, 66.73, 23.32, 0.19373, 2, 24, 18.63, 13.47, 0.90417, 23, 70.3, 17.95, 0.09583, 2, 24, 30.17, 17.54, 0.99704, 23, 80.33, 24.94, 0.00296, 2, 24, 34.98, 11.9, 0.99999, 23, 86.47, 20.79, 1e-05, 1, 24, 30.88, -12.76, 1, 2, 24, 17.61, -22.15, 0.99328, 23, 78.8, -16.66, 0.00672, 2, 24, 4.04, -18.06, 0.88949, 23, 64.63, -16.34, 0.11051, 2, 24, -2.6, -11.83, 0.69495, 23, 56.57, -12.09, 0.30505, 1, 23, 53.16, -0.75, 1, 1, 24, 4.31, -0.86, 1], "hull": 12, "edges": [0, 22, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 2, 2, 4, 24, 2, 8, 10], "width": 45, "height": 41}}, "a1": {"a1": {"type": "mesh", "uvs": [0.21737, 0.00967, 0.49886, 0.05955, 0.62025, 0.12959, 0.74082, 0.27153, 0.91139, 0.59667, 0.9009, 0.68082, 0.97721, 0.70329, 0.98479, 0.78513, 0.82715, 0.87308, 0.70313, 0.99882, 0.67286, 1, 0.58768, 0.9497, 0.57397, 0.91478, 0.31017, 0.78014, 0.28533, 0.69489, 0.1587, 0.58404, 0.09491, 0.47198, 0.06351, 0.36214, 0.01561, 0.29192, 0.01536, 0.18062, 0.10375, 0.02645, 0.72591, 0.76866, 0.23589, 0.18369, 0.47093, 0.47286], "triangles": [2, 22, 1, 20, 0, 22, 22, 0, 1, 10, 11, 9, 9, 11, 12, 12, 21, 8, 12, 13, 21, 8, 21, 5, 13, 14, 21, 14, 23, 21, 5, 21, 4, 14, 15, 23, 4, 21, 23, 23, 3, 4, 15, 16, 23, 23, 17, 22, 23, 2, 3, 2, 23, 22, 23, 16, 17, 17, 18, 22, 18, 19, 22, 19, 20, 22, 8, 9, 12, 7, 5, 6, 7, 8, 5], "vertices": [2, 23, -15.59, -4.81, 0.25516, 22, 61.04, -16.31, 0.74484, 1, 22, 51.11, -1.19, 1, 2, 23, 3.9, 13.6, 0.38473, 22, 49.31, 7.79, 0.61527, 2, 23, 16.49, 15.54, 0.88251, 22, 51.22, 20.38, 0.11749, 2, 24, -9.47, 17.24, 0.09594, 23, 42.21, 14.1, 0.90406, 2, 24, -5.51, 12.61, 0.31407, 23, 47.26, 10.69, 0.68593, 2, 24, -1.04, 15, 0.58688, 23, 50.93, 14.18, 0.41312, 2, 24, 3.58, 11.3, 0.70388, 23, 56.37, 11.85, 0.29612, 1, 24, 1.38, -0.27, 1, 2, 24, 2.61, -12.16, 0.40867, 23, 61.68, -11.03, 0.59133, 2, 24, 1.36, -13.61, 0.39604, 23, 60.86, -12.75, 0.60396, 2, 24, -4.95, -15.03, 0.26862, 23, 55.15, -15.81, 0.73138, 2, 24, -7.38, -13.93, 0.19609, 23, 52.53, -15.39, 0.80391, 2, 24, -25.83, -19.38, 0.00361, 23, 36.19, -25.56, 0.99639, 2, 24, -31.37, -16.31, 0.00027, 23, 30.03, -24.08, 0.99973, 2, 23, 19.25, -27.4, 0.99868, 22, 93.02, 10.17, 0.00132, 2, 23, 10.24, -27.18, 0.97114, 22, 90.12, 1.63, 0.02886, 2, 23, 2.32, -25.24, 0.874, 22, 85.89, -5.34, 0.126, 2, 23, -3.56, -25.54, 0.77143, 22, 84.42, -11.04, 0.22857, 2, 23, -10.65, -21.81, 0.62446, 22, 78.74, -16.69, 0.37554, 2, 23, -17.86, -11.7, 0.36704, 22, 66.94, -20.55, 0.63296, 1, 23, 47.7, -2.02, 1, 2, 23, -3.96, -9.63, 0.598, 22, 69.12, -6.67, 0.402, 1, 23, 21.36, -6.27, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 10, 12, 6, 8, 8, 10, 10, 42, 40, 44, 44, 46], "width": 63, "height": 72}}, "a2": {"a2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [45.42, -188.4, -137.79, 1.68, 46.53, 179.33, 229.74, -10.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 264, "height": 256}}, "a3": {"a3": {"type": "mesh", "uvs": [0.70324, 1e-05, 0.81434, 0.0674, 0.94514, 0.24841, 0.99177, 0.46516, 1, 0.68334, 0.94732, 0.8168, 0.73034, 0.9028, 0.52146, 0.98559, 0.24581, 1, 0.04386, 0.93131, 0, 0.88778, 0.03579, 0.65535, 0.16179, 0.3678, 0.4042, 0.11365, 0.6338, 0.00717, 0.77843, 0.41371, 0.17398, 0.76791, 0.42385, 0.60443], "triangles": [15, 0, 1, 15, 1, 2, 15, 2, 3, 15, 17, 13, 12, 13, 17, 16, 11, 12, 17, 16, 12, 14, 0, 15, 15, 13, 14, 4, 5, 15, 4, 15, 3, 10, 11, 16, 6, 17, 15, 6, 15, 5, 9, 10, 16, 7, 17, 6, 8, 16, 17, 7, 8, 17, 9, 16, 8], "vertices": [1, 22, -10.09, -20.81, 1, 1, 22, -12.96, -11.31, 1, 1, 22, -11.36, 4.82, 1, 1, 22, -3.33, 17.97, 1, 1, 22, 6.91, 29.02, 1, 2, 23, 17.67, 52.43, 0.00012, 22, 16.38, 32.55, 0.99988, 2, 23, 14.91, 34.5, 0.01165, 22, 32.66, 24.55, 0.98835, 2, 23, 12.25, 17.24, 0.106, 22, 48.33, 16.85, 0.894, 1, 23, 2.95, -2.48, 1, 2, 23, -8.69, -14.37, 0.0302, 22, 72.23, -12.59, 0.9698, 2, 23, -12.97, -16.03, 0.0126, 22, 72.53, -17.17, 0.9874, 1, 22, 59.15, -26.45, 1, 1, 22, 38.05, -33.34, 1, 1, 22, 12.11, -32.08, 1, 1, 22, -5.87, -24.36, 1, 1, 22, 6.01, 3.5, 1, 1, 22, 56.98, -13.23, 1, 1, 22, 35.08, -7.14, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 10, 12, 12, 14, 4, 30, 30, 0, 30, 10, 18, 32, 32, 34], "width": 79, "height": 69}}, "a4": {"a4": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 7, 2, 3, 7, 2, 7, 8, 1, 2, 8, 1, 8, 9, 0, 1, 9], "vertices": [3, 7, 4.87, -95.25, 0.49144, 8, -2.7, -83.24, 0.04456, 9, -42.86, -81.79, 0.464, 3, 7, 0.08, -53.27, 0.66823, 8, -7.48, -41.27, 0.13444, 9, -47.64, -39.81, 0.19733, 3, 7, -4.7, -11.29, 0.71083, 8, -12.26, 0.71, 0.23851, 9, -52.42, 2.17, 0.05067, 2, 7, -9.48, 30.69, 0.77333, 8, -17.04, 42.69, 0.22667, 2, 7, -14.26, 72.66, 0.79733, 8, -21.82, 84.67, 0.20267, 2, 7, 72.18, 82.51, 0.808, 8, 64.62, 94.52, 0.192, 2, 7, 76.96, 40.53, 0.784, 8, 69.4, 52.54, 0.216, 3, 7, 81.74, -1.45, 0.72149, 8, 74.18, 10.56, 0.22784, 9, 34.02, 12.01, 0.05067, 3, 7, 86.53, -43.43, 0.66823, 8, 78.96, -31.42, 0.13444, 9, 38.8, -29.97, 0.19733, 3, 7, 91.31, -85.4, 0.49144, 8, 83.74, -73.4, 0.04456, 9, 43.58, -71.94, 0.464], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 169, "height": 87}}, "a5": {"a5": {"type": "mesh", "uvs": [0.03355, 0.00245, 0.04618, 0.06395, 0.08552, 0.09753, 0.14049, 0.12351, 0.19201, 0.13868, 0.22187, 0.11607, 0.27952, 0.13859, 0.33023, 0.17286, 0.37905, 0.21226, 0.42486, 0.2606, 0.50201, 0.24137, 0.58335, 0.23032, 0.67343, 0.23704, 0.74457, 0.26641, 0.77186, 0.23512, 0.78376, 0.18799, 0.78666, 0.13439, 0.79729, 0.10232, 0.78812, 0.05591, 0.76147, 0.01931, 0.79722, 0.01594, 0.82522, 0.04404, 0.85625, 0.07585, 0.885, 0.11674, 0.91213, 0.1618, 0.93526, 0.21298, 0.9507, 0.26233, 0.95949, 0.31176, 0.9948, 0.3126, 0.99084, 0.43944, 0.98243, 0.59267, 0.89004, 0.71742, 0.8849, 0.79221, 0.95116, 0.8526, 0.96997, 0.94381, 0.94455, 0.99378, 0.88525, 1, 0.79985, 0.9281, 0.76358, 0.98641, 0.73826, 0.98847, 0.60248, 0.99952, 0.46601, 0.92759, 0.33644, 0.8593, 0.26946, 0.92517, 0.19266, 0.93926, 0.15944, 0.89403, 0.16765, 0.82814, 0.19329, 0.78491, 0.24407, 0.73389, 0.21312, 0.64529, 0.16544, 0.5296, 0.20252, 0.50512, 0.18436, 0.44323, 0.16485, 0.38494, 0.11006, 0.34407, 0.06341, 0.3031, 0.14597, 0.29744, 0.10421, 0.24836, 0.06802, 0.1974, 0.03711, 0.13994, 0.02151, 0.09447, 0.01296, 0.05581, 0.01481, 0.00593, 0.68871, 0.38848, 0.71789, 0.55889, 0.70677, 0.72698, 0.74428, 0.83131, 0.74567, 0.91245, 0.56937, 0.52391, 0.56236, 0.64476, 0.55681, 0.73866, 0.57626, 0.8314, 0.61932, 0.91023, 0.39221, 0.49505, 0.39528, 0.63324, 0.41067, 0.73788, 0.43806, 0.83239, 0.81616, 0.40836, 0.82728, 0.51965, 0.81477, 0.63905, 0.79671, 0.72367, 0.80505, 0.78975, 0.8245, 0.83148, 0.33845, 0.7852, 0.31483, 0.72261, 0.31882, 0.40098, 0.2688, 0.32447, 0.22712, 0.25607, 0.17155, 0.19115, 0.53466, 0.3948, 0.27389, 0.61124, 0.81138, 0.32322, 0.83775, 0.26122, 0.84853, 0.19622, 0.84254, 0.13322, 0.71216, 0.64542], "triangles": [93, 14, 15, 92, 93, 25, 94, 15, 16, 15, 94, 93, 93, 24, 25, 24, 94, 23, 24, 93, 94, 16, 17, 94, 94, 22, 23, 94, 17, 22, 22, 18, 21, 22, 17, 18, 18, 20, 21, 18, 19, 20, 3, 58, 59, 3, 59, 2, 59, 60, 2, 60, 1, 2, 60, 61, 1, 1, 61, 0, 61, 62, 0, 8, 87, 7, 56, 57, 87, 57, 88, 87, 87, 88, 6, 6, 88, 4, 87, 6, 7, 6, 4, 5, 57, 58, 88, 58, 3, 88, 88, 3, 4, 54, 55, 56, 56, 87, 86, 92, 14, 93, 92, 25, 26, 54, 56, 53, 92, 26, 91, 27, 77, 26, 29, 27, 28, 26, 77, 91, 91, 13, 92, 53, 56, 86, 86, 8, 9, 30, 78, 29, 64, 77, 78, 64, 63, 77, 89, 11, 63, 89, 10, 11, 51, 85, 73, 51, 52, 85, 73, 85, 89, 9, 10, 89, 34, 35, 36, 40, 72, 39, 40, 41, 72, 34, 36, 37, 33, 37, 82, 37, 33, 34, 39, 67, 38, 39, 72, 67, 38, 67, 37, 44, 45, 43, 82, 32, 33, 37, 67, 82, 42, 76, 41, 41, 71, 72, 41, 76, 71, 42, 43, 46, 72, 66, 67, 67, 66, 82, 72, 71, 66, 43, 45, 46, 46, 47, 42, 42, 47, 83, 83, 47, 48, 42, 83, 76, 83, 48, 84, 83, 75, 76, 76, 70, 71, 76, 75, 70, 66, 81, 82, 82, 81, 32, 71, 65, 66, 71, 70, 65, 81, 65, 80, 81, 66, 65, 32, 81, 31, 81, 80, 31, 83, 84, 75, 70, 69, 65, 70, 75, 69, 84, 74, 75, 75, 74, 69, 48, 49, 84, 65, 95, 80, 65, 69, 95, 80, 79, 31, 80, 95, 79, 49, 90, 84, 84, 90, 74, 31, 79, 30, 30, 79, 78, 95, 64, 79, 95, 69, 64, 49, 50, 90, 69, 68, 64, 69, 74, 68, 79, 64, 78, 90, 73, 74, 74, 73, 68, 50, 51, 90, 90, 51, 73, 68, 63, 64, 73, 89, 68, 68, 89, 63, 52, 53, 85, 53, 86, 85, 85, 9, 89, 85, 86, 9, 27, 29, 77, 78, 77, 29, 63, 91, 77, 11, 12, 63, 63, 13, 91, 63, 12, 13, 92, 13, 14, 8, 86, 87], "vertices": [2, 27, 24.16, -4.19, 0.9, 9, 75.7, 93.36, 0.1, 3, 7, 114.01, 77.16, 0, 27, 14.89, -1.03, 0.9, 9, 66.29, 90.62, 0.1, 4, 7, 109.36, 71.44, 0, 26, 31.53, -4.89, 0.18796, 27, 7.79, -3.04, 0.71204, 9, 61.63, 84.9, 0.1, 5, 7, 106.12, 63.83, 0, 26, 23.63, -7.38, 0.48146, 27, 0.77, -7.42, 0.41853, 28, 29.18, 93.37, 1e-05, 9, 58.4, 77.29, 0.1, 5, 7, 104.52, 56.85, 0, 26, 17.29, -10.68, 0.78139, 27, -4.54, -12.21, 0.11853, 28, 26.45, 86.75, 8e-05, 9, 56.79, 70.31, 0.1, 5, 7, 108.49, 53.37, 0.00013, 26, 17.21, -15.97, 0.89185, 27, -3.29, -17.34, 0.00649, 28, 29.8, 82.66, 0.00153, 9, 60.76, 66.83, 0.1, 4, 7, 105.83, 45.46, 0.0037, 26, 9.48, -19.07, 0.88821, 28, 25.88, 75.3, 0.00809, 9, 58.1, 58.92, 0.1, 4, 7, 101.24, 38.25, 0.0864, 26, 1.01, -20.26, 0.7909, 28, 20.16, 68.95, 0.02271, 9, 53.51, 51.71, 0.1, 4, 7, 95.81, 31.2, 0.55187, 26, -7.88, -20.71, 0.33373, 28, 13.65, 62.88, 0.0144, 9, 48.09, 44.66, 0.1, 2, 7, 88.95, 24.38, 0.9, 9, 41.23, 37.84, 0.1, 2, 7, 93.1, 14.68, 0.9, 9, 45.37, 28.14, 0.1, 1, 7, 96.03, 4.29, 1, 2, 7, 96.31, -7.56, 0.9, 9, 48.59, 5.9, 0.1, 2, 7, 92.79, -17.34, 0.9, 9, 45.06, -3.88, 0.1, 5, 7, 98.07, -20.33, 0.70041, 26, -45.61, -55.88, 0.00205, 29, -9.75, 17.18, 0.00543, 28, 7.41, 11.68, 0.19212, 9, 50.35, -6.88, 0.1, 5, 7, 105.6, -21.05, 0.11241, 26, -41.26, -62.07, 0.005, 29, -3.88, 12.42, 0.18102, 28, 14.72, 9.74, 0.60157, 9, 57.88, -7.59, 0.1, 5, 7, 114, -20.47, 0.0003, 26, -35.37, -68.09, 0.00071, 29, 3.43, 8.24, 0.46515, 28, 23.1, 8.92, 0.43383, 9, 66.28, -7.01, 0.1, 3, 29, 7.28, 4.7, 0.75187, 28, 28.06, 7.27, 0.14813, 9, 71.44, -7.83, 0.1, 3, 29, 14.31, 2.45, 0.8841, 28, 35.4, 8.09, 0.0159, 9, 78.54, -5.81, 0.1, 3, 29, 21.02, 2.93, 0.89997, 28, 41.32, 11.28, 3e-05, 9, 83.86, -1.69, 0.1, 2, 29, 19.35, -1.48, 0.9, 9, 84.91, -6.28, 0.1, 2, 29, 13.75, -2.73, 0.9, 9, 80.95, -10.43, 0.1, 3, 29, 7.45, -4.07, 0.77782, 28, 31.81, -0.66, 0.12218, 9, 76.44, -15.03, 0.1, 3, 29, 0.02, -4.49, 0.47782, 28, 25.21, -4.09, 0.42218, 9, 70.49, -19.5, 0.1, 3, 29, -7.9, -4.42, 0.17782, 28, 17.96, -7.27, 0.72218, 9, 63.86, -23.83, 0.1, 3, 7, 103.95, -41.21, 0.11386, 28, 9.78, -9.88, 0.78614, 9, 56.22, -27.75, 0.1, 3, 7, 96.48, -44.1, 0.67306, 28, 1.93, -11.5, 0.22694, 9, 48.75, -30.64, 0.1, 2, 7, 88.9, -46.12, 0.9, 9, 41.17, -32.66, 0.1, 2, 7, 89.29, -50.73, 0.9, 9, 41.57, -37.27, 0.1, 2, 7, 69.45, -52.47, 0.9, 9, 21.72, -39.01, 0.1, 2, 7, 45.42, -54.1, 0.9, 9, -2.31, -40.64, 0.1, 2, 7, 24.59, -44.29, 0.9, 9, -23.14, -30.83, 0.1, 2, 7, 12.85, -44.95, 0.9, 9, -34.88, -31.49, 0.1, 2, 7, 4.41, -54.64, 0.9, 9, -43.32, -41.18, 0.1, 2, 7, -9.54, -58.71, 0.9, 9, -57.27, -45.25, 0.1, 2, 7, -17.71, -56.29, 0.9, 9, -65.44, -42.83, 0.1, 2, 7, -19.56, -48.68, 0.9, 9, -67.29, -35.22, 0.1, 3, 7, -9.61, -36.29, 0.82594, 9, -57.34, -22.83, 0.11246, 8, -17.18, -24.29, 0.0616, 3, 7, -19.24, -32.61, 0.7848, 9, -66.97, -19.15, 0.0872, 8, -26.81, -20.6, 0.128, 3, 7, -19.94, -29.35, 0.7992, 9, -67.67, -15.89, 0.0888, 8, -27.51, -17.34, 0.112, 3, 7, -23.68, -11.87, 0.7992, 9, -71.4, 1.59, 0.0888, 8, -31.24, 0.13, 0.112, 3, 7, -14.48, 7.17, 0.8064, 9, -62.21, 20.63, 0.0896, 8, -22.05, 19.18, 0.104, 2, 7, -5.75, 25.25, 0.9, 9, -53.48, 38.71, 0.1, 2, 7, -17.02, 32.8, 0.9, 9, -64.74, 46.26, 0.1, 2, 7, -20.35, 42.54, 0.9, 9, -68.08, 56, 0.1, 2, 7, -13.79, 47.67, 0.9, 9, -61.52, 61.13, 0.1, 2, 7, -3.39, 47.77, 0.9, 9, -51.12, 61.23, 0.1, 2, 7, 3.73, 45.2, 0.9, 9, -43.99, 58.66, 0.1, 2, 7, 12.44, 39.5, 0.9, 9, -35.28, 52.96, 0.1, 2, 7, 25.81, 45.1, 0.9, 9, -21.92, 58.56, 0.1, 2, 7, 43.14, 53.36, 0.9, 9, -4.58, 66.82, 0.1, 2, 7, 47.51, 48.97, 0.9, 9, -0.21, 62.43, 0.1, 2, 7, 56.9, 52.43, 0.9, 9, 9.17, 65.89, 0.1, 2, 7, 65.7, 56.01, 0.9, 9, 17.98, 69.47, 0.1, 4, 7, 71.27, 63.87, 0.56617, 26, 1.04, 19.16, 0.33382, 28, -5.19, 99.14, 2e-05, 9, 23.54, 77.33, 0.1, 4, 7, 76.96, 70.67, 0.55139, 26, 9.91, 19.24, 0.34859, 28, 1.55, 104.91, 1e-05, 9, 29.24, 84.13, 0.1, 3, 7, 79.07, 60.02, 0.51895, 26, 3.18, 10.73, 0.38105, 9, 31.35, 73.48, 0.1, 4, 7, 86.11, 66.33, 0.08363, 26, 12.55, 9.47, 0.78774, 27, -14.18, 6.12, 0.02864, 9, 38.38, 79.79, 0.1, 3, 26, 21.63, 7.48, 0.57448, 27, -4.89, 6.46, 0.32552, 9, 45.8, 85.41, 0.1, 3, 26, 30.99, 4.29, 0.27448, 27, 4.97, 5.71, 0.62552, 9, 54.3, 90.45, 0.1, 3, 26, 37.6, 0.91, 0.00312, 27, 12.22, 4.1, 0.89688, 9, 61.16, 93.29, 0.1, 2, 27, 18.09, 2.18, 0.9, 9, 67.07, 95.09, 0.1, 2, 27, 24.85, -1.77, 0.9, 9, 74.87, 95.73, 0.1, 2, 7, 72.92, -12.24, 0.72, 8, 65.35, -0.23, 0.28, 2, 7, 46.77, -19.06, 0.72, 8, 39.2, -7.06, 0.28, 2, 7, 20.38, -20.6, 0.72, 8, 12.82, -8.6, 0.28, 2, 7, 4.66, -27.34, 0.72, 8, -2.9, -15.33, 0.28, 2, 7, -7.97, -28.96, 0.816, 8, -15.54, -16.96, 0.184, 2, 7, 50.02, 0.89, 0.688, 8, 42.46, 12.89, 0.312, 2, 7, 31.07, -0.35, 0.688, 8, 23.5, 11.66, 0.312, 2, 7, 16.34, -1.29, 0.704, 8, 8.77, 10.71, 0.296, 2, 7, 2.16, -5.47, 0.712, 8, -5.41, 6.54, 0.288, 2, 7, -9.5, -12.48, 0.832, 8, -17.06, -0.47, 0.168, 2, 7, 51.9, 24.46, 0.752, 8, 44.33, 36.47, 0.248, 2, 7, 30.39, 21.61, 0.824, 8, 22.82, 33.61, 0.176, 2, 7, 14.29, 17.74, 0.824, 8, 6.73, 29.75, 0.176, 2, 7, -0.05, 12.5, 0.824, 8, -7.61, 24.5, 0.176, 2, 7, 71.7, -29.18, 0.72, 8, 64.14, -17.17, 0.28, 2, 7, 54.51, -32.6, 0.72, 8, 46.95, -20.6, 0.28, 2, 7, 35.7, -33.1, 0.744, 8, 28.13, -21.09, 0.256, 2, 7, 22.23, -32.25, 0.792, 8, 14.67, -20.24, 0.208, 2, 7, 12.05, -34.51, 0.872, 8, 4.48, -22.5, 0.128, 1, 7, 5.83, -37.78, 1, 2, 7, 5.84, 26.3, 0.952, 8, -1.73, 38.31, 0.048, 2, 7, 15.25, 30.49, 0.952, 8, 7.69, 42.5, 0.048, 2, 7, 65.48, 35.69, 0.784, 8, 57.92, 47.69, 0.216, 2, 7, 76.68, 43.55, 0.832, 8, 69.11, 55.56, 0.168, 4, 7, 86.73, 50.19, 0.56917, 26, 0.68, -1.47, 0.3586, 28, 7.81, 83.11, 0.00023, 8, 79.16, 62.2, 0.072, 4, 7, 96.03, 58.58, 0, 26, 13.09, -3.1, 0.95939, 28, 18.37, 89.85, 0.00061, 8, 88.47, 70.59, 0.04, 2, 7, 69.65, 7.7, 0.688, 8, 62.08, 19.71, 0.312, 2, 7, 32.02, 37.8, 0.952, 8, 24.45, 49.8, 0.048, 2, 7, 84.92, -27.04, 0.872, 8, 77.35, -15.04, 0.128, 4, 7, 94.98, -29.37, 0.79417, 26, -54.49, -59.4, 0.00054, 29, -17.34, 11.37, 0.00015, 28, 2.87, 3.27, 0.20514, 4, 7, 105.28, -29.62, 0.09283, 26, -48, -67.4, 0.00151, 29, -8.9, 5.46, 0.1515, 28, 12.99, 1.33, 0.75416, 4, 7, 115.02, -27.72, 4e-05, 26, -40.23, -73.57, 0.00015, 29, 0.26, 1.64, 0.22724, 28, 22.91, 1.6, 0.77257, 2, 7, 33.18, -19.86, 0.72, 8, 25.62, -7.85, 0.28], "hull": 63, "edges": [0, 124, 0, 2, 2, 4, 8, 10, 24, 26, 38, 40, 54, 56, 60, 62, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 100, 102, 106, 108, 108, 110, 110, 112, 120, 122, 122, 124, 118, 120, 116, 118, 112, 114, 114, 116, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60, 20, 22, 22, 24, 22, 126, 126, 128, 130, 132, 132, 134, 76, 78, 78, 80, 134, 78, 136, 138, 138, 140, 140, 142, 142, 144, 80, 82, 82, 84, 136, 146, 146, 148, 148, 150, 150, 152, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 84, 166, 166, 168, 96, 98, 98, 100, 146, 170, 170, 172, 172, 174, 174, 176, 102, 104, 104, 106, 34, 36, 36, 38, 136, 178, 100, 180, 62, 64, 64, 66, 154, 182, 182, 184, 184, 186, 186, 188, 128, 190, 190, 130], "width": 131, "height": 157}}, "a6": {"a6": {"type": "mesh", "uvs": [0.44243, 0.00131, 0.49907, 0.00755, 0.57563, 0.05488, 0.6396, 0.12434, 0.6263, 0.1619, 0.64293, 0.19845, 0.72425, 0.22697, 0.80734, 0.25552, 0.88532, 0.3016, 0.92903, 0.40407, 0.92417, 0.48042, 0.84511, 0.55744, 0.8029, 0.62203, 0.86799, 0.63461, 0.95728, 0.72829, 0.95774, 0.78296, 0.93766, 0.81578, 0.85243, 0.84951, 0.90851, 0.89216, 0.98459, 0.95001, 0.9891, 0.98213, 0.90258, 0.99737, 0.84699, 0.99649, 0.77102, 0.96105, 0.72146, 0.92488, 0.63497, 0.92378, 0.54608, 0.86852, 0.49081, 0.91873, 0.42499, 0.96432, 0.33609, 1, 0.22851, 1, 0.09504, 0.95619, 0.08538, 0.92727, 0.14588, 0.88705, 0.21037, 0.83935, 0.13729, 0.79357, 0.13299, 0.72205, 0.18586, 0.61985, 0.06196, 0.49654, 0, 0.34694, 0.0075, 0.2742, 0.04555, 0.21744, 0.14862, 0.19023, 0.25716, 0.17444, 0.32136, 0.14697, 0.38784, 0.11391, 0.39844, 0.07331, 0.40383, 0.02677, 0.53448, 0.12778, 0.58408, 0.24236, 0.639, 0.36708, 0.66026, 0.49036, 0.67115, 0.6035, 0.66229, 0.72967, 0.66229, 0.82539, 0.16913, 0.32079, 0.26284, 0.46807, 0.32773, 0.60322, 0.34931, 0.71695, 0.35793, 0.82943, 0.28888, 0.9293, 0.79494, 0.39133, 0.77587, 0.50659, 0.7533, 0.61104, 0.74403, 0.70585, 0.36226, 0.28236, 0.44729, 0.41434, 0.49512, 0.55212, 0.51992, 0.65799, 0.53586, 0.76966, 0.86217, 0.75853], "triangles": [48, 1, 2, 48, 2, 3, 0, 1, 48, 47, 0, 48, 4, 48, 3, 46, 47, 48, 45, 46, 48, 49, 48, 4, 70, 13, 14, 70, 64, 13, 70, 14, 15, 58, 35, 36, 16, 70, 15, 54, 53, 64, 69, 53, 54, 59, 58, 69, 34, 58, 59, 34, 35, 58, 70, 54, 64, 17, 70, 16, 17, 54, 70, 26, 69, 54, 59, 69, 26, 27, 59, 26, 25, 26, 54, 24, 54, 17, 25, 54, 24, 60, 34, 59, 28, 60, 59, 33, 34, 60, 31, 32, 33, 23, 24, 17, 23, 17, 18, 27, 28, 59, 22, 23, 18, 21, 22, 18, 19, 21, 18, 20, 21, 19, 30, 33, 60, 31, 33, 30, 29, 60, 28, 30, 60, 29, 36, 37, 58, 64, 63, 12, 13, 64, 12, 48, 65, 45, 49, 4, 5, 65, 44, 45, 43, 44, 65, 55, 43, 65, 6, 50, 49, 6, 49, 5, 7, 50, 6, 61, 7, 8, 61, 50, 7, 61, 8, 9, 65, 48, 49, 66, 49, 50, 66, 65, 49, 56, 55, 65, 56, 65, 66, 10, 61, 9, 51, 50, 61, 66, 50, 51, 38, 55, 56, 62, 51, 61, 62, 61, 10, 67, 66, 51, 56, 66, 67, 11, 62, 10, 57, 56, 67, 52, 51, 62, 63, 52, 62, 67, 51, 52, 11, 63, 62, 37, 38, 56, 37, 56, 57, 12, 63, 11, 68, 67, 52, 57, 67, 68, 64, 52, 63, 58, 57, 68, 53, 68, 52, 58, 37, 57, 53, 52, 64, 69, 68, 53, 58, 68, 69, 41, 42, 55, 40, 41, 55, 39, 40, 55, 55, 42, 43, 38, 39, 55], "vertices": [3, 5, 114.88, 9.01, 0.1143, 6, 39.54, 13.46, 0.7857, 11, 74.71, 19.82, 0.1, 3, 5, 113.23, 1.18, 0.0258, 6, 38.71, 5.5, 0.8742, 11, 73.07, 11.99, 0.1, 3, 5, 104.38, -8.92, 0.0544, 6, 30.95, -5.46, 0.8456, 11, 64.21, 1.9, 0.1, 3, 5, 91.88, -16.98, 0.2088, 6, 19.35, -14.77, 0.6912, 11, 51.71, -6.17, 0.1, 3, 5, 85.61, -14.65, 0.4632, 6, 12.87, -13.11, 0.4368, 11, 45.44, -3.84, 0.1, 3, 5, 79.2, -16.51, 0.7088, 6, 6.69, -15.62, 0.1912, 11, 39.04, -5.7, 0.1, 3, 5, 73.5, -27.51, 0.8544, 6, 2.16, -27.15, 0.0456, 11, 33.34, -16.7, 0.1, 2, 5, 67.78, -38.75, 0.9, 11, 27.62, -27.94, 0.1, 2, 5, 59.13, -49.07, 0.9, 11, 18.96, -38.25, 0.1, 2, 5, 41.2, -53.89, 0.9, 11, 1.04, -43.07, 0.1, 2, 5, 28.23, -52.25, 0.9, 11, -11.94, -41.44, 0.1, 2, 5, 15.91, -40.25, 0.9, 11, -24.26, -29.43, 0.1, 3, 5, 5.32, -33.54, 0.8, 4, 36.65, -33.46, 0.1, 11, -34.84, -22.73, 0.1, 3, 5, 2.51, -42.47, 0.6, 4, 33.97, -42.43, 0.3, 11, -37.65, -31.66, 0.1, 3, 5, -14.38, -53.77, 0.3, 4, 17.26, -53.99, 0.6, 11, -54.55, -42.96, 0.1, 3, 5, -23.71, -53.15, 0.075, 4, 7.92, -53.51, 0.825, 11, -63.87, -42.34, 0.1, 2, 4, 2.48, -50.38, 0.9, 11, -69.27, -39.12, 0.1, 2, 4, -2.59, -38.14, 0.9, 11, -74.14, -26.8, 0.1, 2, 4, -10.32, -45.56, 0.9, 11, -81.99, -34.09, 0.1, 2, 4, -20.81, -55.62, 0.9, 11, -92.64, -43.99, 0.1, 2, 4, -26.33, -55.93, 0.9, 11, -98.16, -44.22, 0.1, 2, 4, -28.24, -43.69, 0.9, 11, -99.87, -31.95, 0.1, 1, 4, -27.64, -35.93, 1, 2, 4, -20.97, -25.66, 0.9, 11, -92.33, -14.03, 0.1, 2, 4, -14.4, -19.09, 0.9, 11, -85.65, -7.57, 0.1, 2, 4, -13.51, -7.01, 0.9, 11, -84.58, 4.49, 0.1, 2, 4, -3.36, 4.87, 0.9, 11, -74.24, 16.21, 0.1, 2, 4, -11.49, 13.09, 0.9, 11, -82.24, 24.56, 0.1, 2, 4, -18.74, 22.73, 0.9, 11, -89.34, 34.32, 0.1, 2, 4, -24.12, 35.51, 0.9, 11, -94.51, 47.18, 0.1, 2, 4, -23.25, 50.55, 0.9, 11, -93.41, 62.2, 0.1, 2, 4, -14.69, 68.77, 0.9, 11, -84.57, 80.29, 0.1, 2, 4, -9.68, 69.84, 0.9, 11, -79.54, 81.27, 0.1, 2, 4, -3.3, 60.98, 0.9, 11, -73.3, 72.32, 0.1, 2, 4, 4.32, 51.5, 0.9, 11, -65.83, 62.72, 0.1, 3, 5, -17.11, 61.54, 0.1, 4, 12.73, 61.26, 0.8, 11, -57.27, 72.35, 0.1, 3, 5, -4.86, 61.24, 0.3, 4, 24.97, 61.16, 0.6, 11, -45.03, 72.06, 0.1, 3, 5, 12.02, 52.58, 0.6, 4, 41.99, 52.76, 0.3, 11, -28.14, 63.39, 0.1, 3, 5, 34.32, 68.34, 0.8, 4, 64.04, 68.87, 0.1, 11, -5.84, 79.15, 0.1, 2, 5, 60.47, 75.11, 0.9, 11, 20.31, 85.93, 0.1, 2, 5, 72.8, 73.16, 0.9, 11, 32.63, 83.97, 0.1, 2, 5, 82.09, 67.13, 0.9, 11, 41.92, 77.94, 0.1, 2, 5, 85.67, 52.4, 0.9, 11, 45.51, 63.21, 0.1, 3, 5, 87.25, 37.05, 0.8658, 6, 9.16, 38.48, 0.0342, 11, 47.09, 47.86, 0.1, 3, 5, 91.28, 27.74, 0.7088, 6, 14.13, 29.64, 0.1912, 11, 51.11, 38.55, 0.1, 3, 5, 96.24, 18.04, 0.4632, 6, 20.06, 20.51, 0.4368, 11, 56.07, 28.86, 0.1, 3, 5, 103.05, 16.05, 0.2346, 6, 27.05, 19.24, 0.6654, 11, 62.89, 26.87, 0.1, 3, 5, 110.93, 14.72, 0.1551, 6, 35.02, 18.72, 0.7449, 11, 70.77, 25.53, 0.1, 3, 5, 92.37, -2.26, 0.29912, 6, 18.31, -0.08, 0.62887, 10, 82.67, 8.95, 0.072, 3, 5, 72.32, -7.75, 0.49122, 6, -1.06, -7.62, 0.20478, 10, 62.62, 3.46, 0.304, 3, 5, 50.48, -13.85, 0.59335, 6, -22.14, -15.95, 0.04665, 10, 40.79, -2.64, 0.36, 3, 5, 29.24, -15.28, 0.56889, 4, 60.27, -14.82, 0.07111, 10, 19.54, -4.07, 0.36, 3, 5, 9.84, -15.38, 0.464, 4, 40.87, -15.23, 0.232, 10, 0.14, -4.17, 0.304, 3, 5, -11.59, -12.56, 0.232, 4, 19.4, -12.74, 0.464, 10, -21.29, -1.35, 0.304, 3, 5, -27.92, -11.37, 0.12667, 4, 3.06, -11.8, 0.63333, 10, -37.61, -0.15, 0.24, 2, 5, 63.2, 51.17, 0.808, 10, 53.5, 62.38, 0.192, 3, 5, 37.12, 39.93, 0.71822, 4, 67.28, 40.51, 0.08978, 10, 27.42, 51.14, 0.192, 3, 5, 13.4, 32.57, 0.53867, 4, 43.69, 32.77, 0.26933, 10, 3.71, 43.78, 0.192, 3, 5, -6.21, 30.98, 0.26933, 4, 24.1, 30.87, 0.53867, 10, -15.91, 42.19, 0.192, 3, 5, -25.48, 31.18, 0.08978, 4, 4.83, 30.78, 0.71822, 10, -35.18, 42.39, 0.192, 1, 4, -11.67, 41.41, 1, 2, 5, 44.75, -35.32, 0.72, 10, 35.05, -24.11, 0.28, 3, 5, 25.29, -31.22, 0.61156, 4, 56.57, -30.82, 0.07644, 10, 15.59, -20.01, 0.312, 3, 5, 7.71, -26.76, 0.52, 4, 38.92, -26.63, 0.2, 10, -1.99, -15.55, 0.28, 3, 5, -8.37, -24.27, 0.42933, 4, 22.81, -24.4, 0.30667, 10, -18.07, -13.06, 0.264, 2, 5, 67.77, 23.73, 0.696, 10, 58.07, 34.94, 0.304, 2, 5, 44.39, 13.51, 0.696, 10, 34.69, 24.72, 0.304, 3, 5, 20.4, 8.55, 0.61867, 4, 51.06, 8.87, 0.07733, 10, 10.7, 19.76, 0.304, 3, 5, 2.09, 6.42, 0.50267, 4, 32.79, 6.45, 0.19333, 10, -7.61, 17.63, 0.304, 3, 5, -17.11, 5.59, 0.406, 4, 13.6, 5.32, 0.29, 10, -26.81, 16.8, 0.304, 1, 4, 12.86, -40.4, 1], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 56, 58, 58, 60, 60, 62, 62, 64, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 90, 92, 92, 94, 82, 84, 84, 86, 10, 12, 12, 14, 0, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 34, 36, 36, 38, 44, 46, 46, 48, 52, 54, 54, 56, 82, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 64, 66, 66, 68, 18, 122, 122, 124, 124, 126, 126, 128, 86, 130, 130, 132, 132, 134, 134, 136, 136, 138, 30, 140, 86, 88, 88, 90, 6, 8, 8, 10, 20, 22, 22, 24, 94, 96], "width": 140, "height": 171}}, "a7": {"a7": {"type": "mesh", "uvs": [0.72326, 0.08009, 0.78147, 0.1941, 0.79567, 0.29659, 0.80775, 0.38376, 0.83949, 0.47974, 0.87282, 0.57817, 0.88324, 0.67163, 0.90206, 0.756, 0.92776, 0.82843, 0.95458, 0.88919, 0.9854, 0.93279, 0.99208, 0.95855, 0.78088, 0.99474, 0.55918, 0.99475, 0.32355, 0.93925, 0.10511, 0.95717, 0.16096, 0.89371, 0.20858, 0.83138, 0.25301, 0.7644, 0.28931, 0.68627, 0.30992, 0.60563, 0.31797, 0.50948, 0.31585, 0.42311, 0.28974, 0.3307, 0.2402, 0.23058, 0.15002, 0.11821, 0.00032, 0.04363, 0.0517, 0.03307, 0.6402, 0.0014, 0.57576, 0.90876, 0.58344, 0.82267], "triangles": [30, 19, 6, 13, 14, 29, 30, 29, 14, 13, 29, 12, 12, 29, 11, 11, 29, 10, 9, 29, 8, 29, 30, 8, 29, 9, 10, 30, 7, 8, 30, 6, 7, 6, 20, 5, 5, 21, 4, 4, 22, 3, 23, 2, 3, 17, 18, 30, 15, 16, 14, 14, 16, 17, 30, 14, 17, 18, 19, 30, 6, 19, 20, 5, 20, 21, 4, 21, 22, 22, 23, 3, 2, 23, 1, 24, 0, 1, 0, 25, 28, 0, 24, 25, 26, 27, 25, 25, 27, 28, 23, 24, 1], "vertices": [2, 4, 13.29, -26.96, 0.85185, 53, -32.25, 2.25, 0.14815, 2, 4, -8.42, -31.13, 0.62963, 53, -10.36, 5.35, 0.37037, 3, 4, -27.73, -31.34, 0.37037, 53, 8.94, 4.61, 0.61585, 54, -54.5, 6.69, 0.01378, 3, 4, -44.16, -31.51, 0.14815, 53, 25.35, 3.98, 0.77347, 54, -38.12, 5.48, 0.07838, 3, 4, -62.34, -33.42, 0.03704, 53, 43.61, 5, 0.73213, 54, -19.84, 5.85, 0.23084, 3, 53, 62.34, 6.11, 0.53316, 54, -1.08, 6.3, 0.43456, 55, -44.62, 11.68, 0.03228, 3, 53, 79.91, 5.2, 0.28768, 54, 16.45, 4.78, 0.57845, 55, -27.39, 8.11, 0.13387, 3, 53, 95.87, 5.25, 0.1068, 54, 32.4, 4.27, 0.55138, 55, -11.61, 5.72, 0.34182, 3, 53, 109.66, 6.17, 0.02326, 54, 46.22, 4.71, 0.38043, 55, 2.16, 4.54, 0.59632, 4, 51, 43.2, 64.63, 0.0018, 52, -8.96, 64.12, 0.01281, 54, 57.88, 5.56, 0.17689, 55, 13.84, 4.02, 0.8085, 4, 51, 50.67, 69.05, 0.01028, 52, -2.49, 69.92, 0.05449, 54, 66.4, 7.24, 0.05459, 55, 22.5, 4.68, 0.88063, 4, 51, 55.3, 70.61, 0.02831, 52, 1.74, 72.36, 0.16221, 54, 71.28, 7.17, 0.01316, 55, 27.34, 4.04, 0.79632, 4, 51, 65.82, 52.69, 0.02143, 52, 15.57, 56.84, 0.24689, 54, 75.23, -13.23, 0.00656, 55, 28.87, -16.69, 0.72513, 4, 51, 69.87, 32.47, 0.029, 52, 23.51, 37.81, 0.39221, 54, 72.32, -33.65, 0.00959, 55, 23.58, -36.62, 0.5692, 4, 51, 63.94, 8.93, 0.01176, 52, 22.3, 13.57, 0.65885, 54, 58.89, -53.86, 0.00328, 55, 7.87, -55.12, 0.32611, 4, 51, 71.22, -10.33, 0.0399, 52, 33.23, -3.88, 0.82118, 54, 59.36, -74.45, 0.00019, 55, 5.91, -75.62, 0.13873, 3, 51, 58.51, -7.57, 0.1483, 52, 20.22, -3.68, 0.81067, 55, -4.28, -67.54, 0.04103, 4, 50, 110.23, -16.17, 0.00947, 51, 46.15, -5.53, 0.35936, 52, 7.7, -4.1, 0.62523, 55, -14.47, -60.25, 0.00594, 4, 50, 97.81, -11.55, 0.06543, 51, 32.99, -3.95, 0.56184, 52, -5.52, -5.13, 0.37272, 55, -25.58, -53.02, 0, 3, 50, 83.26, -7.61, 0.20494, 51, 17.92, -3.52, 0.6444, 52, -20.38, -7.66, 0.15066, 3, 50, 68.19, -5.11, 0.43663, 51, 2.68, -4.61, 0.52496, 52, -35.11, -11.72, 0.03841, 4, 4, -65.13, 15.32, 0.03704, 50, 50.15, -3.66, 0.64939, 51, -15.19, -7.42, 0.31304, 52, -52.08, -17.98, 0.00054, 3, 4, -48.91, 14.58, 0.14815, 50, 33.92, -3.23, 0.7321, 51, -31.07, -10.8, 0.11975, 3, 4, -31.42, 16.01, 0.37037, 50, 16.47, -4.99, 0.60206, 51, -47.63, -16.59, 0.02757, 2, 4, -12.37, 19.52, 0.62963, 50, -2.52, -8.86, 0.37037, 2, 4, 9.21, 26.68, 0.85185, 50, -23.95, -16.43, 0.14815, 2, 4, 24.01, 39.77, 0.96296, 50, -38.5, -29.8, 0.03704, 1, 4, 25.71, 34.88, 1, 2, 4, 28.5, -20.1, 0.96296, 53, -47.78, -3.85, 0.03704, 4, 51, 53.71, 30.81, 0.06135, 52, 7.99, 33.02, 0.25122, 54, 56.53, -29.83, 0.02474, 55, 8.35, -30.98, 0.66269, 4, 51, 37.7, 28.34, 0.09668, 52, -7.22, 27.45, 0.24784, 54, 40.61, -26.84, 0.04022, 55, -7.1, -26.13, 0.61525], "hull": 29, "edges": [0, 56, 0, 2, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 48, 50, 50, 52, 52, 54, 54, 56, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 10, 12, 12, 14, 6, 8, 8, 10, 44, 46, 46, 48, 2, 4, 4, 6, 14, 16, 16, 18, 26, 58, 58, 60, 58, 22], "width": 93, "height": 188}}, "a8": {"a8": {"type": "mesh", "uvs": [0, 0.02338, 0.07225, 0.00306, 0.11705, 0.01092, 0.24312, 0.06428, 0.41509, 0.16075, 0.58968, 0.30453, 0.69518, 0.42395, 0.78597, 0.5308, 0.85242, 0.61877, 0.9068, 0.69978, 0.95549, 0.78047, 0.98212, 0.84477, 1, 0.89753, 0.88173, 0.93273, 0.73697, 0.96342, 0.58303, 0.98598, 0.41873, 0.99879, 0.41318, 0.96533, 0.4056, 0.92368, 0.39309, 0.86038, 0.38491, 0.78144, 0.37077, 0.68764, 0.34059, 0.5796, 0.30547, 0.46265, 0.24443, 0.31232, 0.17122, 0.21583, 0.08219, 0.0985, 0, 0.02528], "triangles": [25, 26, 3, 27, 0, 1, 2, 27, 1, 26, 2, 3, 26, 27, 2, 20, 9, 10, 19, 20, 10, 19, 10, 11, 19, 14, 18, 11, 13, 19, 12, 13, 11, 13, 14, 19, 15, 17, 18, 14, 15, 18, 16, 17, 15, 7, 22, 23, 8, 21, 22, 8, 22, 7, 21, 8, 9, 20, 21, 9, 25, 3, 4, 24, 25, 4, 5, 24, 4, 23, 24, 5, 23, 5, 6, 7, 23, 6], "vertices": [1, 12, 1.65, 28.89, 1, 1, 12, -1.38, 33.75, 1, 2, 41, -26.21, -1.25, 0.11111, 12, -0.07, 36.67, 0.88889, 2, 41, -15.23, 2.98, 0.33333, 12, 8.48, 44.75, 0.66667, 2, 41, 3.14, 7.23, 0.66667, 12, 23.85, 55.66, 0.33333, 3, 41, 28.31, 8.66, 0.77875, 42, -30.41, 5.57, 0.11014, 12, 46.6, 66.54, 0.11111, 2, 41, 48.17, 7.45, 0.6686, 42, -10.54, 6.41, 0.3314, 3, 41, 65.83, 6.16, 0.33624, 42, 7.17, 6.93, 0.65243, 43, -31.11, 5.17, 0.01133, 3, 41, 80.15, 4.59, 0.11305, 42, 21.58, 6.84, 0.75318, 43, -16.72, 5.9, 0.13377, 3, 41, 93.16, 2.74, 0.00097, 42, 34.7, 6.33, 0.63171, 43, -3.59, 6.13, 0.36733, 2, 42, 47.67, 5.47, 0.31067, 43, 9.4, 6.02, 0.68933, 2, 42, 57.75, 4.03, 0.09978, 43, 19.55, 5.15, 0.90022, 2, 42, 65.94, 2.6, 4e-05, 43, 27.81, 4.19, 0.99996, 2, 42, 68.73, -6.53, 0.00068, 43, 31.12, -4.77, 0.99932, 2, 42, 70.32, -17.09, 0.0023, 43, 33.3, -15.23, 0.9977, 2, 42, 70.51, -27.84, 0.00581, 43, 34.1, -25.95, 0.99419, 2, 42, 69.05, -38.77, 0.01457, 43, 33.26, -36.94, 0.98543, 2, 42, 63.97, -37.5, 0.04283, 43, 28.12, -35.96, 0.95717, 2, 42, 57.64, -35.96, 0.12249, 43, 21.71, -34.78, 0.87751, 3, 41, 102.28, -38.42, 0.00176, 42, 47.99, -33.68, 0.28463, 43, 11.95, -33.05, 0.71361, 3, 41, 90.81, -33.91, 0.03406, 42, 36.12, -30.37, 0.48511, 43, -0.09, -30.42, 0.48083, 3, 41, 77.06, -28.82, 0.16571, 42, 21.92, -26.72, 0.58869, 43, -14.47, -27.58, 0.2456, 3, 41, 60.85, -23.8, 0.40494, 42, 5.28, -23.38, 0.50917, 43, -31.27, -25.19, 0.08589, 3, 41, 43.24, -18.5, 0.6942, 42, -12.78, -19.92, 0.29059, 43, -49.51, -22.77, 0.01521, 4, 41, 20.17, -12.66, 0.78478, 42, -36.32, -16.47, 0.1038, 43, -73.21, -20.66, 0.00032, 12, 47.17, 43.73, 0.11111, 3, 41, 4.45, -10.96, 0.65489, 42, -52.13, -16.39, 0.01177, 12, 31.98, 39.33, 0.33333, 2, 41, -14.66, -8.9, 0.33333, 12, 13.52, 33.98, 0.66667, 2, 41, -27.3, -9.22, 0.11111, 12, 1.95, 28.88, 0.88889], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 44, 46, 46, 48, 52, 54, 38, 40, 48, 50, 50, 52, 10, 12, 12, 14, 16, 18, 18, 20, 40, 42, 42, 44, 32, 34, 34, 36], "width": 66, "height": 156}}, "a9": {"a9": {"type": "mesh", "uvs": [0.99649, 0, 0.97201, 0.16304, 0.92376, 0.31613, 0.87021, 0.42731, 0.81729, 0.5321, 0.759, 0.63233, 0.70269, 0.72657, 0.65912, 0.80204, 0.60716, 0.88294, 0.56884, 0.94528, 0.53484, 0.99831, 0.4263, 0.97322, 0.30739, 0.92919, 0.18212, 0.83739, 0.09926, 0.71934, 0.06997, 0.66217, 0.14433, 0.60559, 0.22362, 0.54392, 0.30921, 0.47076, 0.4031, 0.39374, 0.50517, 0.31263, 0.58559, 0.24864, 0.67373, 0.17009, 0.76358, 0.09, 0.84733, 0.01535, 0.9963, 0.00074, 0.29105, 0.76151, 0.38826, 0.67743, 0.47877, 0.59594, 0.57095, 0.50023, 0.67487, 0.39028, 0.76873, 0.28292, 0.87371, 0.1481], "triangles": [30, 21, 31, 30, 31, 3, 31, 32, 2, 2, 32, 1, 32, 24, 25, 23, 24, 32, 1, 32, 25, 1, 25, 0, 32, 22, 23, 31, 22, 32, 21, 22, 31, 3, 31, 2, 4, 30, 3, 29, 30, 4, 5, 29, 4, 28, 29, 5, 6, 28, 5, 6, 27, 28, 7, 27, 6, 26, 27, 8, 11, 12, 26, 26, 8, 11, 9, 11, 8, 10, 11, 9, 8, 27, 7, 20, 21, 30, 29, 20, 30, 19, 20, 29, 29, 18, 19, 28, 18, 29, 17, 18, 28, 27, 17, 28, 16, 17, 27, 14, 15, 16, 26, 14, 16, 27, 26, 16, 13, 14, 26, 13, 26, 12], "vertices": [2, 47, -56.14, -5.7, 0.03704, 4, 33.97, 0.66, 0.96296, 2, 47, -26.93, 1.97, 0.14815, 4, 4.22, 5.86, 0.85185, 3, 48, -63.21, 2.21, 0.01705, 47, 1.8, 5.83, 0.35332, 4, -23.5, 14.32, 0.62963, 3, 48, -41.45, 3.81, 0.08819, 47, 23.62, 6.18, 0.54144, 4, -43.49, 23.09, 0.37037, 3, 48, -20.79, 5.01, 0.25045, 47, 44.32, 6.19, 0.6014, 4, -62.31, 31.7, 0.14815, 4, 49, -43.96, 4.41, 0.03704, 48, -0.57, 5.16, 0.45269, 47, 64.51, 5.19, 0.47323, 4, -80.24, 41.03, 0.03704, 3, 49, -24.87, 4.68, 0.14815, 48, 18.52, 5.11, 0.58379, 47, 83.57, 4.04, 0.26806, 3, 49, -9.67, 5.1, 0.37037, 48, 33.72, 5.27, 0.53263, 47, 98.76, 3.33, 0.09699, 4, 46, -3.21, 77.1, 0.00263, 49, 6.94, 4.88, 0.62682, 48, 50.33, 4.76, 0.35056, 47, 115.31, 1.86, 0.01999, 3, 46, 8.51, 81.97, 0.01882, 49, 19.64, 4.92, 0.83087, 48, 63.03, 4.59, 0.15031, 3, 46, 18.63, 85.99, 0.07251, 49, 30.52, 4.78, 0.88245, 48, 73.91, 4.26, 0.04504, 4, 46, 26.98, 72.23, 0.18975, 45, 63.74, 71.82, 0.00154, 49, 33.01, -11.11, 0.78865, 48, 76.13, -11.68, 0.02006, 6, 46, 34.1, 54.91, 0.36781, 45, 70.59, 54.39, 0.01311, 44, 128.62, 46.64, 4e-05, 49, 33, -29.84, 0.57789, 48, 75.79, -30.4, 0.04089, 47, 138.71, -34.7, 0.00026, 6, 46, 35.99, 30.45, 0.56119, 45, 72.12, 29.91, 0.04552, 44, 127.56, 22.14, 0.00223, 49, 25.45, -53.18, 0.31583, 48, 67.84, -53.61, 0.07158, 47, 129.44, -57.41, 0.00365, 6, 46, 30.18, 6.44, 0.75005, 45, 65.95, 5.99, 0.0489, 44, 118.9, -1, 4e-05, 49, 10.94, -73.17, 0.16593, 48, 52.99, -73.35, 0.03482, 47, 113.48, -76.27, 0.00026, 4, 46, 26.22, -4.15, 0.77889, 45, 61.83, -4.55, 0.14592, 49, 3.25, -81.47, 0.06284, 48, 45.16, -81.51, 0.01235, 5, 46, 11.41, -4.81, 0.61913, 45, 47.01, -4.98, 0.3271, 44, 98.91, -9.91, 0.03574, 49, -10.7, -76.44, 0.01498, 48, 31.3, -76.25, 0.00305, 5, 46, -4.55, -5.7, 0.37751, 45, 31.04, -5.63, 0.47659, 44, 82.96, -8.87, 0.14426, 49, -25.8, -71.19, 0.00131, 48, 16.29, -70.73, 0.00033, 3, 46, -22.59, -7.55, 0.15568, 45, 12.97, -7.21, 0.48173, 44, 64.83, -8.54, 0.36259, 4, 46, -41.98, -9.15, 0.0408, 45, -6.44, -8.52, 0.33864, 44, 45.38, -7.79, 0.58352, 4, -33.5, 88.95, 0.03704, 4, 46, -62.74, -10.52, 0.00125, 45, -27.22, -9.58, 0.15467, 44, 24.61, -6.65, 0.69593, 4, -19.44, 73.62, 0.14815, 3, 45, -43.6, -10.43, 0.04093, 44, 8.23, -5.77, 0.5887, 4, -8.34, 61.54, 0.37037, 3, 45, -62.61, -12.49, 0.0013, 44, -10.89, -5.82, 0.36907, 4, 5.37, 48.22, 0.62963, 2, 44, -30.38, -5.87, 0.14815, 4, 19.35, 34.63, 0.85185, 2, 44, -48.55, -5.92, 0.03704, 4, 32.37, 21.97, 0.96296, 1, 4, 33.84, 0.69, 1, 6, 46, 15.15, 30.45, 0.45732, 45, 51.28, 30.21, 0.13578, 44, 106.87, 24.64, 0.02427, 49, 6.17, -45.26, 0.2119, 48, 48.71, -45.36, 0.14346, 47, 110.81, -48.08, 0.02728, 6, 46, -5.46, 28.2, 0.31225, 45, 30.64, 28.28, 0.21029, 44, 86.14, 24.89, 0.08306, 49, -13.74, -39.49, 0.12318, 48, 28.89, -39.25, 0.18691, 47, 91.38, -40.84, 0.0843, 7, 46, -25.04, 25.68, 0.16761, 45, 11.02, 26.05, 0.22484, 44, 66.39, 24.74, 0.17089, 49, -32.81, -34.37, 0.05761, 48, 9.91, -33.81, 0.17796, 47, 72.74, -34.32, 0.16406, 4, -71.26, 80.37, 0.03704, 7, 46, -46.56, 21.37, 0.06601, 45, -10.56, 22.06, 0.17363, 44, 44.52, 23.05, 0.24125, 49, -54.35, -30.17, 0.01952, 48, -11.55, -29.24, 0.12552, 47, 51.57, -28.53, 0.22592, 4, -54.44, 66.29, 0.14815, 7, 46, -71.07, 16.23, 0.01685, 45, -35.14, 17.29, 0.09265, 44, 19.57, 20.9, 0.23648, 49, -78.97, -25.6, 0.00384, 48, -36.09, -24.24, 0.06211, 47, 27.36, -22.13, 0.2177, 4, -35.09, 50.39, 0.37037, 7, 46, -94.19, 10.48, 0.00199, 45, -58.35, 11.9, 0.03208, 44, -4.08, 17.99, 0.17464, 49, -102.54, -22.11, 0.00024, 48, -59.6, -20.35, 0.01996, 47, 4.11, -16.9, 0.15997, 4, -16.14, 35.95, 0.61111, 6, 46, -121.88, 2.05, 3e-05, 45, -86.16, 3.88, 0.00771, 44, -32.58, 12.95, 0.12447, 48, -88.36, -17.13, 0.00426, 47, -24.41, -12.03, 0.11353, 4, 7.77, 19.63, 0.75], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 40, 42, 48, 50, 30, 32, 32, 34, 18, 20, 14, 16, 16, 18, 12, 14, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 8, 10, 10, 12, 4, 6, 6, 8, 26, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 142, "height": 184}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"a25": {"color": [{"color": "ff830000", "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "color": "ff820081", "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5, "color": "ff830000"}]}, "a4": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ffffffff"}]}, "a44": {"color": [{"color": "ff830000", "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "color": "ff820081", "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5, "color": "ff830000"}]}, "a23": {"color": [{"color": "ff830000", "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "color": "ff820081", "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5, "color": "ff830000"}]}, "dian2/qf_qing_jn3_qtgd_dsd_bz_00": {"attachment": [{"time": 0.2667, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_00"}, {"time": 0.3, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_02"}, {"time": 0.3333, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_04"}, {"time": 0.3667, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_06"}, {"time": 0.4, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_08"}, {"time": 0.4333, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_10"}, {"time": 0.4667, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_12"}, {"time": 0.5, "name": null}]}, "a47": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ffffffff"}]}, "a24": {"color": [{"color": "ff830000", "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "color": "ff820081", "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5, "color": "ff830000"}]}, "dian2/qf_qing_jn3_qtgd_dsd_00": {"attachment": [{"time": 0.2333, "name": "dian2/qf_qing_jn3_qtgd_dsd_00"}, {"time": 0.2667, "name": "dian2/qf_qing_jn3_qtgd_dsd_02"}, {"time": 0.3, "name": "dian2/qf_qing_jn3_qtgd_dsd_04"}, {"time": 0.3333, "name": "dian2/qf_qing_jn3_qtgd_dsd_06"}, {"time": 0.3667, "name": "dian2/qf_qing_jn3_qtgd_dsd_08"}, {"time": 0.4, "name": "dian2/qf_qing_jn3_qtgd_dsd_10"}, {"time": 0.4333, "name": "dian2/qf_qing_jn3_qtgd_dsd_12"}, {"time": 0.4667, "name": "dian2/qf_qing_jn3_qtgd_dsd_14"}]}, "a41": {"color": [{"color": "ff830000", "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "color": "ff820081", "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5, "color": "ff830000"}]}, "a40": {"color": [{"color": "ff830000", "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "color": "ff820081", "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5, "color": "ff830000"}]}, "dian1/chisongzi_sd_00000": {"attachment": [{"time": 0.0333, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.0667, "name": "dian1/chisongzi_sd_00004"}, {"time": 0.1, "name": "dian1/chisongzi_sd_00006"}, {"time": 0.1333, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.1667, "name": "dian1/chisongzi_sd_00010"}, {"time": 0.2, "name": "dian1/chisongzi_sd_00011"}, {"time": 0.2333, "name": "dian1/chisongzi_sd_00012"}, {"time": 0.2667, "name": "dian1/chisongzi_sd_00013"}, {"time": 0.3, "name": "dian1/chisongzi_sd_00015"}, {"time": 0.3333, "name": "dian1/chisongzi_sd_00016"}, {"time": 0.3667, "name": "dian1/chisongzi_sd_00017"}, {"time": 0.4, "name": "dian1/chisongzi_sd_00019"}, {"time": 0.4333, "name": "dian1/chisongzi_sd_00021"}, {"time": 0.4667, "name": "dian1/chisongzi_sd_00022"}, {"time": 0.5, "name": "dian1/chisongzi_sd_00024"}]}}, "bones": {"bone2": {"rotate": [{"time": 0.1667}, {"time": 0.2333, "angle": -7.97}, {"time": 0.5}], "translate": [{"y": 23.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1667, "x": -12.89, "y": 52.97, "curve": "stepped"}, {"time": 0.2333, "x": -12.89, "y": 52.97, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.5, "y": 23.32}]}, "bone4": {"rotate": [{}, {"time": 0.1667, "angle": -4.09}, {"time": 0.2333, "angle": 2.97}, {"time": 0.5}], "translate": [{"x": 0.04, "y": 0.8}]}, "bone5": {"rotate": [{}, {"time": 0.1667, "angle": -4.09}, {"time": 0.2333, "angle": 2.97}, {"time": 0.5}], "translate": [{"x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 5.78, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "angle": 1.69, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.2333, "angle": 8.76, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.5, "angle": 5.78}]}, "bone8": {"translate": [{"x": 3.12, "y": 0.05, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "x": 3.49, "y": -29.71, "curve": "stepped"}, {"time": 0.2333, "x": 3.49, "y": -29.71, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.5, "x": 3.12, "y": 0.05}]}, "bone18": {"translate": [{"x": 1.26, "y": -0.16, "curve": 0.345, "c2": 0.38, "c3": 0.679, "c4": 0.72}, {"time": 0.1667, "x": -0.92, "y": -29.84, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.2333, "x": -0.71, "y": -48.92, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.5, "x": 1.26, "y": -0.16}]}, "bone10": {"rotate": [{"angle": -0.38, "curve": 0.338, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": 9.81, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.2333, "angle": 12.16, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.69}, {"time": 0.5, "angle": -0.38}]}, "bone11": {"rotate": [{"angle": -0.23, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.0667, "angle": 54.92, "curve": "stepped"}, {"time": 0.3667, "angle": 54.92, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.5, "angle": -0.23}], "translate": [{"x": 11.59, "y": 14.08}]}, "bone12": {"rotate": [{"angle": 7.79, "curve": 0.309, "c2": 0.22, "c3": 0.645, "c4": 0.57}, {"time": 0.1667, "angle": 21.23, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2667, "angle": 36.04, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.3333, "angle": 23.07, "curve": 0.331, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.5, "angle": 7.79}]}, "bone13": {"rotate": [{"angle": -0.07, "curve": 0.335, "c2": 0.36, "c3": 0.669, "c4": 0.69}, {"time": 0.0667, "angle": 175.57, "curve": 0.336, "c2": 0.37, "c3": 0.67, "c4": 0.71}, {"time": 0.1667, "angle": -83.08, "curve": 0.334, "c2": 0.35, "c3": 0.668, "c4": 0.68}, {"time": 0.2, "angle": 143.69, "curve": 0.334, "c2": 0.35, "c3": 0.668, "c4": 0.69}, {"time": 0.2333, "angle": 88.09, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": 128.46, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -0.07}], "translate": [{"time": 0.1667, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.2333, "x": -14.61, "y": 7.77, "curve": "stepped"}, {"time": 0.3, "x": -14.61, "y": 7.77, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.5}]}, "bone14": {"rotate": [{"angle": -4.79, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.0667, "angle": -72.59, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.7}, {"time": 0.1667, "angle": -91.05, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": 6.43, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.2333, "angle": 5.16, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.3, "angle": -64.41, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.5, "angle": -4.79}], "translate": [{"time": 0.1667, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.2333, "x": 11.44, "y": 1.85, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.3, "x": 29.43, "y": 7.84, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.5}]}, "bone15": {"rotate": [{"angle": -12.49, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.66}, {"time": 0.0667, "angle": -53.93, "curve": 0.332, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.1667, "angle": -12.49, "curve": 0.337, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": -79.32, "curve": 0.337, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.5, "angle": -12.49}]}, "bone3": {"rotate": [{"time": 0.1667}, {"time": 0.2333, "angle": -6.62}, {"time": 0.5}], "translate": [{"time": 0.1667}, {"time": 0.2333, "x": 21.7, "y": 3.04}, {"time": 0.5}]}, "bone67": {"rotate": [{"angle": 3.15, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": 1.28, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 3.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": 3.15}], "translate": [{}, {"time": 0.1, "x": -0.15, "y": -5.31, "curve": "stepped"}, {"time": 0.3333, "x": -0.15, "y": -5.31}, {"time": 0.5}]}, "bone17": {"rotate": [{"angle": 16.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 16.56}]}, "bone68": {"rotate": [{"angle": 6.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 6.2, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": 3.69, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5, "angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1, "angle": -3.95, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1333, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -27.98, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5, "angle": -25.6}]}, "bone70": {"rotate": [{"angle": 0.74, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.42, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 0.74}], "translate": [{"x": 0.25, "y": 8.59, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 0.08, "curve": "stepped"}, {"time": 0.3333, "y": 0.08}, {"time": 0.5, "x": 0.25, "y": 8.59}]}, "bone71": {"rotate": [{"angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 1.84}]}, "bone23": {"rotate": [{"angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -25.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.16}]}, "bone24": {"rotate": [{"angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -5.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 13.03}]}, "bone25": {"rotate": [{"angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 48.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 13.03}]}, "bone26": {"rotate": [{"angle": -13.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -23.66, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -13.94}]}, "bone27": {"rotate": [{"angle": -23.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -23.66}]}, "bone29": {"rotate": [{"angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 26.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -12.84}]}, "bone30": {"rotate": [{"angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -48.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -15.94}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": -24.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -24.43}]}, "bone31": {"rotate": [{"angle": 19.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 28.61, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": 19.32}]}, "bone32": {"rotate": [{"angle": 28.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 28.61}]}, "bone45": {"rotate": [{"angle": 0.51, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 13.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.03, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": 20.33, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -12.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3667, "angle": 14.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 15.24, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": 8.15, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.2333, "angle": -15.48, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2667, "angle": -18.38, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5, "angle": 16.03}]}, "bone59": {"rotate": [{"angle": 3.46, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 11.6, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": 19.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -25.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -4.04, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4333, "angle": -10.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.4, "angle": 8.51, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.5, "angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1667, "angle": -21.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 14.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.4, "angle": 22.46, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": -7.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": 8.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "angle": 12.44, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4333, "angle": -1.54, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 0.5, "angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 0.0333, "angle": 14.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -7.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2333, "angle": -1.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4333, "angle": 24.47, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "angle": 22.9, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.5, "angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 0.0333, "angle": 28.92, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.2333, "angle": -16.57, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3, "angle": -20.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": 19.86, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 0.5, "angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0667, "angle": 8.92, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.2333, "angle": -16.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4, "angle": 13.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.4667, "angle": 20.19, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "angle": -6.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "angle": -14.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 17.31, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.4667, "angle": 10.7, "curve": 0.323, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.5, "angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": -5.48, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "angle": 11.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4, "angle": -3.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": -9.26}]}, "bone20": {"rotate": [{"angle": -1.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -12.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 18.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": -3.4}]}, "a21": {"translate": [{"y": 25.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 33.12, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "y": 25.1}]}, "a20": {"translate": [{"y": 33.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5, "y": 32.55}]}, "a22": {"translate": [{"y": 10.09, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 33.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "y": 10.09}]}}, "events": [{"time": 0.2667, "name": "atk"}]}, "boss_attack2_2": {"bones": {"bone2": {"translate": [{"y": 23.32}]}, "bone4": {"translate": [{"x": 0.04, "y": 0.8}]}, "bone5": {"translate": [{"x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 5.78}]}, "bone8": {"translate": [{"x": 3.12, "y": 0.05}]}, "bone18": {"translate": [{"x": 1.26, "y": -0.16}]}, "bone10": {"rotate": [{"angle": -0.38}]}, "bone11": {"rotate": [{"angle": -0.23}]}, "bone12": {"rotate": [{"angle": 7.79}]}, "bone13": {"rotate": [{"angle": -0.07}]}, "bone14": {"rotate": [{"angle": -4.79}]}, "bone15": {"rotate": [{"angle": -12.49}]}, "bone67": {"rotate": [{"angle": 3.15}]}, "bone17": {"rotate": [{"angle": 16.56}]}, "bone68": {"rotate": [{"angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.55}]}, "bone70": {"rotate": [{"angle": 0.74}], "translate": [{"x": 0.25, "y": 8.59}]}, "bone71": {"rotate": [{"angle": 1.84}]}, "bone23": {"rotate": [{"angle": 11.16}]}, "bone24": {"rotate": [{"angle": 13.03}]}, "bone25": {"rotate": [{"angle": 13.03}]}, "bone26": {"rotate": [{"angle": -13.94}]}, "bone27": {"rotate": [{"angle": -23.66}]}, "bone29": {"rotate": [{"angle": -12.84}]}, "bone30": {"rotate": [{"angle": -15.94}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": -24.43}]}, "bone31": {"rotate": [{"angle": 19.32}]}, "bone32": {"rotate": [{"angle": 28.61}]}, "bone45": {"rotate": [{"angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.62}]}, "bone59": {"rotate": [{"angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26}]}, "bone20": {"rotate": [{"angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4}]}, "a21": {"translate": [{"y": 25.1}]}, "a20": {"translate": [{"y": 33.12}]}, "a22": {"translate": [{"y": 10.09}]}}}, "boss_attack3": {"slots": {"a2": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "dian2/qf_qing_jn3_qtgd_dsd_0": {"color": [{"time": 0.2, "color": "ffc8f1ff", "curve": "stepped"}, {"time": 0.2333, "color": "ffc8f000", "curve": "stepped"}, {"time": 0.3, "color": "ffc8f000", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffc8f1ff"}], "attachment": [{"time": 0.4667, "name": "dian2/qf_qing_jn3_qtgd_dsd_00"}, {"time": 0.5, "name": "dian2/qf_qing_jn3_qtgd_dsd_02"}, {"time": 0.5333, "name": "dian2/qf_qing_jn3_qtgd_dsd_04"}, {"time": 0.5667, "name": "dian2/qf_qing_jn3_qtgd_dsd_06"}, {"time": 0.6, "name": "dian2/qf_qing_jn3_qtgd_dsd_08"}, {"time": 0.6333, "name": "dian2/qf_qing_jn3_qtgd_dsd_10"}, {"time": 0.6667, "name": "dian2/qf_qing_jn3_qtgd_dsd_12"}, {"time": 0.7, "name": "dian2/qf_qing_jn3_qtgd_dsd_14"}, {"time": 0.7333, "name": "dian2/qf_qing_jn3_qtgd_dsd_16"}, {"time": 0.7667, "name": "dian2/qf_qing_jn3_qtgd_dsd_18"}, {"time": 0.8, "name": "dian2/qf_qing_jn3_qtgd_dsd_20"}, {"time": 0.8333, "name": "dian2/qf_qing_jn3_qtgd_dsd_22"}, {"time": 0.8667, "name": "dian2/qf_qing_jn3_qtgd_dsd_24"}]}, "a3": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a32": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a20": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "a39": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "a23": {"color": [{"color": "ff820000"}]}, "a25": {"color": [{"color": "ff820000"}]}, "a49": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a7": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "dazhao/qf_js_nu_jn2_ctj_mz_00": {"attachment": [{"time": 0.5, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_00"}, {"time": 0.5333, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_02"}, {"time": 0.5667, "name": "daz<PERSON>/qf_js_nu_jn2_ctj_mz_06"}, {"time": 0.6, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_10"}, {"time": 0.6333, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_14"}, {"time": 0.6667, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_18"}, {"time": 0.7, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_22"}, {"time": 0.7333, "name": null}]}, "dazhao/qf_js_nu_jn2_ctj_mz_0": {"attachment": [{"time": 0.5667, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_00"}, {"time": 0.6, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_02"}, {"time": 0.6333, "name": "daz<PERSON>/qf_js_nu_jn2_ctj_mz_06"}, {"time": 0.6667, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_10"}, {"time": 0.7, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_14"}, {"time": 0.7333, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_18"}, {"time": 0.7667, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_22"}, {"time": 0.8, "name": null}]}, "a48": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "dian2/qf_qing_jn3_qtgd_dsd_bz_00": {"attachment": [{"time": 0.5333, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_00"}, {"time": 0.5667, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_02"}, {"time": 0.6, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_04"}, {"time": 0.6333, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_06"}, {"time": 0.6667, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_08"}, {"time": 0.7, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_10"}, {"time": 0.7333, "name": "dian2/qf_qing_jn3_qtgd_dsd_bz_12"}, {"time": 0.7667, "name": null}]}, "a21": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "dian2/qf_qing_jn3_qtgd_dsd_1": {"color": [{"time": 0.2, "color": "ffc8f1ff", "curve": "stepped"}, {"time": 0.2333, "color": "ffc8f000", "curve": "stepped"}, {"time": 0.3, "color": "ffc8f000", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffc8f1ff"}], "attachment": [{"time": 0.5, "name": "dian2/qf_qing_jn3_qtgd_dsd_00"}, {"time": 0.5333, "name": "dian2/qf_qing_jn3_qtgd_dsd_02"}, {"time": 0.5667, "name": "dian2/qf_qing_jn3_qtgd_dsd_04"}, {"time": 0.6, "name": "dian2/qf_qing_jn3_qtgd_dsd_06"}, {"time": 0.6333, "name": "dian2/qf_qing_jn3_qtgd_dsd_08"}, {"time": 0.6667, "name": "dian2/qf_qing_jn3_qtgd_dsd_10"}, {"time": 0.7, "name": "dian2/qf_qing_jn3_qtgd_dsd_12"}, {"time": 0.7333, "name": "dian2/qf_qing_jn3_qtgd_dsd_14"}, {"time": 0.7667, "name": "dian2/qf_qing_jn3_qtgd_dsd_16"}, {"time": 0.8, "name": "dian2/qf_qing_jn3_qtgd_dsd_18"}, {"time": 0.8333, "name": "dian2/qf_qing_jn3_qtgd_dsd_20"}, {"time": 0.8667, "name": "dian2/qf_qing_jn3_qtgd_dsd_22"}, {"time": 0.9, "name": "dian2/qf_qing_jn3_qtgd_dsd_24"}]}, "a26": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a44": {"color": [{"color": "ff820000"}]}, "a5": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a19": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a9": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a8": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a34": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a38": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a47": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 0.2, "color": "ffffff00", "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": 0.288, "c2": 0.18, "c3": 0.646, "c4": 0.6}, {"time": 0.5, "color": "ffffffca", "curve": 0.345, "c2": 0.37, "c3": 0.687, "c4": 0.73}, {"time": 0.6667, "color": "ffffffff", "curve": 0.379, "c2": 0.6, "c3": 0.722}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a22": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "a37": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a41": {"color": [{"color": "ff820000"}]}, "a4": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 0.2, "color": "ffffff00", "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": 0.288, "c2": 0.18, "c3": 0.646, "c4": 0.6}, {"time": 0.5, "color": "ffffffca", "curve": 0.345, "c2": 0.37, "c3": 0.687, "c4": 0.73}, {"time": 0.6667, "color": "ffffffff", "curve": 0.379, "c2": 0.6, "c3": 0.722}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a14": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a11": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a17": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a18": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a29": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a51": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a46": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a43": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "a12": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a28": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a10": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a16": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "b1": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}]}, "b2": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}]}, "a0": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a13": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a1": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "dian2/qf_qing_jn3_qtgd_dsd_00": {"color": [{"time": 0.2, "color": "ffc8f1ff", "curve": "stepped"}, {"time": 0.2333, "color": "ffc8f000", "curve": "stepped"}, {"time": 0.3, "color": "ffc8f000", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffc8f1ff"}], "attachment": [{"time": 0.5333, "name": "dian2/qf_qing_jn3_qtgd_dsd_00"}, {"time": 0.5667, "name": "dian2/qf_qing_jn3_qtgd_dsd_02"}, {"time": 0.6, "name": "dian2/qf_qing_jn3_qtgd_dsd_04"}, {"time": 0.6333, "name": "dian2/qf_qing_jn3_qtgd_dsd_06"}, {"time": 0.6667, "name": "dian2/qf_qing_jn3_qtgd_dsd_08"}, {"time": 0.7, "name": "dian2/qf_qing_jn3_qtgd_dsd_10"}, {"time": 0.7333, "name": "dian2/qf_qing_jn3_qtgd_dsd_12"}, {"time": 0.7667, "name": "dian2/qf_qing_jn3_qtgd_dsd_14"}, {"time": 0.8, "name": "dian2/qf_qing_jn3_qtgd_dsd_16"}, {"time": 0.8333, "name": "dian2/qf_qing_jn3_qtgd_dsd_18"}, {"time": 0.8667, "name": "dian2/qf_qing_jn3_qtgd_dsd_20"}, {"time": 0.9, "name": "dian2/qf_qing_jn3_qtgd_dsd_22"}, {"time": 0.9333, "name": "dian2/qf_qing_jn3_qtgd_dsd_24"}]}, "a35": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a33": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a50": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "dian1/chisongzi_sd_00000": {"attachment": [{"time": 0.0333, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.0667, "name": "dian1/chisongzi_sd_00003"}, {"time": 0.1, "name": "dian1/chisongzi_sd_00004"}, {"time": 0.1333, "name": "dian1/chisongzi_sd_00006"}, {"time": 0.1667, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.2, "name": "dian1/chisongzi_sd_00009"}, {"time": 0.2333, "name": "dian1/chisongzi_sd_00010"}, {"time": 0.2667, "name": null}, {"time": 0.3, "name": "dian1/chisongzi_sd_00012"}, {"time": 0.3333, "name": "dian1/chisongzi_sd_00013"}, {"time": 0.3667, "name": "dian1/chisongzi_sd_00014"}, {"time": 0.4, "name": "dian1/chisongzi_sd_00015"}, {"time": 0.4333, "name": "dian1/chisongzi_sd_00016"}, {"time": 0.4667, "name": "dian1/chisongzi_sd_00017"}, {"time": 0.5, "name": "dian1/chisongzi_sd_00019"}, {"time": 0.5333, "name": "dian1/chisongzi_sd_00020"}, {"time": 0.5667, "name": "dian1/chisongzi_sd_00021"}, {"time": 0.6, "name": "dian1/chisongzi_sd_00022"}, {"time": 0.6333, "name": "dian1/chisongzi_sd_00024"}, {"time": 0.6667, "name": "dian1/chisongzi_sd_00000"}, {"time": 0.7, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.7333, "name": "dian1/chisongzi_sd_00003"}, {"time": 0.7667, "name": null}]}, "a31": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "dazhao/qf_qing_jn3_qtgd_xlsd_00": {"attachment": [{"time": 0.5, "name": "dazhao/qf_qing_jn3_qtgd_xlsd_00"}, {"time": 0.5333, "name": "daz<PERSON>/qf_qing_jn3_qtgd_xlsd_02"}, {"time": 0.5667, "name": "daz<PERSON>/qf_qing_jn3_qtgd_xlsd_04"}, {"time": 0.6, "name": "dazhao/qf_qing_jn3_qtgd_xlsd_06"}, {"time": 0.6333, "name": "dazhao/qf_qing_jn3_qtgd_xlsd_08"}, {"time": 0.6667, "name": "da<PERSON><PERSON>/qf_qing_jn3_qtgd_xlsd_10"}, {"time": 0.7, "name": "da<PERSON><PERSON>/qf_qing_jn3_qtgd_xlsd_12"}, {"time": 0.7333, "name": "da<PERSON><PERSON>/qf_qing_jn3_qtgd_xlsd_14"}, {"time": 0.7667, "name": "da<PERSON><PERSON>/qf_qing_jn3_qtgd_xlsd_16"}, {"time": 0.8, "name": "da<PERSON><PERSON>/qf_qing_jn3_qtgd_xlsd_22"}, {"time": 0.8333, "name": null}]}, "a36": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a6": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a30": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a24": {"color": [{"color": "ff820000"}]}, "a45": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "dazhao/qf_js_nu_jn2_ctj_mz_1": {"attachment": [{"time": 0.5667, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_00"}, {"time": 0.6, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_02"}, {"time": 0.6333, "name": "daz<PERSON>/qf_js_nu_jn2_ctj_mz_06"}, {"time": 0.6667, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_10"}, {"time": 0.7, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_14"}, {"time": 0.7333, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_18"}, {"time": 0.7667, "name": "da<PERSON><PERSON>/qf_js_nu_jn2_ctj_mz_22"}, {"time": 0.8, "name": null}]}, "a40": {"color": [{"color": "ff820000"}]}, "a15": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a27": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "a42": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}}, "bones": {"bone2": {"rotate": [{"time": 0.2}, {"time": 0.3, "angle": -8.26, "curve": "stepped"}, {"time": 0.4333, "angle": -8.26}, {"time": 0.5, "angle": -16.23, "curve": "stepped"}, {"time": 0.7667, "angle": -16.23}, {"time": 0.8}], "translate": [{"y": 23.32, "curve": 1, "c3": 0.75}, {"time": 0.1, "y": 147.19, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.2, "y": 194.96, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": 0.78, "y": 273.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4333, "x": 8.2, "y": 385.01, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.5, "x": 59.15, "y": 347.33, "curve": "stepped"}, {"time": 0.7667, "x": 59.15, "y": 347.33, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.8, "x": 163.8, "y": 316.87, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.9667, "y": 23.32}]}, "bone4": {"rotate": [{"time": 0.3}, {"time": 0.4333, "angle": -4.09}, {"time": 0.5, "angle": 2.97, "curve": "stepped"}, {"time": 0.7667, "angle": 2.97}, {"time": 0.8}], "translate": [{"x": 0.04, "y": 0.8}]}, "bone5": {"rotate": [{"time": 0.0667}, {"time": 0.2, "angle": -9.77}, {"time": 0.3}, {"time": 0.4333, "angle": -4.09}, {"time": 0.5, "angle": 2.97, "curve": "stepped"}, {"time": 0.7667, "angle": 2.97}, {"time": 0.8}], "translate": [{"x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 5.78, "curve": 0.334, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.0667, "angle": -10.8, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.68}, {"time": 0.2, "angle": 5.78, "curve": "stepped"}, {"time": 0.3, "angle": 5.78, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.4333, "angle": 1.69, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.5, "angle": 8.76, "curve": "stepped"}, {"time": 0.7667, "angle": 8.76, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.8, "angle": 5.78}]}, "bone8": {"translate": [{"x": 3.12, "y": 0.05, "curve": "stepped"}, {"time": 0.3, "x": 3.12, "y": 0.05, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.4333, "x": 3.49, "y": -29.71, "curve": "stepped"}, {"time": 0.7667, "x": 3.49, "y": -29.71, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.8, "x": 3.12, "y": 0.05}]}, "bone18": {"translate": [{"x": 1.26, "y": -0.16, "curve": "stepped"}, {"time": 0.3, "x": 1.26, "y": -0.16, "curve": 0.345, "c2": 0.38, "c3": 0.679, "c4": 0.72}, {"time": 0.4333, "x": -0.92, "y": -29.84, "curve": "stepped"}, {"time": 0.7667, "x": -0.92, "y": -29.84, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.8, "x": 1.26, "y": -0.16}]}, "bone10": {"rotate": [{"angle": -0.38, "curve": "stepped"}, {"time": 0.3, "angle": -0.38, "curve": 0.338, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.4333, "angle": 77.91, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.5, "angle": 64.13, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.7667, "angle": 77.91, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.8, "angle": -0.38, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8333, "angle": 67.77, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.9667, "angle": -0.38}]}, "bone11": {"rotate": [{"angle": -0.23, "curve": "stepped"}, {"time": 0.4333, "angle": -0.23, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.68}, {"time": 0.5, "angle": -14.01, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.68}, {"time": 0.7667, "angle": -0.23, "curve": "stepped"}, {"time": 0.8, "angle": -0.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "angle": -10.37, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.68}, {"time": 0.9667, "angle": -0.23}]}, "bone12": {"rotate": [{"angle": 7.79, "curve": "stepped"}, {"time": 0.3, "angle": 7.79, "curve": 0.309, "c2": 0.22, "c3": 0.645, "c4": 0.57}, {"time": 0.4333, "angle": 64.48, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.5, "angle": 73.98, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.7667, "angle": 56.74, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.8, "angle": 7.79, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.8333, "angle": 46.59, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.9667, "angle": 7.79}]}, "bone13": {"rotate": [{"angle": -0.07, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.68}, {"time": 0.0667, "angle": 5.36, "curve": 0.334, "c2": 0.35, "c3": 0.668, "c4": 0.68}, {"time": 0.2, "angle": -5.92, "curve": 0.334, "c2": 0.35, "c3": 0.668, "c4": 0.68}, {"time": 0.3, "angle": -0.07, "curve": 0.335, "c2": 0.36, "c3": 0.669, "c4": 0.69}, {"time": 0.4333, "angle": -83.08, "curve": 0.334, "c2": 0.35, "c3": 0.668, "c4": 0.68}, {"time": 0.4667, "angle": 143.69, "curve": 0.334, "c2": 0.35, "c3": 0.668, "c4": 0.69}, {"time": 0.5, "angle": 86.88, "curve": 0.335, "c2": 0.37, "c3": 0.669, "c4": 0.7}, {"time": 0.5667, "angle": -11.15, "curve": "stepped"}, {"time": 0.7667, "angle": -11.15, "curve": 0.335, "c2": 0.37, "c3": 0.669, "c4": 0.7}, {"time": 0.8, "angle": -0.07}]}, "bone14": {"rotate": [{"angle": -4.79, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.0667, "angle": -40.08, "curve": "stepped"}, {"time": 0.2, "angle": -40.08, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.3, "angle": -4.79, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.4333, "angle": -91.05, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.4667, "angle": 6.43, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.5, "angle": -21.54, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.5667, "angle": -64.83, "curve": "stepped"}, {"time": 0.7667, "angle": -64.83, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.8, "angle": -4.79}]}, "bone15": {"rotate": [{"angle": -12.49}]}, "bone3": {"rotate": [{"time": 0.4333}, {"time": 0.5, "angle": -6.62, "curve": "stepped"}, {"time": 0.7667, "angle": -6.62}, {"time": 0.8}], "translate": [{"time": 0.4333}, {"time": 0.5, "x": 21.7, "y": 3.04, "curve": "stepped"}, {"time": 0.7667, "x": 21.7, "y": 3.04}, {"time": 0.8}]}, "bone33": {"rotate": [{"time": 0.2}, {"time": 0.3, "angle": -8.26, "curve": "stepped"}, {"time": 0.7667, "angle": -8.26}, {"time": 0.8}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 148.32}, {"time": 0.3, "x": 373.58, "y": 128.93}, {"time": 0.3333, "x": 68.72, "y": 344.88, "curve": "stepped"}, {"time": 0.7667, "x": 68.72, "y": 344.88}, {"time": 0.8}]}, "bone67": {"rotate": [{"angle": 3.15, "curve": "stepped"}, {"time": 0.3, "angle": 3.15, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 1.28, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5667, "angle": -8.68, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 3.15, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.9333, "angle": 3.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.9667, "angle": 3.15}], "translate": [{"time": 0.3}, {"time": 0.3333, "x": -0.15, "y": -5.31}, {"time": 0.5667, "x": 6.49, "y": -19.58}, {"time": 0.8}, {"time": 0.8333, "x": -0.15, "y": -5.31}, {"time": 0.9667}]}, "bone68": {"rotate": [{"angle": 6.73, "curve": "stepped"}, {"time": 0.2, "angle": 6.73, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.3, "angle": 7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 6.2, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8, "angle": 6.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.8333, "angle": 3.69, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.9667, "angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54, "curve": "stepped"}, {"time": 0.3, "angle": -5.54, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.3333, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -5.54, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.8667, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9667, "angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.55, "curve": "stepped"}, {"time": 0.3, "angle": -25.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -27.98, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.8, "angle": -25.6}]}, "bone70": {"rotate": [{"angle": 0.74, "curve": "stepped"}, {"time": 0.3, "angle": 0.74, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "angle": -11.92, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.8, "angle": 0.74, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8333, "angle": 2.42, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9667, "angle": 0.74}], "translate": [{"x": 0.25, "y": 8.59, "curve": "stepped"}, {"time": 0.3, "x": 0.25, "y": 8.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 0.08}, {"time": 0.8, "x": 0.25, "y": 8.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 0.08}, {"time": 0.9667, "x": 0.25, "y": 8.59}]}, "bone71": {"rotate": [{"angle": 1.84, "curve": "stepped"}, {"time": 0.3, "angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9667, "angle": 1.84}]}, "bone23": {"rotate": [{"angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -40.73, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -40.73, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 11.16}]}, "bone24": {"rotate": [{"angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -5.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.9667, "angle": 13.03}]}, "bone25": {"rotate": [{"angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": 48.02, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 48.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9667, "angle": 13.03}]}, "bone26": {"rotate": [{"angle": -13.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0333, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4667, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -23.66, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9667, "angle": -13.94}]}, "bone27": {"rotate": [{"angle": -23.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -23.66}]}, "bone29": {"rotate": [{"angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 26.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 26.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.9667, "angle": -12.84}]}, "bone30": {"rotate": [{"angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": -48.65, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -48.65, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9667, "angle": -15.94}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": -24.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 36.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -24.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 36.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -24.43}]}, "bone31": {"rotate": [{"angle": 19.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.0333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 28.61, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9667, "angle": 19.32}]}, "bone32": {"rotate": [{"angle": 28.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 28.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 28.61}]}, "b1": {"translate": [{"time": 0.1667, "y": 160.28}, {"time": 0.2667, "y": 114.26}], "scale": [{"time": 0.1667, "x": 0.798, "y": 1.024}, {"time": 0.2, "x": 1.194, "y": 0.385}, {"time": 0.2667, "x": 1.194, "y": 0.018}]}, "a20": {"translate": [{"y": 32.55, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "y": 33.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.9667, "y": 32.55}]}, "a22": {"translate": [{"y": 10.09, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 33.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9667, "y": 10.09}]}, "a21": {"translate": [{"y": 25.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 33.12, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9667, "y": 25.1}]}, "bone45": {"rotate": [{"angle": 0.51, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9667, "angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": 13.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4333, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 20.03, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.9667, "angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": 20.33, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1333, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -12.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.9667, "angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0667, "angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4333, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7333, "angle": 14.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9667, "angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.03, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 16.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0667, "angle": 15.24, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1667, "angle": 8.15, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.4333, "angle": -15.48, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.5, "angle": -18.38, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.9667, "angle": 16.03}]}, "bone59": {"rotate": [{"angle": 3.46, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0667, "angle": 11.6, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1667, "angle": 19.03, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2, "angle": 19.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "angle": -25.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9667, "angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3667, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.7, "angle": -4.04, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8667, "angle": -10.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9667, "angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 16.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.7, "angle": 15.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.7667, "angle": 8.51, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.9667, "angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.3333, "angle": -21.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7, "angle": 14.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.7667, "angle": 21.71, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.8, "angle": 22.46, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.9667, "angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0667, "angle": -7.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4333, "angle": 8.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": 12.44, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.8667, "angle": -1.54, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 0.9667, "angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 0.0333, "angle": 14.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": -7.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": -1.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8667, "angle": 24.47, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.9333, "angle": 22.9, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.9667, "angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 0.0333, "angle": 28.07, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "angle": 28.92, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5, "angle": -16.57, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.5667, "angle": -20.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.9333, "angle": 19.86, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 0.9667, "angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.1333, "angle": 8.92, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": -16.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8, "angle": 13.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.9, "angle": 19.56, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.9333, "angle": 20.19, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.9667, "angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.1333, "angle": -6.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": -14.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 18.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.8, "angle": 17.31, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.9, "angle": 10.7, "curve": 0.323, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.9667, "angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "angle": -5.48, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": 11.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.8, "angle": -3.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.9667, "angle": -9.26}]}, "bone20": {"rotate": [{"angle": -1.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": -7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9667, "angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -12.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 18.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9667, "angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1667, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9667, "angle": -3.4}]}, "bone17": {"rotate": [{"angle": 16.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 25.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.9667, "angle": 16.56}]}, "dian": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 38.52, "y": 292.09}]}, "dianatk1": {"rotate": [{"time": 0.5, "angle": -2.49}], "translate": [{"time": 0.5, "x": 90, "y": 320.38}]}, "diandazhao2": {"translate": [{"time": 0.5, "x": 45.84, "y": 13.99}], "scale": [{"time": 0.5, "x": 0.576, "y": 0.576}]}, "dianatk2": {"rotate": [{"time": 0.5, "angle": -2.49}], "translate": [{"time": 0.5, "x": 90, "y": 320.38}]}, "dianatk3": {"rotate": [{"time": 0.5, "angle": -2.49}], "translate": [{"time": 0.5, "x": 90, "y": 320.38}]}}, "events": [{"time": 0.5667, "name": "atk"}]}, "boss_idle": {"slots": {"a25": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "color": "ff830000"}]}, "a4": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff"}]}, "a44": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "color": "ff830000"}]}, "a23": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "color": "ff830000"}]}, "a47": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff"}]}, "a24": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "color": "ff830000"}]}, "a41": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "color": "ff830000"}]}, "a40": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "color": "ff830000"}]}, "dian1/chisongzi_sd_00000": {"attachment": [{"time": 0.0333, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.0667, "name": "dian1/chisongzi_sd_00003"}, {"time": 0.1, "name": "dian1/chisongzi_sd_00004"}, {"time": 0.1333, "name": "dian1/chisongzi_sd_00006"}, {"time": 0.1667, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.2, "name": "dian1/chisongzi_sd_00009"}, {"time": 0.2333, "name": "dian1/chisongzi_sd_00010"}, {"time": 0.2667, "name": "dian1/chisongzi_sd_00011"}, {"time": 0.3, "name": "dian1/chisongzi_sd_00012"}, {"time": 0.3333, "name": "dian1/chisongzi_sd_00013"}, {"time": 0.3667, "name": "dian1/chisongzi_sd_00014"}, {"time": 0.4, "name": "dian1/chisongzi_sd_00015"}, {"time": 0.4333, "name": "dian1/chisongzi_sd_00016"}, {"time": 0.4667, "name": "dian1/chisongzi_sd_00017"}, {"time": 0.5, "name": "dian1/chisongzi_sd_00019"}, {"time": 0.5333, "name": "dian1/chisongzi_sd_00020"}, {"time": 0.5667, "name": "dian1/chisongzi_sd_00021"}, {"time": 0.6, "name": "dian1/chisongzi_sd_00022"}, {"time": 0.6333, "name": "dian1/chisongzi_sd_00024"}, {"time": 0.6667, "name": "dian1/chisongzi_sd_00000"}, {"time": 0.7, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.7333, "name": "dian1/chisongzi_sd_00003"}, {"time": 0.7667, "name": "dian1/chisongzi_sd_00004"}, {"time": 0.8, "name": "dian1/chisongzi_sd_00006"}, {"time": 0.8333, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.8667, "name": "dian1/chisongzi_sd_00009"}, {"time": 0.9, "name": "dian1/chisongzi_sd_00010"}, {"time": 0.9333, "name": "dian1/chisongzi_sd_00011"}, {"time": 0.9667, "name": "dian1/chisongzi_sd_00012"}, {"time": 1, "name": "dian1/chisongzi_sd_00013"}, {"time": 1.0333, "name": "dian1/chisongzi_sd_00014"}, {"time": 1.0667, "name": "dian1/chisongzi_sd_00015"}, {"time": 1.1, "name": "dian1/chisongzi_sd_00016"}, {"time": 1.1333, "name": "dian1/chisongzi_sd_00017"}, {"time": 1.1667, "name": "dian1/chisongzi_sd_00019"}, {"time": 1.2, "name": "dian1/chisongzi_sd_00020"}, {"time": 1.2333, "name": "dian1/chisongzi_sd_00021"}, {"time": 1.2667, "name": "dian1/chisongzi_sd_00022"}, {"time": 1.3, "name": "dian1/chisongzi_sd_00024"}]}}, "bones": {"bone2": {"translate": [{"y": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 50.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 23.32}]}, "bone4": {"translate": [{"x": 0.04, "y": 0.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.23, "y": 4.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.04, "y": 0.8}]}, "bone5": {"translate": [{"x": 2.48, "y": -0.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.96, "y": -0.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 5.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 5.78}]}, "bone8": {"translate": [{"x": 3.12, "y": 0.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 6.24, "y": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 3.12, "y": 0.05}]}, "bone18": {"translate": [{"x": 1.26, "y": -0.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 6.81, "y": -0.86, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 1.26, "y": -0.16}]}, "bone67": {"rotate": [{"angle": 3.15, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": 1.28, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 3.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 3.15}]}, "bone68": {"rotate": [{"angle": 6.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3, "angle": 6.2, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7667, "angle": 3.69, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.3, "angle": -3.95, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.3333, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.6, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -25.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -27.98, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": -25.6}]}, "bone70": {"rotate": [{"angle": 0.74, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.42, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 0.74}], "translate": [{"x": 0.25, "y": 8.59}]}, "bone71": {"rotate": [{"angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 1.84}]}, "bone23": {"rotate": [{"angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -40.73, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.16}]}, "bone24": {"rotate": [{"angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 13.03}]}, "bone25": {"rotate": [{"angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 48.02, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 13.03}]}, "bone26": {"rotate": [{"angle": -13.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -23.66, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -13.94}]}, "bone27": {"rotate": [{"angle": -23.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -23.66}]}, "bone29": {"rotate": [{"angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 26.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -12.84}]}, "bone30": {"rotate": [{"angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -48.65, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -15.94}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": -24.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 36.63, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -24.43}]}, "bone31": {"rotate": [{"angle": 19.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 28.61, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": 19.32}]}, "bone32": {"rotate": [{"angle": 28.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 28.61}]}, "bone45": {"rotate": [{"angle": 0.51, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 8.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1333, "angle": 13.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 20.03, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1333, "angle": 20.33, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1667, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -12.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.1, "angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1, "angle": 14.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.03, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 16.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1, "angle": 15.24, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.2333, "angle": 8.15, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.6, "angle": -15.48, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.7, "angle": -18.38, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": 16.03}]}, "bone59": {"rotate": [{"angle": 3.46, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.1, "angle": 11.6, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.2333, "angle": 19.03, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2667, "angle": 19.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9333, "angle": -25.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.9333, "angle": -4.04, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.1667, "angle": -10.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 16.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.9333, "angle": 15.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 1.0667, "angle": 8.51, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.3333, "angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": -21.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.9333, "angle": 14.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 1.0667, "angle": 21.71, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.1, "angle": 22.46, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": -7.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 8.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.7667, "angle": 12.44, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.2, "angle": -1.54, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 0.0667, "angle": 14.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5333, "angle": -7.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -1.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.2, "angle": 24.47, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.2667, "angle": 22.9, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 1.3333, "angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 0.0667, "angle": 28.07, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1, "angle": 28.92, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.6667, "angle": -16.57, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.7667, "angle": -20.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2667, "angle": 19.86, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 1.3333, "angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.1667, "angle": 8.92, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.6, "angle": -16.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.1, "angle": 13.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 1.2333, "angle": 19.56, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.2667, "angle": 20.19, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.1667, "angle": -6.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3667, "angle": -14.03, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 18.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.1, "angle": 17.31, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 1.2333, "angle": 10.7, "curve": 0.323, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 1.3333, "angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": -5.48, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.6667, "angle": 11.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1, "angle": -3.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.3333, "angle": -9.26}]}, "bone10": {"rotate": [{"angle": -0.38, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 3.43, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -0.38}]}, "bone11": {"rotate": [{"angle": -0.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 1.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -0.23}]}, "bone12": {"rotate": [{"angle": 7.79, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "angle": -3.65, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 8.3, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": 7.79}]}, "bone13": {"rotate": [{"angle": -0.07, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -6.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.1667, "angle": -1.5, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 1.3333, "angle": -0.07}]}, "bone14": {"rotate": [{"angle": -4.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -9.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.1667, "angle": -6.86, "curve": 0.331, "c2": 0.33, "c3": 0.674, "c4": 0.69}, {"time": 1.3333, "angle": -4.79}]}, "bone15": {"rotate": [{"angle": -12.49, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -14.58, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.1667, "angle": -14.28, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 1.3333, "angle": -12.49}]}, "bone20": {"rotate": [{"angle": -1.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 12.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -12.97, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 18.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 10.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -3.4}]}, "bone17": {"rotate": [{"angle": 16.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 25.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 16.56}]}, "a21": {"translate": [{"y": 25.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 33.12, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "y": 25.1}]}, "a20": {"translate": [{"y": 32.55, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "y": 33.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "y": 32.55}]}, "a22": {"translate": [{"y": 10.09, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 33.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "y": 10.09}]}}}, "die": {"slots": {"a47": {"color": [{"color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00"}]}, "a4": {"color": [{"color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00"}]}, "dian1/chisongzi_sd_00000": {"attachment": [{"time": 0.0333, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.0667, "name": "dian1/chisongzi_sd_00003"}, {"time": 0.1, "name": "dian1/chisongzi_sd_00004"}, {"time": 0.1333, "name": "dian1/chisongzi_sd_00006"}, {"time": 0.1667, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.2, "name": "dian1/chisongzi_sd_00009"}, {"time": 0.2333, "name": null}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 89.81}], "translate": [{"y": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "y": 50.83, "curve": 0.85, "c3": 0.75}, {"time": 0.2667, "y": -149.64, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.3333, "y": -140.11, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.5, "y": -149.64}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -9.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4}], "translate": [{"x": 0.04, "y": 0.8}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -9.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 4.04, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 5.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.04}]}, "bone8": {"translate": [{"x": 3.12, "y": 0.05, "curve": "stepped"}, {"time": 0.2667, "x": 3.12, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 6.48, "y": -32.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 3.16, "y": -5.84}]}, "bone18": {"translate": [{"x": 1.26, "y": -0.16, "curve": "stepped"}, {"time": 0.2667, "x": 1.26, "y": -0.16, "curve": 0.341, "c2": 0.38, "c3": 0.674, "c4": 0.72}, {"time": 0.3333, "x": 2.17, "y": -36.67, "curve": 0.356, "c2": 0.65, "c3": 0.691}, {"time": 0.5, "x": 1.26, "y": -0.16}]}, "bone67": {"rotate": [{"angle": 17.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.98}], "translate": [{"x": 0.24, "y": 8.44}]}, "bone68": {"rotate": [{"angle": -52.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -26.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 5.08}]}, "bone69": {"rotate": [{"angle": -17.72, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -12.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.17}]}, "bone72": {"rotate": [{"angle": -25.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -16.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.39}]}, "bone70": {"rotate": [{"angle": 0.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.74}], "translate": [{"x": 0.25, "y": 8.59}]}, "bone71": {"rotate": [{"angle": 1.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -4.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.37}]}, "a22": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -93.73}], "translate": [{"y": 10.09, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -265.38, "curve": 0.346, "c2": 0.38, "c3": 0.683, "c4": 0.72}, {"time": 0.4, "x": 56.2, "y": -224.82, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.5, "x": 103.09, "y": -265.38}]}, "bone23": {"rotate": [{"angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -40.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -43.08}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -10.42, "y": 31.79}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.75}]}, "bone25": {"rotate": [{"angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 48.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 13.03}]}, "bone49": {"rotate": [{"angle": 4.49, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "angle": -6.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "angle": -14.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 18.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4, "angle": 17.31, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.4667, "angle": 10.7, "curve": 0.323, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.5, "angle": 4.49}]}, "bone52": {"rotate": [{"angle": -9.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.4, "angle": 8.51, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.5, "angle": -9.11}]}, "bone31": {"rotate": [{"angle": 19.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 28.61, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": 3}]}, "bone57": {"rotate": [{"angle": 8.02, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3667, "angle": 14.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -5.19}]}, "bone50": {"rotate": [{"angle": 18.71, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0667, "angle": 8.92, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.2333, "angle": -16.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4, "angle": 13.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.4667, "angle": 20.19, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 18.71}]}, "bone51": {"rotate": [{"angle": -6.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -4.04, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": 17.17}]}, "bone55": {"rotate": [{"angle": 19.45, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 0.0333, "angle": 14.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -7.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2333, "angle": -1.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4333, "angle": 24.47, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "angle": 22.9, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.5, "angle": 19.45}]}, "bone46": {"rotate": [{"angle": 18.9, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 13.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.03, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 18.9}]}, "bone56": {"rotate": [{"angle": 24.88, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 0.0333, "angle": 28.92, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.2333, "angle": -16.57, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3, "angle": -20.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "angle": 19.86, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 0.5, "angle": 24.88}]}, "bone32": {"rotate": [{"angle": 28.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 14.29}]}, "bone29": {"rotate": [{"angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 26.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -12.84}]}, "bone24": {"rotate": [{"angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -5.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 13.03}]}, "bone53": {"rotate": [{"angle": 9.11, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1667, "angle": -21.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 14.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.4, "angle": 22.46, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 9.11}]}, "bone47": {"rotate": [{"angle": 14.83, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": 20.33, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -12.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 14.83}]}, "bone58": {"rotate": [{"angle": 16.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 15.24, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": 8.15, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.2333, "angle": -15.48, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2667, "angle": -18.38, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5, "angle": -4.28}]}, "bone16": {"rotate": [{"angle": -3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": -3.4}]}, "bone20": {"rotate": [{"angle": -1.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": -1.35}]}, "bone27": {"rotate": [{"angle": -23.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -23.66}]}, "bone28": {"rotate": [{"angle": -24.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 36.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.55}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 11.46, "y": -39.37}]}, "bone59": {"rotate": [{"angle": 3.46, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 11.6, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": 19.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -25.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": 3.46}]}, "bone26": {"rotate": [{"angle": -13.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -23.66, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -13.94}]}, "bone30": {"rotate": [{"angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -48.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 8.4}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone45": {"rotate": [{"angle": 0.51, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5, "angle": -10.49}]}, "a21": {"rotate": [{"time": 0.2333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.4, "angle": 82.76}], "translate": [{"y": 25.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -400.78, "curve": 0.32, "c2": 0.29, "c3": 0.665, "c4": 0.66}, {"time": 0.4, "x": -37.18, "y": -365.29, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.5, "y": -400.78}]}, "bone54": {"rotate": [{"angle": -6.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": -7.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": 8.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "angle": 12.44, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4333, "angle": -1.54, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 0.5, "angle": -6.06}]}, "bone21": {"rotate": [{"angle": 10.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -12.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 18.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 10.97}]}, "bone48": {"rotate": [{"angle": -9.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": -5.48, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "angle": 11.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4, "angle": -3.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": 23.46}]}, "bone17": {"rotate": [{"angle": 16.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 16.56}]}, "a20": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 91.92}], "translate": [{"y": 33.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3667, "y": -278.41, "curve": 0.34, "c2": 0.44, "c3": 0.674, "c4": 0.77}, {"time": 0.4333, "x": -79.3, "y": -239.54, "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 0.5, "x": -108.16, "y": -278.41}]}, "bone15": {"rotate": [{"angle": -12.49, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1667, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -81.65}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 32.78, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": 29.16}]}, "bone12": {"rotate": [{"angle": 7.79, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2333, "angle": 15.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -15.29}]}, "bone11": {"rotate": [{"angle": -0.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": -3.38, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.2333, "angle": 28.03, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.5, "angle": -23.6}]}, "bone10": {"rotate": [{"angle": -0.38, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -1.6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.2333, "angle": 22.49, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.3667, "angle": -23.82}]}, "bone14": {"rotate": [{"angle": -4.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "angle": -57.95}]}}}, "hurt": {"slots": {"a25": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a4": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}]}, "a44": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a23": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a47": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}]}, "a24": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a41": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a40": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82009c", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "dian1/chisongzi_sd_00000": {"attachment": [{"name": "dian1/chisongzi_sd_00001"}, {"time": 0.0333, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.0667, "name": "dian1/chisongzi_sd_00012"}, {"time": 0.1, "name": "dian1/chisongzi_sd_00016"}, {"time": 0.1333, "name": "dian1/chisongzi_sd_00021"}, {"time": 0.1667, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.2, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.2333, "name": "dian1/chisongzi_sd_00012"}, {"time": 0.2667, "name": "dian1/chisongzi_sd_00016"}, {"time": 0.3, "name": "dian1/chisongzi_sd_00021"}, {"time": 0.3333, "name": "dian1/chisongzi_sd_00024"}]}}, "bones": {"bone2": {"translate": [{"y": 23.32}]}, "bone4": {"rotate": [{"angle": 3.72, "curve": "stepped"}, {"time": 0.0667, "angle": 3.72, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1667, "angle": 10.11, "curve": "stepped"}, {"time": 0.2333, "angle": 10.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 3.72}], "translate": [{"x": 0.04, "y": 0.8}]}, "bone5": {"rotate": [{"angle": 8.79, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 8.79}], "translate": [{"x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 14.58, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 5.78, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": 14.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": 15.89, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 14.58}]}, "bone8": {"translate": [{"x": 3.12, "y": 0.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 6.24, "y": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 3.12, "y": 0.05}]}, "bone18": {"translate": [{"x": 1.26, "y": -0.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 6.81, "y": -0.86, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "x": 1.26, "y": -0.16}]}, "bone10": {"rotate": [{"angle": -0.38, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 15.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.38}]}, "bone11": {"rotate": [{"angle": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 15.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.23}]}, "bone12": {"rotate": [{"angle": 7.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 23.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.79}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -17.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.07}]}, "bone14": {"rotate": [{"angle": -11.38, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -4.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -22.7, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -11.38}]}, "bone15": {"rotate": [{"angle": -12.49, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.58, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3, "angle": -14.28, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.3333, "angle": -12.49}]}, "bone67": {"rotate": [{"angle": 3.15, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": 1.28, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3.59, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 3.15}]}, "bone17": {"rotate": [{"angle": 16.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 25.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 16.56}]}, "bone68": {"rotate": [{"angle": 6.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.0667, "angle": 6.2, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": 3.69, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.3333, "angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.0667, "angle": -3.95, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.1, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.55, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -27.98, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": -25.6}]}, "bone70": {"rotate": [{"angle": 0.74, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 2.42, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": 0.74}], "translate": [{"x": 0.25, "y": 8.59}]}, "bone71": {"rotate": [{"angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 1.84}]}, "bone23": {"rotate": [{"angle": -21.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -21.05}]}, "bone24": {"rotate": [{"angle": 17.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2333, "angle": 24.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 17.1}]}, "bone25": {"rotate": [{"angle": 20.03, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 24.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 20.03}]}, "bone26": {"rotate": [{"angle": -2.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.87}]}, "bone27": {"rotate": [{"angle": -19.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -12.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -23.66, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -19.59}]}, "bone29": {"rotate": [{"angle": -17.94, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2333, "angle": -26.7, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -17.94}]}, "bone30": {"rotate": [{"angle": -24.7, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -29.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -24.7}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": 9.57, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -21.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.57}]}, "bone31": {"rotate": [{"angle": 5.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.46}]}, "bone32": {"rotate": [{"angle": 23.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 14.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 28.61, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 23.51}]}, "bone45": {"rotate": [{"angle": 0.51, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 13.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -12.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": 14.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 15.24, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 8.15, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.1667, "angle": -18.38, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": 16.03}]}, "bone59": {"rotate": [{"angle": 3.46, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 11.6, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 19.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": -25.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -4.04, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": -10.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.2667, "angle": 8.51, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.3333, "angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1, "angle": -21.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": 14.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.2667, "angle": 22.46, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": -7.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": 8.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "angle": 12.44, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -1.54, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 0.3333, "angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 0.0333, "angle": 14.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -7.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": -1.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3, "angle": 24.47, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 0.0333, "angle": 28.92, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.1667, "angle": -16.57, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": -20.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0333, "angle": 8.92, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1667, "angle": -16.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 13.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3, "angle": 19.56, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.3333, "angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.0333, "angle": -6.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -14.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.31, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3, "angle": 10.7, "curve": 0.323, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.3333, "angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": -5.48, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": 11.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "angle": -3.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": -9.26}]}, "bone20": {"rotate": [{"angle": -1.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -12.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 18.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -3.4}]}, "a21": {"translate": [{"y": 25.1, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": 33.12, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "y": 25.1}]}, "a20": {"translate": [{"y": 33.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "y": 32.55}]}, "a22": {"translate": [{"y": 10.09, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": 33.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "y": 10.09}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"angle": -9.6, "curve": "stepped"}, {"time": 0.0667, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone33": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -78.32, "curve": "stepped"}, {"time": 0.2667, "x": -78.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}}}, "run1": {"slots": {"a25": {"color": [{"color": "ff830000", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "color": "ff82007e", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.5333, "color": "ff830000"}]}, "a4": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffffff"}]}, "a44": {"color": [{"color": "ff830000", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "color": "ff82007e", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.5333, "color": "ff830000"}]}, "a23": {"color": [{"color": "ff830000", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "color": "ff82007e", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.5333, "color": "ff830000"}]}, "a47": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffffff"}]}, "a24": {"color": [{"color": "ff830000", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "color": "ff82007e", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.5333, "color": "ff830000"}]}, "a41": {"color": [{"color": "ff830000", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "color": "ff82007e", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.5333, "color": "ff830000"}]}, "a40": {"color": [{"color": "ff830000", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "color": "ff82007e", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.5333, "color": "ff830000"}]}, "dian1/chisongzi_sd_00000": {"attachment": [{"time": 0.0333, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.0667, "name": "dian1/chisongzi_sd_00004"}, {"time": 0.1, "name": "dian1/chisongzi_sd_00006"}, {"time": 0.1333, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.1667, "name": "dian1/chisongzi_sd_00009"}, {"time": 0.2, "name": "dian1/chisongzi_sd_00010"}, {"time": 0.2333, "name": "dian1/chisongzi_sd_00012"}, {"time": 0.2667, "name": "dian1/chisongzi_sd_00013"}, {"time": 0.3, "name": "dian1/chisongzi_sd_00014"}, {"time": 0.3333, "name": "dian1/chisongzi_sd_00015"}, {"time": 0.3667, "name": "dian1/chisongzi_sd_00016"}, {"time": 0.4, "name": "dian1/chisongzi_sd_00019"}, {"time": 0.4333, "name": "dian1/chisongzi_sd_00020"}, {"time": 0.4667, "name": "dian1/chisongzi_sd_00021"}, {"time": 0.5, "name": "dian1/chisongzi_sd_00022"}, {"time": 0.5333, "name": "dian1/chisongzi_sd_00024"}]}}, "bones": {"bone2": {"translate": [{"y": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 41.35, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 23.32}]}, "bone4": {"translate": [{"x": 0.04, "y": 0.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.23, "y": 4.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "x": 0.04, "y": 0.8}]}, "bone5": {"translate": [{"x": 2.48, "y": -0.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 4.96, "y": -0.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 5.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 7.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 5.78}]}, "bone8": {"translate": [{"x": 11.74, "y": -27.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 8.62, "y": -27.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 14.86, "y": -27.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "x": 11.74, "y": -27.66}]}, "bone18": {"translate": [{"x": 4.1, "y": -11.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "x": 8.07, "y": -10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 9.65, "y": -12.39, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "x": 4.1, "y": -11.69}]}, "bone67": {"rotate": [{"angle": -3.97, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": -5.84, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2, "angle": -6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.53, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -3.97}], "translate": [{"x": 5.27, "y": -17.9}]}, "bone68": {"rotate": [{"angle": 6.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1333, "angle": 6.2, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3, "angle": 3.69, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5333, "angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1333, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -27.98, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5333, "angle": -25.6}]}, "bone70": {"rotate": [{"angle": -6.38, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -7.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.7, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -6.38}], "translate": [{"x": 5.52, "y": -9.31}]}, "bone71": {"rotate": [{"angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": 1.84}]}, "bone23": {"rotate": [{"angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -40.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 11.16}]}, "bone24": {"rotate": [{"angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 13.03}]}, "bone25": {"rotate": [{"angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 48.02, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 13.03}]}, "bone26": {"rotate": [{"angle": -13.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -23.66, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -13.94}]}, "bone27": {"rotate": [{"angle": -23.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -23.66}]}, "bone29": {"rotate": [{"angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -12.84}]}, "bone30": {"rotate": [{"angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -48.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": -15.94}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": -24.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 36.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -24.43}]}, "bone31": {"rotate": [{"angle": 19.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 28.61, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": 19.32}]}, "bone32": {"rotate": [{"angle": 28.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 28.61}]}, "bone45": {"rotate": [{"angle": 0.51, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 8.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 13.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.03, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5333, "angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -12.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1333, "angle": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 14.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 15.24, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.1, "angle": 8.15, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.2333, "angle": -15.48, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2667, "angle": -18.38, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5333, "angle": 16.03}]}, "bone59": {"rotate": [{"angle": 3.46, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 11.6, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.1, "angle": 19.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -25.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3667, "angle": -4.04, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": -10.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5333, "angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 16.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3667, "angle": 15.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.4333, "angle": 8.51, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.5333, "angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1667, "angle": -21.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3667, "angle": 14.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.4333, "angle": 22.46, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.5333, "angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": -7.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": 8.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "angle": 12.44, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4667, "angle": -1.54, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 0.5333, "angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 0.0333, "angle": 14.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -7.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2667, "angle": -1.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4667, "angle": 24.47, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 22.9, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.5333, "angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 0.0333, "angle": 28.92, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.2667, "angle": -16.57, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3, "angle": -20.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 19.86, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 0.5333, "angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0667, "angle": 8.92, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.2333, "angle": -16.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4333, "angle": 13.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.5, "angle": 20.19, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5333, "angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "angle": -6.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "angle": -14.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 18.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4333, "angle": 17.31, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.5, "angle": 10.7, "curve": 0.323, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.5333, "angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": -5.48, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 11.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.4333, "angle": -3.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "angle": -9.26}]}, "bone10": {"rotate": [{"angle": -0.38, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.43, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5333, "angle": -0.38}]}, "bone11": {"rotate": [{"angle": -0.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": -0.23}]}, "bone12": {"rotate": [{"angle": 7.79, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2333, "angle": -3.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.3, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5333, "angle": 7.79}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.4667, "angle": -1.5, "curve": 0.37, "c2": 0.5, "c3": 0.714, "c4": 0.89}, {"time": 0.5333, "angle": -0.07}]}, "bone14": {"rotate": [{"angle": -4.79, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4667, "angle": -6.86, "curve": 0.331, "c2": 0.33, "c3": 0.674, "c4": 0.69}, {"time": 0.5333, "angle": -4.79}]}, "bone15": {"rotate": [{"angle": -12.49, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1667, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -14.58, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4667, "angle": -14.28, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.5333, "angle": -12.49}]}, "bone20": {"rotate": [{"angle": -1.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 12.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -12.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 18.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "angle": -3.4}]}, "bone17": {"rotate": [{"angle": 16.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": 16.56}]}, "a21": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 51.25, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "x": -15.98, "y": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -15.98, "y": 59.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "x": -15.98, "y": 51.25}]}, "a20": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 59.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -15.98, "y": 26.15, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.5333, "x": -15.98, "y": 58.71}]}, "a22": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 36.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "x": -15.98, "y": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -15.98, "y": 59.27, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.5333, "x": -15.98, "y": 36.24}]}, "bone3": {"rotate": [{"angle": -18.04}]}}}, "run1_1": {"bones": {"bone2": {"translate": [{"y": 23.32}]}, "bone4": {"translate": [{"x": 0.04, "y": 0.8}]}, "bone5": {"translate": [{"x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 5.78}]}, "bone8": {"translate": [{"x": 3.12, "y": 0.05}]}, "bone18": {"translate": [{"x": 1.26, "y": -0.16}]}, "bone10": {"rotate": [{"angle": -0.38}]}, "bone11": {"rotate": [{"angle": -0.23}]}, "bone12": {"rotate": [{"angle": 7.79}]}, "bone13": {"rotate": [{"angle": -0.07}]}, "bone14": {"rotate": [{"angle": -4.79}]}, "bone15": {"rotate": [{"angle": -12.49}]}, "bone67": {"rotate": [{"angle": 3.15}]}, "bone17": {"rotate": [{"angle": 16.56}]}, "bone68": {"rotate": [{"angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.55}]}, "bone70": {"rotate": [{"angle": 0.74}], "translate": [{"x": 0.25, "y": 8.59}]}, "bone71": {"rotate": [{"angle": 1.84}]}, "bone23": {"rotate": [{"angle": 11.16}]}, "bone24": {"rotate": [{"angle": 13.03}]}, "bone25": {"rotate": [{"angle": 13.03}]}, "bone26": {"rotate": [{"angle": -13.94}]}, "bone27": {"rotate": [{"angle": -23.66}]}, "bone29": {"rotate": [{"angle": -12.84}]}, "bone30": {"rotate": [{"angle": -15.94}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": -24.43}]}, "bone31": {"rotate": [{"angle": 19.32}]}, "bone32": {"rotate": [{"angle": 28.61}]}, "bone45": {"rotate": [{"angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.62}]}, "bone59": {"rotate": [{"angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26}]}, "bone20": {"rotate": [{"angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4}]}, "a21": {"translate": [{"y": 25.1}]}, "a20": {"translate": [{"y": 33.12}]}, "a22": {"translate": [{"y": 10.09}]}}}, "run2": {"slots": {"a25": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a4": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "a44": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a23": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a47": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "a24": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a41": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a40": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "dian1/chisongzi_sd_00000": {"attachment": [{"time": 0.0333, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.0667, "name": "dian1/chisongzi_sd_00003"}, {"time": 0.1, "name": "dian1/chisongzi_sd_00004"}, {"time": 0.1333, "name": "dian1/chisongzi_sd_00006"}, {"time": 0.1667, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.2, "name": "dian1/chisongzi_sd_00009"}, {"time": 0.2333, "name": "dian1/chisongzi_sd_00010"}, {"time": 0.2667, "name": "dian1/chisongzi_sd_00011"}, {"time": 0.3, "name": "dian1/chisongzi_sd_00012"}, {"time": 0.3333, "name": "dian1/chisongzi_sd_00013"}]}}, "bones": {"bone2": {"rotate": [{"angle": -42.75}], "translate": [{"y": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 41.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 23.32}]}, "bone4": {"translate": [{"x": 0.04, "y": 0.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.23, "y": 4.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "x": 0.04, "y": 0.8}]}, "bone5": {"translate": [{"x": 2.48, "y": -0.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 4.96, "y": -0.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 32.76, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.1667, "angle": 37.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 32.76}]}, "bone8": {"translate": [{"x": 11.74, "y": -27.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "x": 8.62, "y": -27.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 14.86, "y": -27.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 11.74, "y": -27.66}]}, "bone18": {"translate": [{"x": 4.1, "y": -11.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "x": 8.07, "y": -10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 9.65, "y": -12.39, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "x": 4.1, "y": -11.69}]}, "bone67": {"rotate": [{"angle": -44.31, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -51.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -44.31}], "translate": [{"x": 5.27, "y": -17.9}]}, "bone68": {"rotate": [{"angle": 6.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 6.2, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": 3.69, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.3333, "angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.55, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -27.98, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": -25.6}]}, "bone70": {"rotate": [{"angle": -46.73, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -53.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -46.73}], "translate": [{"x": 5.52, "y": -9.31}]}, "bone71": {"rotate": [{"angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 1.84}]}, "bone23": {"rotate": [{"angle": -63.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -40.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -63.6}], "translate": [{"x": 1.8, "y": -12.45}], "shear": [{"x": 141.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": 38.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 141.87}]}, "bone24": {"rotate": [{"angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 13.03}]}, "bone25": {"rotate": [{"angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 48.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 13.03}]}, "bone26": {"rotate": [{"angle": -13.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -23.66, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -13.94}]}, "bone27": {"rotate": [{"angle": -23.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.66}]}, "bone29": {"rotate": [{"angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 26.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": -12.84}]}, "bone30": {"rotate": [{"angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -48.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -15.94}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": -24.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 36.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -24.43}], "shear": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": 42.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}]}, "bone31": {"rotate": [{"angle": 19.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 28.61, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 19.32}]}, "bone32": {"rotate": [{"angle": 28.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 28.61}]}, "bone45": {"rotate": [{"angle": 0.51, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 13.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": 14.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 15.24, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 8.15, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.1667, "angle": -18.38, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": 16.03}]}, "bone59": {"rotate": [{"angle": 3.46, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 11.6, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 19.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": -25.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -4.04, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": -10.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3, "angle": 8.51, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.3333, "angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1, "angle": -21.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": 14.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3, "angle": 22.46, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": -7.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": 8.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "angle": 12.44, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -1.54, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 0.3333, "angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 0.0333, "angle": 14.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -7.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": -1.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3, "angle": 24.47, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 0.0333, "angle": 28.92, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.1667, "angle": -16.57, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": -20.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0333, "angle": 8.92, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1667, "angle": -16.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": 13.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3333, "angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.0333, "angle": -6.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -14.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 18.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3, "angle": 17.31, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3333, "angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": -5.48, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": 11.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -3.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": -9.26}]}, "bone10": {"rotate": [{"angle": 16.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 20.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 16.18}]}, "bone11": {"rotate": [{"angle": 80.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 79.36, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2333, "angle": 83.29, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "angle": 80.37}]}, "bone12": {"rotate": [{"angle": 41.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 45.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 41.32}]}, "bone13": {"rotate": [{"angle": 21.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 28.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 21.15}]}, "bone14": {"rotate": [{"angle": 17.91, "curve": 0.353, "c2": 0.65, "c3": 0.688}, {"time": 0.0667, "angle": 16.36, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 24.02, "curve": 0.354, "c2": 0.45, "c3": 0.689, "c4": 0.8}, {"time": 0.3333, "angle": 17.91}]}, "bone15": {"rotate": [{"angle": -12.49, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.28, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.3333, "angle": -12.49}]}, "bone20": {"rotate": [{"angle": -1.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -12.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 18.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -3.4}]}, "bone17": {"rotate": [{"angle": 16.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 25.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 16.56}]}, "a21": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 51.25, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "x": -15.98, "y": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -15.98, "y": 59.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "x": -15.98, "y": 51.25}]}, "a20": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 59.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -15.98, "y": 26.15, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "x": -15.98, "y": 58.71}]}, "a22": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 36.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "x": -15.98, "y": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -15.98, "y": 59.27, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "x": -15.98, "y": 36.24}]}, "bone3": {"rotate": [{"angle": -18.04}]}, "bone33": {"translate": [{"x": 41.53, "y": -70.37}]}}}, "show_time": {"slots": {"a25": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a4": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "a44": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a23": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a47": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff93", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}]}, "a24": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a41": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "a40": {"color": [{"color": "ff830000", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "color": "ff82006a", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ff830000"}]}, "dian1/chisongzi_sd_00000": {"attachment": [{"time": 0.0333, "name": "dian1/chisongzi_sd_00001"}, {"time": 0.0667, "name": "dian1/chisongzi_sd_00003"}, {"time": 0.1, "name": "dian1/chisongzi_sd_00004"}, {"time": 0.1333, "name": "dian1/chisongzi_sd_00006"}, {"time": 0.1667, "name": "dian1/chisongzi_sd_00008"}, {"time": 0.2, "name": "dian1/chisongzi_sd_00009"}, {"time": 0.2333, "name": "dian1/chisongzi_sd_00010"}, {"time": 0.2667, "name": "dian1/chisongzi_sd_00011"}, {"time": 0.3, "name": "dian1/chisongzi_sd_00012"}, {"time": 0.3333, "name": "dian1/chisongzi_sd_00013"}]}}, "bones": {"bone2": {"rotate": [{"angle": -42.75}], "translate": [{"y": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 41.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 23.32}]}, "bone4": {"translate": [{"x": 0.04, "y": 0.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.23, "y": 4.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "x": 0.04, "y": 0.8}]}, "bone5": {"translate": [{"x": 2.48, "y": -0.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 4.96, "y": -0.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 2.48, "y": -0.28}]}, "bone6": {"rotate": [{"angle": 32.76, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.1667, "angle": 37.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 32.76}]}, "bone8": {"translate": [{"x": 11.74, "y": -27.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "x": 8.62, "y": -27.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 14.86, "y": -27.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 11.74, "y": -27.66}]}, "bone18": {"translate": [{"x": 4.1, "y": -11.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "x": 8.07, "y": -10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 9.65, "y": -12.39, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "x": 4.1, "y": -11.69}]}, "bone67": {"rotate": [{"angle": -44.31, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -51.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -44.31}], "translate": [{"x": 5.27, "y": -17.9}]}, "bone68": {"rotate": [{"angle": 6.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": 7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "angle": 6.2, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "angle": 3.69, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.3333, "angle": 6.73}]}, "bone69": {"rotate": [{"angle": -5.54, "curve": 0.366, "c2": 0.46, "c3": 0.733, "c4": 0.91}, {"time": 0.1, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -5.54}]}, "bone72": {"rotate": [{"angle": -25.55, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -27.98, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": -25.6}]}, "bone70": {"rotate": [{"angle": -46.73, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -53.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -46.73}], "translate": [{"x": 5.52, "y": -9.31}]}, "bone71": {"rotate": [{"angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 1.84}]}, "bone23": {"rotate": [{"angle": -63.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -40.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -63.6}], "translate": [{"x": 1.8, "y": -12.45}], "shear": [{"x": 141.87, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": 38.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 141.87}]}, "bone24": {"rotate": [{"angle": 13.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 13.03}]}, "bone25": {"rotate": [{"angle": 13.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 48.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 13.03}]}, "bone26": {"rotate": [{"angle": -13.94, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -23.66, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -13.94}]}, "bone27": {"rotate": [{"angle": -23.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 29.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.66}]}, "bone29": {"rotate": [{"angle": -12.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 26.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": -12.84}]}, "bone30": {"rotate": [{"angle": -15.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -48.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -15.94}], "translate": [{"x": -23.96, "y": -7.68}]}, "bone28": {"rotate": [{"angle": -24.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 36.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -24.43}], "shear": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": 42.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333}]}, "bone31": {"rotate": [{"angle": 19.32, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 28.61, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 19.32}]}, "bone32": {"rotate": [{"angle": 28.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 28.61}]}, "bone45": {"rotate": [{"angle": 0.51, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.12, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": 0.51}]}, "bone46": {"rotate": [{"angle": 18.9, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0333, "angle": 13.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1667, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.9}]}, "bone47": {"rotate": [{"angle": 14.83, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0333, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.34, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 14.83}]}, "bone57": {"rotate": [{"angle": 8.02, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.1, "angle": 1.5, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": 14.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 8.02}]}, "bone58": {"rotate": [{"angle": 16.62, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0333, "angle": 15.24, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.0667, "angle": 8.15, "curve": 0.329, "c2": 0.32, "c3": 0.709, "c4": 0.81}, {"time": 0.1667, "angle": -18.38, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "angle": 16.03}]}, "bone59": {"rotate": [{"angle": 3.46, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.0333, "angle": 11.6, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.0667, "angle": 19.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "angle": -25.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 3.46}]}, "bone51": {"rotate": [{"angle": -6.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 9.89, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.2333, "angle": -4.04, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3, "angle": -10.14, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -6.45}]}, "bone52": {"rotate": [{"angle": -9.11, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0333, "angle": -17.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.46, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3, "angle": 8.51, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.3333, "angle": -9.11}]}, "bone53": {"rotate": [{"angle": 9.11, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1, "angle": -21.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": 14.46, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3, "angle": 22.46, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 9.11}]}, "bone54": {"rotate": [{"angle": -6.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0333, "angle": -7.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1667, "angle": 8.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "angle": 12.44, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -1.54, "curve": 0.354, "c2": 0.41, "c3": 0.694, "c4": 0.77}, {"time": 0.3333, "angle": -6.06}]}, "bone55": {"rotate": [{"angle": 19.45, "curve": 0.32, "c2": 0.29, "c3": 0.655, "c4": 0.63}, {"time": 0.0333, "angle": 14.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -7.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": -1.87, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3, "angle": 24.47, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 19.45}]}, "bone56": {"rotate": [{"angle": 24.88, "curve": 0.355, "c2": 0.48, "c3": 0.69, "c4": 0.82}, {"time": 0.0333, "angle": 28.92, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.1667, "angle": -16.57, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": -20.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 24.88}]}, "bone50": {"rotate": [{"angle": 18.71, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0333, "angle": 8.92, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1667, "angle": -16.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": 13.44, "curve": 0.367, "c2": 0.5, "c3": 0.708, "c4": 0.87}, {"time": 0.3333, "angle": 18.71}]}, "bone49": {"rotate": [{"angle": 4.49, "curve": 0.337, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.0333, "angle": -6.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -14.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 18.6, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3, "angle": 17.31, "curve": 0.299, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.3333, "angle": 4.49}]}, "bone48": {"rotate": [{"angle": -9.26, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": -5.48, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": 11.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3, "angle": -3.01, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": -9.26}]}, "bone10": {"rotate": [{"angle": 16.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 20.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 16.18}]}, "bone11": {"rotate": [{"angle": 80.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 79.36, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2333, "angle": 83.29, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.3333, "angle": 80.37}]}, "bone12": {"rotate": [{"angle": 41.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 45.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 41.32}]}, "bone13": {"rotate": [{"angle": 21.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 28.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 21.15}]}, "bone14": {"rotate": [{"angle": 17.91, "curve": 0.353, "c2": 0.65, "c3": 0.688}, {"time": 0.0667, "angle": 16.36, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 24.02, "curve": 0.354, "c2": 0.45, "c3": 0.689, "c4": 0.8}, {"time": 0.3333, "angle": 17.91}]}, "bone15": {"rotate": [{"angle": -12.49, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.1, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.28, "curve": 0.294, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.3333, "angle": -12.49}]}, "bone20": {"rotate": [{"angle": -1.35, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -1.35}]}, "bone21": {"rotate": [{"angle": 10.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -12.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 18.62, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 10.97}]}, "bone16": {"rotate": [{"angle": -3.4, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "angle": -3.4}]}, "bone17": {"rotate": [{"angle": 16.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 25.68, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 16.56}]}, "a21": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 51.25, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "x": -15.98, "y": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -15.98, "y": 59.27, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "x": -15.98, "y": 51.25}]}, "a20": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 59.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -15.98, "y": 26.15, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.3333, "x": -15.98, "y": 58.71}]}, "a22": {"rotate": [{"angle": -36.6}], "translate": [{"x": -15.98, "y": 36.24, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.0667, "x": -15.98, "y": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -15.98, "y": 59.27, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.3333, "x": -15.98, "y": 36.24}]}, "bone3": {"rotate": [{"angle": -18.04}]}, "bone33": {"translate": [{"x": 41.53, "y": -70.37}]}}}}}