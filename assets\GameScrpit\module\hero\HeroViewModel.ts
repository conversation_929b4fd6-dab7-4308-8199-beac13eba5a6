import { IConfigHero } from "../../game/JsonDefine";
import { persistent } from "../../lib/decorators/persistent";
import MsgMgr from "../../lib/event/MsgMgr";
import { HeroEvent } from "./HeroConstant";

export class HeroViewModel {
  private _heroDetailAttrMore: boolean = false;
  private _currentHeros: IConfigHero[];
  private _currentPosition: number;

  @persistent
  private _setting_hero_pet_xilianshi: boolean = false;

  @persistent
  private _setting_hero_lvup_ten_combo: boolean = false;

  public get setting_hero_lvup_ten_combo(): boolean {
    return this._setting_hero_lvup_ten_combo;
  }
  public set setting_hero_lvup_ten_combo(value: boolean) {
    this._setting_hero_lvup_ten_combo = value;
  }
  public get setting_hero_pet_xilianshi(): boolean {
    return this._setting_hero_pet_xilianshi;
  }
  public set setting_hero_pet_xilianshi(value: boolean) {
    this._setting_hero_pet_xilianshi = value;
  }

  public get currentHeros(): IConfigHero[] {
    return this._currentHeros;
  }
  public set currentHeros(value: IConfigHero[]) {
    this._currentHeros = value;
    MsgMgr.emit(HeroEvent.HERO_VIEWMODEL, value);
  }
  public get currentPosition(): number {
    return this._currentPosition;
  }
  public set currentPosition(value: number) {
    this._currentPosition = value;
    MsgMgr.emit(HeroEvent.HERO_VIEWMODEL, value);
  }
  public get currentHero(): IConfigHero {
    return this._currentHeros[this._currentPosition];
  }
  public get heroDetailAttrMore(): boolean {
    return this._heroDetailAttrMore;
  }
  public set heroDetailAttrMore(value: boolean) {
    this._heroDetailAttrMore = value;
    MsgMgr.emit(HeroEvent.HERO_VIEWMODEL, value);
  }
}
