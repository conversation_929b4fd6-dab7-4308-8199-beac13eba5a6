import {
  IConfigHero,
  IConfigHeroBreak,
  IConfigHeroLv,
  IConfigHeroSkill,
  IConfigHeroSkillLv,
} from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";

/**
 * 定义一个英雄属性的接口。
 * 该接口包含英雄的基础属性和属性加成。
 */

export enum HeroSort {
  DEFAULT = 1,
  POWER,
  ABILITY,
  LEVEL,
}
export enum HeroType {
  ALL,
  PERSON,
  GOD,
  YAO,
  MING,
  WU,
}
export const HeroAudioName = {
  Effect: {
    点击下方战将页签: 1461,
    点击战将卡牌: 1462,
    点击种族图标: 1463,
    点击下方页签: 1464,
    升级成功: 1465,
    点击印记按钮: 1466,
    晋升成功: 1467,
    点击页签切换: 1468,
    点击技能图标: 1469,
    提升成功: 1470,
    点击前后按钮切换战将: 1471,
  },
  Sound: {},
};

export class HeroConfig {
  /**
   * 根据技能id获取技能信息
   * @param skillId
   * @returns
   */
  public getHeroSkillData(skillId: number): IConfigHeroSkill {
    let config_heroSkill = JsonMgr.instance.jsonList.c_heroSkill[skillId] ?? null;
    return config_heroSkill;
  }
  public getHeroInfo(heroId: number): IConfigHero {
    return JsonMgr.instance.jsonList.c_hero[heroId];
  }
  /**
   * // 获取 c_heroLv(战将升级)配置
   * @param level
   * @returns
   */
  public getHeroLvData(level: number): IConfigHeroLv {
    // id 等级
    let data = JsonMgr.instance.jsonList.c_heroLv[level] ?? null;
    return data;
  }

  public getHeroImprintLvInfo(level: number): IConfigHeroSkillLv {
    let json = JsonMgr.instance.jsonList.c_heroSkillLv;
    // 31000000;
    return json[`${31000000 + level}`];
  }
  public getHeroHaloLvInfo(haloId: number, level: number): IConfigHeroSkillLv {
    let json = JsonMgr.instance.jsonList.c_heroSkillLv;
    // 21010001;//21020001//21030001
    return json[`${haloId * 10000 + level}`];
  }
  public getBreakInfo(level: number): IConfigHeroBreak {
    return JsonMgr.instance.jsonList.c_heroBreak[level];
  }
}
