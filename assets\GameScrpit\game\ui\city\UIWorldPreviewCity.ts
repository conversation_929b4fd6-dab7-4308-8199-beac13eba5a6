import { _decorator, Component, instantiate, isValid, Label, Node, Prefab, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import ResMgr from "../../../lib/common/ResMgr";
import { CityCtrl } from "../ui_gameMap/CityCtrl";
import { CityModule } from "../../../module/city/CityModule";
import { NodeTool } from "../../../lib/utils/NodeTool";
const { ccclass, property } = _decorator;

@ccclass("UIWorldPreviewCity")
export class UIWorldPreviewCity extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UIWorldPreviewCity`;
  }

  private _id: number = null;

  public init(args: any): void {
    super.init(args);
    this._id = args.buildShowId;
  }

  protected onEvtShow(): void {
    const cfgBuildShow = JsonMgr.instance.jsonList.c_buildShow[this._id];
    this.loadCity(cfgBuildShow.buildShowId);
  }

  private loadCity(cityIdList: number[]) {
    for (let i = 0; i < cityIdList.length; i++) {
      let id = cityIdList[i];
      let node = ToolExt.clone(this.getNode("city_card"), this);
      this.getNode("content").addChild(node);
      node.active = true;

      let citydb = JsonMgr.instance.jsonList.c_build[id];
      let maxLv = citydb.unlockLvList[citydb.unlockLvList.length - 1];
      node["lbl_city_name"].getComponent(Label).string = citydb.name;

      let cityInfo = CityModule.data.cityMessageMap.get(id);
      if (cityInfo) {
        node["lbl_city_state"].active = true;
      } else {
        node["lbl_city_state"].active = false;
      }

      ResMgr.loadPrefab(`${BundleEnum.BUNDLE_G_GAME_MAP}?prefab/building/city_${id}`, (pb: Prefab) => {
        if (isValid(this.node) == false) return;

        let nodeCity: Node = instantiate(pb);
        nodeCity.getComponent(CityCtrl).setOnlyShow({
          hideUI: true,
          cityLevel: maxLv,
        });
        nodeCity.setPosition(0, 0);
        node["city_pint"].addChild(nodeCity);
        nodeCity.walk((val) => {
          val.layer = this.node.layer;
        });
        let aniNode = NodeTool.findByName(nodeCity, "ani");
        let imgIndex = CityModule.service.getCityImgIndex(maxLv, id);
        CityModule.service.setBuildSize(aniNode, id, imgIndex);
      });
    }
  }
}
