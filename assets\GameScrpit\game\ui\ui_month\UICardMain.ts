import { _decorator, Label } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { PlayerModule } from "../../../module/player/PlayerModule";
import GameHttpApi from "../../httpNet/GameHttpApi";
import { VipCardMessage, VipCardResponse } from "../../net/protocol/Activity";
import TipMgr from "../../../lib/tips/TipMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { HdVipCardModule } from "../../../module/hd_vipcard/HdVipCardModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import { JsonMgr } from "../../mgr/JsonMgr";
import { divide } from "../../../lib/utils/NumbersUtils";
import { ItemCtrl } from "../../common/ItemCtrl";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HdVipCardAudioName } from "../../../module/hd_vipcard/HdVipCardConfig";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { LangMgr } from "../../mgr/LangMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Sat Oct 12 2024 15:25:58 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_month/UICardMain.ts
 *
 */

@ccclass("UICardMain")
export class UICardMain extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CARD}?prefab/ui/UICardMain`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    BadgeMgr.instance.setBadgeId(this.getNode("btn_month_select"), BadgeType.UITerritory.btn_card.btn_month_select.id);
    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_year_unselect"),
      BadgeType.UITerritory.btn_card.btn_year_unselect.id
    );

    HdVipCardModule.api.vipCard((data: VipCardMessage) => {
      log.log(data);
      this.refreshUI();
    });
    this.refreshMonthText();
    this.refreshYearText();
  }
  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_LIFE_CARD_RECHARGE, this.onLifeCardCharge, this);
    MsgMgr.on(MsgEnum.ON_ACTIVITY_MONTH_CARD_RECHARGE, this.onMonthCardCharge, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_ACTIVITY_LIFE_CARD_RECHARGE, this.onLifeCardCharge, this);
    MsgMgr.off(MsgEnum.ON_ACTIVITY_MONTH_CARD_RECHARGE, this.onMonthCardCharge, this);
  }
  private onLifeCardCharge() {
    TipMgr.showTip("支付成功");
    this.refreshUI();
  }
  private onMonthCardCharge() {
    TipMgr.showTip("支付成功");
    this.refreshUI();
  }

  private refreshMonthText() {
    let str_right = "";
    let val_right1 = JsonMgr.instance.jsonList.c_right[101].reward1List[0][1];
    let str_right1 = LangMgr.txMsgCode(321, [val_right1], "");
    let val_right2 = JsonMgr.instance.jsonList.c_right[101].reward2List[0][1];
    let str_right2 = LangMgr.txMsgCode(322, [val_right2], "");
    let val_right3 = JsonMgr.instance.jsonList.c_right[106].openList[0][2];
    let str_right3 = LangMgr.txMsgCode(323, [val_right3], "");
    let str_right4 = JsonMgr.instance.jsonList.c_message[324].text;
    let val_right5 = JsonMgr.instance.jsonList.c_right[107].openList[0][2];
    let str_right5 = LangMgr.txMsgCode(325, [val_right5], "");
    let str_right6 = JsonMgr.instance.jsonList.c_message[326].text;
    let str_right7 = JsonMgr.instance.jsonList.c_message[327].text;
    let val_right8 = JsonMgr.instance.jsonList.c_right[109].openList[0][2];
    let str_right8 = LangMgr.txMsgCode(328, [val_right8], "");
    let str_right9 = JsonMgr.instance.jsonList.c_message[329].text;
    let str_right10 = JsonMgr.instance.jsonList.c_message[330].text;
    str_right =
      str_right1 +
      "\n" +
      str_right2 +
      "\n" +
      str_right3 +
      "\n" +
      str_right4 +
      "\n" +
      str_right5 +
      "\n" +
      str_right6 +
      "\n" +
      str_right7 +
      "\n" +
      str_right8 +
      "\n" +
      str_right9 +
      "\n" +
      str_right10;
    this.getNode("lbl_month").getComponent(Label).string = `${str_right}`;
  }

  private refreshYearText() {
    let str_right = "";

    // 301			1.获得永久称号：%s
    // 302			2.立即获得%s仙玉
    // 303			3.每日领取%s仙玉
    // 304			4.立即解锁弟子一键培养功能
    // 305			5.每日战盟免费高级捐献
    // 306			6.跳过广告
    // 307			7.建筑繁荣度：+5%
    // 308			8.挑战券上限：+%s
    // 309			9.游历上限：+%s

    // 101	战斗3倍加速	6,1,1
    // 102	跳过广告	6,1,1;7,1,1
    // 103	每日战盟免费中级捐献	6,1,1
    // 104	每日战盟免费高级捐献	7,1,1
    // 105	弟子一键培养	1,10,1;7,1,1
    // 106	游历上限+3	6,1,3;7,1,3
    // 107	挑战券上限+2	6,1,2;7,1,2
    // 108	建筑繁荣度+5%	7,1,500
    // 109	弟子培养上限+100	6,1,100
    let val_right1_item = JsonMgr.instance.jsonList.c_right[101].reward3List[1][0];
    let val_right1 = JsonMgr.instance.jsonList.c_item[val_right1_item].name;
    let str_right1 = LangMgr.txMsgCode(301, [""], "");
    // PlayerModule.service.createTitle(this.getNode("btn_tansuojia"), val_right1_item);
    this.getNode("btn_tansuojia").getComponent(ItemCtrl).setItemId(val_right1_item);

    let val_right2 = JsonMgr.instance.jsonList.c_right[101].reward3List[0][1];
    let str_right2 = LangMgr.txMsgCode(302, [val_right2], "");

    let val_right3 = JsonMgr.instance.jsonList.c_right[101].reward4List[0][1];
    let str_right3 = LangMgr.txMsgCode(303, [val_right3], "");

    let str_right4 = JsonMgr.instance.jsonList.c_message[304].text;

    let str_right5 = JsonMgr.instance.jsonList.c_message[305].text;

    let str_right6 = JsonMgr.instance.jsonList.c_message[306].text;

    let val_right7 = divide(JsonMgr.instance.jsonList.c_right[108].openList[0][2], 100);
    let str_right7 = LangMgr.txMsgCode(307, [val_right7], "");

    let val_right8 = JsonMgr.instance.jsonList.c_right[107].openList[0][2];
    let str_right8 = LangMgr.txMsgCode(308, [val_right8], "");

    let val_right9 = JsonMgr.instance.jsonList.c_right[106].openList[0][2];
    let str_right9 = LangMgr.txMsgCode(309, [val_right9], "");

    str_right =
      str_right1 +
      "\n" +
      str_right2 +
      "\n" +
      str_right3 +
      "\n" +
      str_right4 +
      "\n" +
      str_right5 +
      "\n" +
      str_right6 +
      "\n" +
      str_right7 +
      "\n" +
      str_right8 +
      "\n" +
      str_right9;
    this.getNode("lbl_year").getComponent(Label).string = `${str_right}`;
  }

  private refreshUI() {
    let vipCard = HdVipCardModule.data.vipCardMessage;
    if (vipCard.deadline - TimeUtils.serverTime > 0) {
      this.getNode("btn_card_30").active = false;
      this.getNode("node_cd").active = true;
      FmUtils.setCd(this.getNode("lbl_cd"), vipCard.deadline, false, () => {
        this.refreshUI();
      });
    } else {
      this.getNode("btn_card_30").active = true;
      this.getNode("node_cd").active = false;
    }
    if (vipCard.takeMonthDailyReward) {
      this.getNode("btn_yilingqu_30").active = true;
      this.getNode("btn_get_reward_30").active = false;
    } else {
      this.getNode("btn_yilingqu_30").active = false;
    }
    if (vipCard.life) {
      this.getNode("btn_card_198").active = false;
      this.getNode("node_yigoumai").active = true;
    } else {
      this.getNode("btn_card_198").active = true;
      this.getNode("node_yigoumai").active = false;
    }
    if (vipCard.takeLifeDailyReward) {
      this.getNode("btn_yilingqu_198").active = true;
      this.getNode("btn_get_reward_198").active = false;
    } else {
      this.getNode("btn_yilingqu_198").active = false;
    }
  }
  private on_click_btn_month_unselect() {
    AudioMgr.instance.playEffect(HdVipCardAudioName.Effect.点击下方页签);
    this.getNode("dialog_month").active = true;
    this.getNode("dialog_year").active = false;
    this.getNode("btn_year_unselect").active = true;
    this.getNode("btn_year_select").active = false;
    this.getNode("btn_month_select").active = true;
    this.getNode("btn_month_unselect").active = false;
  }

  private on_click_btn_year_unselect() {
    AudioMgr.instance.playEffect(HdVipCardAudioName.Effect.点击下方页签);
    this.getNode("dialog_month").active = false;
    this.getNode("dialog_year").active = true;
    this.getNode("btn_year_unselect").active = false;
    this.getNode("btn_year_select").active = true;
    this.getNode("btn_month_select").active = false;
    this.getNode("btn_month_unselect").active = true;
  }
  private on_click_btn_get_reward_30() {
    AudioMgr.instance.playEffect(HdVipCardAudioName.Effect.点击领取按钮);
    if (HdVipCardModule.data.vipCardMessage.takeMonthDailyReward) {
      log.log("已领取");
      return;
    }
    HdVipCardModule.api.takeMonthVipDailyReward((data: VipCardResponse) => {
      this.refreshUI();
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
    });
  }
  private on_click_btn_get_reward_198() {
    AudioMgr.instance.playEffect(HdVipCardAudioName.Effect.点击领取按钮);
    if (HdVipCardModule.data.vipCardMessage.takeLifeDailyReward) {
      log.log("已领取");
      return;
    }
    HdVipCardModule.api.takeLifeDailyReward((data: VipCardResponse) => {
      this.refreshUI();
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
    });
  }
  // private on_click_btn_tansuojia() {
  //   this.getNode("node_tansuojia").active = true;
  // }
  private on_click_node_tansuojia() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_tansuojia").active = false;
  }
  private on_click_btn_xufei() {
    AudioMgr.instance.playEffect(HdVipCardAudioName.Effect.点击续费按钮);
    this.buy30();
  }
  private on_click_btn_card_30() {
    AudioMgr.instance.playEffect(HdVipCardAudioName.Effect.点击充值按钮);
    this.buy30();
  }

  private buy30() {
    let orderAmount = 30;

    GameHttpApi.pay({
      goodsId: 1020110,
      goodsType: 4,
      playerId: PlayerModule.data.playerId,
      orderAmount: orderAmount,
      goodsName: "月卡",
      platformType: "TEST",
    }).then((resp: any) => {
      log.log("pay resp");
      if (resp.code != 200) {
        log.error(resp);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  private on_click_btn_card_198() {
    AudioMgr.instance.playEffect(HdVipCardAudioName.Effect.点击充值按钮);
    GameHttpApi.pay({
      goodsId: 1030110,
      goodsType: 5,
      playerId: PlayerModule.data.playerId,
      orderAmount: 198,
      goodsName: "终身卡",
      platformType: "TEST",
    }).then((resp: any) => {
      log.log("pay resp");
      if (resp.code != 200) {
        log.error(resp);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }
}
