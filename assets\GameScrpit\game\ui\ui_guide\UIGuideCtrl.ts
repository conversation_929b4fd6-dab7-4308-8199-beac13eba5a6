import { Vec3 } from "cc";
import { _decorator, Component, Node } from "cc";
import { tween } from "cc";
import { NodeMsgEnum } from "../../event/MsgEnum";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIGuideCtrl")
export class UIGuideCtrl extends Component {
  @property(Node)
  btnClickArea: Node = null;

  private isFingerClick: boolean = false;

  // 强制引导
  private isForce: boolean = false;

  private setForce(isForce: boolean) {
    this.isForce = isForce;
  }

  protected onLoad(): void {
    this.btnClickArea.active = false;

    this.node.on(NodeMsgEnum.UIGuide.setFingerPosSize, this.setFingerPosSize, this);
    this.node.on(NodeMsgEnum.UIGuide.setForce, this.setForce, this);
  }

  protected onDestroy(): void {
    this.node.off(NodeMsgEnum.UIGuide.setFingerPosSize, this.setFingerPosSize, this);
    this.node.off(NodeMsgEnum.UIGuide.setForce, this.setForce, this);
  }

  /**
   * 会冒泡的事件封装
   * @param node 事件节点
   * @param func 事件
   */
  private setNodeEvent(node: Node, func: Function) {
    node.on(Node.EventType.TOUCH_END, (event) => {
      event.preventSwallow = this.isFingerClick || !this.isForce;
    });

    node.on(Node.EventType.TOUCH_START, (event) => {
      try {
        func();
      } catch (error) {
        log.log(error);
      }
      event.preventSwallow = this.isFingerClick || !this.isForce;
    });

    node.on(Node.EventType.TOUCH_MOVE, (event) => {
      event.preventSwallow = this.isFingerClick || !this.isForce;
    });

    node.on(Node.EventType.TOUCH_CANCEL, (event) => {
      event.preventSwallow = this.isFingerClick || !this.isForce;
    });
  }

  start() {
    // 点击自己关闭
    this.setNodeEvent(this.node, () => {
      if (this.isForce) {
        return;
      }

      this.node.destroy();
    });

    this.setNodeEvent(this.btnClickArea, () => {
      this.isFingerClick = true;

      this.node.destroy();
    });

    // 防错7秒后自动关闭
    tween(this.node).delay(7).destroySelf().start();
  }

  /**
   * 设置手指位置
   * @param pos 手指位置
   */
  public setFingerPosSize(pos: Vec3) {
    this.btnClickArea.active = true;
    this.btnClickArea.setWorldPosition(pos);
  }
}
