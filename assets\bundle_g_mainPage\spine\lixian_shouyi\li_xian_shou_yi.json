{"skeleton": {"hash": "h94+uP7a2eoXit3LNEgWO740vkg", "spine": "3.8.75", "x": -408.5, "y": -439.5, "width": 818, "height": 933, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/L离线收益"}, "bones": [{"name": "root"}, {"name": "gd_tan_chuang", "parent": "root"}, {"name": "liusu", "parent": "gd_tan_chuang", "x": -334.98, "y": 383.77}, {"name": "ls3", "parent": "liusu", "length": 55.45, "rotation": -91.3, "x": -0.71, "y": -8.77}, {"name": "ls4", "parent": "ls3", "length": 58.58, "rotation": -0.03, "x": 53.88, "y": -0.64}, {"name": "ls5", "parent": "ls4", "length": 54.51, "rotation": -6.33, "x": 58.58}, {"name": "ls6", "parent": "ls5", "length": 31.96, "rotation": 19.13, "x": 54.51}, {"name": "liusu2", "parent": "gd_tan_chuang", "x": 334.98, "y": 383.77, "scaleX": -1}, {"name": "ls7", "parent": "liusu2", "length": 55.45, "rotation": -91.3, "x": -0.71, "y": -8.77}, {"name": "ls8", "parent": "ls7", "length": 58.58, "rotation": -0.03, "x": 53.88, "y": -0.64}, {"name": "ls9", "parent": "ls8", "length": 54.51, "rotation": -6.33, "x": 58.58}, {"name": "ls10", "parent": "ls9", "length": 31.96, "rotation": 19.13, "x": 54.51}, {"name": "<PERSON><PERSON><PERSON>", "parent": "gd_tan_chuang", "x": -142.78, "y": -110.54}], "slots": [{"name": "kuang", "bone": "gd_tan_chuang"}, {"name": "guangk", "bone": "gd_tan_chuang", "attachment": "guangk", "blend": "additive"}, {"name": "liusu", "bone": "liusu", "attachment": "liusu"}, {"name": "liusu2", "bone": "liusu2", "attachment": "liusu"}, {"name": "cut", "bone": "gd_tan_chuang", "attachment": "cut"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "color": "fff60068", "attachment": "<PERSON><PERSON><PERSON>", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"cut": {"cut": {"type": "clipping", "end": "cut", "vertexCount": 8, "vertices": [-273.17, -57, -254.54, -37.14, 259.17, -37.49, 273.35, -54.15, 273.27, -168.13, 257.45, -183.66, -258.05, -183.22, -272.46, -167.74], "color": "ce3a3aff"}}, "saoguang": {"saoguang": {"width": 242, "height": 147}}, "liusu2": {"liusu": {"type": "mesh", "uvs": [0.42812, 0, 0.41619, 0.08749, 0.43224, 0.15681, 0.42689, 0.22614, 0.27709, 0.25728, 0.24499, 0.28943, 0.29849, 0.30852, 0.11124, 0.34067, 0.05774, 0.38488, 0.17544, 0.4311, 0.35199, 0.46023, 0.31877, 0.48318, 0.41777, 0.50332, 0.26927, 0.5622, 0.14552, 0.62184, 0.02177, 0.69079, 0, 0.75353, 0, 0.83642, 0.05065, 0.91466, 0.14552, 0.96593, 0.26515, 0.96361, 0.40952, 1, 0.53739, 1, 0.59514, 0.89467, 0.66527, 0.79396, 0.70239, 0.70565, 0.69414, 0.63439, 0.69522, 0.5735, 0.6416, 0.51386, 0.74472, 0.49217, 0.73235, 0.46118, 0.89735, 0.44414, 1, 0.40463, 0.97572, 0.35428, 0.8726, 0.32019, 0.7901, 0.30238, 0.83135, 0.27681, 0.79422, 0.2497, 0.70347, 0.23808, 0.6911, 0.15364, 0.71172, 0.08548, 0.69522, 0.00181, 0.47949, 0.31809, 0.62519, 0.3172, 0.48419, 0.45754, 0.54999, 0.50874, 0.45599, 0.59876, 0.40899, 0.70556, 0.33849, 0.79471, 0.30559, 0.8865, 0.53119, 0.27194, 0.51709, 0.38315], "triangles": [1, 0, 41, 23, 22, 20, 22, 21, 20, 19, 18, 20, 20, 49, 23, 20, 18, 49, 18, 17, 49, 49, 48, 23, 23, 48, 24, 49, 17, 48, 17, 16, 48, 24, 48, 47, 48, 16, 47, 24, 47, 25, 16, 15, 47, 47, 26, 25, 15, 14, 47, 47, 46, 26, 47, 14, 46, 26, 46, 27, 14, 13, 46, 27, 46, 13, 27, 45, 28, 45, 27, 13, 45, 13, 12, 28, 45, 29, 12, 44, 45, 29, 44, 30, 29, 45, 44, 12, 11, 44, 44, 11, 10, 44, 51, 30, 30, 51, 31, 44, 10, 51, 32, 31, 51, 51, 10, 9, 32, 51, 33, 9, 8, 51, 51, 7, 42, 42, 7, 6, 8, 7, 51, 51, 42, 43, 42, 6, 50, 6, 5, 50, 33, 51, 43, 34, 33, 43, 43, 35, 34, 43, 42, 50, 43, 50, 35, 35, 50, 36, 37, 50, 38, 50, 37, 36, 5, 4, 50, 4, 3, 50, 50, 3, 38, 38, 3, 39, 3, 2, 39, 2, 1, 39, 39, 1, 40, 1, 41, 40], "vertices": [1, 7, -5.28, 4.7, 1, 1, 8, 5.44, -4.92, 1, 1, 8, 20.19, -3.94, 1, 2, 8, 34.96, -3.82, 0.98915, 9, -18.92, -3.18, 0.01085, 2, 8, 41.73, -9.66, 0.72126, 9, -12.15, -9.02, 0.27874, 2, 8, 48.6, -10.78, 0.35412, 9, -5.27, -10.14, 0.64588, 2, 8, 52.62, -8.55, 0.07948, 9, -1.26, -7.91, 0.92052, 1, 9, 5.76, -15.24, 1, 1, 9, 15.23, -17.16, 1, 1, 9, 24.96, -12.22, 1, 1, 9, 31, -5.02, 1, 1, 9, 35.92, -6.23, 1, 1, 9, 40.11, -2.17, 1, 2, 9, 52.79, -7.82, 0.87318, 10, -4.9, -8.41, 0.12682, 2, 9, 65.6, -12.47, 0.05468, 10, 8.35, -11.62, 0.94532, 1, 10, 23.57, -14.57, 1, 1, 10, 36.93, -13.66, 1, 1, 10, 54.43, -11.3, 1, 1, 10, 70.67, -7.07, 1, 1, 10, 80.99, -1.86, 1, 1, 10, 79.86, 2.82, 1, 1, 10, 86.78, 9.57, 1, 1, 10, 86.09, 14.64, 1, 1, 10, 63.55, 13.94, 1, 1, 10, 41.92, 13.86, 1, 1, 10, 23.08, 12.83, 1, 2, 9, 67.77, 9.53, 0.0237, 10, 8.08, 10.48, 0.9763, 2, 9, 54.8, 9.27, 0.82465, 10, -4.78, 8.79, 0.17535, 1, 9, 42.15, 6.83, 1, 2, 8, 91.32, 10.18, 0.00322, 9, 37.43, 10.84, 0.99678, 2, 8, 84.73, 9.54, 0.03476, 9, 30.85, 10.2, 0.96524, 2, 8, 80.95, 16.05, 0.1381, 9, 27.06, 16.71, 0.8619, 2, 8, 72.45, 19.97, 0.30424, 9, 18.56, 20.62, 0.69576, 2, 8, 61.74, 18.75, 0.60845, 9, 7.86, 19.4, 0.39155, 2, 8, 54.58, 14.46, 0.87596, 9, 0.69, 15.11, 0.12404, 2, 8, 50.86, 11.08, 0.9885, 9, -3.02, 11.72, 0.0115, 1, 8, 45.38, 12.6, 1, 1, 8, 39.64, 10.99, 1, 1, 8, 37.25, 7.3, 1, 1, 8, 19.28, 6.4, 1, 2, 8, 4.74, 6.89, 0.9982, 7, 6.07, -13.67, 0.0018, 1, 7, 5.41, 4.15, 1, 1, 9, 0.61, -0.62, 1, 1, 8, 54.17, 4.55, 1, 2, 8, 84.18, -0.41, 0.00021, 9, 30.3, 0.26, 0.99979, 1, 9, 41.14, 3.14, 1, 2, 9, 60.4, -0.17, 0.06031, 10, 1.83, 0.03, 0.93969, 1, 10, 24.62, 1.2, 1, 1, 10, 43.82, 0.93, 1, 1, 10, 63.37, 2.23, 1, 1, 8, 44.61, 0.58, 1, 2, 8, 68.31, 0.55, 0.01716, 9, 14.43, 1.2, 0.98284], "hull": 42, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 0, 82, 12, 84, 84, 86, 86, 70, 20, 88, 88, 60, 24, 90, 90, 56], "width": 40, "height": 213}}, "liusu": {"liusu": {"type": "mesh", "uvs": [0.42812, 0, 0.41619, 0.08749, 0.43224, 0.15681, 0.42689, 0.22614, 0.27709, 0.25728, 0.24499, 0.28943, 0.29849, 0.30852, 0.11124, 0.34067, 0.05774, 0.38488, 0.17544, 0.4311, 0.35199, 0.46023, 0.31877, 0.48318, 0.41777, 0.50332, 0.26927, 0.5622, 0.14552, 0.62184, 0.02177, 0.69079, 0, 0.75353, 0, 0.83642, 0.05065, 0.91466, 0.14552, 0.96593, 0.26515, 0.96361, 0.40952, 1, 0.53739, 1, 0.59514, 0.89467, 0.66527, 0.79396, 0.70239, 0.70565, 0.69414, 0.63439, 0.69522, 0.5735, 0.6416, 0.51386, 0.74472, 0.49217, 0.73235, 0.46118, 0.89735, 0.44414, 1, 0.40463, 0.97572, 0.35428, 0.8726, 0.32019, 0.7901, 0.30238, 0.83135, 0.27681, 0.79422, 0.2497, 0.70347, 0.23808, 0.6911, 0.15364, 0.71172, 0.08548, 0.69522, 0.00181, 0.47949, 0.31809, 0.62519, 0.3172, 0.48419, 0.45754, 0.54999, 0.50874, 0.45599, 0.59876, 0.40899, 0.70556, 0.33849, 0.79471, 0.30559, 0.8865, 0.53119, 0.27194, 0.51709, 0.38315], "triangles": [1, 0, 41, 23, 22, 20, 22, 21, 20, 19, 18, 20, 20, 49, 23, 20, 18, 49, 18, 17, 49, 49, 48, 23, 23, 48, 24, 49, 17, 48, 17, 16, 48, 24, 48, 47, 48, 16, 47, 24, 47, 25, 16, 15, 47, 47, 26, 25, 15, 14, 47, 47, 46, 26, 47, 14, 46, 26, 46, 27, 14, 13, 46, 27, 46, 13, 27, 45, 28, 45, 27, 13, 45, 13, 12, 28, 45, 29, 12, 44, 45, 29, 44, 30, 29, 45, 44, 12, 11, 44, 44, 11, 10, 44, 51, 30, 30, 51, 31, 44, 10, 51, 32, 31, 51, 51, 10, 9, 32, 51, 33, 9, 8, 51, 51, 7, 42, 42, 7, 6, 8, 7, 51, 51, 42, 43, 42, 6, 50, 6, 5, 50, 33, 51, 43, 34, 33, 43, 43, 35, 34, 43, 42, 50, 43, 50, 35, 35, 50, 36, 37, 50, 38, 50, 37, 36, 5, 4, 50, 4, 3, 50, 50, 3, 38, 38, 3, 39, 3, 2, 39, 2, 1, 39, 39, 1, 40, 1, 41, 40], "vertices": [1, 2, -5.28, 4.7, 1, 1, 3, 5.44, -4.92, 1, 1, 3, 20.19, -3.94, 1, 2, 3, 34.96, -3.82, 0.98915, 4, -18.92, -3.18, 0.01085, 2, 3, 41.73, -9.66, 0.72126, 4, -12.15, -9.02, 0.27874, 2, 3, 48.6, -10.78, 0.35412, 4, -5.27, -10.14, 0.64588, 2, 3, 52.62, -8.55, 0.07948, 4, -1.26, -7.91, 0.92052, 1, 4, 5.76, -15.24, 1, 1, 4, 15.23, -17.16, 1, 1, 4, 24.96, -12.22, 1, 1, 4, 31, -5.02, 1, 1, 4, 35.92, -6.23, 1, 1, 4, 40.11, -2.17, 1, 2, 4, 52.79, -7.82, 0.87318, 5, -4.9, -8.41, 0.12682, 2, 4, 65.6, -12.47, 0.05468, 5, 8.35, -11.62, 0.94532, 1, 5, 23.57, -14.57, 1, 1, 5, 36.93, -13.66, 1, 1, 5, 54.43, -11.3, 1, 1, 5, 70.67, -7.07, 1, 1, 5, 80.99, -1.86, 1, 1, 5, 79.86, 2.82, 1, 1, 5, 86.78, 9.57, 1, 1, 5, 86.09, 14.64, 1, 1, 5, 63.55, 13.94, 1, 1, 5, 41.92, 13.86, 1, 1, 5, 23.08, 12.83, 1, 2, 4, 67.77, 9.53, 0.0237, 5, 8.08, 10.48, 0.9763, 2, 4, 54.8, 9.27, 0.82465, 5, -4.78, 8.79, 0.17535, 1, 4, 42.15, 6.83, 1, 2, 3, 91.32, 10.18, 0.00322, 4, 37.43, 10.84, 0.99678, 2, 3, 84.73, 9.54, 0.03476, 4, 30.85, 10.2, 0.96524, 2, 3, 80.95, 16.05, 0.1381, 4, 27.06, 16.71, 0.8619, 2, 3, 72.45, 19.97, 0.30424, 4, 18.56, 20.62, 0.69576, 2, 3, 61.74, 18.75, 0.60845, 4, 7.86, 19.4, 0.39155, 2, 3, 54.58, 14.46, 0.87596, 4, 0.69, 15.11, 0.12404, 2, 3, 50.86, 11.08, 0.9885, 4, -3.02, 11.72, 0.0115, 1, 3, 45.38, 12.6, 1, 1, 3, 39.64, 10.99, 1, 1, 3, 37.25, 7.3, 1, 1, 3, 19.28, 6.4, 1, 2, 3, 4.74, 6.89, 0.9982, 2, 6.07, -13.67, 0.0018, 1, 2, 5.41, 4.15, 1, 1, 4, 0.61, -0.62, 1, 1, 3, 54.17, 4.55, 1, 2, 3, 84.18, -0.41, 0.00021, 4, 30.3, 0.26, 0.99979, 1, 4, 41.14, 3.14, 1, 2, 4, 60.4, -0.17, 0.06031, 5, 1.83, 0.03, 0.93969, 1, 5, 24.62, 1.2, 1, 1, 5, 43.82, 0.93, 1, 1, 5, 63.37, 2.23, 1, 1, 3, 44.61, 0.58, 1, 2, 3, 68.31, 0.55, 0.01716, 4, 14.43, 1.2, 0.98284], "hull": 42, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 0, 82, 12, 84, 84, 86, 86, 70, 20, 88, 88, 60, 24, 90, 90, 56], "width": 40, "height": 213}}, "guangk": {"guangk": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [409.5, -439.5, -408.5, -439.5, -408.5, 493.5, 409.5, 493.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 409, "height": 466}}}}], "events": {"sj_saoguang_chuxian": {}, "sj_zi_chuxian": {}, "sj_zi_xiaoshi": {}}, "animations": {"chi_xu": {"slots": {"saoguang": {"attachment": [{"name": null}]}, "guangk": {"color": [{"color": "ffffffdf"}]}}, "bones": {"ls3": {"rotate": [{"angle": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.33}]}, "ls4": {"rotate": [{"angle": 3.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 11.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 3.39}]}, "ls5": {"rotate": [{"angle": 8.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 11.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 8.05}]}, "ls6": {"rotate": [{"angle": 11.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 11.11}]}, "ls10": {"rotate": [{"angle": -0.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": -7.65, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.93, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -0.07}]}, "ls9": {"rotate": [{"angle": -2.34}, {"time": 0.1667, "angle": -5.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": -2.87, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 4.7}, {"time": 1, "angle": -2.34}]}, "ls8": {"rotate": [{"angle": -4.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3333, "angle": 3.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "angle": 6.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "angle": -4.09}]}, "ls7": {"rotate": [{"angle": -2.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 5.17, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.8333, "angle": -5.4, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -2.4}]}}, "events": [{"time": 0.3333, "name": "sj_sa<PERSON><PERSON>_chuxian"}]}, "chu_xian": {"slots": {"saoguang": {"attachment": [{"name": null}]}, "guangk": {"color": [{"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffffdf"}], "attachment": [{"name": null}, {"time": 0.3333, "name": "guangk"}]}}, "bones": {"gd_tan_chuang": {"scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "ls3": {"rotate": [{"time": 0.1667, "angle": -12.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.87, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 0.33}]}, "ls4": {"rotate": [{"time": 0.1667, "angle": -12.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.23, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 3.39}]}, "ls5": {"rotate": [{"time": 0.1667, "angle": -12.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 28.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -23.33, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 8.05}]}, "ls6": {"rotate": [{"time": 0.1667, "angle": -12.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 33.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -43, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 11.11}]}, "liusu": {"scale": [{"time": 0.1, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 0.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "ls10": {"rotate": [{"time": 0.1667, "angle": -12.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 33.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -43, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -0.07}]}, "ls9": {"rotate": [{"time": 0.1667, "angle": -12.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 28.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -23.33, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -2.34}]}, "ls8": {"rotate": [{"time": 0.1667, "angle": -12.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.23, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -4.09}]}, "ls7": {"rotate": [{"time": 0.1667, "angle": -12.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.87, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -2.4}]}, "liusu2": {"scale": [{"time": 0.1, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 0.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}}, "events": [{"time": 0.3, "name": "sj_zi_chuxian"}]}, "sao_guang": {"slots": {"guangk": {"attachment": [{"name": null}]}, "liusu": {"attachment": [{"name": null}]}, "liusu2": {"attachment": [{"name": null}]}}, "bones": {"saoguang": {"translate": [{"x": -279.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 536.42, "curve": "stepped"}, {"time": 1, "x": 536.42}]}}}}}