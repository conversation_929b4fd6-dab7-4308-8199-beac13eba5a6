{"skeleton": {"hash": "/IBz5IRREWk9cXsu3ROqIx/0Hvc=", "spine": "3.8.75", "x": -82.48, "y": -15.01, "width": 198.41, "height": 352.32, "images": "./images/", "audio": "D:/spine导出/主角换皮动画/主角女/女主角5"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -13.67}, {"name": "bone2", "parent": "bone", "length": 75.15, "rotation": 0.7, "x": 9.52, "y": 187.96, "color": "022c6fff"}, {"name": "bone3", "parent": "bone2", "length": 30, "rotation": 97.88, "x": -1.99, "y": -2.35}, {"name": "bone4", "parent": "bone3", "length": 34.8, "rotation": 6.28, "x": 29.96, "y": -0.09}, {"name": "bone5", "parent": "bone4", "length": 10.59, "rotation": -7.45, "x": 35.36, "y": -0.07}, {"name": "bone6", "parent": "bone5", "rotation": -5.37, "x": 25.57, "y": -15.54}, {"name": "bone13", "parent": "bone6", "x": 27.12, "y": -3.13}, {"name": "bone14", "parent": "bone13", "length": 11.27, "rotation": 59.56, "x": 0.07, "y": 3.12}, {"name": "bone15", "parent": "bone14", "length": 14.53, "rotation": 63.09, "x": 11.27}, {"name": "bone16", "parent": "bone15", "length": 18, "rotation": 45.48, "x": 14.53}, {"name": "bone17", "parent": "bone16", "length": 14.28, "rotation": -11.69, "x": 17.93, "y": -0.25}, {"name": "bone25", "parent": "bone13", "length": 8.59, "rotation": -68.85, "x": -3.37, "y": -9.5}, {"name": "bone26", "parent": "bone25", "length": 14.51, "rotation": -97.4, "x": 8.59}, {"name": "bone27", "parent": "bone26", "length": 13.54, "rotation": -21.75, "x": 14.51}, {"name": "bone28", "parent": "bone27", "length": 13.77, "rotation": 13.41, "x": 13.54}, {"name": "bone22", "parent": "bone5", "length": 53.56, "rotation": 160.69, "x": 51.95, "y": 9.82, "color": "e509ddff"}, {"name": "bone23", "parent": "bone22", "length": 52.72, "rotation": -4.68, "x": 53.56, "color": "e509ddff"}, {"name": "bone24", "parent": "bone23", "length": 38.95, "rotation": 6.15, "x": 52.72, "color": "e509ddff"}, {"name": "bone41", "parent": "bone5", "length": 52.77, "rotation": -175.85, "x": 44, "y": -23.98, "color": "e509ddff"}, {"name": "bone42", "parent": "bone41", "length": 50.61, "rotation": 2.41, "x": 52.54, "y": 0.05, "color": "e509ddff"}, {"name": "bone43", "parent": "bone42", "length": 40.88, "rotation": -5.38, "x": 50.44, "y": 0.28, "color": "e509ddff"}, {"name": "bone31", "parent": "bone4", "rotation": -6.12, "x": 28.49, "y": 25.75}, {"name": "bone36", "parent": "bone31", "length": 37.62, "rotation": 153.83, "x": -1.83, "y": 1.87}, {"name": "bone37", "parent": "bone36", "length": 40, "rotation": 23.95, "x": 37.62}, {"name": "bone38", "parent": "bone37", "length": 16.25, "rotation": 1.18, "x": 40.38, "y": 0.51}, {"name": "bone62", "parent": "bone38", "length": 42.77, "rotation": 69.27, "x": 6.61, "y": 0.58}, {"name": "bone32", "parent": "bone4", "rotation": -6.12, "x": 12.61, "y": -20.2}, {"name": "bone33", "parent": "bone32", "length": 35.85, "rotation": -175.38, "x": -0.05, "y": -1.81}, {"name": "bone34", "parent": "bone33", "length": 35.66, "rotation": -1.06, "x": 38.76, "y": 0.66}, {"name": "bone35", "parent": "bone34", "length": 15.47, "rotation": -1.56, "x": 35.83, "y": -0.04}, {"name": "bone39", "parent": "bone2", "x": -19.61, "y": -20.35}, {"name": "bone7", "parent": "bone2", "x": -5.1, "y": -25.11}, {"name": "bone44", "parent": "bone7", "length": 20, "rotation": -77.72, "x": 11.82, "y": 14.45, "color": "52f94bff"}, {"name": "bone45", "parent": "bone44", "length": 20, "rotation": 3.07, "x": 19.35, "y": -0.15, "color": "52f94bff"}, {"name": "bone46", "parent": "bone45", "length": 20, "rotation": 2.55, "x": 20.09, "y": 0.17, "color": "52f94bff"}, {"name": "bone49", "parent": "bone7", "length": 20, "rotation": -107.74, "x": -6.57, "y": 14.67, "color": "52f94bff"}, {"name": "bone50", "parent": "bone49", "length": 20, "rotation": 3.09, "x": 19.11, "y": 0.59, "color": "52f94bff"}, {"name": "bone51", "parent": "bone50", "length": 20, "rotation": -2.05, "x": 18.65, "y": -0.2, "color": "52f94bff"}, {"name": "bone52", "parent": "bone2", "length": 73.79, "rotation": -95.34, "x": -26.16, "y": -24.54}, {"name": "bone53", "parent": "bone52", "length": 65, "rotation": -4.26, "x": 73.79}, {"name": "bone54", "parent": "bone53", "length": 20, "rotation": 34.62, "x": 63.97, "y": -0.31}, {"name": "bone56", "parent": "bone2", "length": 76.08, "rotation": -85.57, "x": 1.67, "y": -24.29}, {"name": "bone57", "parent": "bone56", "length": 55.47, "rotation": -8.81, "x": 76.08}, {"name": "bone58", "parent": "bone57", "length": 25, "rotation": 57.57, "x": 55.47}, {"name": "bone19", "parent": "bone2", "length": 17.24, "rotation": -131.13, "x": -17.8, "y": 0.83}, {"name": "bone20", "parent": "bone19", "length": 17.76, "rotation": 4.71, "x": 17.24}, {"name": "bone21", "parent": "bone20", "length": 21.19, "rotation": -10.21, "x": 17.76}, {"name": "bone67", "parent": "bone2", "length": 35, "rotation": -104.06, "x": -17.25, "y": -7.44, "color": "022c6fff"}, {"name": "bone68", "parent": "bone67", "length": 35, "rotation": -0.99, "x": 34.75, "y": 0.13, "color": "022c6fff"}, {"name": "bone69", "parent": "bone68", "length": 35, "rotation": -10.35, "x": 36.28, "y": 0.78, "color": "022c6fff"}, {"name": "bone70", "parent": "bone2", "length": 35, "rotation": -72.89, "x": 9.36, "y": -2.71, "color": "022c6fff"}, {"name": "bone71", "parent": "bone70", "length": 35, "rotation": -5.41, "x": 33.99, "y": 0.51, "color": "022c6fff"}, {"name": "bone72", "parent": "bone71", "length": 35, "rotation": 4.55, "x": 35.95, "y": 0.05, "color": "022c6fff"}, {"name": "bone40", "parent": "bone38", "length": 8.69, "rotation": -3.69, "x": 14.55, "y": 5.2}, {"name": "bone47", "parent": "bone40", "length": 14.74, "rotation": -4.6, "x": 8.69}, {"name": "bone63", "parent": "root", "x": 49.46, "y": 384.69, "scaleX": 1.7354, "scaleY": 1.7354}, {"name": "bone30", "parent": "bone2", "length": 73.12, "rotation": -74.84, "x": -26.53, "y": -23.51, "color": "022c6fff"}, {"name": "bone64", "parent": "bone30", "length": 76.16, "rotation": -43.37, "x": 73.12, "color": "022c6fff"}, {"name": "bone65", "parent": "bone2", "length": 80.56, "rotation": -64.22, "x": 2.06, "y": -23.1, "color": "022c6fff"}, {"name": "bone66", "parent": "bone65", "length": 69.06, "rotation": -54.93, "x": 80.33, "y": 0.11, "color": "022c6fff"}, {"name": "bone55", "parent": "bone", "length": 35.64, "rotation": -0.73, "x": -42.48, "y": 14.61}, {"name": "bone59", "parent": "bone55", "length": 33.98, "rotation": 90.47, "x": -0.3, "y": 0.15}, {"name": "rjio1", "parent": "bone59", "rotation": -89.75, "x": 75.05, "y": -19.8, "color": "ff3f00ff"}, {"name": "rjio2", "parent": "bone59", "rotation": -89.79, "x": 11.64, "y": -9.39, "color": "ff3f00ff"}, {"name": "rjio3", "parent": "rjio2", "x": 7.98, "y": -17.93, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone60", "parent": "bone", "length": 33.04, "rotation": 1.26, "x": 9.86, "y": 13.74}, {"name": "bone61", "parent": "bone60", "length": 34.41, "rotation": 89.74, "x": -0.45, "y": -0.07}, {"name": "ljio1", "parent": "bone61", "rotation": -91, "x": 74.09, "y": -10.12, "color": "ff3f00ff"}, {"name": "ljio2", "parent": "bone61", "rotation": -91, "x": 18.72, "y": -5.57, "color": "ff3f00ff"}, {"name": "ljio3", "parent": "ljio2", "x": 20.21, "y": -14.58, "transform": "noScale", "color": "ff3f00ff"}], "slots": [{"name": "tf54", "bone": "root", "attachment": "tf54"}, {"name": "sss2", "bone": "root", "attachment": "sss2"}, {"name": "jio2", "bone": "root", "attachment": "jio2"}, {"name": "jio1", "bone": "root", "attachment": "jio1"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "pd1", "bone": "bone2", "attachment": "pd1"}, {"name": "qb2", "bone": "bone2", "attachment": "qb2"}, {"name": "qb1", "bone": "bone2", "attachment": "qb1"}, {"name": "yaoj", "bone": "bone2", "attachment": "yaoj"}, {"name": "tou", "bone": "bone5", "attachment": "tou"}, {"name": "biyan", "bone": "bone6"}, {"name": "tf1", "bone": "bone5", "attachment": "tf1"}, {"name": "erduo", "bone": "bone5", "attachment": "erduo"}, {"name": "tf7", "bone": "bone5", "attachment": "tf7"}, {"name": "tf2", "bone": "root", "attachment": "tf2"}, {"name": "tf11", "bone": "root", "attachment": "tf11"}, {"name": "toug", "bone": "bone5", "attachment": "toug"}, {"name": "toushi", "bone": "bone5", "attachment": "toushi"}, {"name": "jio5", "bone": "bone56"}, {"name": "jio3", "bone": "bone52"}, {"name": "tf3", "bone": "bone5"}, {"name": "xiabai", "bone": "bone3"}, {"name": "pd2", "bone": "bone2"}, {"name": "yd", "bone": "bone3"}, {"name": "tou2", "bone": "bone5"}, {"name": "biyan2", "bone": "bone6"}, {"name": "tf4", "bone": "bone5"}, {"name": "erduo2", "bone": "bone5"}, {"name": "tf5", "bone": "bone5"}, {"name": "pd4111", "bone": "bone13"}, {"name": "toushi2", "bone": "bone13"}, {"name": "tf6", "bone": "bone13"}, {"name": "sss3", "bone": "bone33"}, {"name": "yiyi4", "bone": "bone2"}, {"name": "zanzi", "bone": "bone5"}, {"name": "jio6", "bone": "bone56"}, {"name": "jio4", "bone": "bone52"}, {"name": "yiyi2", "bone": "bone2"}, {"name": "body2", "bone": "bone3"}, {"name": "tou3", "bone": "bone5"}, {"name": "toufa111", "bone": "bone5"}, {"name": "erduo3", "bone": "bone5"}, {"name": "yi2", "bone": "bone2"}, {"name": "mutou", "bone": "bone62"}, {"name": "biyan3", "bone": "bone6"}, {"name": "ss3", "bone": "bone36"}, {"name": "tf234", "bone": "bone14"}, {"name": "wuqi3", "bone": "bone62", "attachment": "wuqi3"}, {"name": "ss1", "bone": "bone36", "attachment": "ss1"}, {"name": "mutou3", "bone": "bone63"}, {"name": "xuanz", "bone": "bone63", "color": "ffffff6f", "blend": "additive"}], "ik": [{"name": "ljio1", "order": 7, "bones": ["bone56"], "target": "ljio1", "compress": true, "stretch": true}, {"name": "ljio2", "order": 8, "bones": ["bone57"], "target": "ljio2", "compress": true, "stretch": true}, {"name": "ljio3", "order": 9, "bones": ["bone58"], "target": "ljio3"}, {"name": "ljio4", "order": 5, "bones": ["bone65", "bone66"], "target": "ljio2", "bendPositive": false}, {"name": "ljio5", "bones": ["bone30", "bone64"], "target": "rjio2", "bendPositive": false}, {"name": "rjio1", "order": 2, "bones": ["bone52"], "target": "rjio1", "compress": true, "stretch": true}, {"name": "rjio2", "order": 3, "bones": ["bone53"], "target": "rjio2", "compress": true, "stretch": true}, {"name": "rjio3", "order": 4, "bones": ["bone54"], "target": "rjio3"}], "transform": [{"name": "ljio5", "order": 6, "bones": ["ljio1"], "target": "bone66", "rotation": 118.7, "x": 18.7, "y": -23.48, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "rjio5", "order": 1, "bones": ["rjio1"], "target": "bone64", "rotation": 118.05, "x": 15.21, "y": -20.36, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"qb1": {"qb1": {"type": "mesh", "uvs": [0.09605, 0, 0.21778, 0.00567, 0.30673, 0.08578, 0.43314, 0.21985, 0.53146, 0.31467, 0.51273, 0.38007, 0.61573, 0.52885, 0.66723, 0.67108, 0.75619, 0.79697, 0.8826, 0.9294, 0.92005, 1, 0.76622, 0.99536, 0.63914, 0.99153, 0.41442, 0.86727, 0.3616, 0.77583, 0.30205, 0.67272, 0.22246, 0.52231, 0.15692, 0.34737, 0.06328, 0.21331, 0, 0.1054, 0, 0.01058, 0.13819, 0.08251, 0.20373, 0.21985, 0.27396, 0.3441, 0.34887, 0.47816, 0.4191, 0.61386, 0.51273, 0.7234, 0.60637, 0.84275, 0.73278, 0.94902], "triangles": [25, 6, 7, 15, 16, 25, 26, 25, 7, 15, 25, 26, 14, 15, 26, 26, 7, 8, 27, 26, 8, 14, 26, 27, 13, 14, 27, 27, 8, 9, 28, 27, 9, 28, 13, 27, 12, 13, 28, 11, 28, 9, 12, 28, 11, 11, 9, 10, 24, 23, 5, 24, 16, 17, 24, 5, 6, 25, 24, 6, 16, 24, 25, 23, 3, 4, 5, 23, 4, 24, 17, 23, 23, 22, 3, 17, 18, 22, 17, 22, 23, 21, 0, 1, 20, 0, 21, 21, 1, 2, 19, 20, 21, 18, 19, 21, 22, 18, 21, 2, 22, 21, 3, 22, 2], "vertices": [1, 51, -7.07, 6.21, 1, 1, 51, -4.75, 11.09, 1, 2, 51, 6.06, 11.73, 0.99963, 52, -28.87, 8.54, 0.00037, 2, 51, 23.84, 11.86, 0.677, 52, -11.17, 10.35, 0.323, 2, 51, 36.54, 12.32, 0.01012, 52, 1.42, 12, 0.98988, 1, 52, 9.3, 9.43, 1, 2, 52, 28.58, 9.82, 0.93642, 53, -6.57, 10.33, 0.06358, 1, 53, 11.23, 7.27, 1, 1, 53, 27.54, 6.39, 1, 1, 53, 45.13, 6.84, 1, 1, 53, 54.12, 5.82, 1, 1, 53, 51.58, -0.48, 1, 1, 53, 49.49, -5.69, 1, 1, 53, 31.63, -10.58, 1, 1, 53, 19.93, -9.44, 1, 2, 52, 43.32, -7.55, 0.03394, 53, 6.74, -8.16, 0.96606, 1, 52, 24.05, -6.9, 1, 2, 51, 35.42, -4.63, 0.90078, 52, 1.91, -4.98, 0.09922, 1, 51, 18.08, -3.38, 1, 1, 51, 4.28, -1.87, 1, 1, 51, -7.09, 1.78, 1, 1, 51, 3.4, 4.8, 1, 2, 51, 20.75, 2.25, 0.97617, 52, -13.34, 0.49, 0.02383, 1, 52, 2.61, 0.14, 1, 1, 52, 19.82, -0.27, 1, 2, 52, 37.18, -0.93, 0.02946, 53, 1.15, -1.07, 0.97054, 1, 53, 15.55, -1.16, 1, 1, 53, 31.14, -1.6, 1, 1, 53, 45.57, -0.18, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 20, 22, 22, 24], "width": 44, "height": 126}}, "qb2": {"qb2": {"type": "mesh", "uvs": [0.92872, 0, 1, 0.03462, 1, 0.14253, 1, 0.25697, 0.89561, 0.3142, 0.82204, 0.46461, 0.75951, 0.5905, 0.69329, 0.72129, 0.57926, 0.85699, 0.39165, 0.95999, 0.13047, 1, 0, 1, 0, 0.93383, 0.13047, 0.82429, 0.18565, 0.67551, 0.23715, 0.53, 0.25922, 0.41883, 0.31072, 0.3191, 0.47626, 0.19485, 0.57558, 0.09348, 0.70433, 0.00683, 0.82204, 0.10166, 0.69329, 0.23408, 0.57926, 0.37796, 0.49833, 0.50385, 0.43579, 0.6608, 0.36958, 0.78669, 0.27394, 0.88805, 0.10472, 0.95999], "triangles": [26, 25, 7, 13, 14, 26, 8, 26, 7, 27, 13, 26, 27, 26, 8, 12, 13, 27, 9, 27, 8, 28, 12, 27, 28, 27, 9, 11, 12, 28, 10, 28, 9, 11, 28, 10, 25, 15, 24, 25, 24, 6, 14, 15, 25, 7, 25, 6, 26, 14, 25, 24, 23, 5, 15, 16, 24, 6, 24, 5, 21, 20, 0, 21, 0, 1, 19, 20, 21, 21, 1, 2, 22, 19, 21, 22, 21, 2, 18, 19, 22, 22, 2, 3, 4, 22, 3, 23, 18, 22, 23, 22, 4, 17, 18, 23, 16, 17, 23, 5, 23, 4, 24, 16, 23], "vertices": [1, 48, -17.36, 5.21, 1, 1, 48, -14.04, 10.1, 1, 1, 48, -0.81, 13.24, 1, 2, 48, 13.22, 16.57, 0.96688, 49, -21.81, 16.07, 0.03312, 2, 48, 21.59, 12.55, 0.78778, 49, -13.37, 12.19, 0.21222, 2, 49, 6.01, 12.9, 0.99932, 50, -31.96, 6.48, 0.00068, 2, 49, 22.24, 13.43, 0.8267, 50, -16.08, 9.92, 0.1733, 2, 49, 39.13, 13.92, 0.0354, 50, 0.44, 13.44, 0.9646, 1, 50, 18.64, 14.78, 1, 1, 50, 34.82, 10.65, 1, 1, 50, 45.51, -0.53, 1, 1, 50, 48.56, -7.17, 1, 1, 50, 40.99, -10.65, 1, 2, 49, 59.51, -13.4, 0.00665, 50, 25.4, -9.78, 0.99335, 3, 48, 75.07, -15.62, 0.0003, 49, 40.58, -15.05, 0.57577, 50, 7.07, -14.8, 0.42394, 2, 48, 56.56, -17.04, 0.12616, 49, 22.1, -16.8, 0.87384, 2, 48, 42.65, -19.08, 0.64363, 49, 8.23, -19.07, 0.35637, 2, 48, 29.76, -19.17, 0.98591, 49, -4.66, -19.39, 0.01409, 1, 48, 12.38, -13.77, 1, 1, 48, -1.33, -11.31, 1, 1, 48, -13.62, -6.82, 1, 1, 48, -3.52, 2.36, 1, 1, 48, 14.38, -0.8, 1, 1, 48, 33.5, -2.83, 1, 2, 48, 49.98, -3.58, 0.05357, 49, 15.29, -3.44, 0.94643, 3, 48, 70.03, -2.41, 8e-05, 49, 35.32, -1.94, 0.96297, 50, -0.46, -2.85, 0.03695, 1, 50, 15.5, 0.41, 1, 1, 50, 29.34, 0.88, 1, 1, 50, 41.53, -3.95, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 56, "height": 126}}, "body": {"body": {"type": "mesh", "uvs": [0.44054, 0, 0.51707, 0.03366, 0.57499, 0.10287, 0.57292, 0.2329, 0.6267, 0.29791, 0.72806, 0.33776, 0.82942, 0.39439, 0.84389, 0.48876, 0.8501, 0.56636, 0.91215, 0.65654, 0.92663, 0.77608, 0.8832, 0.87255, 0.83769, 0.96902, 0.7322, 0.97741, 0.61636, 0.98161, 0.4757, 0.99838, 0.32264, 0.9879, 0.22749, 0.91659, 0.14268, 0.80963, 0.05787, 0.69848, 0, 0.62718, 0.00823, 0.49715, 0.05166, 0.35034, 0.16129, 0.32098, 0.26472, 0.27484, 0.27299, 0.16579, 0.29988, 0.06722, 0.35573, 0.01898, 0.44261, 0.10078, 0.44881, 0.23919, 0.49432, 0.34615, 0.58327, 0.48457, 0.65566, 0.62298, 0.65359, 0.77818, 0.65773, 0.9145, 0.70944, 0.42794, 0.72185, 0.52232, 0.79632, 0.63347, 0.79218, 0.78237, 0.75288, 0.9124, 0.26679, 0.36503, 0.28954, 0.48037, 0.38469, 0.63976, 0.41779, 0.80125, 0.44881, 0.93127, 0.13027, 0.52441, 0.21301, 0.67332, 0.29781, 0.82641], "triangles": [18, 46, 47, 18, 19, 46, 46, 19, 45, 19, 20, 45, 46, 45, 41, 20, 21, 45, 21, 22, 45, 45, 40, 41, 45, 23, 40, 45, 22, 23, 23, 24, 40, 38, 9, 10, 38, 37, 9, 38, 33, 37, 33, 32, 37, 37, 8, 9, 32, 36, 37, 37, 36, 8, 32, 31, 36, 36, 7, 8, 31, 35, 36, 36, 35, 7, 35, 6, 7, 35, 5, 6, 35, 4, 5, 25, 29, 24, 25, 28, 29, 29, 40, 24, 30, 29, 3, 25, 26, 28, 26, 27, 28, 29, 28, 3, 3, 28, 2, 28, 1, 2, 27, 0, 28, 28, 0, 1, 47, 42, 43, 47, 46, 42, 33, 43, 32, 32, 43, 42, 46, 41, 42, 42, 31, 32, 31, 41, 30, 31, 42, 41, 31, 4, 35, 31, 30, 4, 30, 41, 40, 30, 40, 29, 30, 3, 4, 16, 44, 15, 15, 44, 14, 17, 47, 16, 44, 47, 43, 44, 16, 47, 14, 34, 13, 14, 44, 34, 13, 39, 12, 13, 34, 39, 12, 39, 11, 34, 44, 33, 17, 18, 47, 44, 43, 33, 34, 33, 39, 39, 38, 11, 39, 33, 38, 11, 38, 10], "vertices": [1, 5, 21.2, -4.2, 1, 2, 5, 18.08, -9.43, 0.99998, 27, 38.33, 12.59, 2e-05, 3, 4, 46.16, -14.57, 0.0022, 5, 12.59, -12.98, 0.99402, 27, 32.77, 9.17, 0.00378, 3, 4, 37.16, -12.02, 0.1998, 5, 3.33, -11.62, 0.72657, 27, 23.54, 10.74, 0.07363, 3, 4, 31.62, -14.62, 0.41437, 5, -1.82, -14.91, 0.28941, 27, 18.31, 7.57, 0.29622, 3, 4, 26.95, -21.03, 0.23287, 5, -5.62, -21.88, 0.07141, 27, 14.35, 0.7, 0.69572, 3, 4, 21.11, -27.14, 0.03922, 5, -10.62, -28.69, 0.01492, 27, 9.2, -6, 0.94586, 4, 3, 47.04, -24.78, 0.00199, 4, 14.27, -26.42, 0.00018, 5, -17.49, -28.86, 0.00217, 27, 2.32, -6.01, 0.99567, 2, 3, 41.45, -24.4, 0.07349, 27, -3.27, -5.61, 0.92651, 2, 3, 34.35, -27.91, 0.26408, 27, -10.38, -9.1, 0.73592, 2, 3, 25.69, -27.67, 0.44567, 27, -19.04, -8.83, 0.55433, 3, 3, 19.29, -23.5, 0.61406, 4, -13.17, -22.1, 0.00015, 27, -25.43, -4.64, 0.38579, 2, 3, 12.92, -19.17, 0.73626, 27, -31.79, -0.3, 0.26374, 2, 3, 13.47, -11.47, 0.83644, 27, -31.21, 7.4, 0.16356, 2, 3, 14.43, -3.06, 0.9759, 27, -30.23, 15.8, 0.0241, 3, 3, 14.77, 7.27, 0.9413, 4, -14.29, 8.97, 0.03205, 22, -40.76, -21.24, 0.02665, 3, 3, 17.19, 18.21, 0.69701, 4, -10.7, 19.58, 0.17407, 22, -38.31, -10.31, 0.12892, 3, 3, 23.3, 24.31, 0.48252, 4, -3.95, 24.98, 0.26841, 22, -32.18, -4.23, 0.24908, 3, 3, 31.84, 29.28, 0.23662, 4, 5.08, 28.99, 0.28105, 22, -23.63, 0.72, 0.48233, 3, 3, 40.68, 34.21, 0.07566, 4, 14.4, 32.92, 0.12908, 22, -14.78, 5.62, 0.79526, 3, 3, 46.38, 37.62, 0.03041, 4, 20.45, 35.68, 0.03671, 22, -9.06, 9.01, 0.93288, 2, 3, 55.55, 35.63, 0.00276, 22, 0.1, 7, 0.99724, 3, 4, 38.74, 26.92, 0.04242, 5, -0.15, 27.2, 0.00325, 22, 10.07, 2.26, 0.95432, 3, 4, 38.73, 18.65, 0.25783, 5, 0.92, 18.99, 0.053, 22, 10.94, -5.97, 0.68917, 3, 4, 40.01, 10.5, 0.37536, 5, 3.24, 11.08, 0.41278, 22, 13.07, -13.94, 0.21186, 3, 4, 47.44, 7.9, 0.05234, 5, 10.94, 9.47, 0.92642, 22, 20.74, -15.73, 0.02124, 3, 4, 53.8, 4.18, 0.00022, 5, 17.73, 6.61, 0.99955, 22, 27.46, -18.75, 0.00023, 1, 5, 20.65, 2.11, 1, 2, 5, 13.99, -3.41, 0.99994, 27, 34.38, 18.7, 6e-05, 3, 4, 39.04, -3.15, 0.02215, 5, 4.05, -2.58, 0.97203, 27, 24.47, 19.76, 0.00582, 3, 4, 30.75, -4.39, 0.74329, 5, -4.02, -4.88, 0.20843, 27, 16.35, 17.65, 0.04827, 3, 4, 19.45, -8.11, 0.71669, 5, -14.74, -10.03, 0.01782, 27, 5.51, 12.75, 0.26549, 3, 3, 39.54, -9.75, 0.05504, 4, 8.46, -10.66, 0.51613, 27, -5.14, 9.04, 0.42883, 3, 3, 28.51, -7.94, 0.5934, 4, -2.3, -7.64, 0.22982, 27, -16.16, 10.89, 0.17677, 3, 3, 18.76, -6.77, 0.91735, 4, -11.87, -5.42, 0.00078, 27, -25.91, 12.08, 0.08187, 3, 4, 21.03, -18.05, 0.21923, 5, -11.88, -19.69, 0.03659, 27, 8.14, 3.03, 0.74419, 4, 3, 45.98, -15.61, 0.00247, 4, 14.23, -17.19, 0.14326, 5, -18.74, -19.71, 0.00392, 27, 1.29, 3.16, 0.85035, 3, 3, 37.26, -19.79, 0.15836, 4, 5.1, -20.39, 0.05355, 27, -7.45, -0.99, 0.78809, 3, 3, 26.7, -17.9, 0.51274, 4, -5.19, -17.35, 0.04643, 27, -18, 0.94, 0.44083, 3, 3, 17.87, -13.66, 0.78189, 4, -13.5, -12.17, 0.00342, 27, -26.82, 5.2, 0.21469, 3, 4, 33.69, 12.02, 0.53691, 5, -3.22, 11.77, 0.09528, 22, 6.63, -13.1, 0.3678, 4, 3, 53.68, 15.14, 0.00331, 4, 25.24, 12.54, 0.61914, 5, -11.67, 11.19, 0.00089, 22, -1.83, -13.48, 0.37666, 3, 3, 41.3, 9.99, 0.04687, 4, 12.37, 8.77, 0.80928, 22, -14.23, -18.6, 0.14385, 3, 3, 29.44, 9.33, 0.46858, 4, 0.51, 9.42, 0.44897, 22, -26.09, -19.22, 0.08246, 3, 3, 19.84, 8.49, 0.87716, 4, -9.12, 9.63, 0.07977, 22, -35.68, -20.04, 0.04307, 3, 3, 52.28, 27.11, 0.0092, 4, 25.16, 24.59, 0.07798, 22, -3.19, -1.51, 0.91282, 3, 3, 40.78, 22.74, 0.11056, 4, 13.25, 21.51, 0.35084, 22, -14.71, -5.85, 0.5386, 3, 3, 28.95, 18.26, 0.4151, 4, 1, 18.35, 0.34803, 22, -26.54, -10.29, 0.23688], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 86, 88, 90, 92, 92, 94], "width": 73, "height": 72}}, "tf7": {"tf7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [18.07, 10.48, 20.01, 25.36, 52.73, 21.1, 50.8, 6.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 33}}, "wuqi3": {"wuqi3": {"x": 53.06, "y": 1.54, "rotation": -34.64, "width": 164, "height": 119}}, "tf1": {"tf1": {"x": 59.79, "y": -6.04, "rotation": -97.41, "width": 82, "height": 61}}, "toug": {"toug": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39.82, -54.87, 51.95, 38.35, 106.49, 31.25, 94.36, -61.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 94, "height": 55}}, "ss1": {"ss1": {"type": "mesh", "uvs": [0.75858, 0.38151, 0.78676, 0.35011, 0.86189, 0.27432, 0.9558, 0.18121, 1, 0.1065, 0.94328, 0.03071, 0.86189, 0.0004, 0.63963, 0.00148, 0.49876, 0.04695, 0.46432, 0.09892, 0.39545, 0.16389, 0.3485, 0.24834, 0.3078, 0.30031, 0.18571, 0.30139, 0, 0.30572, 0.03232, 0.34903, 0.1638, 0.38259, 0.24832, 0.38584, 0.22328, 0.45297, 0.2045, 0.52334, 0.18571, 0.60888, 0.19511, 0.68791, 0.27337, 0.76695, 0.39545, 0.78103, 0.52067, 0.76695, 0.54884, 0.82758, 0.59893, 0.88713, 0.65528, 0.95751, 0.75232, 1, 0.84936, 0.97916, 0.89632, 0.92719, 0.97458, 0.8514, 1, 0.78103, 0.9965, 0.67817, 0.93076, 0.57207, 0.83371, 0.48328, 0.7711, 0.4194, 0.74606, 0.08268, 0.62397, 0.18987, 0.55823, 0.27757, 0.49875, 0.35444, 0.28589, 0.34361, 0.10745, 0.33387, 0.49562, 0.40533, 0.51128, 0.48328, 0.55823, 0.57531, 0.60519, 0.6576, 0.65215, 0.7323, 0.72415, 0.8265, 0.76171, 0.91853, 0.76171, 0.96725], "triangles": [49, 48, 31, 26, 48, 49, 27, 26, 49, 28, 27, 50, 50, 49, 30, 30, 49, 31, 29, 50, 30, 28, 50, 29, 50, 27, 49, 31, 48, 32, 26, 25, 48, 25, 24, 48, 24, 47, 48, 48, 47, 32, 47, 33, 32, 23, 22, 24, 47, 24, 46, 47, 46, 33, 46, 34, 33, 46, 24, 21, 24, 22, 21, 21, 20, 46, 20, 45, 46, 46, 45, 34, 20, 19, 45, 19, 44, 45, 45, 35, 34, 45, 44, 35, 19, 18, 44, 44, 36, 35, 18, 43, 44, 44, 43, 36, 18, 17, 43, 43, 0, 36, 43, 17, 40, 17, 16, 41, 15, 42, 16, 16, 42, 41, 15, 14, 42, 43, 40, 0, 40, 17, 41, 0, 40, 1, 41, 12, 40, 40, 39, 1, 40, 12, 39, 1, 39, 2, 42, 13, 41, 41, 13, 12, 42, 14, 13, 12, 11, 39, 39, 38, 2, 39, 11, 38, 2, 38, 3, 11, 10, 38, 10, 9, 38, 38, 37, 3, 38, 9, 37, 3, 37, 4, 37, 5, 4, 9, 8, 37, 8, 7, 37, 37, 6, 5, 37, 7, 6], "vertices": [2, 23, 35.8, 12.22, 0.51133, 24, 3.29, 11.9, 0.48867, 2, 23, 31.43, 12.2, 0.81798, 24, -0.71, 13.67, 0.18202, 2, 23, 20.77, 12.48, 0.99956, 24, -10.33, 18.24, 0.00044, 1, 23, 7.66, 12.89, 1, 1, 23, -2.42, 11.86, 1, 1, 23, -11.26, 6.35, 1, 1, 23, -13.99, 1.57, 1, 1, 23, -10.79, -8.14, 1, 1, 23, -3.08, -12.51, 1, 1, 23, 3.99, -11.95, 1, 1, 23, 13.19, -12.39, 1, 2, 23, 24.55, -11.09, 0.98877, 24, -16.45, -4.83, 0.01123, 2, 23, 31.7, -10.8, 0.77579, 24, -9.79, -7.47, 0.22421, 2, 23, 33.52, -16.12, 0.55648, 24, -10.29, -13.07, 0.44352, 2, 23, 36.63, -24.1, 0.49737, 24, -10.68, -21.62, 0.50263, 2, 23, 41.68, -20.95, 0.48404, 24, -4.79, -20.8, 0.51596, 2, 23, 44.13, -13.85, 0.40894, 24, 0.33, -15.3, 0.59106, 2, 23, 43.38, -10.01, 0.26735, 24, 1.2, -11.48, 0.73265, 2, 23, 52.24, -8.43, 0.00281, 24, 9.94, -13.64, 0.99719, 1, 24, 19.14, -15.56, 1, 2, 24, 30.34, -17.71, 0.99957, 25, -10.41, -18.01, 0.00043, 2, 24, 40.84, -18.48, 0.89324, 25, 0.07, -18.99, 0.10676, 2, 24, 51.69, -16.09, 0.59082, 25, 10.96, -16.83, 0.40918, 2, 24, 54.19, -10.73, 0.44982, 25, 13.57, -11.51, 0.55018, 2, 24, 52.98, -4.79, 0.14466, 25, 12.49, -5.56, 0.85534, 2, 25, 20.65, -5.35, 0.56, 54, 6.77, -10.13, 0.44, 2, 54, 14.83, -8.39, 0.544, 55, 6.8, -7.87, 0.456, 1, 55, 16.14, -5.18, 1, 1, 55, 21.74, -0.66, 1, 2, 25, 42.48, 5.65, 0.008, 55, 18.92, 3.78, 0.992, 1, 55, 11.99, 5.86, 1, 2, 54, 11.3, 9.18, 0.728, 55, 1.87, 9.36, 0.272, 2, 25, 17.3, 16.04, 0.568, 54, 2.05, 11, 0.432, 2, 24, 43.74, 18.3, 0.09458, 25, 3.72, 17.72, 0.90542, 2, 24, 29.37, 16.89, 0.67031, 25, -10.67, 16.61, 0.32969, 3, 23, 47.68, 19.57, 0.0067, 24, 17.13, 13.8, 0.95996, 25, -22.97, 13.77, 0.03334, 3, 23, 40.43, 14.27, 0.16892, 24, 8.37, 11.9, 0.83045, 25, -31.78, 12.05, 0.00063, 1, 23, -1.95, -0.24, 1, 1, 23, 13.33, -1.33, 1, 1, 23, 25.37, -0.72, 1, 1, 23, 35.94, -0.27, 1, 2, 23, 37.5, -10.04, 0.52447, 24, -4.18, -9.13, 0.47553, 2, 23, 38.72, -18.26, 0.49306, 24, -6.4, -17.14, 0.50694, 1, 24, 5.07, -0.47, 1, 1, 24, 15.45, -0.94, 1, 1, 24, 27.86, -0.18, 1, 2, 24, 38.97, 0.72, 0.71436, 25, -1.4, 0.25, 0.28564, 1, 25, 8.73, 1.05, 1, 2, 54, 7.19, -2.08, 0.512, 55, -1.32, -2.19, 0.488, 1, 55, 10.9, -0.34, 1, 1, 55, 17.38, -0.27, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 12, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 80, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100], "width": 46, "height": 133}}, "tf54": {"tf54": {"type": "mesh", "uvs": [0.46248, 0, 0.57966, 0.00632, 0.6708, 0.03207, 0.7173, 0.08787, 0.71172, 0.17372, 0.71358, 0.28532, 0.74334, 0.38297, 0.7917, 0.43662, 0.85494, 0.41516, 0.85866, 0.35185, 0.90888, 0.33897, 0.95538, 0.37975, 0.9777, 0.45594, 0.95166, 0.51496, 0.894, 0.53856, 0.83076, 0.53105, 0.87912, 0.58471, 0.96096, 0.64265, 1, 0.72206, 1, 0.84224, 1, 0.96672, 0.87354, 0.9635, 0.7452, 0.96887, 0.58823, 1, 0.43677, 1, 0.29556, 0.98994, 0.12159, 0.96986, 0, 0.91201, 0, 0.80692, 0, 0.70183, 0, 0.58966, 0, 0.46922, 0.06224, 0.38184, 0.15638, 0.32044, 0.19118, 0.2555, 0.17276, 0.15513, 0.19322, 0.06185, 0.35082, 0.00636, 0.46543, 0.11381, 0.46747, 0.22362, 0.45724, 0.33225, 0.45519, 0.44442, 0.46338, 0.55423, 0.47361, 0.70419, 0.50431, 0.85769, 0.76629, 0.82935, 0.71921, 0.68294, 0.66805, 0.5625, 0.62916, 0.4397, 0.59846, 0.32634, 0.59232, 0.22716, 0.58618, 0.13624, 0.59232, 0.06776, 0.91774, 0.41254, 0.89318, 0.47866, 0.80722, 0.47984, 0.73354, 0.47984, 0.32216, 0.10318, 0.33035, 0.17284, 0.33444, 0.26494, 0.30988, 0.3476, 0.271, 0.44678, 0.23416, 0.57313, 0.2096, 0.70183, 0.19527, 0.83053], "triangles": [30, 31, 62, 63, 30, 62, 29, 30, 63, 28, 29, 63, 62, 61, 42, 64, 28, 63, 62, 42, 43, 63, 62, 43, 27, 28, 64, 26, 27, 64, 64, 43, 44, 43, 64, 63, 25, 64, 44, 26, 64, 25, 24, 25, 44, 60, 59, 40, 33, 34, 60, 41, 60, 40, 61, 33, 60, 61, 60, 41, 32, 33, 61, 31, 32, 61, 62, 31, 61, 61, 41, 42, 57, 36, 37, 37, 0, 38, 57, 37, 38, 35, 36, 57, 58, 57, 38, 35, 57, 58, 58, 38, 39, 34, 35, 58, 59, 58, 39, 34, 58, 59, 40, 59, 39, 60, 34, 59, 14, 15, 54, 42, 48, 47, 47, 16, 46, 15, 47, 56, 16, 47, 15, 43, 42, 47, 43, 47, 46, 46, 18, 45, 17, 46, 16, 18, 46, 17, 45, 18, 19, 45, 44, 43, 45, 43, 46, 21, 45, 19, 21, 19, 20, 22, 44, 45, 22, 45, 21, 23, 24, 44, 22, 23, 44, 49, 5, 6, 10, 53, 9, 11, 53, 10, 8, 9, 53, 48, 49, 6, 48, 6, 7, 41, 40, 49, 48, 41, 49, 53, 11, 12, 54, 8, 53, 54, 53, 12, 7, 8, 54, 56, 48, 7, 55, 7, 54, 56, 7, 55, 13, 54, 12, 15, 55, 54, 13, 14, 54, 42, 41, 48, 47, 48, 56, 56, 55, 15, 52, 1, 2, 52, 2, 3, 52, 38, 0, 52, 0, 1, 51, 38, 52, 51, 52, 3, 4, 51, 3, 39, 38, 51, 50, 51, 4, 39, 51, 50, 50, 4, 5, 49, 50, 5, 49, 40, 39, 49, 39, 50], "vertices": [2, 19, -33.34, -14.06, 0.38284, 16, -28.66, 8.36, 0.61716, 2, 19, -28.21, 2.83, 0.75752, 16, -30.68, 25.89, 0.24248, 2, 19, -18.91, 14.88, 0.94546, 16, -26.95, 40.65, 0.05454, 1, 19, -3.3, 18.81, 1, 1, 19, 18.4, 13.51, 1, 2, 19, 46.89, 7.97, 0.94141, 20, -5.32, 8.16, 0.05859, 1, 20, 20.4, 6.36, 1, 2, 20, 35.69, 10.03, 0.97995, 21, -15.6, 8.32, 0.02005, 2, 20, 32.56, 20.58, 0.94709, 21, -19.7, 18.53, 0.05291, 2, 20, 16.72, 25.1, 0.99984, 21, -35.89, 21.55, 0.00016, 1, 20, 15.29, 33.22, 1, 2, 20, 27.27, 37.43, 0.97951, 21, -26.55, 34.81, 0.02049, 2, 20, 47.3, 35.89, 0.8115, 21, -6.47, 35.16, 0.1885, 2, 20, 61.25, 28.4, 0.58849, 21, 8.12, 29, 0.41151, 2, 20, 65.11, 18.52, 0.44726, 21, 12.9, 19.53, 0.55274, 2, 20, 60.93, 9.79, 0.1833, 21, 9.55, 10.44, 0.8167, 1, 21, 24.43, 15.53, 1, 1, 21, 41.16, 25.42, 1, 2, 21, 62.45, 28.12, 0.99361, 18, 35.65, 129.39, 0.00639, 2, 21, 93.35, 23.45, 0.9421, 18, 66.38, 135.05, 0.0579, 2, 21, 125.35, 18.61, 0.91002, 18, 98.21, 140.91, 0.08998, 2, 21, 121.68, -0.02, 0.8766, 18, 100.82, 122.1, 0.1234, 2, 21, 120.19, -19.26, 0.77381, 18, 105.68, 103.42, 0.22619, 2, 21, 124.67, -43.75, 0.6155, 18, 117.9, 81.73, 0.3845, 2, 21, 121.28, -66.21, 0.47848, 18, 122.02, 59.38, 0.52152, 2, 21, 115.52, -86.77, 0.34672, 18, 123.28, 38.08, 0.65328, 2, 21, 106.46, -111.79, 0.22176, 18, 122.87, 11.47, 0.77824, 2, 21, 88.86, -127.57, 0.16074, 18, 111.37, -9.19, 0.83926, 2, 21, 61.85, -123.49, 0.0971, 18, 84.5, -14.14, 0.9029, 2, 21, 34.83, -119.41, 0.01578, 18, 57.63, -19.08, 0.98422, 2, 17, 84.11, -21.12, 0.00578, 18, 28.95, -24.36, 0.99422, 2, 17, 54.09, -30.06, 0.50236, 18, -1.85, -30.03, 0.49764, 2, 17, 29.66, -27.59, 0.95268, 18, -25.88, -24.96, 0.04732, 2, 16, 62.33, -19.39, 0.1155, 17, 10.33, -18.61, 0.8845, 2, 16, 44.73, -17.76, 0.81423, 17, -7.34, -18.42, 0.18577, 1, 16, 19.77, -25.85, 1, 1, 16, -4.59, -27.85, 1, 2, 19, -35.08, -30.8, 0.09498, 16, -23.59, -7.69, 0.90502, 2, 19, -4.26, -19.56, 0.39514, 16, 0.2, 14.89, 0.60486, 4, 19, 23.77, -24.98, 0.43282, 20, -29.8, -23.79, 0.00357, 16, 28.08, 21.08, 0.5567, 17, -27.11, 18.93, 0.00692, 6, 19, 51.13, -32.14, 0.20687, 20, -2.76, -32.1, 0.20351, 21, -49.93, -37.23, 0.00183, 16, 56.03, 25.4, 0.23194, 17, 0.39, 25.52, 0.35553, 18, -49.29, 30.98, 0.00033, 6, 19, 79.65, -38.29, 0.01128, 20, 25.47, -39.44, 0.34199, 21, -21.14, -41.89, 0.08314, 16, 84.63, 31.12, 0.00393, 17, 28.43, 33.55, 0.48452, 18, -20.55, 35.95, 0.07514, 4, 20, 53.47, -45.14, 0.14863, 21, 7.27, -44.94, 0.33552, 17, 55.45, 42.87, 0.1706, 18, 7.31, 42.33, 0.34525, 4, 20, 91.68, -53.07, 0.00422, 21, 46.05, -49.25, 0.50937, 17, 92.38, 55.46, 0.00492, 18, 45.38, 50.9, 0.48149, 2, 21, 86.2, -50.67, 0.55065, 18, 83.79, 62.65, 0.44935, 2, 21, 84.79, -10.71, 0.85265, 18, 69.43, 99.96, 0.14735, 3, 21, 46.1, -12, 0.92227, 17, 76.57, 89.2, 0.00081, 18, 33.27, 86.13, 0.07691, 4, 20, 62.97, -15.87, 0.07565, 21, 13.99, -14.91, 0.8338, 17, 48.75, 72.91, 0.0281, 18, 3.87, 72.91, 0.06245, 6, 19, 83.67, -12.48, 0.00025, 20, 30.58, -13.82, 0.82195, 21, -18.45, -15.91, 0.06174, 16, 78.05, 56.4, 0.00115, 17, 19.81, 58.21, 0.09658, 18, -26.48, 61.4, 0.01832, 6, 19, 53.88, -11.08, 0.41308, 20, 0.87, -11.18, 0.46403, 21, -48.28, -16.05, 1e-05, 16, 50.16, 45.81, 0.05207, 17, -7.12, 45.38, 0.07076, 18, -54.63, 51.53, 5e-05, 3, 19, 28.43, -6.82, 0.9181, 16, 25.12, 39.59, 0.07951, 17, -31.57, 37.14, 0.00239, 2, 19, 5.08, -2.98, 0.96297, 16, 2.17, 33.82, 0.03703, 2, 19, -12.18, 1.49, 0.88804, 16, -15.44, 31.05, 0.11196, 2, 20, 34.18, 29.89, 0.94164, 21, -18.97, 27.95, 0.05836, 2, 20, 49.97, 22.16, 0.70006, 21, -2.52, 21.74, 0.29994, 2, 20, 47.16, 9.58, 0.75859, 21, -4.14, 8.94, 0.24141, 3, 20, 44.49, -1.15, 0.99649, 17, 25.35, 76.19, 0.00234, 18, -19.05, 78.68, 0.00117, 1, 16, 1.93, -6.71, 1, 1, 16, 19.4, -1.77, 1, 3, 19, 30.3, -46.68, 0.0252, 20, -24.19, -45.75, 0.00357, 16, 42.71, 3.77, 0.97123, 4, 19, 50.62, -54.6, 0.01096, 20, -4.22, -54.52, 0.02206, 21, -49.29, -59.68, 0.00051, 17, 10.53, 5.47, 0.96647, 5, 19, 74.71, -65.48, 0.00012, 20, 19.39, -66.4, 0.02823, 21, -24.66, -69.3, 0.01055, 17, 36.91, 7.24, 0.94337, 18, -14.94, 8.89, 0.01773, 4, 20, 49.94, -79.7, 0.00948, 21, 6.99, -79.68, 0.036, 17, 69.97, 11.32, 0.01294, 18, 18.36, 9.4, 0.94158, 3, 20, 81.52, -91.35, 0.00016, 21, 39.53, -88.32, 0.09769, 18, 51.94, 11.84, 0.90216, 2, 21, 72.29, -95.45, 0.19501, 18, 85.24, 15.78, 0.80499], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 0, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 42, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 20, 106, 106, 108, 108, 110, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128], "width": 150, "height": 260}}, "tf11": {"tf11": {"type": "mesh", "uvs": [0, 0.21315, 0.04357, 0.15576, 0.19849, 0.07356, 0.41772, 0.01152, 0.60187, 0.02703, 0.76941, 0.08236, 0.88834, 0.12164, 0.92633, 0.2147, 0.85034, 0.30466, 0.83572, 0.44115, 0.85618, 0.5947, 0.84741, 0.72809, 0.7948, 0.84752, 0.96433, 0.83201, 0.9731, 0.8956, 0.94972, 0.97005, 0.72757, 1, 0.51418, 0.94833, 0.36218, 0.79633, 0.32711, 0.64743, 0.27157, 0.48613, 0.31541, 0.38531, 0.4148, 0.30001, 0.40603, 0.23797, 0.25988, 0.23952, 0.09034, 0.26899, 0.02895, 0.24882, 0.09618, 0.20229, 0.28034, 0.15266, 0.48788, 0.1356, 0.6428, 0.17748, 0.63403, 0.28915, 0.59018, 0.39152, 0.56095, 0.50629, 0.55803, 0.59625, 0.57849, 0.69397, 0.59018, 0.80099, 0.65157, 0.87854, 0.75387, 0.93127], "triangles": [11, 35, 10, 18, 19, 35, 36, 35, 11, 18, 35, 36, 12, 36, 11, 37, 36, 12, 12, 13, 14, 38, 37, 12, 38, 12, 14, 17, 18, 36, 17, 36, 37, 15, 38, 14, 38, 17, 37, 16, 38, 15, 16, 17, 38, 33, 21, 32, 33, 32, 9, 20, 21, 33, 33, 9, 10, 34, 20, 33, 34, 33, 10, 19, 20, 34, 35, 34, 10, 19, 34, 35, 29, 3, 4, 30, 29, 4, 5, 30, 4, 23, 28, 29, 23, 29, 30, 31, 23, 30, 30, 5, 6, 30, 6, 7, 22, 23, 31, 8, 30, 7, 31, 30, 8, 32, 22, 31, 21, 22, 32, 8, 32, 31, 9, 32, 8, 28, 2, 3, 29, 28, 3, 1, 2, 28, 27, 1, 28, 0, 1, 27, 24, 27, 28, 24, 28, 23, 26, 0, 27, 25, 26, 27, 25, 27, 24], "vertices": [1, 12, -4.81, 0.52, 1, 1, 12, -2.66, 2.65, 1, 1, 12, 2.63, 4.77, 1, 2, 12, 9.07, 5.32, 0.91736, 13, -5.34, -0.21, 0.08264, 2, 12, 13.17, 2.73, 0.32759, 13, -3.3, 4.19, 0.67241, 2, 12, 16.1, -1.47, 0.00633, 13, 0.49, 7.64, 0.99367, 1, 13, 3.19, 10.09, 1, 1, 13, 7.85, 9.8, 1, 2, 13, 11.55, 6.7, 0.9626, 14, -5.24, 5.12, 0.0374, 2, 13, 17.88, 4.51, 0.17115, 14, 1.45, 5.44, 0.82885, 2, 14, 8.88, 6.75, 0.86371, 15, -2.96, 7.65, 0.13629, 2, 14, 15.41, 7.2, 0.18888, 15, 3.49, 6.57, 0.81112, 1, 15, 9.11, 4.46, 1, 1, 15, 8.93, 8.93, 1, 1, 15, 12.05, 8.75, 1, 1, 15, 15.59, 7.67, 1, 1, 15, 16.29, 1.75, 1, 1, 15, 13.06, -3.42, 1, 2, 14, 20.04, -5, 0.02919, 15, 5.17, -6.37, 0.97081, 3, 13, 24.01, -10.96, 0.00104, 14, 12.88, -6.66, 0.74011, 15, -2.19, -6.33, 0.25885, 2, 13, 16.01, -10.2, 0.15272, 14, 5.17, -8.92, 0.84728, 2, 13, 11.56, -7.76, 0.4958, 14, 0.14, -8.3, 0.5042, 3, 12, 3.43, -7.65, 0.02148, 13, 8.25, -4.13, 0.90436, 14, -4.29, -6.16, 0.07416, 3, 12, 4.42, -4.76, 0.32518, 13, 5.26, -3.53, 0.67336, 14, -7.29, -6.7, 0.00146, 2, 12, 0.89, -3.33, 0.93975, 13, 4.3, -7.2, 0.06025, 2, 12, -3.73, -2.92, 1, 13, 4.49, -11.84, 0, 1, 12, -4.8, -1.39, 1, 1, 12, -2.3, 0.02, 1, 1, 12, 3.06, 0.37, 1, 2, 12, 8.35, -0.99, 0.05228, 13, 1.01, -0.11, 0.94772, 1, 13, 4.08, 3.21, 1, 1, 13, 9.29, 1.5, 1, 2, 13, 13.8, -0.97, 0.72562, 14, -0.3, -1.16, 0.27438, 2, 13, 19.01, -3.23, 0.0105, 14, 5.37, -1.33, 0.9895, 2, 13, 23.23, -4.5, 0, 14, 9.76, -0.95, 1, 1, 15, 0.92, -0.14, 1, 1, 15, 6.16, -0.52, 1, 1, 15, 10.14, 0.57, 1, 1, 15, 13.04, 2.87, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 28], "width": 26, "height": 49}}, "mutou3": {"mutou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.4, -51.34, -68.62, 12.5, -48.9, 51.12, 76.12, -12.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 164, "height": 119}}, "tou": {"tou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-3.99, -35.05, 5.3, 36.35, 91.58, 25.12, 82.29, -46.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 86}}, "yaoj": {"yaoj": {"x": -2.6, "y": -1.44, "rotation": -0.7, "width": 55, "height": 67}}, "jio1": {"jio1": {"type": "mesh", "uvs": [0.55098, 0, 0.79354, 0.02419, 1, 0.07317, 1, 0.19563, 0.94902, 0.31521, 0.79976, 0.43767, 0.70024, 0.52843, 0.73134, 0.57453, 0.64427, 0.62495, 0.55098, 0.71428, 0.48256, 0.79639, 0.51366, 0.85402, 0.71268, 0.89868, 0.82463, 0.94478, 0.77488, 0.99809, 0.48256, 0.99809, 0.22134, 0.96495, 0.12805, 0.92173, 0, 0.89868, 0.0161, 0.83961, 0.0161, 0.78487, 0.04098, 0.69411, 0, 0.62207, 0.04098, 0.57309, 0.09073, 0.52987, 0.0472, 0.46072, 0.00366, 0.34546, 0, 0.2158, 0.02232, 0.08614, 0.18403, 0.02275, 0.53232, 0.08038, 0.43903, 0.19707, 0.40793, 0.32529, 0.37683, 0.45928, 0.35817, 0.55292, 0.36505, 0.51842, 0.33329, 0.59326, 0.29598, 0.67538, 0.25244, 0.79783, 0.27732, 0.86987, 0.42037, 0.91885, 0.61939, 0.95631], "triangles": [15, 41, 14, 14, 41, 13, 16, 40, 15, 15, 40, 41, 16, 17, 40, 41, 12, 13, 41, 40, 12, 17, 39, 40, 17, 18, 39, 40, 11, 12, 40, 39, 11, 18, 19, 39, 19, 38, 39, 11, 39, 38, 11, 38, 10, 20, 21, 38, 10, 38, 9, 38, 21, 37, 9, 38, 37, 9, 37, 8, 21, 22, 37, 37, 36, 8, 37, 22, 36, 8, 36, 7, 22, 23, 36, 36, 34, 7, 36, 23, 34, 34, 6, 7, 23, 24, 34, 34, 35, 6, 34, 24, 35, 24, 25, 35, 35, 33, 6, 19, 20, 38, 6, 33, 5, 35, 25, 33, 25, 26, 33, 33, 32, 5, 33, 26, 32, 5, 32, 4, 26, 27, 32, 32, 31, 4, 32, 27, 31, 4, 31, 3, 27, 28, 31, 31, 30, 3, 31, 28, 30, 3, 30, 2, 30, 1, 2, 28, 29, 30, 30, 0, 1, 30, 29, 0], "vertices": [1, 39, -6.8, 12.31, 1, 1, 39, -8.65, 15.83, 1, 1, 39, 0.03, 24.29, 1, 1, 39, 21.75, 24.29, 1, 1, 39, 42.96, 22.2, 1, 2, 39, 64.67, 16.08, 0.95249, 40, -10.49, 15.28, 0.04751, 2, 39, 80.77, 11.99, 0.1636, 40, 6.04, 12.52, 0.8364, 2, 39, 88.94, 13.27, 0.00387, 40, 14.16, 14.46, 0.99613, 1, 40, 23.45, 11.62, 1, 1, 40, 39.71, 9.1, 1, 2, 40, 54.59, 7.48, 0.98568, 41, -3.75, 11.6, 0.01432, 2, 40, 64.77, 9.58, 0.18072, 41, 5.98, 7.92, 0.81928, 1, 41, 16.84, 11.42, 1, 1, 41, 26.28, 11.64, 1, 1, 41, 33.77, 5.39, 1, 2, 40, 90.59, 10.38, 0.00133, 41, 28.21, -5.22, 0.99867, 2, 40, 85.55, -0.77, 0.06603, 41, 17.98, -11.95, 0.93397, 2, 40, 78.15, -5.2, 0.23206, 41, 9.36, -11.73, 0.76794, 2, 40, 74.47, -10.77, 0.31063, 41, 3.27, -14.46, 0.68937, 2, 40, 63.87, -10.96, 0.25196, 41, -5.78, -8.95, 0.74804, 2, 40, 54.1, -11.75, 0.69092, 41, -14.46, -4.38, 0.30908, 2, 40, 37.82, -12.04, 0.99361, 41, -28.36, 4.09, 0.00639, 1, 40, 25.1, -14.75, 1, 2, 39, 88.69, -15.04, 0.00352, 40, 16.22, -13.78, 0.99648, 2, 39, 81.02, -13, 0.1288, 40, 8.34, -12.36, 0.8712, 2, 39, 68.76, -14.78, 0.8123, 40, -3.85, -15.14, 0.1877, 1, 39, 48.32, -16.56, 1, 1, 39, 26.09, -14.2, 1, 1, 39, -1.58, -4.72, 1, 1, 39, -5.03, -1.55, 1, 1, 39, 1.31, 5.12, 1, 1, 39, 22, 1.29, 1, 1, 39, 44.74, 0.01, 1, 2, 39, 68.5, -1.26, 0.99563, 40, -5.22, -1.69, 0.00437, 1, 40, 11.56, -1.1, 1, 2, 39, 78.99, -1.75, 0.00467, 40, 5.38, -1.32, 0.99533, 1, 40, 18.84, -1.54, 1, 1, 40, 33.62, -1.88, 1, 2, 40, 55.62, -1.9, 0.92353, 41, -7.9, 3.12, 0.07647, 2, 40, 68.39, 0.15, 0.30776, 41, 3.99, -1.98, 0.69224, 2, 40, 76.65, 6.7, 0.00583, 41, 14.47, -0.87, 0.99417, 1, 41, 24.19, 3.23, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 70, 70, 68, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82], "width": 41, "height": 177}}, "jio2": {"jio2": {"type": "mesh", "uvs": [0.32746, 0, 0.56532, 0.02796, 0.68755, 0.12357, 0.68425, 0.23998, 0.65782, 0.34703, 0.60826, 0.45408, 0.59175, 0.53099, 0.5488, 0.59959, 0.54538, 0.6695, 0.48549, 0.74052, 0.47167, 0.79415, 0.60988, 0.84198, 0.80799, 0.86082, 0.92317, 0.86807, 0.97846, 0.9072, 0.86328, 0.93764, 0.68821, 0.95938, 0.53156, 0.93619, 0.31963, 0.93619, 0.18142, 0.9246, 0.14456, 0.87821, 0.14917, 0.80429, 0.11692, 0.72747, 0.08467, 0.65065, 0.06163, 0.59992, 0.11692, 0.53905, 0.08928, 0.48832, 0.05242, 0.40425, 0.00635, 0.29699, 0, 0.17234, 0.04321, 0.06798, 0.12153, 0.0129, 0.32885, 0.07668, 0.33806, 0.17814, 0.30581, 0.27235, 0.30581, 0.37961, 0.30121, 0.48832, 0.31503, 0.54339, 0.29199, 0.61876, 0.28738, 0.69993, 0.30581, 0.80284, 0.37492, 0.87097, 0.57303, 0.89126, 0.80338, 0.9101], "triangles": [41, 40, 10, 17, 42, 16, 16, 43, 15, 16, 42, 43, 15, 43, 14, 42, 17, 41, 17, 18, 41, 43, 13, 14, 43, 12, 13, 43, 42, 12, 42, 11, 12, 42, 41, 11, 18, 19, 41, 19, 20, 41, 41, 20, 40, 41, 10, 11, 20, 21, 40, 21, 22, 40, 22, 39, 40, 10, 40, 9, 9, 40, 39, 9, 39, 8, 22, 23, 39, 39, 38, 8, 39, 23, 38, 8, 38, 7, 23, 24, 38, 24, 25, 38, 38, 37, 7, 38, 25, 37, 7, 37, 6, 25, 36, 37, 37, 36, 6, 25, 26, 36, 6, 36, 5, 5, 36, 35, 35, 36, 27, 36, 26, 27, 5, 35, 4, 27, 28, 35, 35, 34, 4, 35, 28, 34, 4, 34, 3, 28, 29, 34, 34, 29, 33, 34, 33, 3, 32, 29, 30, 29, 32, 33, 3, 33, 2, 33, 32, 2, 32, 1, 2, 30, 31, 32, 31, 0, 32, 32, 0, 1], "vertices": [1, 42, -14.14, 7.34, 1, 1, 42, -8, 20.16, 1, 1, 42, 9.56, 25.47, 1, 1, 42, 30.18, 23.45, 1, 1, 42, 49.03, 20.28, 1, 2, 42, 67.76, 15.83, 0.92862, 43, -10.62, 14.37, 0.07138, 2, 42, 81.32, 13.69, 0.28195, 43, 3.08, 14.33, 0.71805, 2, 42, 93.27, 10.21, 0.00219, 43, 15.4, 12.72, 0.99781, 1, 43, 27.81, 13.33, 1, 2, 43, 40.62, 10.8, 0.96815, 44, 1.27, 18.32, 0.03185, 2, 43, 50.18, 10.64, 0.50004, 44, 6.21, 10.13, 0.49996, 2, 43, 58.16, 18.91, 0.00544, 44, 17.46, 7.76, 0.99456, 1, 44, 28.42, 11.52, 1, 1, 44, 34.41, 14.25, 1, 1, 44, 40.98, 10.41, 1, 1, 44, 38.91, 2.25, 1, 1, 44, 33.21, -6.61, 1, 1, 44, 23.68, -8.39, 1, 1, 44, 14.05, -15.32, 1, 1, 44, 6.57, -18.17, 1, 2, 43, 66.26, -6.67, 0.05058, 44, 0.07, -12.69, 0.94942, 2, 43, 53.14, -7.27, 0.93631, 44, -7.39, -1.88, 0.06369, 1, 43, 39.63, -9.95, 1, 1, 43, 26.13, -12.63, 1, 2, 42, 90.91, -16.97, 0.00102, 43, 17.21, -14.5, 0.99898, 2, 42, 80.39, -12.92, 0.19899, 43, 6.22, -12.11, 0.80101, 2, 42, 71.26, -13.66, 0.76267, 43, -2.68, -14.24, 0.23733, 2, 42, 56.17, -14.39, 0.99994, 43, -17.45, -17.26, 6e-05, 1, 42, 36.92, -15.27, 1, 1, 42, 14.79, -13.65, 1, 1, 42, -3.5, -9.59, 1, 1, 42, -12.88, -4.35, 1, 1, 42, -0.54, 6.2, 1, 1, 42, 17.5, 5.11, 1, 1, 42, 34.04, 1.83, 1, 1, 42, 53.06, 0.13, 1, 2, 42, 72.31, -1.84, 0.95904, 43, -3.44, -2.4, 0.04096, 2, 42, 82.14, -1.94, 0.00534, 43, 6.28, -0.99, 0.99466, 1, 43, 19.72, -1.41, 1, 1, 43, 34.13, -0.74, 1, 2, 43, 52.32, 1.47, 0.86651, 44, -0.42, 3.45, 0.13349, 1, 44, 9.79, -4.11, 1, 1, 44, 20.9, -0.55, 1, 1, 44, 33.33, 4.27, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 0, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 56, "height": 178}}, "sss2": {"sss2": {"type": "mesh", "uvs": [0.24164, 0.06995, 0.37205, 0.06995, 0.46986, 0.08664, 0.50518, 0.15656, 0.55137, 0.23482, 0.57854, 0.31203, 0.62201, 0.32664, 0.71167, 0.32143, 0.78231, 0.32143, 0.77416, 0.35586, 0.7008, 0.39551, 0.65462, 0.40803, 0.71711, 0.43934, 0.79861, 0.48943, 0.87741, 0.55099, 0.94261, 0.61777, 1, 0.67412, 1, 0.72421, 0.86654, 0.72212, 0.80405, 0.72525, 0.75243, 0.78264, 0.70624, 0.86195, 0.66005, 0.93812, 0.60028, 1, 0.5242, 1, 0.4753, 0.92873, 0.43454, 0.85777, 0.35031, 0.79516, 0.31228, 0.71273, 0.27967, 0.62612, 0.29054, 0.53951, 0.29326, 0.47273, 0.28239, 0.42682, 0.2362, 0.38403, 0.17914, 0.31308, 0.12752, 0.23273, 0.1085, 0.16073, 0.13296, 0.10021, 0.29054, 0.15447, 0.33945, 0.23169, 0.38292, 0.30577, 0.42096, 0.37673, 0.44269, 0.4143, 0.49703, 0.49882, 0.5079, 0.58543, 0.55409, 0.67829, 0.57582, 0.77012, 0.57039, 0.8776, 0.56767, 0.95273, 0.64103, 0.49882, 0.71711, 0.57186, 0.80133, 0.65221, 0.94261, 0.70125, 0.69809, 0.35377, 0.56496, 0.37464], "triangles": [24, 48, 23, 23, 48, 22, 24, 25, 48, 22, 48, 47, 48, 25, 47, 22, 47, 21, 25, 26, 47, 21, 47, 46, 47, 26, 46, 21, 46, 20, 26, 27, 46, 27, 28, 46, 20, 46, 19, 28, 45, 46, 46, 45, 19, 45, 51, 19, 19, 51, 18, 18, 52, 17, 52, 16, 17, 18, 51, 52, 52, 51, 16, 28, 29, 45, 29, 44, 45, 45, 50, 51, 45, 44, 50, 51, 15, 16, 15, 50, 14, 15, 51, 50, 29, 30, 44, 30, 43, 44, 44, 49, 50, 44, 43, 49, 50, 13, 14, 50, 49, 13, 30, 31, 43, 13, 49, 12, 31, 42, 43, 12, 49, 11, 49, 43, 11, 11, 43, 42, 11, 42, 54, 31, 32, 42, 32, 41, 42, 32, 33, 41, 42, 41, 54, 11, 54, 10, 54, 53, 10, 10, 53, 9, 33, 40, 41, 33, 34, 40, 54, 41, 5, 54, 6, 53, 54, 5, 6, 5, 41, 40, 9, 53, 8, 53, 7, 8, 53, 6, 7, 34, 39, 40, 34, 35, 39, 40, 4, 5, 40, 39, 4, 39, 3, 4, 35, 38, 39, 35, 36, 38, 39, 38, 3, 36, 37, 38, 38, 2, 3, 37, 0, 38, 38, 1, 2, 38, 0, 1], "vertices": [1, 28, -12.09, -5.36, 1, 1, 28, -10.49, 1.37, 1, 1, 28, -7.05, 5.88, 1, 1, 28, 2.77, 5.47, 1, 1, 28, 13.84, 5.35, 1, 2, 28, 24.55, 4.29, 0.9993, 29, -14.28, 3.37, 0.0007, 2, 28, 27.04, 6.07, 0.97992, 29, -11.81, 5.19, 0.02008, 2, 28, 27.44, 10.86, 0.94701, 29, -11.51, 9.99, 0.05299, 2, 28, 28.3, 14.5, 0.94215, 29, -10.71, 13.65, 0.05785, 2, 28, 32.83, 12.98, 0.90549, 29, -6.16, 12.21, 0.09451, 2, 28, 37.25, 7.93, 0.64411, 29, -1.64, 7.25, 0.35589, 2, 28, 38.37, 5.15, 0.26251, 29, -0.47, 4.49, 0.73749, 1, 29, 4.45, 6.8, 1, 1, 29, 12.13, 9.55, 1, 2, 29, 21.32, 11.82, 0.99279, 30, -14.83, 11.46, 0.00721, 2, 29, 31.06, 13.24, 0.73435, 30, -5.13, 13.14, 0.26565, 2, 29, 39.3, 14.55, 0.26101, 30, 3.08, 14.67, 0.73899, 2, 29, 46.06, 13.08, 0.09698, 30, 9.87, 13.39, 0.90302, 2, 29, 44.27, 6.23, 0.05194, 30, 8.27, 6.49, 0.94806, 2, 29, 43.99, 2.9, 0.00546, 30, 8.08, 3.16, 0.99454, 1, 30, 15.35, -1.01, 1, 1, 30, 25.64, -5.45, 1, 1, 30, 35.52, -9.82, 1, 1, 30, 43.32, -14.52, 1, 1, 30, 42.56, -18.48, 1, 1, 30, 32.42, -19.19, 1, 2, 29, 57.68, -20.13, 0.01495, 30, 22.4, -19.49, 0.98505, 2, 29, 48.29, -22.65, 0.11998, 30, 13.08, -22.27, 0.88002, 2, 29, 36.74, -22.2, 0.45912, 30, 1.52, -22.13, 0.54088, 3, 28, 63.05, -21.14, 0.00059, 29, 24.7, -21.34, 0.85923, 30, -10.54, -21.6, 0.14017, 3, 28, 51.56, -17.82, 0.05802, 29, 13.14, -18.23, 0.93467, 30, -22.18, -18.81, 0.00731, 2, 28, 42.63, -15.54, 0.31908, 29, 4.17, -16.13, 0.68092, 2, 28, 36.33, -14.64, 0.69167, 29, -2.14, -15.34, 0.30833, 2, 28, 30.02, -15.66, 0.94016, 29, -8.43, -16.47, 0.05984, 1, 28, 19.79, -16.34, 1, 1, 28, 8.37, -16.44, 1, 1, 28, -1.53, -15.12, 1, 1, 28, -9.35, -11.93, 1, 1, 28, -0.14, -5.53, 1, 1, 28, 10.83, -5.47, 1, 1, 28, 21.31, -5.6, 1, 2, 28, 31.3, -5.9, 0.96868, 29, -7.33, -6.69, 0.03132, 2, 28, 36.61, -5.98, 0.70503, 29, -2.02, -6.67, 0.29497, 2, 28, 48.62, -5.87, 0.04959, 29, 9.99, -6.34, 0.95041, 3, 28, 60.39, -8.07, 0.00129, 29, 21.79, -8.32, 0.98206, 30, -13.81, -8.67, 0.01665, 2, 29, 34.83, -8.66, 0.57816, 30, -0.76, -8.65, 0.42184, 2, 29, 47.46, -10.24, 0.05699, 30, 11.91, -9.88, 0.94301, 2, 29, 61.89, -13.68, 0.00083, 30, 26.43, -12.93, 0.99917, 1, 30, 36.58, -15, 1, 1, 29, 11.61, 1.12, 1, 1, 29, 22.32, 2.91, 1, 2, 29, 34.11, 4.91, 0.698, 30, -1.85, 4.89, 0.302, 2, 29, 42.31, 10.78, 0.13197, 30, 6.19, 10.99, 0.86803, 2, 28, 31.61, 9.12, 0.89937, 29, -7.3, 8.33, 0.10063, 2, 28, 32.78, 1.59, 0.973, 29, -5.99, 0.83, 0.027], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 0, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 98, 100, 100, 102, 102, 104, 16, 106, 106, 108], "width": 53, "height": 138}}, "biyan": {"biyan": {"x": 2.82, "y": 3.24, "rotation": -92.05, "width": 51, "height": 21}}, "tf2": {"tf2": {"type": "mesh", "uvs": [1, 0.2576, 0.97575, 0.18818, 0.89151, 0.10596, 0.80547, 0.03289, 0.68, 0, 0.54736, 0.01644, 0.42368, 0.01279, 0.32868, 0.04568, 0.25877, 0.16077, 0.20321, 0.30327, 0.18528, 0.46404, 0.18707, 0.6175, 0.16556, 0.74356, 0.07953, 0.78923, 0, 0.80019, 0, 0.88423, 0, 0.99385, 0.14943, 1, 0.31075, 0.98471, 0.42547, 0.87692, 0.50971, 0.70337, 0.59575, 0.56635, 0.62443, 0.4275, 0.58858, 0.29962, 0.61905, 0.24116, 0.70868, 0.24664, 0.81802, 0.27404, 0.91481, 0.29779, 1, 0.29962, 0.93632, 0.24664, 0.81443, 0.16991, 0.68537, 0.12606, 0.54198, 0.11875, 0.41292, 0.18452, 0.38066, 0.32337, 0.3717, 0.475, 0.3466, 0.65221, 0.29641, 0.78923, 0.19604, 0.88971, 0.09028, 0.89702], "triangles": [11, 10, 35, 36, 11, 35, 12, 11, 36, 37, 12, 36, 20, 37, 36, 19, 37, 20, 15, 14, 13, 38, 12, 37, 13, 12, 38, 39, 13, 38, 15, 13, 39, 18, 37, 19, 38, 37, 18, 16, 15, 39, 17, 39, 38, 17, 38, 18, 16, 39, 17, 34, 8, 33, 34, 33, 23, 9, 8, 34, 35, 9, 34, 10, 9, 35, 22, 34, 23, 22, 35, 34, 21, 35, 22, 20, 36, 35, 21, 20, 35, 32, 6, 5, 32, 5, 31, 33, 7, 6, 33, 6, 32, 8, 7, 33, 24, 32, 31, 23, 32, 24, 33, 32, 23, 31, 4, 3, 5, 4, 31, 30, 3, 2, 31, 3, 30, 25, 31, 30, 24, 31, 25, 29, 2, 1, 30, 2, 29, 29, 1, 0, 26, 30, 29, 25, 30, 26, 27, 26, 29, 29, 0, 28, 27, 29, 28], "vertices": [1, 8, -10.09, -0.5, 1, 1, 8, -7.25, -3.07, 1, 1, 8, -1.29, -4.7, 1, 1, 8, 4.53, -5.88, 1, 2, 8, 11.2, -4.22, 0.92614, 9, -3.8, -1.84, 0.07386, 2, 8, 16.97, -0.12, 0.00178, 9, 2.47, -5.14, 0.99822, 2, 9, 7.75, -9.03, 0.984, 10, -11.19, -1.5, 0.016, 2, 9, 12.86, -10.49, 0.84776, 10, -8.64, -6.17, 0.15224, 3, 9, 19.32, -7.68, 0.33503, 10, -2.11, -8.8, 0.66045, 11, -17.9, -12.43, 0.00453, 3, 9, 25.96, -3.26, 0.00392, 10, 5.69, -10.44, 0.89833, 11, -9.92, -12.46, 0.09775, 2, 10, 14.09, -9.95, 0.44225, 11, -1.8, -10.28, 0.55775, 2, 10, 21.94, -8.49, 0.00338, 11, 5.59, -7.26, 0.99662, 1, 11, 12.11, -5.92, 1, 1, 11, 15.99, -9.29, 1, 1, 11, 18.06, -13, 1, 1, 11, 22.13, -11.4, 1, 1, 11, 27.43, -9.31, 1, 1, 11, 24.83, -1.82, 1, 2, 10, 39.63, 1.23, 0.00072, 11, 20.95, 5.84, 0.99928, 2, 10, 33.07, 6.26, 0.07576, 11, 13.51, 9.44, 0.92424, 3, 9, 24.44, 23.09, 2e-05, 10, 23.42, 9.12, 0.67371, 11, 3.48, 10.29, 0.32627, 3, 9, 16.64, 19.83, 0.02299, 10, 15.62, 12.4, 0.9756, 11, -4.83, 11.92, 0.0014, 2, 9, 11.28, 14.76, 0.13347, 10, 8.25, 12.66, 0.86653, 3, 8, 8.05, 11.79, 0.04228, 9, 9.06, 8.21, 0.51516, 10, 2.02, 9.66, 0.44256, 3, 8, 8.07, 8.35, 0.27383, 9, 6, 6.63, 0.6318, 10, -1.25, 10.73, 0.09437, 3, 8, 3.76, 6.34, 0.83078, 9, 2.25, 9.57, 0.16623, 10, -1.78, 15.46, 0.00299, 2, 8, -2.02, 4.84, 0.99719, 9, -1.7, 14.04, 0.00281, 1, 8, -7.12, 3.49, 1, 1, 8, -11.13, 1.42, 1, 1, 8, -6.85, 0.6, 1, 2, 8, 0.73, 0.16, 0.99981, 9, -4.63, 9.48, 0.00019, 3, 8, 7.83, 1.41, 0.90146, 9, -0.3, 3.71, 0.09847, 10, -7.75, 13.17, 7e-05, 1, 9, 5.73, -0.93, 1, 2, 9, 13.3, -2.01, 0.89368, 10, -2.29, -0.54, 0.10632, 2, 10, 5.11, -0.99, 0.99709, 11, -12.4, -3.32, 0.00291, 2, 10, 12.96, -0.11, 0.99494, 11, -4.89, -0.87, 0.00506, 2, 10, 22.27, 0.15, 0.10874, 11, 4.17, 1.27, 0.89126, 2, 10, 29.75, -1.26, 0.00864, 11, 11.77, 1.4, 0.99136, 1, 11, 18.59, -1.63, 1, 1, 11, 20.99, -6.7, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78], "width": 53, "height": 52}}, "erduo": {"erduo": {"x": 33.39, "y": 23.62, "rotation": -97.41, "width": 20, "height": 23}}, "toushi": {"toushi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44.37, 11.1, 48.11, 39.85, 67.95, 37.27, 64.2, 8.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 29, "height": 20}}, "pd1": {"pd1": {"type": "mesh", "uvs": [0.47754, 0, 0.66851, 0.0055, 0.77892, 0.07313, 0.84754, 0.22034, 0.93706, 0.3934, 1, 0.57641, 1, 0.76539, 0.85948, 0.92254, 0.68343, 0.99415, 0.50738, 1, 0.28359, 0.95636, 0.08964, 0.85889, 0, 0.7455, 0, 0.5605, 0.06577, 0.35958, 0.12545, 0.17657, 0.24779, 0.04329, 0.4865, 0.1527, 0.49843, 0.34566, 0.48948, 0.54459, 0.49545, 0.73356, 0.49843, 0.89469, 0.68343, 0.16862, 0.71327, 0.35163, 0.73118, 0.57243, 0.73714, 0.77534, 0.29851, 0.15668, 0.3015, 0.34964, 0.28061, 0.5605, 0.27166, 0.76141], "triangles": [26, 16, 0, 15, 16, 26, 18, 27, 26, 18, 26, 17, 15, 26, 27, 14, 15, 27, 19, 27, 18, 28, 27, 19, 28, 14, 27, 13, 14, 28, 12, 13, 28, 29, 12, 28, 20, 29, 28, 11, 12, 29, 29, 20, 21, 10, 29, 21, 11, 29, 10, 10, 21, 9, 24, 5, 6, 6, 25, 24, 21, 20, 25, 7, 25, 6, 8, 21, 25, 7, 8, 25, 9, 21, 8, 19, 23, 24, 24, 4, 5, 20, 19, 24, 28, 19, 20, 25, 20, 24, 17, 0, 1, 22, 17, 1, 17, 26, 0, 2, 22, 1, 22, 2, 3, 18, 17, 22, 23, 22, 3, 18, 22, 23, 23, 3, 4, 19, 18, 23, 24, 23, 4], "vertices": [2, 33, -19.93, -5.38, 0.67689, 36, -22.41, 6.72, 0.32311, 2, 33, -16.83, 7.15, 0.92312, 36, -25.99, 19.12, 0.07688, 2, 33, -9.17, 12.71, 0.99059, 36, -22.15, 27.76, 0.00941, 3, 33, 5.12, 13.82, 0.99377, 34, -13.46, 14.71, 0.00288, 35, -32.87, 16.02, 0.00334, 3, 33, 22.05, 15.61, 0.44288, 34, 3.54, 15.6, 0.34962, 35, -15.85, 16.15, 0.2075, 3, 33, 39.51, 15.59, 0.00466, 34, 20.98, 14.64, 0.0437, 35, 1.53, 14.42, 0.95164, 1, 35, 18.19, 8.82, 1, 3, 34, 49.45, -2.9, 0.04278, 35, 29.2, -4.36, 0.89752, 38, 15.96, 54.24, 0.0597, 3, 34, 52.7, -15.94, 0.14459, 35, 31.86, -17.54, 0.63713, 38, 25.63, 44.91, 0.21828, 3, 34, 50.07, -27.3, 0.19981, 35, 28.73, -28.77, 0.42107, 38, 29.42, 33.88, 0.37912, 3, 34, 42.12, -40.6, 0.18894, 35, 20.2, -41.7, 0.18804, 38, 29.75, 18.39, 0.62302, 3, 34, 29.87, -50.76, 0.08178, 35, 7.51, -51.31, 0.04646, 38, 24.76, 3.28, 0.87176, 1, 38, 16.37, -5.51, 1, 1, 37, 18.12, -10.44, 1, 2, 37, -0.8, -7.37, 0.95232, 36, 17.25, -7.7, 0.04768, 3, 33, -6.92, -27.22, 0.0011, 37, -18.14, -4.42, 0.00283, 36, -0.22, -5.68, 0.99607, 2, 33, -12.11, -17.39, 0.23026, 36, -9.63, 0.24, 0.76974, 3, 33, -5.97, -7.98, 0.68861, 34, -25.7, -6.46, 0.00022, 36, -9.02, 11.46, 0.31117, 4, 33, 11.67, -11.26, 0.46845, 34, -8.26, -10.68, 0.22613, 38, -28.83, 17.01, 0.01345, 36, 7.9, 17.44, 0.29197, 5, 33, 29.4, -16.12, 0.00071, 34, 9.19, -16.48, 0.65987, 38, -10.97, 21.36, 0.19662, 37, 9.21, 21.94, 0.01599, 36, 25.67, 22.11, 0.12681, 4, 34, 26.05, -21.23, 0.48556, 35, 5, -21.64, 0.11946, 38, 5.84, 26.28, 0.38142, 36, 42.39, 27.34, 0.01357, 4, 34, 40.48, -25.23, 0.24361, 35, 19.24, -26.27, 0.35367, 38, 20.2, 30.55, 0.40259, 36, 56.67, 31.87, 0.00012, 1, 33, -1.85, 4.98, 1, 2, 33, 15.15, 2.96, 0.99396, 35, -23.95, 4.24, 0.00604, 3, 34, 16.01, -1.32, 0.99068, 38, -13.23, 37.83, 0.00736, 36, 23.11, 38.54, 0.00196, 4, 34, 34.19, -6.46, 0.20856, 35, 13.79, -7.25, 0.71793, 38, 4.91, 43.11, 0.07327, 36, 41.15, 44.15, 0.00024, 2, 33, -8.1, -21.8, 0.15916, 36, -3.95, -1.58, 0.84084, 4, 33, 9.42, -25.63, 0.07968, 34, -11.28, -24.91, 0.08377, 38, -23.84, 3.35, 0.01187, 36, 13.13, 3.87, 0.82467, 4, 34, 6.54, -31.21, 0.24019, 38, -5.39, 7.47, 0.4456, 37, 14.28, 7.86, 0.1767, 36, 31.5, 8.32, 0.13751, 4, 34, 24.39, -36.84, 0.20206, 35, 2.66, -37.16, 0.05319, 38, 12.73, 12.17, 0.73935, 36, 49.53, 13.35, 0.00541], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58], "width": 62, "height": 93}}, "xuanz": {"xuanz": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-104.92, 12.46, -21.22, -104.34, 105.49, -13.54, 21.79, 103.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 256}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"bones": {"bone26": {"rotate": [{"angle": -10.18, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333, "angle": -36.49, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.0667, "angle": -34.46, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2667, "angle": -10.18}]}, "bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -20.44, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -36.08, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -9.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -80.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -113.18, "curve": "stepped"}, {"time": 0.3667, "angle": -113.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.27}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -92.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.81, "curve": "stepped"}, {"time": 0.3667, "angle": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.44}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -30.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 74.78, "curve": "stepped"}, {"time": 0.3667, "angle": 74.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.62}]}, "bone27": {"rotate": [{"angle": -20.36, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -41.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "angle": -46.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -20.36}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": "stepped"}, {"time": 0.1, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -30.18, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -3.87}]}, "bone45": {"rotate": [{"angle": -19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -16.95, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": 2.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -22.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": -19}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -10.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -26.31, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.5}]}, "bone44": {"rotate": [{"angle": -16.76, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -20.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.76}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -34.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.6}]}, "bone23": {"rotate": [{"angle": -17, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.0333, "angle": -14.57, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "angle": -11.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -25.15, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5333, "angle": -17}]}, "bone28": {"rotate": [{"angle": -5.78, "curve": "stepped"}, {"time": 0.1, "angle": -5.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -32.09, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -5.78}]}, "bone22": {"rotate": [{"angle": -5.66, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.75, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5333, "angle": -5.66}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.51, "curve": "stepped"}, {"time": 0.3667, "angle": -46.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone24": {"rotate": [{"angle": -26.21, "curve": 0.322, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.0333, "angle": -23.83, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1667, "angle": -16.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -29.13, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.5333, "angle": -26.21}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 68.61, "curve": "stepped"}, {"time": 0.3667, "angle": 68.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.77}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": "stepped"}, {"time": 0.1, "angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 58.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 44.1, "curve": "stepped"}, {"time": 0.4667, "angle": 44.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -4.1}]}, "bone49": {"rotate": [{"angle": -3.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -3.16}]}, "bone5": {"rotate": [{"angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24}]}, "bone19": {"rotate": [{"angle": -2.76}]}, "bone30": {"rotate": [{"angle": -14.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -40.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.58}]}, "bone4": {"rotate": [{"angle": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.71, "curve": "stepped"}, {"time": 0.3667, "angle": 17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 117.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.39, "curve": "stepped"}, {"time": 0.3667, "angle": 31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.22}]}, "bone50": {"rotate": [{"angle": -16.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -16.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -16.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": -16.19}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -18.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -70.9, "curve": "stepped"}, {"time": 0.3667, "angle": -70.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.48}]}, "bone51": {"rotate": [{"angle": -11.55, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -6.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3, "angle": 13.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.28, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -11.55}]}, "bone21": {"rotate": [{"angle": -13.37, "curve": "stepped"}, {"time": 0.1, "angle": -13.37, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 0.3667, "angle": -39.68, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -13.37}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 76.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 38.17, "curve": "stepped"}, {"time": 0.3667, "angle": 38.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.08}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 30.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.55, "curve": "stepped"}, {"time": 0.3667, "angle": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.52}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 54.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -19.21, "curve": "stepped"}, {"time": 0.3667, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.69}]}, "bone2": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -24.9, "y": -51.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 48.13, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 36.43, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone46": {"rotate": [{"angle": -16.25, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -12.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "angle": 7.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -16.87, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -16.25}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 28.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -57.6, "curve": "stepped"}, {"time": 0.3667, "angle": -57.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.235, "y": 1.832, "curve": "stepped"}, {"time": 0.3667, "x": 1.235, "y": 1.832, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone55": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -57.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 53.48, "curve": "stepped"}, {"time": 0.3333, "x": 53.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone59": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.55, "curve": "stepped"}, {"time": 0.3667, "angle": -46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 15.13, "y": 86.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 48.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.291, "angle": -13.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone42": {"rotate": [{"angle": -2.93, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.05, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5333, "angle": -2.93}]}, "bone43": {"rotate": [{"angle": -7.76, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -13.05, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.5333, "angle": -7.76}]}, "bone70": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3016, "angle": 11.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone71": {"rotate": [{"angle": 2.81, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 12.53, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5333, "angle": 2.81}]}, "bone72": {"rotate": [{"angle": 7.45, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -19.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.53, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.5333, "angle": 7.45}]}, "bone67": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -21.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone68": {"rotate": [{"angle": -4.02, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -17.92, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5333, "angle": -4.02}]}, "bone69": {"rotate": [{"angle": -10.65, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -17.92, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.5333, "angle": -10.65}]}}, "events": [{"time": 0.2667, "name": "atk"}]}, "boss_attack3": {"slots": {"wuqi3": {"attachment": [{"time": 0.2667, "name": null}]}, "xuanz": {"attachment": [{"time": 0.2667, "name": "xuanz"}, {"time": 0.5333, "name": null}]}, "mutou3": {"attachment": [{"time": 0.2667, "name": "mutou"}, {"time": 0.5333, "name": null}]}}, "bones": {"bone63": {"rotate": [{"time": 0.2667}, {"time": 0.3333, "angle": -120}, {"time": 0.4, "angle": 120}, {"time": 0.4333}, {"time": 0.5333, "angle": -120}, {"time": 0.5667, "angle": 120}, {"time": 0.6667}], "translate": [{"x": -6.78, "y": -184.11, "curve": "stepped"}, {"time": 0.2667, "x": -6.78, "y": -184.11, "curve": 0.321, "c2": 0.51, "c3": 0.75}, {"time": 0.6667, "x": 777.72, "y": -196.09}]}, "bone2": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 5.42, "curve": "stepped"}, {"time": 0.2, "angle": 5.42, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": -28, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -11.2, "y": -18.17, "curve": "stepped"}, {"time": 0.2, "x": -11.2, "y": -18.17, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "x": 8.84, "y": -18.08, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 9.89, "curve": "stepped"}, {"time": 0.2, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone4": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone5": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 9.26, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "angle": 3.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone15": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone16": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone17": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone22": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone23": {"rotate": [{"angle": -3.95, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -17.69, "curve": 0.347, "c2": 0.38, "c3": 0.697, "c4": 0.77}, {"time": 0.7, "angle": -3.95}]}, "bone24": {"rotate": [{"angle": -9.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -17.69, "curve": 0.337, "c2": 0.34, "c3": 0.675, "c4": 0.69}, {"time": 0.7, "angle": -9.41}]}, "bone25": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone26": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone27": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone28": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone33": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 15.99, "curve": "stepped"}, {"time": 0.2, "angle": 15.99, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": -39.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone34": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 62.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "angle": -10.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone36": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 37.6, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.1667, "angle": -177.28, "curve": "stepped"}, {"time": 0.2, "angle": -177.28, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 71.15, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.7}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.74, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": 18.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7}]}, "bone44": {"rotate": [{"curve": 0.278, "c3": 0.622, "c4": 0.4}, {"time": 0.1667, "angle": 21.58, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.2519, "angle": 38.51, "curve": 0.328, "c2": 0.32, "c3": 0.663, "c4": 0.66}, {"time": 0.3333, "angle": 36.9, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone45": {"rotate": [{"angle": -2.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -2.01}]}, "bone46": {"rotate": [{"angle": -5.81, "curve": 0.349, "c2": 0.39, "c3": 0.687, "c4": 0.74}, {"time": 0.3333, "angle": -42.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7, "angle": -5.81}]}, "bone49": {"rotate": [{"angle": -3.8, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9, "curve": 0.339, "c2": 0.35, "c3": 0.681, "c4": 0.71}, {"time": 0.7, "angle": -3.8}]}, "bone50": {"rotate": [{"angle": -7.91, "curve": 0.349, "c2": 0.39, "c3": 0.695, "c4": 0.76}, {"time": 0.2, "angle": -2.01, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.7, "angle": -7.91}]}, "bone51": {"rotate": [{"angle": -6.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -9, "curve": 0.335, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.2, "angle": -5.81, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 0.4667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.7, "angle": -6.82}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -37.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone41": {"rotate": [{"angle": -4.82, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.57, "curve": 0.35, "c2": 0.39, "c3": 0.692, "c4": 0.75}, {"time": 0.7, "angle": -4.82}]}, "bone42": {"rotate": [{"angle": -9.03, "curve": 0.36, "c2": 0.45, "c3": 0.7, "c4": 0.81}, {"time": 0.1333, "angle": -1.52, "curve": 0.359, "c2": 0.64, "c3": 0.694}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -16.57, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.7, "angle": -9.03}]}, "bone43": {"rotate": [{"angle": -13.98, "curve": 0.35, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1333, "angle": -4.82, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -16.57, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.7, "angle": -13.98}]}, "bone70": {"rotate": [{"curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.3667, "angle": 19.12, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.7}]}, "bone71": {"rotate": [{"curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.3667, "angle": 19.12, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.7}]}, "bone72": {"rotate": [{"curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.3667, "angle": -21.84, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.7}]}}, "deform": {"default": {"mutou3": {"mutou": [{"time": 0.2667, "vertices": [-37.74933, -20.70584, 19.49765, 38.3871, 37.74922, 20.70564, -19.49762, -38.38722]}]}}}, "events": [{"time": 0.4667, "name": "atk"}]}, "boss_idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.8, "y": -0.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.51, "y": -0.08}]}, "bone5": {"rotate": [{"angle": 1.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.52, "y": -0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.09, "y": -0.24}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.64, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.87}]}, "bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -13.64, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -9.77}]}, "bone19": {"rotate": [{"angle": -2.76}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -13.64, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -8.6}]}, "bone21": {"rotate": [{"angle": -13.37, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 0.2667, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -13.64, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -13.37}]}, "bone22": {"rotate": [{"angle": -4.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -16.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -4.56}]}, "bone23": {"rotate": [{"angle": -11.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -16.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -11.51}]}, "bone24": {"rotate": [{"angle": -16.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -16.08}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.63, "y": 2.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.82, "y": 1.09}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.22, "y": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.11, "y": -1.69}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.48}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.22}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.62}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.72, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.1}]}, "bone44": {"translate": [{"x": -3.89}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.04}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.67}]}, "bone49": {"rotate": [{"angle": 7.66}], "translate": [{"x": -3.89}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10.7}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 7.67}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone67": {"rotate": [{"angle": -13.49}]}, "bone68": {"rotate": [{"angle": -0.41}]}, "bone69": {"rotate": [{"angle": 2.62}]}, "bone71": {"rotate": [{"angle": 1.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.99, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.13}]}, "bone72": {"rotate": [{"angle": 1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.99}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone26": {"rotate": [{"angle": 1.22, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 6, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 1.22}]}, "bone27": {"rotate": [{"angle": 3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 3}]}, "bone28": {"rotate": [{"angle": 5.22, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 6, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 5.22}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.2, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -23.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 52.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 103.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 84.48}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 11.86, "y": -31.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.74, "y": 42.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.74, "y": 57.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.74, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.7, "y": -124.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 3.7, "y": -99.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 3.7, "y": -124.49}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.5}]}, "bone33": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 45.03, "curve": "stepped"}, {"time": 0.3333, "angle": 45.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -24.05}]}, "bone34": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 30.81, "curve": "stepped"}, {"time": 0.3667, "angle": 30.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.76}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 73.21, "curve": "stepped"}, {"time": 0.3, "angle": 73.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 22.07}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.22, "curve": "stepped"}, {"time": 0.3, "angle": -15.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.67}]}, "bone44": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": 2.35, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone45": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": 2.35, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone46": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": 2.35, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone49": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": -3.02, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone50": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": -3.02, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone51": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "angle": -25.31, "curve": 0.339, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": -3.02, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.5667}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.73}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 16.4}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.98}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.81}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 49.39}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -45.22, "curve": "stepped"}, {"time": 0.3, "angle": -45.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -72.27}]}, "bone55": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 177.46}]}, "bone60": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 139.15}]}, "bone59": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 82.57}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 7.16, "y": 100.94, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 7.16, "y": 132.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 7.16, "y": 55.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 7.16, "y": 3.52}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 82.57}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2413, "x": 7.16, "y": 100.94, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "x": 7.16, "y": 124.74, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "x": 7.16, "y": 51.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 7.16, "y": 5.73}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.1667, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.12, "y": -8.93, "curve": "stepped"}, {"time": 0.2, "x": -1.12, "y": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone4": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone33": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone36": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 62, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone37": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -52.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 29.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 35.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -40.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -39.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}, "run1": {"bones": {"ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone": {"translate": [{"y": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 3.84}]}, "bone22": {"rotate": [{"angle": -1.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -12.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.64}]}, "bone23": {"rotate": [{"angle": -4.58, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0667, "angle": -1.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -4.58}]}, "bone24": {"rotate": [{"angle": -7.94, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": -4.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.94}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone27": {"rotate": [{"angle": -1.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.65, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.26}]}, "bone28": {"rotate": [{"angle": -3.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.65, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.55}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone16": {"rotate": [{"angle": -0.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.31, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -0.43}]}, "bone17": {"rotate": [{"angle": -1.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.31, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -1.22}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.82}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 7.87}]}, "bone66": {"rotate": [{"angle": -26.49}]}, "bone38": {"rotate": [{"angle": 16.21}]}, "bone60": {"translate": [{"x": 41.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -103.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -28.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 41.64}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -67.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -50.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"x": 0.48, "y": 33.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.79, "y": 70.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.48, "y": 33.04}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 60.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -58.5}], "translate": [{"x": -1.86, "y": 67.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 16.3, "y": 58.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.7, "y": 52.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.86, "y": 67.28}]}, "bone55": {"translate": [{"x": -65.06, "y": -0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 100.15, "y": 0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 16.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -65.06, "y": -0.4}]}, "bone2": {"rotate": [{"angle": -22.85}]}, "bone35": {"rotate": [{"angle": 35.12}]}, "bone44": {"rotate": [{"angle": 40.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 40.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 24.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 40.08}]}, "bone45": {"rotate": [{"angle": 0.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 6.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2333, "angle": -9.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -9.3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 0.57}]}, "bone46": {"rotate": [{"angle": -7.1, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -7.1}]}, "bone49": {"rotate": [{"angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 22.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 22.3}]}, "bone50": {"rotate": [{"angle": -5.08, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 0.66, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -14.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -14.95, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -5.08}]}, "bone51": {"rotate": [{"angle": -2, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 11.57, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.3, "angle": -4.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -4.03, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -2}]}, "bone36": {"rotate": [{"angle": 24.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 24.85}]}, "bone37": {"rotate": [{"angle": 25.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 24.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 27.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 25.91}]}, "bone34": {"rotate": [{"angle": 53.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 43.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 69.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 53.22}]}, "bone33": {"rotate": [{"angle": -54.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -54.26}]}, "bone70": {"rotate": [{"angle": 40.39}]}, "bone72": {"rotate": [{"angle": -6.08, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -1.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -13.71, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -6.08}]}, "bone67": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone68": {"rotate": [{"angle": -2.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -7.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -2.68}]}, "bone69": {"rotate": [{"angle": -6.34, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -7.28, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -6.34}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -12.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone42": {"rotate": [{"angle": -1.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -12.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.64}]}, "bone43": {"rotate": [{"angle": -4.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -4.63}]}}}, "run2": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 75.4}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone22": {"rotate": [{"angle": -8.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -16.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -8.11}]}, "bone23": {"rotate": [{"angle": -14.65, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -4.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.21, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.3667, "angle": -14.65}]}, "bone24": {"rotate": [{"angle": -14.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -16.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": -11.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -14.1}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone46": {"rotate": [{"angle": -12.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -20.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -20.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -12.22}]}, "bone51": {"rotate": [{"angle": -13.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "angle": -5.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -13.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.68}]}, "bone50": {"rotate": [{"angle": -22.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0333, "angle": -9.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -31.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -31.13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -22.66}]}, "bone44": {"rotate": [{"angle": 50.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 20.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 50.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 20.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 50.68}], "translate": [{"x": 4.54, "y": 11.49}]}, "bone45": {"rotate": [{"angle": -0.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -22.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -0.98}]}, "bone49": {"rotate": [{"angle": 35.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 43.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 13.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 43.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 13.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 35.26}]}, "bone70": {"rotate": [{"angle": 59.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 52.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 59.24}], "translate": [{"x": 6.56, "y": 5.44}]}, "bone68": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone69": {"rotate": [{"angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -3.09}]}, "bone67": {"rotate": [{"angle": -2.63}], "translate": [{"x": -1.78, "y": -1.79}]}, "bone26": {"rotate": [{"angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 15.38}]}, "bone27": {"rotate": [{"angle": 10.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 10.82}]}, "bone28": {"rotate": [{"angle": 7.55, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 7.55}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone17": {"rotate": [{"angle": -9.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -25.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -9.3}]}, "bone40": {"rotate": [{"angle": 8.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": 8.28}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone42": {"rotate": [{"angle": -4.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.21, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -4.6}]}, "bone43": {"rotate": [{"angle": -11.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -11.61}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone72": {"rotate": [{"angle": -16.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -25.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": -16.11}]}}}, "show_time": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 75.4}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone22": {"rotate": [{"angle": -8.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -16.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -8.11}]}, "bone23": {"rotate": [{"angle": -14.65, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -4.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.21, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.3667, "angle": -14.65}]}, "bone24": {"rotate": [{"angle": -14.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -16.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": -11.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -14.1}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}], "translate": [{"x": -0.8, "y": -5.5}]}, "bone46": {"rotate": [{"angle": -12.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -20.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -20.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -12.22}]}, "bone51": {"rotate": [{"angle": -13.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "angle": -5.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -13.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.68}]}, "bone50": {"rotate": [{"angle": -22.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0333, "angle": -9.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -31.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -31.13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -22.66}]}, "bone44": {"rotate": [{"angle": 50.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 20.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 50.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 20.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 50.68}], "translate": [{"x": 4.54, "y": 11.49}]}, "bone45": {"rotate": [{"angle": -0.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -22.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -0.98}]}, "bone49": {"rotate": [{"angle": 35.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 43.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 13.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 43.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 13.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 35.26}]}, "bone70": {"rotate": [{"angle": 59.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 52.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 59.24}], "translate": [{"x": 6.56, "y": 5.44}]}, "bone68": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone69": {"rotate": [{"angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -3.09}]}, "bone67": {"rotate": [{"angle": -2.63}], "translate": [{"x": -1.78, "y": -1.79}]}, "bone26": {"rotate": [{"angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 15.38}]}, "bone27": {"rotate": [{"angle": 10.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 10.82}]}, "bone28": {"rotate": [{"angle": 7.55, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 15.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 7.55}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone17": {"rotate": [{"angle": -9.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -25.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -9.3}]}, "bone40": {"rotate": [{"angle": 8.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": 8.28}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone42": {"rotate": [{"angle": -4.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.21, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -4.6}]}, "bone43": {"rotate": [{"angle": -11.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -11.61}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone72": {"rotate": [{"angle": -16.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -25.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": -16.11}]}}}}}