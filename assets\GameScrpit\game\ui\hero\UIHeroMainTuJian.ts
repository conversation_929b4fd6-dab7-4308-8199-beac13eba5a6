import { _decorator, Component, Label, Node } from "cc";
import { TuJianAdapter } from "./adapter/TuJianAdapter";
import { JsonMgr } from "../../mgr/JsonMgr";
import { TuJianLevelAdapter } from "./adapter/TuJianLevelAdapter";
import { HeroModule } from "../../../module/hero/HeroModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { IConfigHeroPicture } from "../../JsonDefine";
import { LangMgr } from "../../mgr/LangMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Mon Dec 09 2024 19:39:19 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroMainTuJian.ts
 *
 */

@ccclass("UIHeroMainTuJian")
export class UIHeroMainTuJian extends Component {
  @property(Node)
  private listView: Node;
  @property(Node)
  private listViewHolder: Node;
  @property(Node)
  private nodeLevelList: Node;
  @property(Node)
  private listViewLevelHolder: Node;
  @property(Label)
  private lblTipTitle: Label;
  @property(Label)
  private lblUpgradeTips: Label;
  @property(Node)
  private nodeTips: Node;

  @property(Label)
  private lblHeroNum: Label;
  @property(Label)
  private lblActivateTujianNum: Label;

  private _adapter: TuJianAdapter;
  private _levelAdapter: TuJianLevelAdapter;
  start() {
    this._adapter = new TuJianAdapter(this.listViewHolder, this);
    this.listView.getComponent(AdapterView).setAdapter(this._adapter);
    let datas: IConfigHeroPicture[] = Object.values(JsonMgr.instance.jsonList.c_heroPicture);
    this._adapter.setData(datas);
    this.refreshData();
    MsgMgr.on(MsgEnum.ON_HERO_UPDATE, this.refreshData, this);
  }
  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_HERO_UPDATE, this.refreshData, this);
  }
  private refreshData() {
    this._adapter.notifyDataSetChanged("DATAONLY");
    let datas: IConfigHeroPicture[] = Object.values(JsonMgr.instance.jsonList.c_heroPicture);
    let owendNum = Object.values(HeroModule.data.pictureMessage.pictureMap).length;
    log.log("激活图鉴", HeroModule.data.pictureMessage);
    this.lblActivateTujianNum.string = `激活图鉴：${owendNum}/${datas.length}`;
    this.lblHeroNum.string = `拥有战将：${HeroModule.data.getOwnedHeros().length}/${
      HeroModule.data.allHeroList.length
    }`;
  }
  update(deltaTime: number) {}

  public showTips(data: IConfigHeroPicture) {
    this.lblTipTitle.string = data.name;
    this.nodeTips.active = true;
    let pictureLv = HeroModule.data.pictureMessage?.pictureMap[data.id] ?? 0;
    if (pictureLv == data.level.length) {
      this.lblUpgradeTips.node.active = false;
    } else {
      this.lblUpgradeTips.node.active = true;
      let strContent = LangMgr.txMsgCode(227, [], ".获得所有战将可激活和升级羁绊.");
      this.lblUpgradeTips.string = strContent;
    }
    //
    this._levelAdapter = new TuJianLevelAdapter(this.listViewLevelHolder);
    this.nodeLevelList.getComponent(AdapterView).setAdapter(this._levelAdapter);
    this._levelAdapter.setData(data);
  }
  private onClickBlank() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.nodeTips.active = false;
  }
}
