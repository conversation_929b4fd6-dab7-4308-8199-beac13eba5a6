import { RedeemVO } from "../activity/ActivityConfig";

export class AfterConfig {}

export class AfterMsgEnum {
  /**红点更新 */
  public static readonly AFTERMSGENUM_RED_DOT_UPDATE = "AFTERMSGENUM_RED_DOT_UPDATE";
}

export const AfterAudioName = {
  Effect: {
    点击累充回馈图标: 1381,
    点击领取按钮: 1382,
    点击下方页签: 1383,
    点击前往按钮: 1384,
    点击充值按钮: 1385,
  },
  Sound: {},
};

export class TopUpVO extends RedeemVO {
  /**
   * 充值签到
   */
  signVOList: Array<TopUpSignVO>;

  /**
   * 累计充值
   */
  costRewardVOList: Array<TopUpCostRewardVO>;
}

/**累充回馈 */
export class TopUpSignVO {
  id: number;

  /**
   * 累计天数
   */
  day: number;

  name: string;

  /**
   * 奖励
   */
  rewardList: Array<number>;

  /**
   * 是否关闭
   */
  close: boolean;
}

export class TopUpCostRewardVO {
  id: number;

  /**
   * 金额
   */
  money: number;

  name: string;

  /**
   * 奖励
   */
  rewardList: Array<number>;
}
