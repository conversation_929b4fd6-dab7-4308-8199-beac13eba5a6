import { JsonMgr } from "../../game/mgr/JsonMgr";
import {
  ClubBossBuddyMessage,
  ClubBossChanceMessage,
  ClubFormMessage,
  ClubMessage,
} from "../../game/net/protocol/Club";
import MsgMgr from "../../lib/event/MsgMgr";
import { CLUB_POSITION, ClubEvent } from "./ClubConstant";
import { ClubModule } from "./ClubModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ClubData {
  private _clubFormMessage: ClubFormMessage;
  private _position: number;
  private _bossMessage: ClubBossBuddyMessage;
  private _joinTime: number;

  public init() {
    this._clubFormMessage = null;
    this._position = 0;
  }

  public get bossMessage(): ClubBossBuddyMessage {
    return this._bossMessage;
  }
  public set bossMessage(value: ClubBossBuddyMessage) {
    this._bossMessage = value;
    MsgMgr.emit(ClubEvent.CLUB_DATA_CHANGE);
  }
  public get clubFormMessage(): ClubFormMessage {
    return this._clubFormMessage;
  }

  public set clubFormMessage(value: ClubFormMessage) {
    this._clubFormMessage = value;
    MsgMgr.emit(ClubEvent.CLUB_DATA_CHANGE);
  }

  public get clubMessage(): ClubMessage {
    return this._clubFormMessage?.clubMessage ?? null;
  }

  public get level() {
    if (!this._clubFormMessage) {
      return 0;
    }

    if (!this._clubFormMessage.clubMessage) {
      return 0;
    }
    return this._clubFormMessage.clubMessage.level;
  }

  public set clubMessage(value: ClubMessage) {
    if (!this._clubFormMessage) {
      log.error("clubFormMessage is null");
      return;
    }
    this._clubFormMessage.clubMessage = value;
    MsgMgr.emit(ClubEvent.CLUB_DATA_CHANGE);
  }

  public get position() {
    return this._position;
  }

  public set position(value: number) {
    this._position = value;
  }

  public get joinTime() {
    return this._joinTime;
  }
  public set joinTime(value: number) {
    this._joinTime = value;
  }
  public get positionName() {
    if (this.position == CLUB_POSITION.盟主) {
      return "盟主";
    } else if (this.position == CLUB_POSITION.副盟主) {
      return "副盟主";
    }
    return "成员";
  }

  public get bossChance(): ClubBossChanceMessage {
    return this._clubFormMessage.bossChance;
  }

  public set bossChance(value: ClubBossChanceMessage) {
    if (!this._clubFormMessage) {
      log.error("clubFormMessage is null");
      return;
    }
    this._clubFormMessage.bossChance = value;
    MsgMgr.emit(ClubEvent.CLUB_BOSS_STATE_CHANGE);
  }
}
