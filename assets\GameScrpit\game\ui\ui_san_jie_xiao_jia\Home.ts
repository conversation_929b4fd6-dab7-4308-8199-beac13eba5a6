import { _decorator, Label, Sprite, SpriteFrame, v3 } from "cc";
import { CityModule } from "../../../module/city/CityModule";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityRouteName } from "../../../module/city/CityConstant";
import { LongValue } from "../../net/protocol/ExternalMessage";
import Formate from "../../../lib/utils/Formate";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import ToolExt from "../../common/ToolExt";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("Home")
export class Home extends BaseCtrl {
  private _homeId: number = 0;
  private _dbHomeId: number = 0;

  private _isInit: boolean = false;

  protected start(): void {
    super.start();
  }

  public setHome(homeId: number) {
    this._homeId = homeId;
    this.getNode("city").getComponent(Sprite).spriteFrame = null;
    if (this._isInit == false) {
      this._isInit = true;
      BadgeMgr.instance.setBadgeId(this.getNode("state2"), BadgeType.UIMajorCity.btn_sanjiexiaojia[this.node.name].id);
      BadgeMgr.instance.setBadgeId(this.getNode("state3"), BadgeType.UIMajorCity.btn_sanjiexiaojia[this.node.name].id);
    }

    this.getDbHomeId();
    this.setCitySprite();
    this.setMessageName();
    this.setMessageState();
  }

  /**
   * 设置dbId ID为一级时的各建筑ID 只包括1xx
   */
  private getDbHomeId() {
    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[this._homeId] || 1;
    this._dbHomeId = lv * 100 + (this._homeId % 100);
  }

  private setCitySprite() {
    let db = JsonMgr.instance.jsonList.c_home[this._dbHomeId];
    let spineName = db.spineName;

    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[this._homeId] || 0;

    if (lv == 0) {
      spineName = db["spineName2"];
    }

    this.getNode("city").setPosition(v3(db.place[0], db.place[1], 1));
    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_G_SAN_JIE_XIAO_JIA,
      `images/home/<USER>
      (spf: SpriteFrame) => {
        if (this.isValid == false) return;
        this.getNode("city").getComponent(Sprite).spriteFrame = spf;
      }
    );
  }

  private setMessageName() {
    let db = JsonMgr.instance.jsonList.c_home[this._dbHomeId];
    let name = db.name;
    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[this._homeId] || 0;
    if (lv > 0) {
      name += lv + "级";
    }

    this.getNode("lbl_1").getComponent(Label).string = name;
    this.getNode("lbl_2").getComponent(Label).string = name;
    this.getNode("lbl_5").getComponent(Label).string = name;
  }

  private setMessageState() {
    for (let i of this.getNode("spr_message_bg").children) {
      i.active = false;
    }

    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[this._homeId] || 0;
    /**我的繁荣度 */
    let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
    let nextDb = this.nextLvDb();
    let unlock = nextDb?.unlock || 0;

    if (lv <= 0 && bloom < unlock) {
      this.funState1();
      return;
    }

    if (lv <= 0 && bloom >= unlock) {
      this.funState2();
      return;
    }

    if (lv > 0 && bloom >= unlock && nextDb) {
      this.funState3();
      return;
    }

    if (lv > 0 && bloom < unlock && nextDb) {
      this.funState3_1();
      return;
    }

    if (lv > 0 && !nextDb) {
      this.funState4();
    }
  }

  /**
   * 未解锁
   */
  private funState1() {
    let db = JsonMgr.instance.jsonList.c_home[this._dbHomeId];
    this.getNode("state1").active = true;
    this.getNode("lbl_1").active = true;
    this.getNode("lbl_3").active = true;
    this.getNode("lbl_3").getComponent(Label).string = "繁荣度" + Formate.format(db.unlock) + "解锁";
  }

  /**
   * 待解锁
   */
  private funState2() {
    let db = JsonMgr.instance.jsonList.c_home[this._dbHomeId];
    this.getNode("state2").active = true;
    this.getNode("lbl_5").active = true;
    this.getNode("lbl_4").active = true;
    this.getNode("lbl_4").getComponent(Label).string = "繁荣度" + Formate.format(db.unlock) + "解锁";
  }

  /**
   *  可升级
   */
  private funState3() {
    let nextDb = this.nextLvDb();
    let unlock = nextDb?.unlock || -1;

    this.getNode("state3").active = true;
    this.getNode("lbl_5").active = true;
    this.getNode("lbl_4").active = true;
    this.getNode("skt1").active = true;
    this.getNode("lbl_4").getComponent(Label).string = "繁荣度" + Formate.format(unlock) + "升级";
  }

  /**
   *  不可升级
   */
  private funState3_1() {
    let nextDb = this.nextLvDb();
    let unlock = nextDb?.unlock || -1;
    this.getNode("lbl_1").active = true;
    this.getNode("lbl_3").active = true;
    this.getNode("lbl_3").getComponent(Label).string = "繁荣度" + Formate.format(unlock) + "升级";
  }

  /**
   *  满级状态
   */
  private funState4() {
    this.getNode("state4").active = true;
    this.getNode("lbl_2").active = true;
  }

  private nextLvDb() {
    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[this._homeId] || 0;
    lv += 1;
    let newdbId = lv * 100 + (this._homeId % 100);
    let db = JsonMgr.instance.jsonList.c_home[newdbId];

    if (!db) {
      return null;
    }

    return db;
  }

  private on_click_state2() {
    TipsMgr.setEnableTouch(false, 0.5);
    this.openHome();
  }

  private openHome() {
    CityModule.api.levelUpSmallHomeBuild(this._homeId, (rs: LongValue) => {
      AudioMgr.instance.playEffect(1800);
      this.setHome(this._homeId);
      UIMgr.instance.showDialog(CityRouteName.UISanJieXiaoJiaOpen, { id: this._homeId, lv: rs.value });
    });
  }

  private on_click_state3() {
    TipsMgr.setEnableTouch(false, 0.5);
    this.upLvHome();
  }

  private upLvHome() {
    CityModule.api.levelUpSmallHomeBuild(this._homeId, (rs: LongValue) => {
      AudioMgr.instance.playEffect(1801);
      this.setHome(this._homeId);
      UIMgr.instance.showDialog(CityRouteName.UISanJieXiaoJiaLvUp, { id: this._homeId, lv: rs.value });
    });
  }

  private on_click_spr_message_bg() {
    TipsMgr.setEnableTouch(false, 0.5);
    let lv = CityModule.data.cityAggregateMessage.smallHomeLevelMap[this._homeId] || 0;
    /**我的繁荣度 */
    let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
    let nextDb = this.nextLvDb();
    let unlock = nextDb?.unlock || 0;

    if (lv <= 0 && bloom < unlock) {
      TipMgr.showTip("繁荣度达到相应值后可解锁");
      return;
    }

    if (lv <= 0 && bloom >= unlock) {
      this.openHome();
      return;
    }

    if (lv > 0 && bloom >= unlock && nextDb) {
      this.upLvHome();
      return;
    }

    if (lv > 0 && bloom < unlock && nextDb) {
      TipMgr.showTip("繁荣度达到相应值后可升级");
      return;
    }

    if (lv > 0 && !nextDb) {
      TipMgr.showTip("目标已达到最高等级");
    }
  }

  private on_click_skt1() {
    TipsMgr.setEnableTouch(false, 0.5);
    this.upLvHome();
  }
}
