import CmdMgr from "../../game/mgr/CmdMgr";

/** 常量放这这里 **/
const ChatMain = 24;
export enum ChatSubCmd {
  //   路由: 24 - 1  --- 【查看聊天记录 start,end左开右开】 --- 【ChatAction:53】【messageList】
  //     方法参数: ChatPackFindRequest
  //     方法返回值: ByteValueList<ChatPackMessage>

  // 路由: 24 - 2  --- 【发送消息】 --- 【ChatAction:70】【addMessage】
  //     方法参数: ChatPackAddRequest
  //     方法返回值: ChatPackMessage
  messageList = CmdMgr.getMergeCmd(ChatMain, 1),
  addMessage = CmdMgr.getMergeCmd(ChatMain, 2),

  newMessage = CmdMgr.getMergeCmd(ChatMain, 51),
  //     方法参数: ChatPackNewRequest
  //     方法返回值: ChatPackMessage
}
export enum MarketType {
  CLUB = 1,
  WORLD = 2,
}
