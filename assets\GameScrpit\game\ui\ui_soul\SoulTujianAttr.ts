import { _decorator, Component, Label, math, Node, Sprite } from "cc";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import { times } from "../../../lib/utils/NumbersUtils";
import ResMgr from "../../../lib/common/ResMgr";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

@ccclass("SoulTujianAttr")
export class SoulTujianAttr extends Component {
  @property(Label)
  private lblAttr: Label;
  @property(Node)
  private bgColor: Node;

  private _color: number;

  COLOR = {
    0: "bg_shouhun_shuxing_1",
    1: "bg_shouhun_shuxing_2",
    2: "bg_shouhun_shuxing_3",
    3: "bg_shouhun_shuxing_4",
    4: "bg_shouhun_shuxing_5",
  };
  OUT_LINE_COLOR = {
    0: "#328924",
    1: "#1a75c8",
    2: "#4c56bc",
    3: "#8e5b02",
    4: "#c11329", //"#c04c02",
  };
  start() {}

  update(deltaTime: number) {}

  /**
   * 返回1-5
   * @returns
   */
  public getColor() {
    //1-5
    return this._color + 1;
  }
  public setAttrId(pictureId: number, attrId: number, value: number) {
    let configAttr = JsonMgr.instance.jsonList.c_attribute[attrId];
    let configPicture = JsonMgr.instance.jsonList.c_soulPicture[pictureId];
    if (!configAttr || !configPicture) {
      return;
    }
    if (times(value, 10000) > 500) {
      this._color = 4;
    } else {
      this._color = configPicture.color.findIndex((row) => row.includes(times(value, 10000))); // 查找包含值的行索引
      this._color = this._color >= 0 ? this._color : 0;
    }
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_G_SOUL,
      `atlas_dialog/${this.COLOR[this._color]}`,
      this.bgColor.getComponent(Sprite)
    );
    this.lblAttr.string = `${configAttr.name}+${times(value, 100)}%`;
    this.lblAttr.outlineColor = math.color(this.OUT_LINE_COLOR[this._color]);
  }
}
