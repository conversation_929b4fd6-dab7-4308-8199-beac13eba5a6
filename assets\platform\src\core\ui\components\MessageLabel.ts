import { _decorator, Component, Node, Label, CCString, CCInteger } from "cc";
import Lo<PERSON>, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { LangMgr } from "../../../../../GameScrpit/game/mgr/LangMgr";

const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property, executeInEditMode } = _decorator;
const Editor = window["Editor"];
@ccclass("MessageLabel")
@executeInEditMode
export class MessageLabel extends Label {
  // public set string(val: string) {
  //   super.string = val;
  // }

  @property({ serializable: true })
  private _args: string[] = [];
  @property({ type: CCInteger, serializable: true })
  private _messageKey: number = 0;

  @property({ type: CCString, displayName: "参数", tooltip: "参数" })
  public set args(val: string[]) {
    this._args = val;
    this.updateMessage();
  }
  public get args() {
    return this._args;
  }
  @property({ type: CCInteger, displayName: "消息ID", tooltip: "消息ID", step: 1 })
  public set messageKey(val: number) {
    this._messageKey = val;
    this.updateMessage();
  }
  public get messageKey() {
    return this._messageKey;
  }
  onFocusInEditor(): void {
    this.updateMessage();
  }
  updateMessage() {
    if (this._messageKey == 0) {
      return;
    }
    if (typeof Editor !== "undefined" && Editor.Project) {
      const projectSourcePath = Editor.Project.path;
      const filePath = projectSourcePath + "/assets/bundle_common_json/json/c_message.json";
      const fs = require("fs");
      const path = require("path");

      if (fs.existsSync(filePath)) {
        fs.readFile(filePath, "utf8", (err, data) => {
          if (err) {
            log.error("读取文件时出错:", err);
            return;
          }
          try {
            const jsonData = JSON.parse(data);
            this.string = jsonData[this.messageKey].text;
            for (let i = 0; i < this.args.length; i++) {
              this.string = this.string.replace("s%", this.args[i] || "");
            }
            log.log("读取到的JSON数据:", jsonData);
          } catch (parseError) {
            log.error("解析JSON数据时出错:", parseError);
          }
        });
      } else {
        log.log("文件不存在:", filePath);
      }
    } else {
      this.string = LangMgr.txMsgCode(this.messageKey, this.args, "");
      // this.string = JsonMgr.instance.jsonList.c_message[this.messageKey]?.text;
      // log.log("当前环境不是Cocos Creator编辑器环境，无法获取项目源码路径。");
    }
  }

  start() {
    this.updateMessage();
  }

  update(deltaTime: number) {}
}
