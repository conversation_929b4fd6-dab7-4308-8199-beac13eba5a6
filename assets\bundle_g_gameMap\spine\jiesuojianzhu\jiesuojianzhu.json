{"skeleton": {"hash": "ue5e94Yo43p5OohznyUIEOO3N58", "spine": "3.8.75", "x": -348.53, "y": -448.63, "width": 701, "height": 900, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/J_解锁建筑"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 244.94, "x": 651.28, "y": 0.16}, {"name": "shang", "parent": "bone", "length": 115.9, "rotation": 1.64, "x": -715.39, "y": 357.48}, {"name": "xia", "parent": "bone", "length": 115.9, "rotation": 1.64, "x": -715.39, "y": -345.89}, {"name": "zhong", "parent": "bone", "length": 158.23, "rotation": -89.51, "x": -651.71, "y": 85.42}, {"name": "gd_zi", "parent": "root", "x": -2.84, "y": 273.81, "color": "10ff00ff"}, {"name": "piaodai0", "parent": "shang", "rotation": -1.64, "x": -188.46, "y": -6.38, "color": "ff0000ff"}, {"name": "p<PERSON><PERSON><PERSON>", "parent": "piaodai0", "length": 80.48, "rotation": -103.24, "x": -2.38, "y": -13.34}, {"name": "piaodai2", "parent": "p<PERSON><PERSON><PERSON>", "length": 77.43, "rotation": 8.31, "x": 79.27, "y": -0.39}, {"name": "piaodai3", "parent": "piaodai2", "length": 74.03, "rotation": 22.71, "x": 77.43}, {"name": "piaodai4", "parent": "piaodai3", "length": 56.71, "rotation": 32.93, "x": 74.03}, {"name": "guang", "parent": "bone", "x": -659.15, "y": 483.2, "color": "ffe800ff"}, {"name": "gd_faguang", "parent": "root", "rotation": -90.76, "y": 23.48, "scaleX": 2.1756, "scaleY": 2.1756, "color": "00ff1fff"}], "slots": [{"name": "guang", "bone": "guang", "attachment": "guang"}, {"name": "p<PERSON><PERSON><PERSON>", "bone": "piaodai0", "attachment": "p<PERSON><PERSON><PERSON>"}, {"name": "shang2", "bone": "shang", "attachment": "shang"}, {"name": "xia2", "bone": "xia", "attachment": "xia"}, {"name": "caijian", "bone": "bone", "attachment": "caijian"}, {"name": "zhong", "bone": "zhong", "attachment": "zhong"}, {"name": "shang", "bone": "shang", "attachment": "shang"}, {"name": "xia", "bone": "xia", "attachment": "xia"}], "skins": [{"name": "default", "attachments": {"piaodai": {"piaodai": {"type": "mesh", "uvs": [0.60047, 0, 0.50564, 0.02602, 0.60548, 0.0482, 0.4636, 0.10919, 0.31582, 0.16822, 0.21256, 0.2297, 0.11825, 0.30765, 0.12139, 0.39664, 0.11207, 0.47612, 0.05675, 0.56798, 0, 0.63766, 0.05057, 0.7298, 0.20112, 0.82951, 0.40791, 0.92285, 0.83827, 0.98996, 0.87664, 0.90807, 0.6548, 0.83269, 0.49304, 0.77176, 0.38524, 0.69408, 0.32822, 0.62619, 0.45524, 0.67741, 0.60705, 0.74388, 0.84197, 0.76837, 0.86791, 0.69343, 0.59739, 0.61848, 0.44784, 0.53095, 0.54419, 0.43561, 0.69895, 0.35474, 0.79159, 0.28305, 0.82495, 0.1879, 0.86201, 0.11165, 0.90459, 0.05377, 1, 0.03866, 1, 0, 0.78048, 0, 0.65671, 0.0829, 0.60114, 0.11598, 0.48146, 0.18965, 0.37459, 0.27384, 0.31475, 0.34826, 0.29338, 0.41366, 0.42161, 0.32721, 0.5413, 0.26181, 0.64816, 0.18589, 0.67808, 0.12124], "triangles": [19, 9, 25, 24, 19, 25, 10, 9, 19, 20, 19, 24, 20, 24, 23, 10, 19, 18, 11, 10, 18, 21, 20, 23, 22, 21, 23, 11, 18, 17, 12, 11, 17, 12, 17, 16, 13, 12, 16, 13, 16, 15, 14, 13, 15, 39, 6, 38, 41, 39, 38, 7, 6, 39, 40, 7, 39, 40, 39, 41, 27, 40, 41, 26, 40, 27, 8, 7, 40, 26, 8, 40, 25, 8, 26, 9, 8, 25, 34, 33, 32, 2, 0, 34, 31, 2, 34, 1, 0, 2, 32, 31, 34, 35, 2, 31, 3, 2, 35, 30, 35, 31, 36, 3, 35, 44, 36, 35, 30, 44, 35, 43, 36, 44, 29, 44, 30, 43, 44, 29, 36, 4, 3, 37, 36, 43, 37, 4, 36, 5, 4, 37, 42, 37, 43, 38, 5, 37, 42, 43, 29, 42, 38, 37, 28, 42, 29, 6, 5, 38, 41, 38, 42, 41, 42, 28, 27, 41, 28], "vertices": [1, 7, -7.67, -8.67, 1, 1, 7, 0.78, -11.65, 1, 1, 7, 5.88, -5.22, 1, 1, 7, 24.75, -8.21, 1, 1, 7, 43.96, -10.3, 1, 2, 7, 63.07, -10.76, 0.96652, 8, -17.53, -7.92, 0.03348, 1, 8, 5.73, -9.41, 1, 2, 8, 30.7, -8.87, 1, 9, -46.52, 9.85, 0, 2, 8, 53.58, -5.83, 0.99996, 9, -24.25, 3.84, 4e-05, 2, 8, 79.21, -8.44, 0.46511, 9, -1.62, -8.48, 0.53489, 1, 9, 19.06, -15.95, 1, 1, 9, 45.43, -24.13, 1, 1, 9, 72.65, -25.26, 1, 1, 9, 110.62, -15.31, 1, 1, 9, 137.9, -0.57, 1, 1, 9, 115.65, 8.88, 1, 1, 9, 78.69, -2.21, 1, 1, 9, 57.6, -4.33, 1, 1, 9, 36.5, -2.88, 1, 1, 9, 16.38, -0.41, 1, 1, 9, 34.61, 0.55, 1, 1, 9, 59.2, 5.63, 1, 1, 9, 81.92, 23.03, 1, 1, 9, 64.26, 24.03, 1, 1, 9, 29.1, 13.86, 1, 2, 8, 70.66, 11.95, 0.75354, 9, -1.63, 13.63, 0.24646, 3, 7, 115.98, 19.29, 0.00464, 8, 39.17, 14.17, 0.99536, 9, -29.82, 27.84, 0, 3, 7, 91.32, 19.78, 0.45399, 8, 14.84, 18.22, 0.54601, 9, -50.7, 40.97, 0, 2, 7, 70, 19.62, 1, 9, -69.05, 51.81, 0, 2, 7, 42.75, 14.96, 1, 9, -94.81, 61.86, 0, 2, 7, 20.79, 11.73, 1, 9, -115.29, 70.42, 0, 2, 7, 2.3, 10.29, 1, 9, -131.88, 78.72, 0, 1, 7, -1.42, 13.73, 1, 1, 7, -12.34, 11.17, 1, 1, 7, -9.77, 0.27, 1, 1, 7, 15.08, -0.37, 1, 1, 7, 25.06, -0.93, 1, 1, 7, 47.9, -1.01, 1, 2, 7, 73.22, 0.05, 1, 9, -76.38, 33.38, 0, 2, 8, 15.75, 0.05, 1, 9, -56.88, 23.86, 0, 2, 7, 113.31, 5.14, 0.00019, 8, 34.48, 0.55, 0.99981, 3, 7, 87.48, 5.49, 0.27166, 8, 8.97, 4.62, 0.72834, 9, -61.36, 30.69, 0, 2, 7, 67.49, 6.77, 1, 9, -77.83, 42.1, 0, 2, 7, 44.56, 6.52, 1, 9, -97.61, 53.7, 0, 2, 7, 25.68, 3.28, 1, 9, -115.46, 60.66, 0], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 70], "width": 51, "height": 290}}, "xia": {"xia": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [343.4, -30.09, -221.37, -13.95, -220.26, 25.03, 344.51, 8.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 565, "height": 39}}, "xia2": {"xia": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [343.4, -30.09, -221.37, -13.95, -220.26, 25.03, 344.51, 8.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 565, "height": 39}}, "shang": {"shang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [343.21, -36.46, -221.56, -20.33, -220.5, 16.66, 344.27, 0.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 565, "height": 37}}, "guang": {"guang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [360.34, -931.99, -340.66, -931.99, -340.66, -31.99, 360.34, -31.99], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 701, "height": 900}}, "shang2": {"shang": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [343.21, -36.46, -221.56, -20.33, -220.5, 16.66, 344.27, 0.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 565, "height": 37}}, "caijian": {"caijian": {"type": "clipping", "end": "caijian", "vertexCount": 4, "vertices": [-933.45, 351.3, -357.02, 342.46, -365.86, -344.94, -935.42, -342], "color": "ce3a3aff"}}, "zhong": {"zhong": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [439.78, 253.65, 435.38, -257.33, -260.59, -251.33, -256.19, 259.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 511, "height": 696}}}}], "events": {"sj_biaoqian": {}, "sj_close": {}, "sj_jianzhutupian": {}, "sj_qianwangjianzao": {}, "sj_zi_jiesuojianzhu": {}}, "animations": {"animation": {"slots": {"guang": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}]}}, "bones": {"xia": {"translate": [{"y": 332.59, "curve": "stepped"}, {"time": 0.2, "y": 332.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "shang": {"translate": [{"y": -330.65, "curve": "stepped"}, {"time": 0.2, "y": -330.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "piaodai4": {"rotate": [{"time": 0.2}, {"time": 0.7667, "angle": 4.31}, {"time": 1.2333, "angle": 6.24}]}, "piaodai3": {"rotate": [{"time": 0.2}, {"time": 0.7667, "angle": 4.31}, {"time": 1.2333, "angle": 3.93}]}, "piaodai2": {"rotate": [{"time": 0.2}, {"time": 0.7667, "angle": 4.31}, {"time": 1.2333, "angle": 1.47}]}, "piaodai": {"rotate": [{"time": 0.2}, {"time": 0.7667, "angle": 4.31}, {"time": 1.2333}]}, "piaodai0": {"scale": [{"x": 0.494, "y": 0.122, "curve": "stepped"}, {"time": 0.2, "x": 0.494, "y": 0.122, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "gd_zi": {"scale": [{"time": 0.2, "curve": "stepped"}, {"time": 0.5333, "x": 0.019, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "gd_faguang": {"rotate": [{"angle": 90.76}], "scale": [{"time": 1.0333, "x": -0.004, "y": -0.004}]}}, "deform": {"default": {"caijian": {"caijian": [{"vertices": [1.25903, -337.86108, -5.97382, -328.7162, -4.95374, 341.8981, -0.48694, 339.38004], "curve": 0.531, "c3": 0.75}, {"time": 0.2, "vertices": [1.25903, -322.0911, -5.97382, -312.9462, -4.95374, 334.0131, -0.48694, 331.49503], "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "vertices": [-9.58307, -12.27121, -0.55286, -1.31918, -4.95374, 2.965, -0.48694, 0.44693]}]}}}, "events": [{"time": 0.6667, "name": "sj_<PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.6667, "name": "sj_ji<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.6667, "name": "sj_zi_ji<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.7667, "name": "sj_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.8667, "name": "sj_close"}]}, "animation2": {"bones": {"piaodai4": {"rotate": [{"angle": 6.24, "curve": 0.315, "c2": 0.27, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 7.01, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 1.3333, "angle": 6.24}]}, "piaodai3": {"rotate": [{"angle": 3.93, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 7.01, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "angle": 3.93}]}, "piaodai2": {"rotate": [{"angle": 1.47, "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 7.01, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 1.3333, "angle": 1.47}]}, "piaodai": {"rotate": [{}, {"time": 0.7, "angle": 7.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}}}}}