import { CCFloat, Component, Material, Sprite, Vec2, _decorator } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass
@executeInEditMode
export default class FlashLight extends Component {
  @property(Sprite)
  sprite!: Sprite;

  @property({ type: CCFloat, tooltip: "光束宽度" })
  lightWidth = 0.4;
  @property({ type: CCFloat, tooltip: "时间" })
  LoopTime = 1;
  @property({ type: CCFloat, tooltip: "TimeInterval" })
  TimeInterval = 3.0;
  @property({ type: CCFloat, tooltip: "起始位置" })
  startPos = -1.5;

  /**记录时间 */
  private time: number = 0;
  /**精灵上的材质 */
  private material: Material = null!;
  private moveLength = 0;
  private Speed = 0;
  private dttime = 0;
  private _isLoop = false;

  start() {
    this.time = 0;
    this.dttime = 0;
    this.material = this.sprite.getMaterialInstance(0); //获取材质

    log.log("材质", this.material.refCount);
    // this.sprite.setSharedMaterial(null, 0);
    // this.material.setProperty('mainTexture', this.sprite.spriteFrame.texture);
    // this.material.setProperty('lightColor', this.lightColor);
    // this.material.setProperty('lightAngle', this.lightAngle);
    if (this._isLoop) {
      this.material?.setProperty("lightWidth", this.lightWidth);
    } else {
      this.material.setProperty("lightWidth", 0);
    }
    // this.material.setProperty('enableGradient', this.enableGradient);
    // this.material.setProperty('cropAlpha', this.cropAlpha);
    // this.startFlash(true);
    // log.log("falsh start");
    // this.moveLength = this.lightWidth + 1;
    this.Speed = 1 / this.LoopTime;
    this.time = this.startPos;
  }
  public startFlash(loop: boolean) {
    this.material?.setProperty("lightWidth", this.lightWidth);
    if (this._isLoop) {
      return;
    }
    // this.sprite.setSharedMaterial(this.material, 0);
    this._isLoop = loop;
    this.time = this.startPos;
    this.dttime = 0;
  }
  public stopFlash() {
    this.material?.setProperty("lightWidth", 0);
    // this.sprite.setSharedMaterial(null, 0);
    this.time = this.startPos;
    this.dttime = 0;
  }
  protected onDestroy(): void {
    // this.material.decRef();
  }

  update(dt: number) {
    // if (!this.sprite.getSharedMaterial(0)) {
    //   return;
    // }
    this.time += dt * this.Speed;
    this.dttime += dt;
    this.material.setProperty("lightCenterPoint", new Vec2(this.time, 0)); //设置材质对应的属性
    if (this.dttime > this.LoopTime + this.TimeInterval) {
      if (this._isLoop) {
        this.time = this.startPos;
        this.dttime = 0;
      } else {
        this.stopFlash();
      }
    }
  }
}
