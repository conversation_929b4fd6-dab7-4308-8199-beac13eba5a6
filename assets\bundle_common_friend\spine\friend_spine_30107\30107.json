{"skeleton": {"hash": "GPKEFpUl/qFCGjBmYYVxAibVO+8", "spine": "3.8.75", "x": -527.12, "y": -634.4, "width": 1180.88, "height": 1379.68, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/zixia"}, "bones": [{"name": "root", "y": 0.7}, {"name": "bone", "parent": "root", "length": 144.52, "rotation": -175.66, "x": 76.58, "y": -59.96}, {"name": "bone2", "parent": "bone", "length": 133.32, "rotation": -88.31, "x": 2.3, "y": -14.37}, {"name": "bone3", "parent": "bone2", "length": 99.83, "rotation": 24.16, "x": 134.78, "y": 2.26}, {"name": "bozo", "parent": "bone3", "length": 31.13, "rotation": -21.52, "x": 100.41, "y": -0.34}, {"name": "bone5", "parent": "bozo", "x": 119.5, "y": -30.12}, {"name": "bone6", "parent": "bozo", "length": 60.74, "rotation": 138, "x": 171.18, "y": -17.93}, {"name": "bone7", "parent": "bone6", "length": 64.99, "rotation": 20.05, "x": 60.74}, {"name": "bone8", "parent": "bone7", "length": 58, "rotation": 27.78, "x": 66.79, "y": 2.82}, {"name": "bone9", "parent": "bozo", "length": 56.34, "rotation": -149.02, "x": 170.02, "y": -52.26}, {"name": "bone10", "parent": "bone9", "length": 56.58, "rotation": -21.12, "x": 56.34}, {"name": "bone11", "parent": "bone10", "length": 47.62, "rotation": -23.38, "x": 56.58}, {"name": "bone12", "parent": "bozo", "x": 244.17, "y": 1.99}, {"name": "bone13", "parent": "bozo", "length": 36.8, "rotation": 32.04, "x": 213.98, "y": 49.13, "scaleX": 1.2919}, {"name": "bone14", "parent": "bone13", "length": 59.49, "rotation": -23.79, "x": 40.89, "y": 2.65, "scaleX": 0.6657}, {"name": "bone15", "parent": "bozo", "length": 39.49, "rotation": -20.54, "x": 218.53, "y": -40.84, "scaleX": 0.8646}, {"name": "bone16", "parent": "bone15", "length": 50.16, "rotation": 10.54, "x": 44.42, "y": -1.48, "scaleX": 0.7313}, {"name": "bone17", "parent": "bozo", "length": 123.82, "rotation": 149.34, "x": 204.02, "y": -2.45}, {"name": "bone18", "parent": "bone17", "length": 107.45, "rotation": 16.84, "x": 133.28, "y": 6.73}, {"name": "bone19", "parent": "bone18", "length": 108.06, "rotation": -14.32, "x": 118.57, "y": -3.19}, {"name": "bone20", "parent": "bone19", "length": 143.75, "rotation": 34.03, "x": 109.53, "y": 3.54, "scaleX": 0.6745}, {"name": "bone21", "parent": "bone17", "length": 89.72, "rotation": -9.63, "x": 248.77, "y": 7.74}, {"name": "bone22", "parent": "bone21", "length": 73.13, "rotation": 30.17, "x": 95.92, "y": -1.87}, {"name": "bone23", "parent": "bone22", "length": 55.93, "rotation": 45.34, "x": 73.13}, {"name": "bone24", "parent": "bone3", "x": 77.27, "y": 65.96}, {"name": "bone25", "parent": "bone3", "x": 45.18, "y": -52.46}, {"name": "bone29", "parent": "bone25", "length": 158.64, "rotation": -176.75, "x": -11.3, "y": 4.27}, {"name": "bone26", "parent": "bone24", "length": 155.11, "rotation": 179.78, "x": -14.01, "y": 3.55}, {"name": "bone27", "parent": "bone", "length": 100, "rotation": -141.95, "x": 28.31, "y": -34.89}, {"name": "bone28", "parent": "bone27", "length": 54.86, "rotation": -10.86, "x": 133.68, "y": -0.34}, {"name": "bone30", "parent": "bone", "length": 101.02, "rotation": -102.61, "x": -96.79, "y": -61.95}, {"name": "bone32", "parent": "root", "length": 125.36, "rotation": -89.98, "x": 176.9, "y": 74.41}, {"name": "bone33", "parent": "bone27", "length": 131.36, "rotation": -145.05, "x": 27.21, "y": -33.87}, {"name": "bone34", "parent": "bone33", "length": 98.27, "rotation": -22.19, "x": 131.36}, {"name": "bone35", "parent": "bone34", "length": 161.35, "rotation": -21.24, "x": 98.27}, {"name": "bone36", "parent": "bone35", "length": 92.22, "rotation": 8.88, "x": 164.35, "y": 0.59}, {"name": "bone37", "parent": "bone", "length": 127.29, "rotation": 66.51, "x": 29.79, "y": 5.68}, {"name": "bone38", "parent": "bone37", "length": 94.12, "rotation": -20.26, "x": 127.29}, {"name": "bone39", "parent": "bone38", "length": 99.07, "rotation": -18.05, "x": 94.12}, {"name": "bone40", "parent": "bone39", "length": 76.76, "rotation": -15.63, "x": 99.07}, {"name": "bone41", "parent": "bone37", "rotation": -66.51, "x": 434.4, "y": -123.34}, {"name": "bone42", "parent": "bone37", "rotation": -66.51, "x": 380.05, "y": -5.87}, {"name": "bone43", "parent": "bone37", "rotation": -66.51, "x": 322.16, "y": 51.09}, {"name": "bone44", "parent": "bone37", "rotation": -66.51, "x": 206.48, "y": 96.45}, {"name": "bone46", "parent": "bone", "x": 62.82, "y": 361.54}, {"name": "bone47", "parent": "bone", "x": -23.16, "y": 327.94}, {"name": "bone45", "parent": "bone", "x": 160.99, "y": 365.33}, {"name": "bone75", "parent": "bone", "rotation": 83.8, "x": -120.14, "y": -129.2}, {"name": "bone48", "parent": "bone75", "length": 100, "x": 65.86, "y": 2.04}, {"name": "bone49", "parent": "bone48", "length": 110.55, "rotation": 8.76, "x": 100.63, "y": 0.03}, {"name": "bone50", "parent": "bone49", "length": 86.51, "rotation": -0.44, "x": 110.42, "y": -0.03}, {"name": "bone51", "parent": "bone50", "length": 76.08, "rotation": 23.97, "x": 88.03, "y": -4.4}, {"name": "bone52", "parent": "bone51", "length": 52.95, "rotation": 48.15, "x": 63.9, "y": -3.82}, {"name": "bone31", "parent": "bone30", "length": 62.82, "rotation": 16.28, "x": 106.97, "y": 2.9}, {"name": "bone53", "parent": "bone34", "length": 86.21, "rotation": 28.12, "x": -23.04, "y": 6.73}, {"name": "bone54", "parent": "bone3", "length": 83.59, "rotation": 128.31, "x": 55.81, "y": 102.39}, {"name": "bone55", "parent": "bone54", "length": 77.31, "rotation": -29.16, "x": 87.44, "y": 4.48}, {"name": "bone56", "parent": "bone55", "length": 95.35, "rotation": -28.55, "x": 73.74, "y": -0.02}, {"name": "bone57", "parent": "bone20", "length": 139.83, "rotation": 44.3, "x": 146.35, "y": 0.08}, {"name": "bone58", "parent": "bone57", "length": 75.66, "rotation": 60.67, "x": 139.83}, {"name": "bone59", "parent": "bone17", "length": 108.23, "rotation": 27.27, "x": -14.67, "y": 65.19}, {"name": "bone60", "parent": "bone59", "length": 85.2, "rotation": -33.51, "x": 108.23}, {"name": "bone61", "parent": "bone60", "length": 97.78, "rotation": 0.17, "x": 84.63, "y": -0.4}, {"name": "bone62", "parent": "bone61", "length": 76.82, "rotation": 6.63, "x": 97.78}, {"name": "bone63", "parent": "bone62", "length": 66.37, "rotation": 30.61, "x": 75.88, "y": 0.29}, {"name": "bone64", "parent": "bone63", "length": 58.98, "rotation": 18.38, "x": 66.37}, {"name": "bone65", "parent": "bone64", "length": 68.14, "rotation": 52.04, "x": 58.98}, {"name": "bone66", "parent": "bone22", "length": 64.85, "rotation": 55, "x": 56.2, "y": 14.86}, {"name": "bone67", "parent": "bone16", "length": 19.99, "rotation": 50.51, "x": 62.26, "y": 1.8}, {"name": "bone68", "parent": "bone31", "length": 30.59, "rotation": 0.79, "x": 26.45, "y": 18.05}, {"name": "bone69", "parent": "bone68", "length": 26.15, "rotation": -45.42, "x": 31.02, "y": -0.81}, {"name": "bone70", "parent": "bone31", "length": 44.52, "rotation": -13.64, "x": 22.14, "y": -7.3}, {"name": "bone71", "parent": "bone70", "length": 17.3, "rotation": 134.77, "x": 56.79, "y": 5.91}, {"name": "bone72", "parent": "bone56", "length": 58.85, "rotation": 11.37, "x": 97.92, "y": 1.07}, {"name": "bone73", "parent": "bone27", "x": 18.43, "y": 9.24}, {"name": "bone4", "parent": "bone3", "length": 83.08, "rotation": -86.93, "x": -15.82, "y": 27.74}, {"name": "bozo2", "parent": "bozo", "x": 61.09, "y": -23.75, "color": "abe323ff"}, {"name": "bozo3", "parent": "bozo2", "x": 23.49, "y": 1.19, "color": "abe323ff"}, {"name": "bozo4", "parent": "bozo", "x": 104.22, "y": -23.55}, {"name": "bone74", "parent": "bone53", "length": 59.95, "rotation": 2.57, "x": 96.09, "y": 2.77}, {"name": "bone76", "parent": "bone48", "length": 100.61, "rotation": -3.53, "x": 110.96, "y": -37.35}, {"name": "bone77", "parent": "bone76", "length": 80.94, "rotation": 0.48, "x": 100.61}, {"name": "bone78", "parent": "bone75", "length": 45.44, "rotation": 25.12, "x": 12.67, "y": 11.82}, {"name": "bone79", "parent": "bone78", "length": 79.28, "rotation": -28, "x": 45.53, "y": -1.54}, {"name": "bone80", "parent": "bone79", "length": 51.21, "rotation": 26.34, "x": 77.14, "y": 0.29}, {"name": "bone81", "parent": "bone80", "length": 55.06, "rotation": -38.89, "x": 51.39, "y": 0.46}, {"name": "bone82", "parent": "root", "x": 53.68, "y": 121.08}, {"name": "bone83", "parent": "bone3", "x": -25.83, "y": -16.49, "color": "abe323ff"}, {"name": "bone84", "parent": "bone", "rotation": -64.15, "x": -52.55, "y": -181.32}, {"name": "bone85", "parent": "bone37", "rotation": -66.51, "x": 172.06, "y": -51.33}, {"name": "bone86", "parent": "bone37", "rotation": -66.51, "x": 219.59, "y": -104.06}, {"name": "bone87", "parent": "bone37", "rotation": -66.51, "x": 305.47, "y": -161.33}, {"name": "bone88", "parent": "bone23", "length": 95.52, "rotation": 40.74, "x": 55.88, "y": 0.64}, {"name": "bone89", "parent": "bone10", "length": 59.26, "rotation": -5.62, "x": 25.2, "y": 14.85}, {"name": "bone90", "parent": "bone27", "x": -49.72, "y": -23.71}, {"name": "bone91", "parent": "bone37", "x": 28.41, "y": -40.89}, {"name": "bone92", "parent": "root", "x": 170.54, "y": 87, "color": "abe323ff"}, {"name": "bone93", "parent": "root", "rotation": -0.32, "x": 118.22, "y": 334.7}, {"name": "bone94", "parent": "bone93", "x": -385.54, "y": 88.65, "color": "ff0000ff"}, {"name": "bone95", "parent": "bone93", "x": -361.32, "y": -61.86, "color": "ff0000ff"}, {"name": "bone96", "parent": "bone93", "x": 5.44, "y": 145.74, "color": "ff0000ff"}, {"name": "bone97", "parent": "bone93", "x": -44.73, "y": -8.23, "color": "ff0000ff"}, {"name": "bone98", "parent": "bone93", "x": 316.84, "y": 40.21, "color": "ff0000ff"}, {"name": "bone99", "parent": "bone93", "x": 159.41, "y": -162.2, "color": "ff0000ff"}, {"name": "bone100", "parent": "bozo2", "x": 36.25, "y": 41.17}, {"name": "bone101", "parent": "bozo2", "x": 29.2, "y": -29.4}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "jiguang", "bone": "bone93", "color": "df3cff94", "attachment": "jiguang", "blend": "additive"}, {"name": "tou_fa_shang", "bone": "bone67", "attachment": "tou_fa_shang"}, {"name": "tou_fa_zuo", "bone": "bone21", "attachment": "tou_fa_zuo"}, {"name": "tou_fa_hou", "bone": "bone58", "attachment": "tou_fa_hou"}, {"name": "yifu_piaodai", "bone": "bone72", "attachment": "yifu_piaodai"}, {"name": "yifu_2", "bone": "bone", "attachment": "yifu_2"}, {"name": "gebo_you_1", "bone": "bone29", "attachment": "gebo_you_1"}, {"name": "shenti", "bone": "bone40", "attachment": "shenti"}, {"name": "shenti_shang", "bone": "bone4", "attachment": "shenti"}, {"name": "yifu_sidai", "bone": "bone74", "attachment": "yifu_sidai"}, {"name": "you_gebo_2", "bone": "bone52", "attachment": "you_gebo_2"}, {"name": "shou_you_3", "bone": "bone31", "attachment": "shou_you_3"}, {"name": "jian", "bone": "bone32", "attachment": "jian"}, {"name": "shou_you_2", "bone": "bone71", "attachment": "shou_you_2"}, {"name": "shou_you_1", "bone": "bone69", "attachment": "shou_you_1"}, {"name": "gebo_zuo1", "bone": "bone26", "attachment": "gebo_zuo1"}, {"name": "gebo_zuo2", "bone": "bone36", "attachment": "gebo_zuo2"}, {"name": "yanjing", "bone": "bozo3", "attachment": "yanjing"}, {"name": "lian", "bone": "bone5", "attachment": "lian"}, {"name": "yanjing2", "bone": "bozo3"}, {"name": "tou_liuhai", "bone": "bone9", "attachment": "tou_liuhai"}, {"name": "ui", "bone": "root"}], "transform": [{"name": "bone83", "order": 2, "bones": ["bone82"], "target": "bone83", "rotation": -120.19, "x": 55.99, "y": 2.55, "shearY": 360, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "bone92", "order": 3, "bones": ["bone32"], "target": "bone92", "rotation": -89.98, "x": 6.37, "y": -12.59, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "bone92-2", "order": 7, "bones": ["bone48"], "target": "bone92", "rotation": -91.86, "x": 15.97, "y": -74.93, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "bone92-3", "order": 6, "bones": ["bone75"], "target": "bone92", "rotation": -91.86, "x": 16.07, "y": -9.04, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "bone93", "order": 4, "bones": ["bone30"], "target": "bone92", "rotation": 81.73, "x": -2.13, "y": -77.87, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "bone94", "order": 5, "bones": ["bone75", "bone48"], "target": "bone92", "rotation": -91.86, "x": 16.07, "y": -9.04, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "bozo2", "bones": ["bone5"], "target": "bozo2", "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "face", "order": 1, "bones": ["bozo4"], "target": "bozo2", "x": 43.13, "y": 0.2, "rotateMix": -1.003, "translateMix": -1.003, "scaleMix": -1.003, "shearMix": -1.003}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [306, -550.87, -306, -550.87, -306, 741.89, 306, 741.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 214, "height": 359}}, "jiguang": {"jiguang": {"type": "mesh", "uvs": [1, 1, 0.83333, 1, 0.66667, 1, 0.5, 1, 0.33333, 1, 0.16667, 1, 0, 1, 0, 0.83333, 0, 0.66667, 0, 0.5, 0, 0.33333, 0, 0.16667, 0, 0, 0.16667, 0, 0.33333, 0, 0.5, 0, 0.66667, 0, 0.83333, 0, 1, 0, 1, 0.16667, 1, 0.33333, 1, 0.5, 1, 0.66667, 1, 0.83333, 0.83333, 0.83333, 0.66667, 0.83333, 0.5, 0.83333, 0.33333, 0.83333, 0.16667, 0.83333, 0.83333, 0.66667, 0.66667, 0.66667, 0.5, 0.66667, 0.33333, 0.66667, 0.16667, 0.66667, 0.83333, 0.5, 0.66667, 0.5, 0.5, 0.5, 0.33333, 0.5, 0.16667, 0.5, 0.83333, 0.33333, 0.66667, 0.33333, 0.5, 0.33333, 0.33333, 0.33333, 0.16667, 0.33333, 0.83333, 0.16667, 0.66667, 0.16667, 0.5, 0.16667, 0.33333, 0.16667, 0.16667, 0.16667], "triangles": [1, 24, 23, 1, 2, 24, 2, 3, 25, 3, 4, 26, 4, 5, 27, 5, 6, 28, 9, 10, 43, 6, 7, 28, 10, 11, 48, 7, 8, 33, 8, 9, 38, 48, 11, 13, 48, 13, 14, 11, 12, 13, 0, 1, 23, 23, 24, 22, 22, 29, 21, 21, 34, 20, 20, 39, 19, 19, 44, 18, 46, 15, 16, 47, 14, 15, 44, 17, 18, 45, 16, 17, 47, 48, 14, 32, 37, 36, 37, 42, 41, 5, 28, 27, 27, 28, 32, 28, 33, 32, 32, 33, 37, 33, 38, 37, 37, 38, 42, 38, 43, 42, 42, 43, 47, 43, 48, 47, 28, 7, 33, 33, 8, 38, 38, 9, 43, 43, 10, 48, 41, 46, 45, 45, 46, 16, 41, 42, 46, 42, 47, 46, 46, 47, 15, 39, 44, 19, 40, 45, 44, 44, 45, 17, 24, 29, 22, 29, 34, 21, 34, 39, 20, 2, 25, 24, 24, 25, 29, 25, 30, 29, 29, 30, 34, 30, 35, 34, 34, 35, 39, 35, 40, 39, 39, 40, 44, 3, 26, 25, 25, 26, 30, 26, 31, 30, 30, 31, 35, 31, 36, 35, 35, 36, 40, 36, 41, 40, 40, 41, 45, 4, 27, 26, 26, 27, 31, 27, 32, 31, 31, 32, 36, 36, 37, 41], "vertices": [1, 97, 533.3, -966.85, 1, 1, 97, 337.75, -966.85, 1, 1, 97, 142.2, -966.85, 1, 1, 97, -53.34, -966.85, 1, 1, 97, -248.89, -966.85, 1, 1, 97, -444.44, -966.85, 1, 1, 97, -639.99, -966.85, 1, 1, 97, -639.99, -737.98, 1, 1, 97, -639.99, -509.12, 1, 1, 97, -639.99, -280.25, 1, 1, 97, -639.99, -51.38, 1, 1, 97, -639.99, 177.48, 1, 1, 97, -639.99, 406.35, 1, 1, 97, -444.44, 406.35, 1, 1, 97, -248.89, 406.35, 1, 1, 97, -53.34, 406.35, 1, 1, 97, 142.2, 406.35, 1, 1, 97, 337.75, 406.35, 1, 1, 97, 533.3, 406.35, 1, 1, 97, 533.3, 177.48, 1, 1, 97, 533.3, -51.38, 1, 1, 97, 533.3, -280.25, 1, 1, 97, 533.3, -509.12, 1, 1, 97, 533.3, -737.98, 1, 4, 103, 178.34, -575.79, 0.83571, 102, 20.91, -778.2, 0.00263, 101, 382.48, -729.76, 0.00122, 99, 699.07, -676.13, 0.16045, 4, 103, -17.21, -575.79, 0.7349, 102, -174.64, -778.2, 1e-05, 101, 186.93, -729.76, 0.01083, 99, 503.52, -676.13, 0.25425, 3, 103, -212.76, -575.79, 0.59438, 101, -8.62, -729.76, 0.02317, 99, 307.97, -676.13, 0.38245, 3, 103, -408.3, -575.79, 0.46353, 101, -204.16, -729.76, 0.02209, 99, 112.43, -676.13, 0.51438, 3, 103, -603.85, -575.79, 0.37568, 101, -399.71, -729.76, 0.00953, 99, -83.12, -676.13, 0.61479, 4, 103, 178.34, -346.92, 0.86412, 102, 20.91, -549.33, 0.02445, 101, 382.48, -500.89, 0.00182, 99, 699.07, -447.26, 0.10962, 4, 103, -17.21, -346.92, 0.7646, 102, -174.64, -549.33, 0.00155, 101, 186.93, -500.89, 0.0256, 99, 503.52, -447.26, 0.20826, 3, 103, -212.76, -346.92, 0.57678, 101, -8.62, -500.89, 0.0634, 99, 307.97, -447.26, 0.35981, 3, 103, -408.3, -346.92, 0.40987, 101, -204.16, -500.89, 0.05919, 99, 112.43, -447.26, 0.53094, 3, 103, -603.85, -346.92, 0.31108, 101, -399.71, -500.89, 0.02437, 99, -83.12, -447.26, 0.66455, 4, 103, 178.34, -118.06, 0.85728, 102, 20.91, -320.47, 0.10232, 101, 382.48, -272.03, 0.00021, 99, 699.07, -218.4, 0.04019, 4, 103, -17.21, -118.06, 0.82849, 102, -174.64, -320.47, 0.00472, 101, 186.93, -272.03, 0.0441, 99, 503.52, -218.4, 0.12269, 3, 103, -212.76, -118.06, 0.5235, 101, -8.62, -272.03, 0.16862, 99, 307.97, -218.4, 0.30788, 3, 103, -408.3, -118.06, 0.29894, 101, -204.16, -272.03, 0.1373, 99, 112.43, -218.4, 0.56375, 3, 103, -603.85, -118.06, 0.20003, 101, -399.71, -272.03, 0.0381, 99, -83.12, -218.4, 0.76187, 3, 103, 178.34, 110.81, 0.64742, 102, 20.91, -91.6, 0.35173, 99, 699.07, 10.47, 0.00085, 3, 103, -17.21, 110.81, 0.97331, 101, 186.93, -43.16, 0.01936, 99, 503.52, 10.47, 0.00733, 3, 103, -212.76, 110.81, 0.3348, 101, -8.62, -43.16, 0.47163, 99, 307.97, 10.47, 0.19356, 3, 103, -408.3, 110.81, 0.11991, 101, -204.16, -43.16, 0.25536, 99, 112.43, 10.47, 0.62472, 4, 103, -603.85, 110.81, 0.06012, 101, -399.71, -43.16, 0.01353, 99, -83.12, 10.47, 0.89749, 98, -58.9, -140.04, 0.02887, 2, 103, 178.34, 339.68, 0.04411, 102, 20.91, 137.27, 0.95589, 4, 103, -17.21, 339.68, 0.24708, 102, -174.64, 137.27, 0.31351, 101, 186.93, 185.71, 0.18632, 100, 136.76, 31.74, 0.25309, 5, 102, -370.19, 137.27, 0.00029, 101, -8.62, 185.71, 0.86664, 100, -58.79, 31.74, 0.10851, 99, 307.97, 239.34, 0.00698, 98, 332.19, 88.83, 0.01758, 5, 103, -408.3, 339.68, 0.00182, 101, -204.16, 185.71, 0.30004, 100, -254.33, 31.74, 0.05872, 99, 112.43, 239.34, 0.35983, 98, 136.65, 88.83, 0.2796, 2, 99, -83.12, 239.34, 0.43021, 98, -58.9, 88.83, 0.56979], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 0], "width": 672, "height": 672}}, "shou_you_2": {"shou_you_2": {"type": "mesh", "uvs": [0.55086, 0.00738, 0.41016, 0.05888, 0.20747, 0.14759, 0.11924, 0.28638, 0.15978, 0.37365, 0.30124, 0.35088, 0.39519, 0.29321, 0.49257, 0.24993, 0.49124, 0.27779, 0.35293, 0.33502, 0.2194, 0.40799, 0.12163, 0.50528, 0.02146, 0.63405, 0.00955, 0.73134, 0.16216, 0.71418, 0.08586, 0.87299, 0.14309, 0.96456, 0.38155, 0.9059, 0.56278, 0.87299, 0.68201, 0.9989, 0.8704, 1, 0.92763, 0.84581, 0.9777, 0.73707, 0.92524, 0.65694, 0.98963, 0.5153, 0.93955, 0.43374, 0.96578, 0.28351, 0.82929, 0.23294, 0.82336, 0.1977, 0.80601, 0.09465, 0.63193, 0.01739, 0.57186, 0.20743, 0.75186, 0.18903, 0.58386, 0.26423, 0.69586, 0.24103, 0.38678, 0.18294, 0.57785, 0.1249, 0.39621, 0.46035, 0.60616, 0.37543, 0.46226, 0.62736, 0.70288, 0.5481, 0.47406, 0.80429, 0.67457, 0.75192, 0.21929, 0.57499, 0.31601, 0.70521, 0.2877, 0.87081, 0.76421, 0.88496, 0.26411, 0.26644], "triangles": [7, 31, 33, 31, 36, 32, 33, 31, 34, 34, 31, 32, 27, 32, 28, 27, 34, 32, 19, 46, 20, 20, 46, 21, 19, 18, 46, 16, 45, 17, 16, 15, 45, 17, 41, 18, 17, 45, 41, 21, 46, 42, 15, 14, 45, 46, 18, 42, 18, 41, 42, 45, 44, 41, 45, 14, 44, 21, 42, 22, 44, 39, 41, 41, 39, 42, 42, 23, 22, 42, 40, 23, 42, 39, 40, 13, 12, 14, 44, 14, 43, 14, 12, 43, 44, 43, 39, 23, 40, 24, 12, 11, 43, 43, 37, 39, 39, 37, 40, 37, 11, 10, 37, 43, 11, 37, 38, 40, 40, 38, 25, 40, 25, 24, 27, 25, 38, 38, 34, 27, 10, 9, 37, 38, 9, 8, 38, 37, 9, 25, 27, 26, 8, 33, 38, 38, 33, 34, 8, 7, 33, 32, 29, 28, 4, 47, 5, 4, 3, 47, 6, 5, 47, 3, 2, 47, 35, 7, 6, 6, 47, 35, 47, 2, 35, 7, 35, 31, 31, 35, 36, 32, 36, 29, 35, 1, 36, 35, 2, 1, 1, 0, 36, 36, 30, 29, 36, 0, 30], "vertices": [1, 72, -2.62, -6.27, 1, 1, 72, 3.44, -6.07, 1, 1, 72, 13.51, -7.72, 1, 1, 72, 21.9, -2.94, 1, 1, 72, 24.28, 2.45, 1, 1, 72, 19.18, 5.19, 1, 1, 72, 10.98, 4.58, 1, 1, 72, 8.73, 5.35, 1, 1, 71, 43.22, 10.15, 1, 2, 72, 22.18, 5.62, 0.00186, 71, 37.18, 17.7, 0.99814, 1, 71, 31.01, 21.7, 1, 1, 71, 23.5, 24.07, 1, 1, 71, 15.25, 25.35, 1, 1, 71, 15.05, 20.56, 1, 1, 71, 15.1, 19.07, 1, 1, 71, 5.37, 21.24, 1, 1, 71, -0.35, 18.46, 1, 1, 71, 3.62, 10.05, 1, 1, 71, 6.74, 2.68, 1, 1, 71, -0.6, -2.68, 1, 1, 71, 0.02, -10, 1, 1, 71, 10.21, -11.28, 1, 1, 71, 17.43, -12.56, 1, 1, 71, 22.42, -10.03, 1, 1, 71, 31.83, -11.67, 1, 1, 71, 36.92, -9.23, 1, 2, 72, -4.5, 17.53, 0.00745, 71, 47.51, -9.63, 0.99255, 1, 71, 50.98, -3.79, 1, 2, 72, -3.79, 9.29, 0.408, 71, 52.86, -3.32, 0.592, 1, 72, -6.71, 4.43, 1, 1, 72, -4.65, -3.76, 1, 2, 72, 4.69, 4.95, 0.056, 71, 49.97, 5.76, 0.944, 2, 72, -1.77, 7.55, 0.16, 71, 52.67, -0.66, 0.84, 1, 71, 45.45, 6.31, 1, 1, 71, 48.54, 1.69, 1, 1, 72, 11.28, -0.36, 1, 2, 72, 2.73, 0.43, 0.99026, 71, 54.56, 7.55, 0.00974, 1, 71, 29.51, 13.92, 1, 2, 72, 12.54, 14.19, 0.01343, 71, 37.89, 4.82, 0.98657, 1, 71, 20.29, 9.42, 1, 1, 71, 27.39, -0.06, 1, 1, 71, 10.13, 7.14, 1, 1, 71, 14.81, -0.64, 1, 1, 71, 21.15, 19.29, 1, 1, 71, 15.72, 13.75, 1, 1, 71, 5.67, 13.71, 1, 1, 71, 6.96, -5.19, 1, 1, 72, 18.13, 1.24, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 0, 60, 54, 56, 56, 58, 14, 62, 62, 64, 64, 56, 16, 66, 66, 68, 68, 54, 70, 72, 74, 76, 78, 80, 82, 84, 74, 86, 78, 88, 82, 90, 70, 94], "width": 37, "height": 63}}, "tou_fa_hou": {"tou_fa_hou": {"type": "mesh", "uvs": [0.60495, 0.07044, 0.68753, 0.06776, 0.77574, 0.10924, 0.83767, 0.18683, 0.8555, 0.2952, 0.8433, 0.40289, 0.77199, 0.48985, 0.67909, 0.56076, 0.61939, 0.6039, 0.59567, 0.62091, 0.54317, 0.67188, 0.51088, 0.7424, 0.52023, 0.8264, 0.55018, 0.87751, 0.62732, 0.91244, 0.7208, 0.91956, 0.67426, 0.93847, 0.51442, 0.98909, 0.43156, 1, 0.30771, 1, 0.19545, 0.96813, 0.11882, 0.90589, 0.07338, 0.82777, 0.0814, 0.72932, 0.12506, 0.62706, 0.20576, 0.52844, 0.30936, 0.42956, 0.37413, 0.34228, 0.40057, 0.24111, 0.41909, 0.14811, 0.50019, 0.0859, 0.62954, 0.15414, 0.62258, 0.26222, 0.59894, 0.3584, 0.53495, 0.45062, 0.40698, 0.58745, 0.3263, 0.70346, 0.3875, 0.90971, 0.44036, 0.95234, 0.51382, 0.17703, 0.49713, 0.26429, 0.47627, 0.35849, 0.39002, 0.45169, 0.28153, 0.5558, 0.20641, 0.67777, 0.19389, 0.79774, 0.23841, 0.88897, 0.33717, 0.95044, 0.71934, 0.14943, 0.73825, 0.24381, 0.72952, 0.3382, 0.6786, 0.44606, 0.58985, 0.52903, 0.49382, 0.63482, 0.42689, 0.73128, 0.4298, 0.82048, 0.49527, 0.8993, 0.53684, 0.96718, 0.48576, 0.96344, 0.37008, 0.92702, 0.31524, 0.87562, 0.30548, 0.78245, 0.31478, 0.80615, 0.33928, 0.83867, 0.3851, 0.88579, 0.43093, 0.92006, 0.48952, 0.9372, 0.54885, 0.94815, 0.61497, 0.94951, 0.29979, 0.83476], "triangles": [48, 1, 2, 31, 0, 1, 31, 1, 48, 31, 39, 30, 31, 30, 0, 29, 30, 39, 48, 2, 3, 28, 29, 39, 49, 48, 3, 32, 39, 31, 31, 48, 49, 40, 28, 39, 40, 39, 32, 49, 3, 4, 27, 28, 40, 66, 65, 56, 16, 14, 15, 67, 56, 13, 67, 13, 14, 66, 56, 67, 68, 67, 14, 68, 14, 16, 38, 65, 66, 67, 57, 66, 57, 67, 68, 16, 17, 68, 55, 11, 12, 64, 63, 55, 56, 55, 12, 65, 37, 64, 54, 36, 53, 11, 54, 10, 54, 62, 36, 55, 54, 11, 62, 54, 55, 52, 53, 35, 9, 53, 52, 10, 53, 9, 36, 35, 53, 54, 53, 10, 52, 34, 51, 52, 51, 7, 35, 34, 52, 8, 52, 7, 8, 9, 52, 33, 40, 32, 33, 32, 50, 5, 50, 4, 51, 33, 50, 51, 50, 5, 34, 33, 51, 6, 51, 5, 7, 51, 6, 32, 31, 49, 50, 32, 49, 50, 49, 4, 61, 62, 69, 61, 69, 45, 69, 62, 63, 60, 69, 63, 60, 63, 64, 46, 45, 69, 46, 69, 60, 21, 22, 45, 46, 21, 45, 37, 59, 60, 37, 60, 64, 47, 60, 59, 46, 60, 47, 59, 37, 65, 38, 59, 65, 58, 38, 66, 57, 58, 66, 20, 21, 46, 20, 46, 47, 17, 58, 57, 17, 57, 68, 19, 20, 47, 38, 47, 59, 18, 38, 58, 18, 58, 17, 18, 47, 38, 19, 47, 18, 44, 43, 36, 23, 24, 44, 61, 44, 36, 62, 61, 36, 45, 23, 44, 45, 44, 61, 22, 23, 45, 63, 62, 55, 56, 12, 13, 64, 55, 56, 56, 65, 64, 43, 25, 26, 43, 42, 35, 43, 24, 25, 44, 24, 43, 36, 43, 35, 41, 27, 40, 33, 41, 40, 34, 41, 33, 42, 27, 41, 42, 41, 34, 26, 27, 42, 43, 26, 42, 35, 42, 34], "vertices": [1, 4, 225.28, -8.23, 1, 1, 4, 221.6, -42.92, 1, 3, 17, -34.94, 65.91, 0.00083, 60, -17.69, 9.92, 0.02317, 4, 191.72, -76.02, 0.976, 2, 60, 27.56, 37.23, 0.024, 4, 142.37, -94.94, 0.976, 3, 60, 91.49, 46.42, 0.29922, 61, -39.58, 29.46, 0.04478, 4, 77.82, -92.71, 0.656, 4, 60, 155.36, 42.92, 0.02946, 61, 15.6, 61.81, 0.48272, 62, -68.85, 62.41, 0.00782, 4, 15.57, -78.02, 0.48, 3, 60, 207.6, 14.17, 0.44, 61, 75.03, 66.68, 0.36122, 62, -9.4, 67.11, 0.19878, 5, 60, 250.58, -23.94, 0.352, 61, 131.91, 58.64, 0.05319, 62, 47.45, 58.9, 0.55543, 63, -43.2, 64.32, 0.03644, 64, -69.88, 115.74, 0.00295, 4, 61, 167.27, 52.65, 0.00195, 62, 82.8, 52.81, 0.53379, 63, -8.79, 54.19, 0.40381, 64, -45.43, 89.5, 0.06045, 5, 61, 181.27, 50.23, 0.00141, 62, 96.78, 50.34, 0.40033, 63, 4.82, 50.12, 0.50117, 64, -35.78, 79.07, 0.09698, 65, -72, 107.25, 0.00012, 5, 60, 317.83, -79.57, 0.2, 62, 134.2, 49.39, 0.04861, 63, 41.88, 44.86, 0.57048, 64, -6.57, 55.67, 0.18047, 65, -51.65, 75.83, 0.00043, 3, 63, 85.11, 52.58, 0.16314, 64, 34.57, 40.31, 0.74633, 65, -17.46, 48.28, 0.09053, 5, 20, 113.29, 153.44, 0.344, 63, 127.07, 79.55, 0.00638, 64, 84.42, 42.16, 0.07181, 65, 30.43, 34.31, 0.46783, 66, 9.49, 43.62, 0.10998, 3, 20, 153.03, 172.33, 0.48, 65, 63.2, 35.39, 0.06098, 66, 30.5, 18.44, 0.45902, 2, 20, 172.53, 208.58, 0.296, 66, 67.71, 8.29, 0.704, 2, 20, 165.98, 248.01, 0.44, 66, 106.64, 15.91, 0.56, 3, 20, 188.48, 231.26, 0.352, 58, 174.25, 157.66, 0.00111, 66, 91.18, -0.58, 0.64689, 1, 58, 189.63, 70.97, 1, 1, 58, 185.98, 32.48, 1, 2, 58, 168.18, -18.2, 0.98036, 66, -45.86, -81.05, 0.01964, 1, 58, 127.91, -50.73, 1, 1, 58, 69.74, -55.9, 1, 2, 20, 174.92, -30.49, 0.20849, 58, 4.03, -41.64, 0.79151, 2, 19, 181.4, -4.19, 0.05365, 20, 89.47, -39.79, 0.94635, 2, 19, 123.08, -28.73, 0.94089, 20, -4.06, -34.89, 0.05911, 1, 19, 56.42, -39.89, 1, 2, 18, 87.41, -38.02, 0.70997, 19, -16.55, -43.71, 0.29003, 3, 17, 167.55, -16.31, 0.03778, 18, 29.71, -28.67, 0.54622, 4, 80.86, 112.31, 0.416, 3, 17, 109.67, -35.33, 0.07178, 18, -30.6, -37.28, 0.00022, 4, 138.39, 92.26, 0.928, 2, 17, 57.65, -54.97, 0.056, 4, 191.64, 76.24, 0.944, 2, 17, 8.9, -42.69, 0.048, 4, 222.89, 36.86, 0.952, 5, 17, 18.05, 24.6, 0.21809, 18, -110.32, 37.77, 4e-05, 60, 10.49, -51.07, 0.07781, 61, -53.29, -96.55, 6e-05, 4, 174.73, -11.02, 0.704, 4, 17, 75.56, 52.81, 0.39121, 18, -48.76, 55.48, 0.08561, 60, 74.52, -52.35, 0.37456, 61, 0.81, -62.25, 0.14862, 5, 17, 130.27, 71.46, 0.04525, 18, 8.37, 64.26, 0.28013, 60, 131.7, -60.85, 0.03914, 61, 53.17, -37.77, 0.61176, 62, -31.57, -37.28, 0.02373, 3, 18, 68.74, 56.16, 0.3049, 61, 113.38, -28.58, 0.03371, 62, 28.67, -28.27, 0.66139, 5, 18, 162.77, 30.93, 0.0039, 19, 28.97, 47.73, 0.32112, 20, -76.15, 76.19, 0.01149, 62, 126, -26.33, 0.0182, 63, 24.99, -29.41, 0.6453, 4, 19, 103.54, 65.48, 0.01531, 20, 34.18, 57.8, 0.37405, 63, 101.61, -26.96, 0.10409, 64, 8.28, -36.55, 0.50655, 4, 20, 202.63, 109.43, 0.00044, 58, 111.25, 52.42, 0.41011, 65, 56.66, -35.55, 0.46455, 66, -29.46, -20.03, 0.1249, 1, 58, 151.15, 56.12, 1, 2, 17, 53.42, -11.69, 0.408, 4, 168.7, 39.3, 0.592, 5, 17, 102.1, 6.99, 0.29628, 18, -30.65, 5.72, 0.01687, 60, 77.12, -105.24, 0.00271, 61, 32.17, -104.92, 0.00414, 4, 118.69, 54.04, 0.68, 4, 18, 24.99, 15.23, 0.91225, 60, 133.09, -112.6, 0.00152, 61, 82.9, -80.15, 0.05888, 62, -1.96, -79.74, 0.02735, 2, 18, 88.92, -1.58, 0.99563, 19, -28.24, -9.16, 0.00437, 1, 19, 48.42, -4.95, 1, 3, 20, 28.38, 5.12, 0.98048, 63, 112.11, -78.74, 0.00463, 64, -9.05, -86.46, 0.01489, 3, 20, 132.89, 15.32, 0.74747, 58, -1.39, 20.29, 0.21671, 64, 61.69, -94.72, 0.03582, 4, 20, 205.04, 45.34, 5e-05, 58, 74.11, 0.14, 0.99965, 64, 116.44, -78.22, 6e-05, 65, 22.85, -90.02, 0.00024, 1, 58, 134.88, 14.7, 1, 3, 17, -2.62, 56.49, 0.03276, 60, 6.72, -13.26, 0.22324, 4, 171.78, -48.9, 0.744, 4, 17, 42.52, 90.36, 0.0116, 18, -74.72, 98.23, 0.0011, 60, 62.37, -3.84, 0.98437, 61, -36.12, -28.52, 0.00294, 1, 61, 11.77, 0.51, 1, 2, 61, 76.4, 19.53, 0.72761, 62, -8.17, 19.95, 0.27239, 4, 61, 138.12, 17.02, 0.00708, 62, 53.54, 17.26, 0.97708, 63, -41.95, 22.26, 0.01478, 64, -90.23, 78.9, 0.00107, 3, 62, 128.09, 19.77, 0.02391, 63, 32.39, 16.14, 0.93448, 64, -29.36, 35.78, 0.04161, 3, 63, 96.06, 18.23, 0.02836, 64, 26.51, 5.17, 0.96961, 65, -36.19, 17.47, 0.00202, 2, 20, 120.45, 115.43, 0.304, 65, 13.61, -0.12, 0.696, 3, 20, 179.13, 152.49, 0.488, 58, 118.86, 100.89, 0.00038, 66, 12.17, -0.72, 0.51162, 1, 58, 176.25, 89.35, 1, 1, 58, 166.07, 70.03, 1, 1, 58, 121.86, 38.01, 1, 1, 58, 75.04, 37.19, 1, 5, 19, 145.11, 88.63, 0.00491, 20, 104.68, 59.34, 0.34465, 58, 3.06, 72.38, 0.34247, 63, 146.97, -12.61, 0.03338, 64, 54.62, -47.29, 0.27459, 4, 19, 153.39, 100.62, 0.00304, 20, 123.74, 66.2, 0.67907, 63, 157.48, -2.52, 0.02069, 64, 68.81, -43.96, 0.2972, 5, 20, 148.29, 80.46, 0.14717, 58, 50.51, 62.57, 0.39731, 64, 88.48, -34.44, 0.20556, 65, 10.12, -39.65, 0.224, 66, -61.32, 14.14, 0.02596, 4, 20, 182.47, 105.38, 0.38967, 64, 117.16, -16.29, 0.04502, 65, 43.06, -31.48, 0.4, 66, -34.61, -6.81, 0.16532, 3, 20, 205.63, 128.64, 0.35227, 65, 68.89, -20.6, 0.21009, 66, -10.15, -20.48, 0.43765, 4, 20, 212.39, 154.98, 0.328, 58, 146.74, 82.6, 0.0215, 65, 87.15, -1.08, 0.02205, 66, 16.47, -22.88, 0.62844, 2, 20, 213.75, 180.82, 0.232, 66, 42.31, -21.68, 0.768, 3, 20, 205.97, 208.24, 0.168, 58, 174.1, 128.75, 0.03328, 66, 69.2, -14.21, 0.79872, 1, 58, 41.87, 48.05, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 0, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 60, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 2, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 16, 14, 24, 22, 22, 20, 20, 18, 18, 16, 34, 114, 114, 116, 116, 76, 76, 118, 118, 120, 72, 124, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 112, 134, 132, 134, 134, 136, 30, 32, 136, 32, 32, 34, 120, 138, 138, 122], "width": 422, "height": 592}}, "shou_you_1": {"shou_you_1": {"type": "mesh", "uvs": [0.60989, 0, 0.959, 0.11265, 0.95716, 0.34365, 0.78188, 0.54259, 0.79406, 0.71761, 0.92106, 0.97905, 0.39132, 1, 0.08606, 0.83289, 0.04976, 0.54366, 0.20818, 0.33959, 0.49527, 0.20289, 0.59879, 0], "triangles": [5, 6, 4, 8, 9, 3, 4, 7, 8, 4, 8, 3, 6, 7, 4, 10, 11, 0, 1, 10, 0, 2, 10, 1, 9, 10, 2, 3, 9, 2], "vertices": [1, 70, 32.4, 6.89, 1, 1, 70, 27.16, -3.16, 1, 2, 70, 11.52, -7.99, 0.98855, 69, 35.25, -13.22, 0.01145, 2, 70, -1.27, -11.52, 0.00019, 69, 21.6, -8.68, 0.99981, 1, 69, 4.28, -5.92, 1, 2, 69, -6.39, -4.06, 0.464, 31, -58.99, -12.52, 0.536, 1, 31, -22.08, -14.47, 1, 1, 69, 6.54, 12.04, 1, 1, 69, 22.75, 9.54, 1, 2, 70, -0.27, 7, 0.63445, 69, 34.24, 5.81, 0.36555, 1, 70, 11.78, 8.63, 1, 1, 70, 32.32, 7.14, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 16, 18, 18, 20], "width": 21, "height": 69}}, "yifu_2": {"yifu_2": {"type": "mesh", "uvs": [0.74318, 0, 0.62221, 0.09792, 0.53506, 0.31835, 0.4297, 0.54302, 0.28662, 0.64264, 0.15785, 0.66807, 0.02127, 0.71258, 0, 0.85035, 0.00826, 1, 0.12533, 1, 0.2619, 1, 0.40629, 1, 0.54807, 1, 0.71716, 1, 0.87325, 1, 0.86414, 0.82704, 0.85634, 0.64264, 0.91487, 0.46884, 0.92138, 0.2802, 0.86805, 0.11276, 0.85504, 0, 0.78422, 0.20744, 0.7824, 0.47196, 0.74227, 0.70676, 0.70944, 0.88509, 0.52887, 0.60571, 0.49786, 0.91481, 0.26622, 0.86131, 0.66931, 0.37388, 0.64378, 0.59679, 0.40484, 0.7662, 0.61642, 0.78998], "triangles": [30, 4, 3, 27, 5, 4, 27, 4, 30, 9, 8, 7, 5, 7, 6, 9, 5, 27, 5, 9, 7, 10, 9, 27, 27, 30, 11, 10, 27, 11, 11, 30, 26, 30, 3, 25, 26, 30, 25, 31, 26, 25, 12, 26, 31, 12, 31, 24, 11, 26, 12, 12, 24, 13, 19, 21, 0, 19, 0, 20, 1, 0, 21, 21, 19, 18, 28, 1, 21, 2, 1, 28, 18, 22, 21, 22, 28, 21, 18, 17, 22, 29, 2, 28, 29, 28, 22, 25, 3, 2, 29, 25, 2, 16, 22, 17, 23, 29, 22, 23, 22, 16, 31, 25, 29, 31, 29, 23, 23, 16, 15, 24, 31, 23, 24, 23, 15, 14, 13, 24, 14, 24, 15], "vertices": [2, 45, 2.22, -199.54, 0.97453, 44, -83.77, -233.15, 0.02547, 3, 45, 65.71, -177.65, 0.90913, 44, -20.27, -211.26, 0.0902, 46, -118.44, -215.05, 0.00067, 4, 45, 85, -106.01, 0.59684, 44, -0.99, -139.62, 0.24607, 46, -99.16, -143.41, 0.02909, 42, -25.82, -43.87, 0.128, 4, 45, 132.17, -52.99, 0.1387, 44, 46.18, -86.59, 0.35415, 46, -51.98, -90.38, 0.22715, 42, 21.35, 9.16, 0.28, 3, 45, 192.39, -32.41, 0.00729, 44, 106.4, -66.02, 0.12948, 46, 8.24, -69.81, 0.86323, 2, 44, 159.4, -63.58, 0.00254, 46, 61.23, -67.37, 0.99746, 1, 46, 117.77, -60.39, 1, 1, 46, 130.02, -26.63, 1, 1, 46, 178.57, 89.29, 1, 1, 46, 133.5, 91.74, 1, 1, 46, 79.83, 98.51, 1, 3, 44, 122.01, 96.35, 0.32228, 46, 23.84, 92.56, 0.54972, 42, 97.18, 192.1, 0.128, 3, 45, 138.32, 148.06, 0.0118, 44, 52.34, 114.46, 0.94047, 46, -45.83, 110.67, 0.04773, 2, 45, 52.6, 150.8, 0.60417, 44, -33.39, 117.2, 0.39583, 2, 45, -30.34, 144.13, 0.93904, 44, -116.33, 110.53, 0.06096, 2, 45, -35.87, 25.72, 0.98398, 44, -121.85, -7.89, 0.01602, 1, 45, -39.73, -18.81, 1, 1, 45, -66.61, -57.34, 1, 2, 45, -73.28, -103.79, 0.99978, 44, -159.27, -137.4, 0.00022, 2, 45, -54, -131.37, 0.99393, 44, -139.99, -164.98, 0.00607, 2, 45, -49.55, -199.93, 0.99209, 44, -135.54, -233.54, 0.00791, 3, 45, -18.67, -126.21, 0.95834, 44, -104.66, -159.82, 0.04135, 46, -202.83, -163.61, 0.00031, 3, 45, -12.86, -62.15, 0.93511, 44, -98.85, -95.76, 0.05912, 46, -197.02, -99.55, 0.00577, 3, 45, 7.82, -3.49, 0.76687, 44, -78.17, -37.09, 0.23118, 46, -176.34, -40.88, 0.00195, 3, 45, 24.5, 40.76, 0.60641, 44, -61.49, 7.15, 0.38787, 46, -159.66, 3.37, 0.00572, 4, 45, 92.92, -34.58, 0.2679, 44, 6.93, -68.19, 0.3121, 46, -91.24, -71.98, 0.156, 42, -17.9, 27.56, 0.264, 4, 45, 111.33, 42.08, 0.06456, 44, 25.34, 8.47, 0.49416, 46, -72.83, 4.69, 0.20928, 42, 0.51, 104.22, 0.232, 3, 45, 204.8, 21.7, 0.00255, 44, 118.81, -11.91, 0.04536, 46, 20.65, -15.7, 0.95209, 3, 45, 31.35, -88.96, 0.82275, 44, -54.64, -122.56, 0.15942, 46, -152.81, -126.35, 0.01783, 3, 45, 45.91, -33.73, 0.62203, 44, -40.07, -67.33, 0.29096, 46, -138.24, -71.12, 0.08701, 4, 45, 146.49, 2.08, 0.04999, 44, 60.5, -31.52, 0.32815, 46, -37.67, -35.31, 0.40587, 42, 35.67, 64.23, 0.216, 3, 45, 60.66, 14.11, 0.44138, 44, -25.33, -19.5, 0.43616, 46, -123.49, -23.29, 0.12246], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 4, 50, 50, 52, 8, 54], "width": 409, "height": 251}}, "yanjing2": {"yanjing2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-31.76, -49.17, -13.67, 69.46, 38.72, 61.47, 20.63, -57.16], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 53}}, "tou_fa_zuo": {"tou_fa_zuo": {"type": "mesh", "uvs": [0.96195, 0.00698, 0.84405, 0.12082, 0.69778, 0.21804, 0.49692, 0.3063, 0.25458, 0.40607, 0.04935, 0.50456, 0, 0.5762, 0, 0.68748, 0.10175, 0.79877, 0.2131, 0.864, 0.4205, 0.95738, 0.63367, 1, 0.76244, 0.96022, 0.78413, 1, 0.83113, 0.86384, 0.78606, 0.85261, 0.55114, 0.7879, 0.41395, 0.71051, 0.33099, 0.61713, 0.37029, 0.51352, 0.56023, 0.4163, 0.75017, 0.31909, 0.90518, 0.20013, 0.9947, 0.0914, 0.99906, 0, 0.92702, 0.10675, 0.76109, 0.2449, 0.5384, 0.35874, 0.30261, 0.45724, 0.15415, 0.56852, 0.19563, 0.71178, 0.25676, 0.82947, 0.41177, 0.91773, 0.5908, 0.96761, 0.33972, 0.7476, 0.49037, 0.82691], "triangles": [13, 12, 14, 10, 33, 11, 11, 33, 12, 33, 14, 12, 35, 33, 32, 9, 32, 10, 32, 33, 10, 9, 31, 32, 32, 31, 35, 14, 33, 35, 35, 15, 14, 35, 16, 15, 31, 34, 35, 34, 17, 35, 35, 17, 16, 17, 34, 18, 34, 30, 18, 30, 29, 18, 31, 30, 34, 9, 8, 31, 8, 30, 31, 8, 7, 30, 7, 29, 30, 7, 6, 29, 18, 29, 19, 6, 5, 29, 29, 28, 19, 29, 5, 28, 5, 4, 28, 28, 4, 27, 19, 28, 20, 20, 28, 27, 20, 27, 21, 4, 3, 27, 26, 21, 27, 21, 26, 22, 27, 3, 2, 26, 27, 2, 25, 22, 26, 26, 2, 1, 22, 25, 23, 25, 26, 1, 25, 1, 0, 25, 0, 23, 23, 0, 24], "vertices": [1, 21, -89.43, -42.3, 1, 1, 21, -49.4, -25.51, 1, 1, 21, -8.17, -16.3, 1, 1, 21, 39.78, -15.58, 1, 2, 21, 96.35, -16.48, 0.14516, 22, -6.4, -13.13, 0.85484, 1, 22, 39.76, -34.03, 1, 2, 22, 64.87, -32.12, 0.93223, 23, -27.9, -17.94, 0.06777, 2, 22, 97.38, -16.04, 0.08327, 23, 6.88, -28.25, 0.91673, 1, 23, 47.19, -19.94, 1, 2, 23, 73.63, -5.59, 0.66639, 92, 11.89, -14.58, 0.33361, 1, 92, 61.69, -10.65, 1, 1, 92, 101.23, 6.3, 1, 2, 67, 144.6, 39.96, 0.00798, 92, 110.81, 32.4, 0.99202, 2, 67, 136.27, 57.62, 0.00729, 92, 114.85, 42.13, 0.99271, 2, 67, 123.88, 63.19, 0.00971, 92, 104.5, 56.96, 0.99029, 2, 67, 115.11, 59.49, 0.02242, 92, 91.58, 60.26, 0.97758, 2, 67, 76.38, 28.53, 0.6064, 92, 42.71, 47.3, 0.3936, 1, 67, 42.18, 16.15, 1, 3, 21, 124.99, 47.76, 0.00131, 22, 48.8, 30.46, 0.05456, 67, 7.87, 15.37, 0.94413, 3, 21, 99.06, 24.85, 0.36174, 22, 15.19, 22.21, 0.56122, 67, -19.13, 37.01, 0.07703, 1, 21, 51.08, 20.54, 1, 1, 21, 3.1, 16.23, 1, 1, 21, -43.65, 2.27, 1, 1, 21, -78.31, -16.35, 1, 1, 21, -96.51, -39.97, 1, 1, 21, -64.92, -19.91, 1, 1, 21, -12.8, -2.11, 1, 1, 21, 43.42, 2.91, 1, 3, 21, 98.73, 2.41, 0.12179, 22, 4.48, 2.49, 0.87759, 67, -41.3, 33.5, 0.00062, 1, 22, 49.57, -6.84, 1, 3, 23, 25.1, 5.32, 0.85242, 67, 24.15, -21.45, 0.14093, 92, -23.81, 20.09, 0.00665, 2, 23, 65.2, 5.6, 0.04838, 92, 10.54, -0.63, 0.95162, 1, 92, 51.8, -2.17, 1, 2, 67, 132.3, 9.48, 0.00726, 92, 88.08, 8.66, 0.99274, 3, 23, 44.12, 28.38, 0.00513, 67, 46.77, -1.91, 0.96368, 92, 4.04, 30.15, 0.0312, 2, 67, 82.67, 12.5, 0.49288, 92, 42.9, 29.96, 0.50712], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 68, 70, 70, 28, 24, 26, 28, 30, 26, 28], "width": 189, "height": 324}}, "yifu_sidai": {"yifu_sidai": {"type": "mesh", "uvs": [0.50719, 0.0174, 0.26262, 0.17912, 0.11375, 0.38019, 0, 0.59286, 0.09249, 0.83646, 0.17755, 1, 0.52845, 1, 0.77302, 0.78039, 1, 0.56192, 1, 0.30092, 1, 0.14239, 0.9362, 0, 0.61352, 0.25839, 0.50719, 0.44399, 0.45402, 0.64312], "triangles": [7, 14, 8, 4, 3, 14, 4, 14, 7, 6, 5, 4, 7, 6, 4, 0, 11, 10, 1, 0, 10, 12, 1, 10, 12, 10, 9, 2, 1, 12, 12, 9, 13, 12, 13, 2, 13, 9, 8, 3, 2, 13, 14, 3, 13, 8, 14, 13], "vertices": [1, 54, -42.36, -7.53, 1, 1, 54, 28.91, -13.57, 1, 1, 54, 62.38, -14.11, 1, 2, 54, 97.63, -13.39, 0.71154, 79, 6.76, -13.79, 0.28846, 1, 79, 46.65, -8.1, 1, 1, 79, 73.37, -3.59, 1, 1, 79, 72.61, 6.91, 1, 1, 79, 35.93, 11.59, 1, 2, 54, 89.04, 15.81, 0.9354, 79, -0.51, 15.76, 0.0646, 1, 54, 46.27, 10.75, 1, 1, 54, 20.3, 7.69, 1, 1, 54, -49.7, 2.95, 1, 1, 54, 40.66, -1.58, 1, 1, 54, 71.45, -1.16, 1, 1, 79, 14.04, 0.4, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 24, 26, 26, 28], "width": 28, "height": 163}}, "shenti_shang": {"shenti": {"type": "mesh", "uvs": [0.57594, 0.01953, 0.59128, 0.06398, 0.58644, 0.08667, 0.56221, 0.11167, 0.51538, 0.13158, 0.52264, 0.17093, 0.55171, 0.21167, 0.59128, 0.25427, 0.62116, 0.30705, 0.62923, 0.3464, 0.63892, 0.37881, 0.73906, 0.38946, 0.85857, 0.38946, 0.92236, 0.36075, 0.94093, 0.31909, 0.93695, 0.29227, 0.93447, 0.27556, 0.93367, 0.24779, 0.95062, 0.21815, 0.95708, 0.18806, 0.94187, 0.17878, 0.91913, 0.16491, 0.87919, 0.14479, 0.86584, 0.13806, 0.82384, 0.11445, 0.78266, 0.09407, 0.74632, 0.09083, 0.71806, 0.08065, 0.70595, 0.04685, 0.70191, 0.00657, 0.68334, 0, 0.59774, 0, 0.61236, 0.15145, 0.69908, 0.1276, 0.77342, 0.11949, 0.6513, 0.18697, 0.71678, 0.15957, 0.80351, 0.14739, 0.66811, 0.21589, 0.75395, 0.1961, 0.8513, 0.18646, 0.68581, 0.24684, 0.78316, 0.23619, 0.86988, 0.22858, 0.69554, 0.26562, 0.79909, 0.27069, 0.86634, 0.26308, 0.69112, 0.30317, 0.7867, 0.3194, 0.88847, 0.30824], "triangles": [30, 1, 0, 30, 0, 31, 30, 28, 1, 28, 30, 29, 27, 1, 28, 38, 35, 39, 39, 37, 40, 40, 22, 21, 40, 21, 20, 18, 20, 19, 43, 40, 20, 18, 43, 20, 42, 39, 40, 42, 40, 43, 41, 39, 42, 17, 43, 18, 7, 38, 41, 46, 42, 43, 46, 43, 17, 44, 41, 42, 45, 42, 46, 44, 42, 45, 46, 17, 16, 44, 7, 41, 47, 44, 45, 44, 8, 7, 47, 8, 44, 49, 46, 16, 49, 16, 15, 49, 15, 14, 48, 47, 45, 49, 48, 45, 49, 45, 46, 9, 8, 47, 13, 49, 14, 11, 10, 9, 11, 47, 48, 11, 9, 47, 12, 48, 49, 12, 49, 13, 11, 48, 12, 41, 38, 39, 7, 6, 38, 40, 37, 22, 1, 27, 2, 34, 26, 25, 34, 25, 24, 33, 27, 26, 33, 26, 34, 33, 2, 27, 32, 3, 2, 37, 34, 24, 37, 24, 23, 37, 23, 22, 33, 32, 2, 4, 3, 32, 36, 33, 34, 36, 34, 37, 32, 33, 36, 5, 4, 32, 35, 32, 36, 39, 36, 37, 35, 36, 39, 6, 5, 32, 6, 32, 35, 6, 35, 38], "vertices": [1, 4, 57.56, 31.79, 1, 3, 3, 133.84, 19.26, 0.34848, 75, 16.48, 149, 2e-05, 4, 23.92, 30.5, 0.6515, 1, 3, 120.37, 29.61, 1, 1, 3, 109.62, 47.93, 1, 2, 3, 107.01, 72.63, 0.93767, 75, -38.24, 125.06, 0.06233, 2, 3, 80.25, 84.82, 0.86801, 75, -51.85, 99, 0.13199, 2, 3, 47.88, 89.53, 0.67621, 75, -58.29, 66.93, 0.32379, 2, 3, 12.06, 91.09, 0.31497, 75, -61.76, 31.24, 0.68503, 2, 3, -28.18, 100.04, 0.03188, 75, -72.86, -8.47, 0.96812, 2, 3, -55.12, 111.94, 0.0004, 75, -86.18, -34.72, 0.9996, 1, 75, -96.07, -57.06, 1, 1, 75, -64.92, -87.26, 1, 2, 75, -22.52, -115.44, 0.7, 87, -106.47, 60.53, 0.3, 1, 75, 11.92, -112.71, 1, 1, 75, 35.65, -91.31, 1, 1, 75, 45.26, -73.77, 1, 2, 75, 51.25, -62.85, 0.832, 87, -50.01, -10.31, 0.168, 2, 75, 62.39, -45.47, 0.528, 87, -32.06, -20.5, 0.472, 1, 75, 70.1, -24.25, 1, 1, 75, 78.02, -2.72, 1, 2, 3, -7.37, -52.08, 0.00037, 75, 80.16, 4.17, 0.99963, 2, 3, 4.62, -52.08, 0.04265, 75, 80.8, 16.14, 0.95735, 3, 3, 21.45, -53.08, 0.31867, 75, 82.7, 32.89, 0.55333, 87, 47.28, -36.59, 0.128, 2, 3, 27.02, -53.52, 0.50478, 75, 83.44, 38.43, 0.49522, 2, 3, 51.22, -47.02, 0.84333, 75, 78.25, 62.94, 0.15667, 2, 3, 73.17, -39.6, 0.96207, 75, 72.01, 85.26, 0.03793, 1, 3, 83.11, -27.49, 1, 1, 3, 95.74, -20.96, 1, 2, 3, 119.99, -29.28, 0.0696, 4, 28.84, -19.74, 0.9304, 1, 4, 58.67, -22.72, 1, 1, 4, 64.73, -15.67, 1, 1, 4, 70.44, 20.34, 1, 2, 3, 73.3, 44.55, 0.9029, 75, -12.01, 89.9, 0.0971, 2, 3, 69.8, 3.72, 0.95149, 75, 28.57, 84.21, 0.04851, 2, 3, 58.9, -26.62, 0.95038, 75, 58.29, 71.71, 0.04962, 2, 3, 42.14, 43.66, 0.79341, 75, -12.8, 58.74, 0.20659, 3, 3, 45.51, 9.29, 0.7586, 75, 21.71, 60.26, 0.1614, 87, 71.34, 25.78, 0.08, 3, 3, 34.53, -27.13, 0.66598, 75, 57.49, 47.34, 0.21002, 87, 60.36, -10.64, 0.124, 3, 3, 20, 48.41, 0.19666, 75, -18.72, 36.88, 0.71534, 87, 45.82, 64.9, 0.088, 3, 3, 19.81, 6.11, 0.15317, 75, 23.51, 34.43, 0.64683, 87, 45.64, 22.6, 0.2, 3, 3, 7.7, -34.19, 0.14164, 75, 63.1, 20.18, 0.55836, 87, 33.53, -17.7, 0.3, 3, 3, -3.64, 53.6, 0.16327, 75, -25.17, 13.55, 0.70073, 87, 22.18, 70.09, 0.136, 2, 75, 20.14, -2.91, 0.8, 87, 8.18, 23.96, 0.2, 2, 75, 47.05, -18.15, 0.7, 87, -5.6, -3.73, 0.3, 3, 3, -17.77, 57.11, 0.06006, 75, -29.43, -0.36, 0.85994, 87, 8.06, 73.6, 0.08, 2, 75, 5.22, -27.91, 0.664, 87, -17.59, 37.52, 0.336, 2, 75, 32.21, -39.06, 0.468, 87, -27.28, 9.97, 0.532, 3, 3, -40.84, 72.91, 0.00806, 75, -46.44, -22.56, 0.90394, 87, -15.01, 89.4, 0.088, 1, 75, -19.21, -55.14, 1, 2, 75, 21.49, -72.22, 0.788, 87, -60.97, 18.9, 0.212], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 64, 66, 66, 68, 68, 48, 70, 72, 72, 74, 42, 44, 44, 46, 74, 44, 76, 78, 78, 80, 38, 40, 40, 42, 80, 40, 82, 84, 84, 86, 86, 36, 88, 90, 90, 92, 92, 34, 94, 96, 96, 98, 28, 30, 30, 32, 98, 30], "width": 426, "height": 743}}, "yanjing": {"yanjing": {"type": "mesh", "uvs": [0.92603, 0.17336, 0.90642, 0.5072, 0.85212, 0.72842, 0.79045, 0.89581, 0.51772, 0.95027, 0.18646, 0.98281, 0.09323, 0.87124, 0.00846, 0.64625, 0.0074, 0.31506, 0.08877, 0.09106, 0.26899, 0.07463, 0.86811, 0.00132], "triangles": [1, 11, 0, 7, 8, 9, 10, 11, 4, 2, 11, 1, 6, 9, 10, 7, 9, 6, 11, 2, 4, 10, 5, 6, 4, 2, 3, 4, 5, 10], "vertices": [5.88, -46.82, -8.62, -42.23, -17.47, -34.29, -23.81, -25.84, -21.3, 6.88, -16.75, 46.4, -10.1, 56.7, 1.44, 65.23, 16.19, 63.11, 24.68, 51.94, 22.16, 30.45, 14.58, -41.12], "hull": 12, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 2, 2, 4, 4, 6, 0, 22], "width": 118, "height": 43}}, "tou_liuhai": {"tou_liuhai": {"type": "mesh", "uvs": [0.45259, 0.20024, 0.53564, 0.203, 0.62406, 0.27469, 0.71917, 0.40566, 0.76606, 0.52698, 0.77409, 0.69104, 0.74864, 0.7903, 0.63477, 0.88267, 0.6897, 0.92816, 0.81294, 0.91162, 0.93485, 0.81925, 1, 0.66622, 0.97906, 0.48976, 0.91074, 0.37809, 0.83706, 0.25815, 0.74864, 0.13545, 0.64683, 0.04859, 0.50751, 0, 0.37489, 0, 0.2503, 0.061, 0.15519, 0.18094, 0.09892, 0.33397, 0.05338, 0.51044, 0.00917, 0.67036, 0, 0.81098, 0.04802, 0.93506, 0.17528, 0.96263, 0.31996, 0.94333, 0.31059, 0.83993, 0.24226, 0.72275, 0.21145, 0.5711, 0.23155, 0.43461, 0.35211, 0.3257, 0.46464, 0.07892, 0.36953, 0.11615, 0.2503, 0.23884, 0.1726, 0.37395, 0.12572, 0.5518, 0.12304, 0.71034, 0.15385, 0.8551, 0.58387, 0.12304, 0.67228, 0.19197, 0.754, 0.29537, 0.82232, 0.40704, 0.86787, 0.56834, 0.87323, 0.7255, 0.82634, 0.82477], "triangles": [12, 43, 13, 43, 14, 13, 44, 12, 11, 9, 8, 6, 9, 46, 10, 8, 7, 6, 9, 6, 46, 46, 45, 10, 46, 6, 45, 10, 45, 11, 6, 5, 45, 5, 44, 45, 45, 44, 11, 5, 4, 44, 4, 43, 44, 12, 44, 43, 4, 3, 43, 3, 42, 43, 3, 2, 42, 2, 41, 42, 41, 15, 42, 42, 15, 14, 2, 1, 41, 1, 40, 41, 40, 1, 33, 40, 16, 41, 41, 16, 15, 33, 17, 40, 40, 17, 16, 43, 42, 14, 25, 39, 26, 27, 26, 28, 26, 39, 28, 25, 24, 39, 24, 38, 39, 39, 29, 28, 39, 38, 29, 24, 23, 38, 38, 30, 29, 38, 37, 30, 38, 23, 37, 23, 22, 37, 30, 37, 31, 31, 37, 36, 37, 22, 36, 22, 21, 36, 31, 36, 32, 36, 21, 35, 34, 18, 33, 36, 35, 32, 21, 20, 35, 32, 34, 0, 32, 35, 34, 20, 19, 35, 35, 19, 34, 1, 0, 33, 0, 34, 33, 34, 19, 18, 33, 18, 17], "vertices": [2, 6, 7.12, 13.88, 0.85876, 9, -5.5, -33.24, 0.14124, 2, 6, -3.89, 27.64, 0.24785, 9, 6.84, -20.67, 0.75215, 3, 6, -4.84, 51.48, 0.00164, 9, 30.4, -16.87, 0.99122, 10, -19.4, -24.11, 0.00714, 2, 9, 63.9, -20.3, 0.14214, 10, 13.51, -16.93, 0.85786, 2, 10, 40.37, -18.23, 0.85165, 11, -8.84, -22.74, 0.14835, 2, 10, 71.84, -30.67, 0.02562, 11, 25, -23.43, 0.97438, 1, 11, 45.01, -30.25, 1, 1, 11, 62.29, -55.67, 1, 1, 11, 72.46, -44.72, 1, 1, 11, 70.9, -18.41, 1, 2, 11, 53.75, 8.71, 0.98838, 93, 85.89, -21.48, 0.01162, 1, 93, 61.75, 3.05, 1, 1, 93, 26.35, 12.43, 1, 1, 93, -0.39, 7.53, 1, 3, 9, 58.46, 18.67, 0.53548, 10, -3.81, 18.4, 0.33849, 93, -29.13, 2.2, 0.12603, 1, 9, 27.17, 21.99, 1, 1, 9, -0.61, 18.21, 1, 2, 6, -31.8, -4.06, 0.53678, 9, -27.98, 3.25, 0.46322, 2, 6, -13.54, -25.44, 0.97473, 9, -47.01, -17.44, 0.02527, 1, 6, 13.18, -37.35, 1, 2, 6, 45.06, -36.63, 0.94542, 7, -27.28, -29.04, 0.05458, 2, 6, 77.3, -26.27, 0.24829, 7, 6.55, -30.35, 0.75171, 1, 7, 44.36, -24.42, 1, 2, 7, 78, -22.78, 0.75768, 8, -2.02, -27.87, 0.24232, 2, 7, 105.82, -14.47, 0.16477, 8, 26.47, -33.48, 0.83523, 2, 7, 126.2, 4.01, 0.01296, 8, 53.12, -26.63, 0.98704, 1, 8, 62.18, -0.59, 1, 1, 8, 61.99, 26.92, 1, 1, 8, 46.46, 30.91, 1, 1, 8, 14.97, 19.78, 1, 2, 7, 43.84, 10.25, 0.907, 8, -16.84, 17.27, 0.093, 2, 6, 74.27, 9.63, 0.00223, 7, 16.01, 4.4, 0.99777, 1, 6, 40.61, 14.48, 1, 2, 6, -13.54, -0.41, 0.7491, 9, -22.16, -14.44, 0.2509, 1, 6, 5.39, -10.76, 1, 2, 6, 41.03, -13.55, 0.99198, 7, -23.16, -5.97, 0.00802, 2, 6, 73.12, -8.47, 0.09867, 7, 8.74, -12.2, 0.90133, 1, 7, 46.7, -7.69, 1, 2, 7, 77.27, 2.72, 0.13694, 8, 9.22, -4.97, 0.86306, 2, 7, 102.92, 19.27, 0.0012, 8, 39.63, -2.28, 0.9988, 2, 6, -23.05, 24.71, 0.01687, 9, 1.64, -1.99, 0.98313, 1, 9, 24.78, 2.19, 1, 3, 9, 52.18, 0.52, 0.99822, 10, -4.12, -0.8, 0.0012, 93, -28.55, -17, 0.00058, 2, 10, 22.82, 2.86, 0.88127, 93, -1.82, -12.09, 0.11873, 2, 10, 57.06, -2.11, 0.37871, 11, 1.19, -1.81, 0.62129, 1, 11, 33.56, -2.96, 1, 1, 11, 53.26, -14.32, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 12, 14, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 212, "height": 206}}, "yifu_piaodai": {"yifu_piaodai": {"type": "mesh", "uvs": [0.78351, 0, 0.72256, 0.12683, 0.68913, 0.2124, 0.54755, 0.27784, 0.41581, 0.34077, 0.50036, 0.41124, 0.61834, 0.45025, 0.58884, 0.55974, 0.50429, 0.6214, 0.31552, 0.64028, 0.17394, 0.68684, 0.13265, 0.76109, 0, 0.84793, 0.02057, 0.89449, 0.13855, 0.87435, 0.25063, 1, 0.32142, 0.99517, 0.33125, 0.92595, 0.39221, 0.81646, 0.48266, 0.75354, 0.70289, 0.67426, 0.84643, 0.59372, 0.96442, 0.45277, 1, 0.30175, 0.99784, 0.16458, 0.94082, 0.04629, 0.88183, 0, 0.85627, 0.1306, 0.83464, 0.24261, 0.78941, 0.40117, 0.25172, 0.79836, 0.38242, 0.70017, 0.85693, 0.37648, 0.87966, 0.46377, 0.86318, 0.52734, 0.8088, 0.5559, 0.81856, 0.47826, 0.81299, 0.3935, 0.67218, 0.28394, 0.68894, 0.29857, 0.65694, 0.39806, 0.58683, 0.39318, 0.62036, 0.32686, 0.16741, 0.83639], "triangles": [30, 31, 18, 11, 10, 30, 43, 11, 30, 12, 11, 43, 14, 12, 43, 13, 12, 14, 17, 30, 18, 43, 30, 17, 14, 43, 17, 17, 15, 14, 16, 15, 17, 8, 7, 20, 31, 9, 8, 19, 31, 8, 19, 8, 20, 31, 10, 9, 18, 31, 19, 31, 30, 10, 37, 39, 32, 37, 29, 39, 22, 32, 23, 33, 32, 22, 37, 32, 33, 37, 36, 29, 33, 36, 37, 34, 36, 33, 36, 6, 29, 35, 36, 34, 6, 40, 29, 35, 6, 36, 21, 35, 34, 22, 21, 34, 22, 34, 33, 35, 7, 6, 20, 35, 21, 20, 7, 35, 27, 0, 26, 27, 26, 25, 1, 0, 27, 27, 25, 24, 28, 1, 27, 28, 27, 24, 2, 1, 28, 38, 3, 2, 38, 2, 28, 39, 38, 28, 28, 24, 23, 42, 3, 38, 42, 38, 39, 32, 28, 23, 39, 28, 32, 42, 4, 3, 41, 4, 42, 40, 42, 39, 41, 42, 40, 5, 4, 41, 6, 41, 40, 5, 41, 6, 29, 40, 39], "vertices": [1, 55, -38.78, -4.18, 1, 1, 55, 2.55, -16.5, 1, 1, 55, 30.42, -23.2, 1, 2, 55, 51.95, -52.46, 0.97466, 56, -6.27, -66.81, 0.02534, 2, 55, 72.64, -79.68, 0.92445, 56, 24.41, -81.9, 0.07555, 2, 55, 95.38, -61.9, 0.8148, 56, 36.8, -55.81, 0.1852, 3, 55, 107.85, -37.24, 0.34594, 56, 36.91, -28.19, 0.65337, 57, -25.52, -43.76, 0.00069, 3, 55, 143.87, -43.26, 0.00076, 56, 71.83, -17.45, 0.30945, 57, 1.17, -18.82, 0.68979, 1, 57, 31.43, -11.35, 1, 1, 57, 67.99, -32.33, 1, 2, 57, 107.14, -37.42, 0.85861, 73, -9.49, -34.86, 0.14139, 2, 57, 126.81, -17.21, 0.27879, 73, 16.64, -27.34, 0.72121, 1, 73, 55.96, -30.95, 1, 1, 73, 65, -18.09, 1, 1, 73, 44.45, -3.17, 1, 1, 73, 61.44, 40.67, 1, 1, 73, 50.97, 51.08, 1, 1, 73, 33.18, 37.88, 1, 2, 57, 87.53, 25.02, 0.39959, 73, 5, 27.44, 0.60041, 3, 56, 142.79, -8.44, 0.00384, 57, 61.03, 20.36, 0.9793, 73, -20.11, 37.13, 0.01686, 2, 56, 98.48, 20.61, 0.92618, 57, 8.47, 27.08, 0.07382, 1, 56, 57.16, 35.28, 1, 2, 55, 108.03, 34.74, 0.00233, 56, 4.87, 36.28, 0.99767, 2, 55, 58.88, 41.71, 0.7018, 56, -42.2, 20.52, 0.2982, 2, 55, 14.31, 40.87, 0.99045, 56, -81.69, -0.17, 0.00955, 1, 55, -24.03, 28.67, 1, 1, 55, -38.97, 16.26, 1, 1, 55, 3.53, 11.32, 1, 2, 55, 39.97, 7.15, 0.98945, 56, -43.66, -18.85, 0.01055, 2, 55, 91.58, -1.8, 0.19933, 56, 6.51, -3.77, 0.80067, 2, 57, 108.17, 3.12, 0.00334, 73, 10.44, -0.48, 0.99666, 1, 57, 67.26, -6.04, 1, 2, 55, 83.43, 12.17, 0.22962, 56, -7.03, 5.08, 0.77038, 1, 56, 16.08, 22.21, 1, 1, 56, 36.03, 28.56, 1, 1, 56, 49.45, 22.72, 1, 1, 56, 26.06, 13.04, 1, 2, 55, 89.04, 3.08, 0.02022, 56, 2.06, -0.54, 0.97978, 2, 55, 53.7, -26.52, 0.99716, 56, -16.31, -42.82, 0.00284, 2, 55, 58.42, -23, 0.99501, 56, -13.67, -37.56, 0.00499, 2, 55, 90.81, -29.37, 0.62784, 56, 18.15, -28.76, 0.37216, 2, 55, 89.36, -43.96, 0.74222, 56, 23.38, -42.47, 0.25778, 2, 55, 67.74, -37.18, 0.94215, 56, 1.01, -46.07, 0.05785, 2, 57, 128.38, 6.48, 0.00182, 73, 30.87, -2.1, 0.99818], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 60, 62, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 64, 76, 78, 78, 80, 80, 82, 82, 84, 84, 76, 28, 86, 86, 60], "width": 206, "height": 323}}, "tou_fa_shang": {"tou_fa_shang": {"type": "mesh", "uvs": [0.24296, 0.48794, 0.29099, 0.35615, 0.43945, 0.26554, 0.60829, 0.25113, 0.71745, 0.35615, 0.71308, 0.48794, 0.76839, 0.37468, 0.80187, 0.20994, 0.77858, 0.09051, 0.70726, 0.05962, 0.72909, 0, 0.85863, 0.02873, 0.87319, 0.16464, 0.89648, 0.34997, 0.87755, 0.5003, 0.90084, 0.60326, 0.82224, 0.72476, 0.70726, 0.72064, 0.55152, 0.733, 0.42198, 0.75977, 0.3332, 0.86479, 0.26479, 0.95334, 0.17164, 0.92245, 0.19493, 0.83802, 0.11342, 0.70211, 0.04501, 0.60944, 0.02463, 0.43852, 0, 0.33968, 0.00426, 0.07815, 0.05956, 0, 0.11778, 0.09257, 0.10469, 0.24083, 0.17309, 0.31291, 0.79022, 0.60532, 0.26902, 0.76883, 0.19479, 0.61582, 0.13816, 0.46323, 0.8237, 0.45911, 0.83971, 0.2779, 0.05811, 0.28614, 0.05083, 0.14817, 0.3575, 0.60509, 0.50687, 0.40953, 0.63687, 0.58873, 0.29834, 0.65022, 0.35593, 0.778, 0.74206, 0.56692, 0.74337, 0.641, 0.47747, 0.60299], "triangles": [8, 11, 12, 9, 10, 8, 8, 10, 11, 37, 38, 13, 38, 6, 7, 38, 12, 13, 38, 7, 12, 7, 8, 12, 17, 47, 16, 47, 33, 16, 16, 33, 15, 17, 43, 47, 43, 46, 47, 47, 46, 33, 33, 14, 15, 33, 37, 14, 33, 46, 37, 43, 5, 46, 46, 5, 37, 14, 37, 13, 5, 6, 37, 37, 6, 38, 25, 36, 35, 25, 26, 36, 36, 32, 0, 26, 39, 36, 36, 39, 32, 32, 39, 31, 26, 27, 39, 27, 40, 39, 27, 28, 40, 39, 40, 31, 31, 40, 30, 30, 40, 29, 40, 28, 29, 22, 23, 21, 21, 34, 20, 21, 23, 34, 20, 45, 19, 20, 34, 45, 34, 23, 35, 34, 44, 45, 45, 41, 19, 45, 44, 41, 23, 24, 35, 34, 35, 44, 19, 41, 48, 24, 25, 35, 41, 44, 0, 44, 35, 0, 35, 36, 0, 41, 0, 1, 19, 48, 18, 18, 43, 17, 18, 48, 43, 42, 48, 41, 42, 41, 1, 48, 42, 43, 42, 1, 2, 43, 42, 5, 5, 42, 4, 4, 42, 3, 42, 2, 3], "vertices": [3, 12, 11.29, 55.85, 0.11453, 13, 30.78, -14.62, 0.8743, 14, -3.44, -19.88, 0.01117, 2, 12, 27.5, 44.1, 0.44246, 13, 36.59, -33.18, 0.55754, 2, 12, 35.32, 14.23, 0.81152, 13, 29.45, -62.65, 0.18848, 3, 12, 32.38, -17.95, 0.93366, 13, 14.31, -88.37, 0.00502, 15, 52.77, 43.66, 0.06131, 2, 12, 15.23, -36.42, 0.76337, 15, 41.69, 20.33, 0.23663, 2, 12, -2.24, -32.92, 0.36114, 15, 21.35, 17.49, 0.63886, 3, 12, 11.29, -45.66, 0.00623, 15, 41.17, 10.3, 0.78405, 16, -1.41, 12.17, 0.20972, 1, 16, 34.05, 5.62, 1, 2, 16, 59.09, 10.01, 0.33872, 68, 4.32, 7.67, 0.66128, 1, 68, 18.4, 12.09, 1, 1, 68, 23.38, -0.61, 1, 1, 68, 1.48, -13.14, 1, 1, 16, 44.69, -8.11, 1, 2, 15, 50.77, -12.96, 0.04237, 16, 5.66, -12.45, 0.95763, 2, 15, 26.94, -13.6, 0.92607, 16, -26.53, -8.71, 0.07393, 1, 15, 12.27, -20.81, 1, 1, 15, -9.87, -9.49, 1, 3, 12, -33.13, -27.08, 0.23069, 13, -32.43, -61.36, 0.00034, 15, -14.47, 12.11, 0.76897, 3, 12, -30.29, 2.57, 0.71425, 13, -18.39, -37.72, 0.13799, 15, -23.44, 40.88, 0.14775, 3, 12, -37.31, 24.77, 0.31588, 13, -13.88, -15.18, 0.68052, 15, -40.05, 59.2, 0.00361, 2, 12, -47.92, 44.6, 0.00077, 13, -12.69, 7.26, 0.99923, 1, 13, -8.19, 23.2, 1, 1, 13, 3.24, 33.96, 1, 2, 13, 7.68, 23.16, 0.99606, 14, -58.07, 5.39, 0.00394, 2, 13, 26.3, 22.99, 0.74885, 14, -32.36, 12.75, 0.25115, 2, 13, 40.24, 24.74, 0.19789, 14, -14.26, 19.96, 0.80211, 1, 14, 14.38, 15.13, 1, 1, 14, 31.52, 14.54, 1, 1, 14, 73.76, 1.09, 1, 1, 14, 83.95, -12.19, 1, 1, 14, 66.31, -17.67, 1, 2, 13, 63.69, -16.36, 0.00181, 14, 42.85, -8.21, 0.99819, 2, 13, 51.38, -19.92, 0.13306, 14, 28.09, -16.42, 0.86694, 1, 15, 6.93, -0.19, 1, 1, 13, 6.01, 6.34, 1, 2, 13, 25.29, 3.61, 0.97762, 14, -22.02, -5.4, 0.02238, 3, 12, 17.6, 75.14, 9e-05, 13, 42.84, -1.62, 0.17855, 14, 5.27, -3.12, 0.82137, 2, 15, 30.79, -2.39, 0.9712, 16, -18.55, 1.61, 0.0288, 1, 16, 20.16, -1.6, 1, 1, 14, 37.6, 1.98, 1, 1, 14, 60.3, -3.49, 1, 2, 12, -7.64, 36.61, 0.29552, 13, 10.45, -20.89, 0.70448, 2, 12, 5.84, 2.91, 0.95934, 13, 5.46, -56.6, 0.04066, 3, 12, -13.5, -16.48, 0.60462, 13, -15.19, -62.78, 0.00255, 15, 2.49, 28.93, 0.39284, 2, 12, -11.96, 48.7, 0.07258, 13, 12.58, -8.35, 0.92742, 2, 12, -35.15, 38.97, 0.06193, 13, -6.63, -4.29, 0.93807, 2, 12, -13.61, -36.78, 0.1354, 15, 10.6, 9.88, 0.8646, 2, 12, -23.54, -35.52, 0.10016, 15, -0.66, 7.58, 0.89984, 3, 12, -13.43, 13.3, 0.71583, 13, -2.92, -37.57, 0.26534, 15, -9.53, 56.84, 0.01883], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 68, 70, 70, 72, 66, 74, 74, 76, 72, 78, 78, 80, 0, 88, 88, 90, 10, 92, 92, 94, 94, 34], "width": 189, "height": 133}}, "lian": {"lian": {"type": "mesh", "uvs": [0.60507, 0, 0.69649, 0.01375, 0.79578, 0.09085, 0.88819, 0.18463, 0.91965, 0.29611, 0.92161, 0.33154, 0.96487, 0.33362, 1, 0.39613, 0.99141, 0.50554, 0.93439, 0.56701, 0.9176, 0.58025, 0.89212, 0.60035, 0.87738, 0.73892, 0.79578, 0.85145, 0.74872, 0.89691, 0.6847, 0.95877, 0.61883, 0.98273, 0.54904, 0.95981, 0.43304, 0.90563, 0.28361, 0.80456, 0.19768, 0.78522, 0.18894, 0.71416, 0.09468, 0.6503, 0.01417, 0.57106, 0.0067, 0.4892, 0.06566, 0.43199, 0.15702, 0.45135, 0.19831, 0.31124, 0.26823, 0.16402, 0.40899, 0.06225, 0.51433, 0.00989, 0.65528, 0.14971, 0.66446, 0.32058, 0.66184, 0.50257, 0.66446, 0.68873, 0.65659, 0.81237, 0.80783, 0.16839, 0.81408, 0.31125, 0.80351, 0.40535, 0.85882, 0.409, 0.8962, 0.44087, 0.92923, 0.44844, 0.90334, 0.49101, 0.87299, 0.54494, 0.83729, 0.59414, 0.78283, 0.61779, 0.7257, 0.61022, 0.69714, 0.58752, 0.70071, 0.53075, 0.74155, 0.43793, 0.74624, 0.5563, 0.80337, 0.52697, 0.86139, 0.49101, 0.89977, 0.46547, 0.79067, 0.62923, 0.79451, 0.70822, 0.77881, 0.79023, 0.6431, 0.91903, 0.44435, 0.18825, 0.45457, 0.34953, 0.44928, 0.43087, 0.51589, 0.46989, 0.54201, 0.53007, 0.54088, 0.58784, 0.48637, 0.63358, 0.40461, 0.65164, 0.33079, 0.62395, 0.28878, 0.56257, 0.26606, 0.51202, 0.32656, 0.46019, 0.39589, 0.43935, 0.32123, 0.53103, 0.39083, 0.55408, 0.45957, 0.56238, 0.40738, 0.72434, 0.41335, 0.81919, 0.54609, 0.70609, 0.53922, 0.79711, 0.53836, 0.88358, 0.72729, 0.69244, 0.7187, 0.79529, 0.71183, 0.87357, 0.54492, 0.36762, 0.60128, 0.47164, 0.60808, 0.51593, 0.68971, 0.50254, 0.69942, 0.4325, 0.77036, 0.3532, 0.88697, 0.3429, 0.31558, 0.37483, 0.23396, 0.43559, 0.17176, 0.54889, 0.198, 0.64982, 0.09818, 0.5458, 0.23228, 0.7075, 0.28767, 0.75796, 0.85043, 0.62762, 0.84166, 0.71591, 0.81755, 0.78793, 0.28228, 0.2807, 0.61467, 0.59951, 0.663, 0.58522, 0.60945, 0.70253, 0.59377, 0.81331, 0.58541, 0.90526, 0.33247, 0.70807, 0.48403, 0.71915, 0.48298, 0.81663, 0.54151, 0.1719, 0.73383, 0.15418, 0.75264, 0.33142, 0.45083, 0.40181, 0.53037, 0.42757, 0.57104, 0.46629, 0.57586, 0.5262, 0.56897, 0.59413, 0.54335, 0.64382, 0.48419, 0.66646, 0.40595, 0.68695, 0.32704, 0.66645, 0.25604, 0.60509, 0.21538, 0.53203, 0.24984, 0.47286, 0.33186, 0.42318, 0.39321, 0.41004, 0.72113, 0.42956, 0.76195, 0.39249, 0.8112, 0.37702, 0.84722, 0.37009, 0.87856, 0.38167, 0.69125, 0.52226, 0.88614, 0.41787, 0.90505, 0.53039, 0.92092, 0.55238, 0.80742, 0.62093, 0.85013, 0.60994, 0.86356, 0.5873, 0.8721, 0.57178, 0.34947, 0.5041, 0.39514, 0.49341, 0.44878, 0.49397, 0.49179, 0.51424, 0.52313, 0.54463, 0.52153, 0.57672, 0.32663, 0.56658, 0.3638, 0.61386, 0.42169, 0.62287, 0.47321, 0.62118, 0.49976, 0.60655, 0.72379, 0.53208, 0.74869, 0.49215, 0.78891, 0.47253, 0.83552, 0.47321, 0.85275, 0.53005, 0.82849, 0.57268, 0.79785, 0.59703, 0.76401, 0.60245, 0.73464, 0.59771, 0.71062, 0.57895], "triangles": [17, 104, 16, 16, 57, 15, 16, 104, 57, 18, 78, 17, 17, 78, 104, 15, 81, 14, 15, 57, 81, 104, 103, 57, 57, 35, 81, 57, 103, 35, 19, 75, 18, 19, 95, 75, 95, 105, 75, 105, 74, 75, 18, 107, 78, 18, 75, 107, 104, 78, 103, 56, 13, 14, 80, 56, 81, 14, 81, 56, 78, 77, 103, 78, 107, 77, 81, 35, 80, 13, 98, 12, 13, 56, 98, 75, 74, 107, 107, 106, 77, 107, 74, 106, 77, 76, 103, 103, 102, 35, 103, 76, 102, 35, 34, 80, 35, 102, 34, 19, 20, 95, 77, 106, 76, 80, 79, 56, 80, 34, 79, 56, 55, 98, 56, 79, 55, 98, 97, 12, 98, 55, 97, 20, 94, 95, 20, 21, 94, 95, 94, 105, 97, 96, 12, 12, 96, 11, 105, 118, 74, 74, 118, 106, 106, 117, 76, 106, 118, 117, 97, 55, 96, 21, 92, 94, 21, 22, 92, 55, 134, 96, 79, 54, 55, 55, 54, 134, 94, 119, 105, 105, 119, 118, 94, 120, 119, 94, 92, 120, 117, 116, 76, 76, 116, 102, 102, 116, 100, 102, 100, 34, 100, 116, 115, 34, 46, 79, 79, 45, 54, 79, 46, 45, 100, 101, 34, 34, 47, 46, 34, 101, 47, 119, 65, 118, 118, 65, 117, 117, 64, 116, 117, 65, 64, 119, 120, 66, 119, 66, 65, 66, 120, 67, 66, 145, 65, 65, 147, 64, 65, 146, 147, 65, 145, 146, 22, 91, 92, 22, 93, 91, 22, 23, 93, 120, 91, 121, 120, 92, 91, 64, 63, 116, 116, 63, 115, 63, 148, 143, 63, 64, 148, 64, 147, 148, 54, 45, 134, 134, 135, 96, 11, 135, 136, 11, 96, 135, 67, 144, 66, 66, 144, 145, 145, 72, 146, 146, 73, 147, 146, 72, 73, 147, 73, 148, 134, 44, 135, 134, 45, 44, 45, 46, 156, 46, 157, 156, 45, 155, 44, 45, 156, 155, 145, 144, 72, 47, 158, 46, 46, 158, 157, 135, 44, 136, 148, 73, 143, 67, 121, 68, 67, 120, 121, 155, 156, 50, 136, 137, 11, 11, 137, 10, 115, 114, 100, 114, 84, 100, 100, 84, 101, 156, 157, 50, 157, 158, 50, 155, 154, 44, 155, 51, 154, 155, 50, 51, 136, 44, 137, 63, 62, 115, 115, 62, 114, 143, 142, 63, 63, 142, 62, 158, 47, 48, 43, 137, 44, 84, 33, 101, 47, 101, 48, 101, 130, 48, 130, 33, 85, 130, 101, 33, 10, 137, 133, 133, 137, 43, 10, 133, 9, 133, 43, 132, 158, 149, 50, 158, 48, 149, 143, 73, 142, 43, 44, 154, 154, 51, 153, 43, 154, 153, 23, 24, 93, 9, 133, 8, 67, 71, 144, 144, 71, 72, 67, 68, 71, 73, 72, 140, 72, 139, 140, 73, 141, 142, 73, 140, 141, 50, 150, 51, 50, 149, 150, 71, 138, 72, 72, 138, 139, 133, 132, 8, 121, 91, 26, 24, 25, 93, 91, 93, 26, 93, 25, 26, 153, 52, 43, 42, 43, 52, 42, 132, 43, 142, 141, 62, 149, 48, 150, 121, 122, 68, 122, 26, 90, 122, 121, 26, 138, 71, 69, 49, 150, 48, 132, 42, 8, 141, 61, 62, 62, 113, 114, 62, 61, 113, 153, 51, 52, 150, 151, 51, 51, 152, 52, 51, 151, 152, 114, 83, 84, 114, 113, 83, 48, 130, 49, 84, 83, 33, 141, 140, 61, 71, 68, 69, 68, 122, 69, 42, 41, 8, 8, 41, 7, 138, 69, 139, 85, 33, 86, 125, 49, 130, 125, 85, 86, 85, 125, 130, 32, 86, 33, 139, 70, 140, 140, 60, 61, 140, 70, 60, 139, 69, 70, 150, 49, 151, 52, 53, 42, 42, 53, 41, 53, 52, 40, 151, 38, 152, 52, 152, 40, 152, 39, 40, 152, 38, 39, 69, 122, 123, 151, 49, 38, 83, 32, 33, 61, 112, 113, 61, 60, 112, 82, 83, 113, 53, 40, 41, 69, 123, 70, 123, 122, 90, 90, 26, 27, 41, 131, 5, 41, 6, 7, 41, 5, 6, 39, 131, 40, 41, 40, 131, 88, 5, 129, 123, 124, 70, 70, 124, 60, 49, 126, 38, 49, 125, 126, 90, 89, 123, 89, 27, 99, 89, 90, 27, 113, 112, 82, 83, 82, 32, 126, 125, 86, 60, 111, 112, 60, 124, 111, 87, 126, 86, 110, 86, 32, 112, 111, 82, 123, 89, 124, 39, 129, 131, 5, 131, 129, 111, 124, 59, 38, 127, 39, 127, 128, 39, 39, 128, 129, 38, 126, 127, 111, 59, 82, 59, 124, 89, 126, 87, 127, 87, 86, 110, 129, 128, 88, 128, 127, 37, 89, 99, 59, 127, 87, 37, 128, 37, 88, 32, 82, 108, 87, 110, 37, 99, 58, 59, 82, 59, 108, 59, 58, 108, 5, 88, 4, 88, 37, 4, 110, 36, 37, 36, 110, 109, 110, 32, 109, 108, 31, 32, 32, 31, 109, 37, 3, 4, 37, 36, 3, 27, 28, 99, 99, 28, 58, 28, 29, 58, 58, 29, 108, 29, 30, 108, 108, 0, 31, 108, 30, 0, 109, 2, 36, 3, 36, 2, 2, 109, 1, 109, 31, 1, 31, 0, 1], "vertices": [2, 4, 174.94, -31.73, 0.7, 76, 113.85, -7.98, 0.3, 2, 4, 170.23, -47.38, 0.8, 76, 109.14, -23.63, 0.2, 2, 4, 154.85, -62.82, 0.85, 76, 93.76, -39.07, 0.15, 2, 4, 136.9, -76.62, 0.9, 76, 75.81, -52.87, 0.1, 1, 4, 117.66, -79.32, 1, 1, 4, 111.76, -78.78, 1, 1, 4, 110.26, -86.29, 1, 1, 4, 99, -90.87, 1, 1, 4, 81.17, -86.61, 1, 1, 4, 72.54, -75.09, 1, 1, 4, 70.8, -71.81, 1, 1, 4, 68.17, -66.85, 1, 1, 4, 45.68, -60.78, 1, 2, 4, 29.28, -43.67, 0.9, 76, -31.81, -19.92, 0.1, 2, 4, 23.03, -34.29, 0.85, 76, -38.06, -10.54, 0.15, 2, 4, 14.53, -21.54, 0.8, 76, -46.56, 2.21, 0.2, 2, 4, 12.33, -9.41, 0.7, 76, -48.76, 14.34, 0.3, 2, 4, 17.97, 2.23, 0.8, 76, -43.12, 25.98, 0.2, 2, 4, 30.01, 21.16, 0.85, 76, -31.08, 44.91, 0.15, 1, 4, 50.68, 44.77, 1, 1, 4, 56.17, 59.31, 1, 1, 4, 68.13, 59.06, 1, 1, 4, 81.19, 73.94, 1, 1, 4, 96.42, 86.03, 1, 1, 4, 110.13, 85.28, 1, 1, 4, 118.01, 73.52, 1, 1, 4, 112.37, 58.03, 1, 2, 4, 134.4, 47.28, 0.9, 76, 73.31, 71.03, 0.1, 2, 4, 156.84, 31.34, 0.85, 76, 95.75, 55.09, 0.15, 2, 4, 169.89, 4.14, 0.8, 76, 108.8, 27.9, 0.2, 2, 4, 175.72, -15.6, 0.8, 76, 114.64, 8.15, 0.2, 2, 4, 148.88, -36.75, 0.7, 76, 87.79, -13, 0.3, 2, 4, 120.43, -34.06, 0.7, 76, 59.34, -10.31, 0.3, 2, 4, 90.45, -29.02, 0.7, 76, 29.36, -5.27, 0.3, 2, 4, 59.65, -24.79, 0.7, 76, -1.44, -1.04, 0.3, 2, 4, 39.45, -20.3, 0.7, 76, -21.64, 3.45, 0.3, 2, 4, 141.73, -62.97, 0.85, 76, 80.64, -39.22, 0.15, 2, 4, 117.98, -60.47, 0.85, 76, 56.89, -36.72, 0.15, 2, 4, 102.72, -56.25, 0.85, 76, 41.63, -32.5, 0.15, 2, 4, 100.65, -65.84, 0.9, 76, 39.56, -42.09, 0.1, 2, 4, 94.39, -71.58, 0.9, 76, 33.3, -47.83, 0.1, 2, 4, 92.26, -77.17, 0.9, 76, 31.17, -53.42, 0.1, 2, 4, 85.92, -71.57, 0.9, 76, 24.83, -47.82, 0.1, 2, 4, 77.82, -64.9, 0.9, 76, 16.73, -41.15, 0.1, 2, 4, 70.65, -57.41, 0.9, 76, 9.56, -33.66, 0.1, 2, 4, 68.2, -47.29, 0.85, 76, 7.11, -23.54, 0.15, 2, 4, 70.98, -37.48, 0.8, 76, 9.89, -13.73, 0.2, 2, 4, 75.49, -33.06, 0.8, 76, 14.4, -9.31, 0.2, 2, 4, 84.76, -35.11, 0.8, 76, 23.67, -11.36, 0.2, 2, 4, 99, -44.59, 0.8, 76, 37.91, -20.84, 0.2, 2, 4, 79.33, -42.43, 0.8, 76, 18.24, -18.68, 0.2, 2, 4, 82.65, -53.17, 0.85, 76, 21.56, -29.42, 0.15, 2, 4, 87.04, -64.22, 0.9, 76, 25.95, -40.47, 0.1, 2, 4, 90.23, -71.58, 0.9, 76, 29.14, -47.83, 0.1, 2, 4, 66.1, -48.37, 0.85, 76, 5.01, -24.62, 0.15, 2, 4, 52.96, -47.06, 0.85, 76, -8.13, -23.31, 0.15, 2, 4, 39.84, -42.24, 0.85, 76, -21.25, -18.49, 0.15, 2, 4, 22.2, -15.26, 0.7, 76, -38.89, 8.49, 0.3, 2, 4, 148.14, 1.13, 0.8, 76, 87.05, 24.88, 0.2, 2, 4, 121.25, 3.4, 0.8, 76, 60.16, 27.15, 0.2, 2, 4, 107.96, 6.37, 0.8, 76, 46.87, 30.12, 0.2, 2, 4, 99.74, -4.3, 0.824, 76, 38.65, 19.45, 0.176, 2, 4, 89.11, -7.36, 0.824, 76, 28.02, 16.39, 0.176, 2, 4, 79.6, -5.7, 0.824, 76, 18.51, 18.05, 0.176, 2, 4, 73.5, 4.98, 0.8, 76, 12.41, 28.73, 0.2, 2, 4, 72.7, 19.75, 0.85, 76, 11.61, 43.5, 0.15, 2, 4, 79.24, 31.96, 0.85, 76, 18.15, 55.71, 0.15, 2, 4, 90.5, 37.77, 0.9, 76, 29.41, 61.52, 0.1, 2, 4, 99.45, 40.47, 0.9, 76, 38.36, 64.22, 0.1, 2, 4, 106.39, 28.58, 0.85, 76, 45.3, 52.33, 0.15, 2, 4, 107.98, 15.93, 0.85, 76, 46.89, 39.68, 0.15, 2, 4, 94.84, 31.3, 0.88, 76, 33.75, 55.05, 0.12, 2, 4, 89.18, 19.7, 0.85, 76, 28.09, 43.45, 0.15, 2, 4, 85.97, 7.88, 0.8, 76, 24.88, 31.63, 0.2, 2, 4, 60.63, 21.09, 0.85, 76, -0.46, 44.84, 0.15, 2, 4, 44.81, 22.43, 0.85, 76, -16.28, 46.18, 0.15, 2, 4, 59.94, -3.64, 0.824, 76, -1.15, 20.11, 0.176, 2, 4, 45.1, -0.15, 0.856, 76, -15.99, 23.6, 0.144, 2, 4, 30.84, 2.18, 0.824, 76, -30.25, 25.93, 0.176, 2, 4, 57.36, -35.69, 0.8, 76, -3.73, -11.94, 0.2, 2, 4, 40.61, -31.6, 0.8, 76, -20.48, -7.85, 0.2, 2, 4, 27.87, -28.43, 0.8, 76, -33.22, -4.68, 0.2, 2, 4, 115.85, -11.95, 0.8, 76, 54.76, 11.8, 0.2, 2, 4, 97.17, -19.2, 0.8, 76, 36.08, 4.55, 0.2, 2, 4, 89.68, -19.27, 0.8, 76, 28.59, 4.48, 0.2, 2, 4, 89.71, -33.89, 0.8, 76, 28.62, -10.14, 0.2, 2, 4, 101.02, -37.36, 0.8, 76, 39.93, -13.61, 0.2, 2, 4, 112.22, -51.77, 0.8, 76, 51.13, -28.02, 0.2, 2, 4, 110.81, -72.43, 0.9, 76, 49.72, -48.68, 0.1, 2, 4, 120.78, 28.36, 0.85, 76, 59.69, 52.11, 0.15, 2, 4, 112.92, 44.17, 0.9, 76, 51.83, 67.92, 0.1, 1, 4, 95.88, 57.9, 1, 1, 4, 78.51, 55.85, 1, 1, 4, 98.35, 70.7, 1, 1, 4, 68.08, 51.3, 1, 2, 4, 58.27, 42.88, 0.9, 76, -2.82, 66.63, 0.1, 2, 4, 64.78, -58.87, 0.9, 76, 3.69, -35.12, 0.1, 2, 4, 50.43, -55.11, 0.9, 76, -10.66, -31.36, 0.1, 2, 4, 39.19, -49.08, 0.9, 76, -21.9, -25.33, 0.1, 2, 4, 137.21, 31.81, 0.85, 76, 76.12, 55.56, 0.15, 2, 4, 75.71, -18.32, 0.8, 76, 14.62, 5.43, 0.2, 2, 4, 76.78, -27.14, 0.7, 76, 15.69, -3.39, 0.3, 2, 4, 58.84, -14.82, 0.8, 76, -2.25, 8.93, 0.2, 2, 4, 40.97, -9.29, 0.8, 76, -20.12, 14.46, 0.2, 2, 4, 26.01, -5.51, 0.8, 76, -35.08, 18.24, 0.2, 2, 4, 65.31, 33.79, 0.85, 76, 4.22, 57.54, 0.15, 2, 4, 59.44, 7.55, 0.8, 76, -1.65, 31.3, 0.2, 2, 4, 43.37, 10.18, 0.8, 76, -17.72, 33.93, 0.2, 2, 4, 148.25, -16.28, 0.8, 76, 87.16, 7.47, 0.2, 2, 4, 146.05, -50.38, 0.8, 76, 84.96, -26.63, 0.2, 2, 4, 116.28, -49.21, 0.8, 76, 55.19, -25.46, 0.2, 2, 4, 112.71, 5.37, 0.8, 76, 51.62, 29.12, 0.2, 2, 4, 106.34, -7.9, 0.81337, 76, 45.25, 15.85, 0.18663, 2, 4, 98.86, -14.04, 0.80777, 76, 37.77, 9.71, 0.19223, 2, 4, 88.85, -13.38, 0.81168, 76, 27.76, 10.37, 0.18832, 2, 4, 77.81, -10.46, 0.81489, 76, 16.72, 13.29, 0.18511, 2, 4, 70.29, -4.73, 0.824, 76, 9.2, 19.02, 0.176, 2, 4, 68.13, 6.19, 0.80079, 76, 7.04, 29.94, 0.19921, 2, 4, 66.84, 20.4, 0.85, 76, 5.75, 44.15, 0.15, 2, 4, 72.33, 33.69, 0.8569, 76, 11.24, 57.44, 0.1431, 2, 4, 84.35, 44.57, 0.93566, 76, 23.26, 68.32, 0.06434, 2, 4, 97.5, 49.85, 0.95381, 76, 36.41, 73.6, 0.04619, 2, 4, 106.35, 42.33, 0.89987, 76, 45.26, 66.08, 0.10013, 2, 4, 112.36, 26.72, 0.85, 76, 51.27, 50.47, 0.15, 2, 4, 112.89, 15.66, 0.83208, 76, 51.8, 39.41, 0.16792, 2, 4, 100.92, -41.23, 0.8, 76, 39.83, -17.48, 0.2, 2, 4, 105.96, -49.3, 0.8, 76, 44.87, -25.55, 0.2, 2, 4, 107.19, -58.31, 0.85, 76, 46.1, -34.56, 0.15, 2, 4, 107.38, -64.79, 0.87718, 76, 46.29, -41.04, 0.12282, 2, 4, 104.63, -69.98, 0.9, 76, 43.54, -46.23, 0.1, 2, 4, 86.42, -33.67, 0.77795, 76, 25.33, -9.92, 0.22205, 2, 4, 98.45, -70.4, 0.9, 76, 37.36, -46.65, 0.1, 2, 4, 79.37, -70.87, 0.93234, 76, 18.28, -47.12, 0.06766, 2, 4, 75.32, -73.1, 0.9714, 76, 14.23, -49.35, 0.0286, 2, 4, 67.03, -51.51, 0.86657, 76, 5.94, -27.76, 0.13343, 2, 4, 67.7, -59.26, 0.91269, 76, 6.61, -35.51, 0.08731, 2, 4, 71.08, -62.18, 0.93591, 76, 9.99, -38.43, 0.06409, 2, 4, 73.42, -64.07, 0.93132, 76, 12.33, -40.32, 0.06868, 2, 4, 98.53, 25.68, 0.85499, 76, 37.44, 49.43, 0.14501, 2, 4, 99.08, 17.42, 0.84898, 76, 37.99, 41.17, 0.15102, 2, 4, 97.55, 8.05, 0.80335, 76, 36.46, 31.8, 0.19665, 2, 4, 93.06, 1.03, 0.81255, 76, 31.97, 24.78, 0.18745, 2, 4, 87.21, -3.69, 0.82243, 76, 26.12, 20.06, 0.17757, 2, 4, 81.95, -2.6, 0.82392, 76, 20.86, 21.15, 0.17608, 2, 4, 88.83, 31.25, 0.86794, 76, 27.74, 55, 0.13206, 2, 4, 80.03, 25.93, 0.85, 76, 18.94, 49.68, 0.15, 2, 4, 77, 16.03, 0.83435, 76, 15.91, 39.78, 0.16565, 2, 4, 75.9, 6.98, 0.80481, 76, 14.81, 30.73, 0.19519, 2, 4, 77.61, 1.96, 0.81103, 76, 16.52, 25.71, 0.18897, 2, 4, 83.93, -39.11, 0.8, 76, 22.84, -15.36, 0.2, 2, 4, 89.85, -44.48, 0.81105, 76, 28.76, -20.73, 0.18895, 2, 4, 92.02, -52.01, 0.83966, 76, 30.93, -28.26, 0.16034, 2, 4, 90.67, -60.15, 0.87876, 76, 29.58, -36.4, 0.12124, 2, 4, 80.82, -61.73, 0.88705, 76, 19.73, -37.98, 0.11295, 2, 4, 74.43, -56.41, 0.88551, 76, 13.34, -32.66, 0.11449, 2, 4, 71.23, -50.44, 0.86037, 76, 10.14, -26.69, 0.13963, 2, 4, 71.24, -44.38, 0.82945, 76, 10.15, -20.63, 0.17055, 2, 4, 72.8, -39.36, 0.80348, 76, 11.71, -15.61, 0.19652, 2, 4, 76.54, -35.63, 0.8, 76, 15.45, -11.88, 0.2], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 0, 62, 62, 64, 64, 66, 68, 70, 4, 72, 72, 74, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 76, 100, 102, 102, 104, 104, 106, 106, 82, 108, 110, 110, 112, 70, 114, 26, 28, 28, 30, 112, 28, 58, 116, 116, 118, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 120, 136, 142, 142, 144, 144, 146, 148, 150, 152, 154, 154, 156, 92, 158, 158, 160, 160, 162, 118, 164, 164, 166, 166, 168, 170, 172, 172, 174, 174, 74, 74, 176, 118, 178, 178, 180, 52, 182, 182, 184, 50, 186, 188, 190, 192, 194, 194, 196, 168, 200, 66, 202, 202, 68, 200, 204, 204, 206, 206, 208, 60, 216, 2, 218, 218, 220, 118, 222, 222, 120, 222, 224, 224, 226, 226, 228, 228, 230, 126, 232, 232, 152, 230, 232, 232, 234, 130, 236, 236, 148, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 250, 252, 74, 254, 254, 76, 252, 254, 254, 256, 256, 258, 250, 260, 258, 262, 262, 80, 106, 84, 84, 264, 264, 266, 266, 18, 268, 270, 270, 272, 272, 274, 18, 20, 20, 22, 274, 20, 142, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 142, 288, 288, 290, 290, 292, 292, 294, 294, 296, 296, 286, 146, 286, 298, 300, 300, 302, 302, 304, 304, 104, 104, 306, 306, 308, 308, 310, 310, 312, 312, 314, 94, 316, 316, 100, 314, 316, 316, 298], "width": 175, "height": 165}}, "jian": {"jian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [588.57, 32.43, 588.56, -28.57, -189.44, -28.36, -189.43, 32.64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 59, "height": 776}}, "gebo_you_1": {"gebo_you_1": {"type": "mesh", "uvs": [0.04587, 0.02972, 0.15664, 0.04493, 0.23263, 0.10539, 0.28127, 0.19921, 0.34285, 0.2782, 0.45225, 0.21026, 0.48618, 0.29992, 0.53779, 0.37123, 0.56049, 0.45462, 0.64862, 0.46523, 0.73808, 0.58804, 0.77413, 0.71085, 0.72339, 0.8382, 0.61123, 0.89278, 0.48972, 0.86701, 0.39359, 0.7442, 0.33884, 0.59562, 0.27609, 0.47736, 0.20666, 0.37275, 0.11586, 0.27268, 0.01736, 0.15291, 0, 0.04223, 0.41495, 0.44552, 0.62592, 0.71236, 0.5004, 0.59865, 0.43382, 0.28727, 0.3687, 0.31561], "triangles": [25, 4, 5, 25, 5, 6, 19, 20, 1, 19, 1, 2, 1, 20, 0, 20, 21, 0, 12, 13, 23, 13, 14, 23, 23, 15, 24, 23, 14, 15, 12, 23, 11, 15, 16, 24, 23, 24, 9, 23, 10, 11, 23, 9, 10, 9, 24, 8, 16, 22, 24, 24, 22, 8, 16, 17, 22, 17, 26, 22, 26, 18, 4, 26, 17, 18, 22, 7, 8, 22, 6, 7, 22, 25, 6, 22, 26, 25, 18, 3, 4, 18, 19, 3, 26, 4, 25, 19, 2, 3], "vertices": [2, 26, -39.84, -8.28, 0.00048, 25, 28, 14.8, 0.99952, 1, 25, 15.69, -1.68, 1, 3, 26, -5.17, 13.57, 0.21612, 25, -5.37, -8.99, 0.67907, 88, 37.38, 7.61, 0.10481, 3, 26, 10.65, 11.14, 0.74813, 25, -21.3, -7.45, 0.01987, 88, 21.45, 9.14, 0.232, 2, 26, 21.1, 14.13, 0.552, 88, 11.19, 5.56, 0.448, 2, 26, 22.86, 34.63, 0.056, 88, 10.59, -15.01, 0.944, 2, 26, 33.76, 34.03, 0.632, 88, -0.33, -15.02, 0.368, 2, 26, 41.09, 32.75, 0.864, 88, -7.72, -14.16, 0.136, 1, 26, 61.77, 28.67, 1, 1, 26, 74.56, 39.94, 1, 1, 26, 101.83, 49.26, 1, 1, 26, 121.03, 41.66, 1, 1, 26, 130.44, 22.21, 1, 1, 26, 128.12, 1.21, 1, 1, 26, 113.33, -14.37, 1, 1, 26, 81.87, -18.1, 1, 1, 26, 64.45, -15.05, 1, 1, 26, 40.07, -12.75, 1, 1, 26, 21.88, -13.42, 1, 2, 26, -6.89, -21.18, 0.6305, 25, -5.62, 25.81, 0.3695, 2, 26, -34.23, -21.65, 0.03896, 25, 21.65, 27.83, 0.96104, 1, 25, 33.02, 20.74, 1, 1, 26, 39.77, 10.92, 1, 1, 26, 107.44, 20.89, 1, 1, 26, 71.87, 11.48, 1, 2, 26, 28.69, 27.05, 0.776, 88, 4.34, -7.77, 0.224, 2, 26, 26.01, 14.87, 0.768, 88, 6.33, 4.54, 0.232], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 44, 48, 48, 46, 44, 50, 50, 52], "width": 174, "height": 153}}, "gebo_zuo1": {"gebo_zuo1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [238.43, 30.6, 145.54, -130.55, -52.86, -16.18, 40.03, 144.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 184, "height": 227}}, "gebo_zuo2": {"gebo_zuo2": {"type": "mesh", "uvs": [0.90005, 0, 0.76384, 0.06483, 0.65876, 0.14641, 0.56147, 0.23919, 0.50893, 0.37035, 0.47585, 0.49672, 0.41358, 0.62789, 0.32796, 0.72867, 0.17229, 0.80865, 0.03803, 0.92382, 0, 1, 0.08473, 1, 0.2404, 0.95421, 0.40385, 0.89663, 0.53812, 0.82944, 0.64319, 0.73666, 0.73854, 0.6103, 0.81054, 0.52392, 0.86502, 0.39595, 0.89227, 0.26478, 0.89616, 0.12561, 0.98956, 0.10002, 1, 0, 0.78914, 0.14801, 0.73854, 0.23919, 0.68017, 0.35116, 0.64319, 0.47753, 0.55758, 0.5959, 0.49531, 0.69348, 0.37272, 0.79425, 0.21899, 0.87903, 0.87086, 0.06803, 0.97413, 0.04575], "triangles": [23, 31, 20, 20, 31, 21, 21, 32, 22, 32, 0, 22, 31, 32, 21, 31, 0, 32, 31, 1, 0, 23, 1, 31, 24, 23, 19, 19, 23, 20, 3, 2, 24, 24, 2, 23, 2, 1, 23, 30, 8, 29, 12, 30, 13, 10, 9, 11, 12, 11, 30, 11, 9, 30, 9, 8, 30, 30, 29, 13, 13, 29, 14, 29, 28, 14, 14, 28, 15, 8, 7, 29, 29, 7, 28, 7, 6, 28, 28, 6, 27, 28, 27, 15, 15, 27, 16, 6, 5, 27, 27, 26, 16, 27, 5, 26, 16, 26, 17, 17, 26, 18, 5, 4, 26, 26, 25, 18, 26, 4, 25, 25, 24, 18, 18, 24, 19, 4, 3, 25, 25, 3, 24], "vertices": [2, 29, 35.53, 28.89, 0.128, 96, -10.68, 18.75, 0.872, 3, 28, 107.3, 34.08, 0.43133, 74, 88.87, 24.84, 0.00067, 29, -32.39, 28.84, 0.568, 2, 28, 45.88, 32.99, 0.55959, 74, 27.46, 23.75, 0.44041, 4, 32, 2.28, -73.9, 0.01644, 33, -91.61, -117.18, 0.00133, 28, -16.99, 25.4, 0.67113, 74, -35.41, 16.15, 0.3111, 3, 32, 73.34, -80.77, 0.47511, 33, -23.21, -96.7, 0.12243, 28, -79.17, -9.68, 0.40246, 4, 32, 140.17, -80.12, 0.22971, 33, 38.42, -70.86, 0.66081, 34, -30.11, -87.73, 0.04173, 28, -133.57, -48.5, 0.06774, 4, 32, 212.14, -91.02, 0.00106, 33, 109.18, -53.77, 0.23066, 34, 29.65, -46.16, 0.76749, 28, -198.81, -80.79, 0.00079, 3, 33, 172.73, -53.81, 0.088, 34, 88.9, -23.18, 0.70771, 35, -78.22, -11.83, 0.20429, 2, 34, 166.87, -25.71, 0.55596, 35, -1.57, -26.37, 0.44404, 1, 35, 80.75, -21.37, 1, 1, 35, 120.82, -4.77, 1, 1, 35, 92.91, 21.02, 1, 2, 34, 184.77, 52.91, 0.35357, 35, 28.26, 48.55, 0.64643, 3, 33, 225.5, 22.32, 0.112, 34, 110.5, 66.9, 0.61061, 35, -42.96, 73.83, 0.2774, 2, 33, 164.37, 49.25, 0.01939, 34, 43.76, 69.86, 0.98061, 2, 33, 99.48, 58.44, 0.61214, 34, -20.05, 54.91, 0.38786, 2, 32, 172.89, 41.71, 0.10429, 33, 22.71, 54.31, 0.89571, 2, 32, 122.6, 61.74, 0.80779, 33, -31.42, 53.86, 0.19221, 1, 32, 52.97, 69.77, 1, 3, 32, -15.73, 66.16, 0.76696, 28, 78.01, -79.09, 0.23215, 29, -39.84, -87.82, 0.00089, 2, 94, 177.41, -3.31, 0.288, 96, -12.29, -48.19, 0.712, 1, 96, 27.39, -34.93, 1, 1, 96, 31.79, 18.78, 1, 2, 32, -64.99, 10.12, 0.01939, 28, 86.28, -4.94, 0.98061, 2, 32, -14.27, -0.49, 0.60699, 28, 38.63, -25.3, 0.39301, 3, 32, 47.66, -11.96, 0.89338, 33, -72.98, -42.68, 0.00222, 28, -18.7, -51.37, 0.1044, 3, 32, 114.85, -12.92, 0.8259, 33, -10.41, -18.2, 0.15689, 28, -74.32, -89.07, 0.01721, 4, 32, 182.55, -34.96, 0.00718, 33, 60.6, -13.04, 0.96203, 34, -30.38, -25.8, 0.02848, 28, -142.44, -109.79, 0.0023, 2, 33, 117.12, -5.88, 0.152, 34, 19.7, 1.35, 0.848, 3, 33, 189.66, -18.81, 0.104, 34, 92, 15.58, 0.66971, 35, -69.17, 25.98, 0.22629, 2, 34, 170.67, 15.57, 0.192, 35, 8.56, 13.83, 0.808, 2, 29, 5.54, 3.67, 0.472, 96, -23.06, -18.43, 0.528, 2, 29, 49.54, -8.51, 0.104, 96, 20.82, -5.8, 0.896], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 46, 62, 62, 64], "width": 425, "height": 517}}, "shenti": {"shenti": {"type": "mesh", "uvs": [0.86096, 0.29225, 0.70946, 0.28473, 0.60673, 0.32012, 0.59873, 0.39804, 0.53992, 0.4782, 0.49196, 0.54946, 0.38394, 0.56882, 0.25567, 0.62107, 0.17635, 0.6801, 0.06495, 0.73332, 0.0507, 0.80812, 0, 0.84751, 0, 0.92686, 0.08014, 0.97525, 0.20504, 1, 0.38325, 0.9948, 0.53409, 0.96486, 0.68636, 0.90497, 0.79401, 0.851, 0.89863, 0.78049, 1, 0.71784, 0.98986, 0.65107, 1, 0.57172, 1, 0.4914, 0.96792, 0.40528, 0.94598, 0.31915, 0.71925, 0.35624, 0.68481, 0.4695, 0.5942, 0.58691, 0.4456, 0.65445, 0.28069, 0.7012, 0.85516, 0.3656, 0.85697, 0.44041, 0.84972, 0.51833, 0.80623, 0.616, 0.69206, 0.70951, 0.5199, 0.79056, 0.33143, 0.85186, 0.13934, 0.88095], "triangles": [28, 27, 33, 4, 3, 27, 28, 4, 27, 27, 3, 26, 32, 27, 26, 2, 1, 26, 32, 24, 23, 3, 2, 26, 30, 9, 8, 7, 6, 29, 30, 7, 29, 8, 7, 30, 29, 6, 5, 29, 5, 28, 5, 4, 28, 32, 31, 24, 26, 1, 0, 31, 26, 0, 31, 0, 25, 31, 25, 24, 32, 26, 31, 33, 32, 23, 33, 27, 32, 33, 23, 22, 34, 28, 33, 34, 33, 22, 35, 28, 34, 29, 28, 35, 30, 29, 36, 36, 29, 35, 37, 30, 36, 37, 10, 30, 30, 10, 9, 38, 10, 37, 11, 10, 38, 12, 11, 38, 13, 12, 38, 14, 38, 37, 14, 13, 38, 37, 15, 14, 16, 36, 17, 37, 36, 16, 15, 37, 16, 18, 35, 19, 36, 35, 18, 17, 36, 18, 20, 34, 21, 20, 19, 34, 21, 34, 22, 35, 34, 19], "vertices": [1, 2, 51.05, -14.87, 1, 6, 37, -172.14, -102.21, 0, 36, -69.59, -36.28, 0.00012, 2, 63.13, 34.65, 0.64254, 89, -107.19, -218.89, 9e-05, 1, 38.26, -75.87, 0.35668, 95, -100.03, 0.16, 0.00056, 5, 37, -128.4, -136.35, 0, 36, -40.39, -83.45, 0.00039, 2, 60.97, 89.93, 0.28489, 89, -52.73, -212.97, 0.00026, 1, 92.71, -69.94, 0.71445, 6, 37, -95.09, -107.8, 0, 36, 0.74, -68.2, 0.00015, 2, 17.62, 97.27, 0.02109, 89, -51.35, -165.48, 9e-05, 1, 94.1, -22.46, 0.27241, 95, -28.85, -29.89, 0.70626, 6, 37, -38.26, -90.98, 0.00799, 36, 59.88, -72.1, 0.23734, 2, -32, 129.98, 0.001, 89, -25.66, -111.17, 0.124, 1, 119.79, 31.86, 0.24999, 95, 31.47, -31.23, 0.37969, 6, 37, 32.09, -71.23, 0.04421, 36, 132.73, -77.94, 0.09303, 2, -92.47, 171.01, 0.00012, 89, 8.73, -46.68, 0.68465, 90, -58.58, -69.25, 0.08055, 95, 104.32, -37.05, 0.09744, 6, 36, 161.45, -116.66, 0.00126, 2, -98.34, 218.86, 1e-05, 89, 55.69, -35.77, 0.49458, 90, -11.62, -58.34, 0.49867, 91, -98.37, -114.28, 0.0006, 95, 133.04, -75.77, 0.00487, 4, 39, 4.24, -98.61, 0.00012, 89, 113.07, -1.13, 0.01997, 90, 45.77, -23.7, 0.78308, 91, -40.98, -79.64, 0.19683, 4, 39, 49.29, -66.43, 0.00491, 38, 128.64, -77.25, 0.00215, 90, 82.73, 17.52, 0.23254, 91, -4.01, -38.42, 0.7604, 3, 40, 29.71, -135.91, 0.00178, 39, 106.17, -42.33, 0.31645, 91, 46.26, -2.52, 0.68177, 3, 40, 35.05, -83.68, 0.27136, 39, 122.76, 7.49, 0.68475, 91, 51.6, 49.71, 0.04389, 2, 40, 65.62, -53.52, 0.6215, 39, 159.15, 30.28, 0.3785, 2, 40, 70.01, 5.28, 0.90594, 39, 176.24, 86.71, 0.09406, 3, 41, 166.05, 46.84, 0.01111, 40, 36.66, 43.82, 0.98752, 39, 152.06, 131.59, 0.00136, 2, 41, 114.37, 69.15, 0.21321, 40, -15.03, 66.13, 0.78679, 2, 41, 44.82, 74.35, 0.66009, 40, -84.58, 71.32, 0.33991, 3, 42, 58.53, 90.9, 0.09037, 41, -16.78, 60.51, 0.85745, 40, -146.18, 57.49, 0.05218, 3, 43, 81.23, 148.46, 0.00166, 42, -6.47, 60.43, 0.7158, 41, -81.79, 30.04, 0.28254, 3, 43, 30.03, 109.74, 0.17702, 42, -57.67, 21.72, 0.82132, 41, -132.99, -8.67, 0.00166, 3, 43, -5.32, 61.2, 0.63923, 42, -93.03, -26.83, 0.36036, 38, -62.92, 152.05, 0.00041, 4, 43, -80.03, -26.32, 0.73829, 37, -33.46, 157.51, 0.00197, 36, 150.44, 159.35, 0.19137, 1, -56.24, 207.18, 0.06837, 4, 43, -76, -46.11, 0.64635, 37, -44.97, 140.92, 0.00871, 36, 133.9, 147.77, 0.25124, 1, -51.71, 187.44, 0.0937, 4, 43, -86.32, -104.88, 0.29846, 37, -94.56, 107.73, 0.00504, 36, 75.88, 133.81, 0.45231, 1, -60.42, 128.97, 0.2442, 4, 43, -93.6, -165.27, 0.10509, 36, 17.6, 116.42, 0.43068, 2, -87.87, -54.78, 0.00269, 1, -64.86, 69.45, 0.46155, 4, 43, -88.06, -231.53, 0.01635, 36, -40.96, 84.93, 0.12812, 2, -21.14, -56.2, 0.31366, 1, -56.01, 4.62, 0.54186, 2, 2, 43.9, -55.07, 0.97965, 1, -51.46, -59.89, 0.02035, 6, 37, -147.08, -84.69, 0, 36, -40.02, -28.52, 8e-05, 2, 33.63, 42.55, 0.35809, 89, -101.99, -188, 7e-05, 1, 43.46, -44.98, 0.40928, 95, -69.73, 6.83, 0.23248, 5, 36, 39.39, -17.53, 0.07109, 2, -41.11, 72.41, 0, 89, -83.87, -108.21, 0.00019, 1, 61.57, 34.82, 0.00092, 95, 10.99, 23.36, 0.9278, 5, 37, 21.4, -20.54, 0.63789, 36, 140.25, -26.68, 0.08017, 2, -124.31, 130.14, 4e-05, 89, -35.29, -19.35, 0.24632, 95, 111.84, 14.21, 0.03557, 4, 38, 13.28, -31.9, 0.32752, 37, 96.87, -34.45, 0.07071, 89, 26.94, 25.54, 0.33418, 90, -40.36, 2.97, 0.26759, 4, 39, 9.41, -39.12, 0.23149, 38, 97.59, -40.21, 0.17642, 90, 37.87, 35.49, 0.32676, 91, -48.88, -20.45, 0.26533, 2, 2, 15.89, -14.58, 0.97522, 1, -11.81, -30.69, 0.02478, 4, 43, -38.72, -209.51, 0.01712, 36, -1.1, 48.46, 0.32505, 2, -38.03, -5.02, 0.01608, 1, -9.45, 23.73, 0.64175, 4, 43, -28.44, -152.65, 0.10025, 37, -89.05, 32.89, 0.00191, 36, 55.14, 61.69, 0.63797, 1, -2.49, 80.65, 0.25987, 4, 43, 11.71, -84.75, 0.28134, 37, -12.23, 50.84, 0.28899, 36, 133.42, 51.93, 0.38329, 1, 35.61, 148.68, 0.04639, 5, 43, 60.22, -9.58, 0.35951, 42, -27.49, -97.6, 0.1473, 38, -38.61, 58.71, 0.11051, 37, 75.61, 67.78, 0.38265, 36, 221.69, 37.4, 2e-05, 7, 43, 142.87, 39.49, 0.01083, 42, 55.16, -48.54, 0.24137, 41, -20.16, -78.92, 0.17177, 40, -149.55, -81.95, 0.00029, 39, -57.05, 49.35, 0.02707, 38, 57.42, 62.9, 0.54143, 37, 168.21, 42.01, 0.00724, 5, 42, 130.02, -25.36, 0.0067, 41, 54.71, -55.75, 0.2665, 40, -74.69, -58.77, 0.12366, 39, 21.06, 55.68, 0.49696, 38, 134.35, 47.95, 0.10618, 3, 41, 112.36, -21.67, 0.08206, 40, -17.03, -24.7, 0.64961, 39, 84.75, 76.39, 0.26833], "hull": 26, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 38, 40, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 2, 4, 50, 0, 0, 2], "width": 426, "height": 743}}, "shou_you_3": {"shou_you_3": {"type": "mesh", "uvs": [0.83217, 0.09584, 0.97119, 0.49865, 0.76555, 0.95552, 0.40167, 0.97908, 0.29518, 0.92837, 0.06988, 0.53403, 0.1356, 0.11516, 0.40508, 0.08316, 0.66815, 0.06888, 0.77024, 0.05282], "triangles": [7, 5, 6, 1, 4, 5, 1, 7, 0, 0, 8, 9, 0, 7, 8, 7, 1, 5, 2, 4, 1, 3, 4, 2], "vertices": [70.56, -17.85, 23.38, -17.67, -27.78, -0.92, -28.16, 16.04, -21.65, 20.07, 25.09, 23.96, 73.65, 13.93, 75.64, 1.13, 77.61, -8.25, 80.31, -11.37], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 10, 12, 12, 14], "width": 44, "height": 114}}, "you_gebo_2": {"you_gebo_2": {"type": "mesh", "uvs": [0.17156, 0.1755, 0.3215, 0.14569, 0.48298, 0.16291, 0.52917, 0.20403, 0.55219, 0.22451, 0.63292, 0.28875, 0.68137, 0.35432, 0.63062, 0.42652, 0.63062, 0.47354, 0.71828, 0.54242, 0.68598, 0.61276, 0.61216, 0.64852, 0.61678, 0.70747, 0.64677, 0.76244, 0.68829, 0.82562, 0.68829, 0.89914, 0.81516, 0.92629, 1, 0.94352, 1, 0.95875, 0.83131, 0.97597, 0.61908, 0.97531, 0.45761, 0.94219, 0.33765, 0.90709, 0.18771, 0.8806, 0.01239, 0.85145, 0, 0.79979, 0.02623, 0.74283, 0.05622, 0.67726, 0.05161, 0.62097, 0.02854, 0.5501, 0.01239, 0.48321, 0.017, 0.40571, 0.06083, 0.32624, 0.10466, 0.25272, 0.18333, 0.37505, 0.41097, 0.39854, 0.56391, 0.35054, 0.36117, 0.25965, 0.20112, 0.82029, 0.17978, 0.74983, 0.2189, 0.65384, 0.15132, 0.54559, 0.15843, 0.44551, 0.56747, 0.47513, 0.56747, 0.55172, 0.40386, 0.51802, 0.40386, 0.6375, 0.45365, 0.76311, 0.46076, 0.84174, 0.52854, 0.25217, 0.52854, 0.31909, 0.48456, 0.36287, 0.37022, 0.37844, 0.23389, 0.36834, 0.21337, 0.30436, 0.21923, 0.23366, 0.2163, 0.18147], "triangles": [35, 51, 36, 35, 36, 7, 56, 0, 1, 56, 3, 55, 2, 56, 1, 3, 56, 2, 3, 37, 55, 4, 49, 3, 55, 33, 0, 55, 0, 56, 49, 37, 3, 54, 33, 55, 54, 55, 37, 19, 16, 17, 20, 15, 16, 18, 19, 17, 20, 16, 19, 48, 14, 15, 22, 23, 48, 22, 48, 15, 21, 22, 15, 20, 21, 15, 26, 27, 39, 12, 47, 46, 47, 12, 13, 39, 40, 47, 25, 26, 39, 38, 39, 47, 25, 39, 38, 14, 48, 47, 14, 47, 13, 38, 47, 48, 24, 25, 38, 23, 24, 38, 23, 38, 48, 44, 8, 9, 10, 44, 9, 28, 29, 41, 46, 45, 44, 41, 45, 46, 11, 46, 44, 40, 28, 41, 10, 11, 44, 46, 40, 41, 27, 28, 40, 39, 27, 40, 12, 46, 11, 40, 46, 47, 49, 4, 5, 50, 49, 5, 37, 49, 50, 54, 37, 50, 34, 32, 33, 36, 50, 5, 36, 5, 6, 51, 54, 50, 51, 50, 36, 52, 53, 54, 54, 34, 33, 53, 34, 54, 51, 52, 54, 35, 52, 51, 53, 52, 35, 34, 53, 35, 31, 32, 34, 7, 36, 6, 42, 31, 34, 42, 34, 35, 43, 35, 7, 8, 43, 7, 42, 35, 43, 30, 31, 42, 45, 42, 43, 41, 30, 42, 41, 42, 45, 29, 30, 41, 44, 43, 8, 45, 43, 44], "vertices": [2, 48, -110.72, -47.88, 0.272, 47, -44.87, -45.83, 0.728, 1, 47, -63.99, -20.05, 1, 2, 48, -120.22, 6.65, 0.128, 47, -54.36, 8.69, 0.872, 2, 48, -95.29, 15.6, 0.20249, 47, -29.44, 17.64, 0.79751, 2, 48, -82.88, 20.05, 0.224, 47, -17.02, 22.1, 0.776, 1, 48, -43.98, 35.54, 1, 1, 48, -4.08, 45.37, 1, 1, 48, 40.44, 37.88, 1, 2, 48, 69.82, 39.37, 0.91394, 49, -32.03, 38.21, 0.08606, 2, 48, 118.62, 54.67, 0.26675, 49, 15.96, 54.13, 0.73325, 3, 48, 164.19, 41.92, 0.01025, 49, 60.7, 42.65, 0.9895, 50, -40.64, 52.01, 0.00025, 2, 49, 80.65, 26.72, 0.94894, 50, -23.22, 32.31, 0.05106, 3, 49, 112.78, 23.96, 0.40308, 50, 9.17, 23.24, 0.59597, 51, -53.24, 60.53, 0.00095, 3, 49, 142.7, 31.4, 0.02691, 50, 40.23, 24.72, 0.8962, 51, -26.23, 45.03, 0.07688, 2, 50, 76.39, 30.32, 0.43492, 51, 7.17, 30.15, 0.56508, 3, 50, 119.27, 33.79, 0.00403, 51, 44.68, 10.19, 0.96961, 52, -13.69, 26.6, 0.02636, 2, 51, 82.45, 22.02, 0.00867, 52, 22.45, 11.96, 0.99133, 1, 52, 56.51, 7.98, 1, 1, 52, 58.36, -1.17, 1, 2, 51, 105.33, 6.05, 0.24587, 52, 30.16, -15.15, 0.75413, 2, 51, 81.47, -23.58, 0.43211, 52, -7.13, -22.97, 0.56789, 2, 51, 48.32, -35.28, 0.47718, 52, -40.31, -10.81, 0.52282, 3, 50, 125.52, -28.55, 0.08275, 51, 16.91, -45.16, 0.81322, 52, -71.09, 1.51, 0.10403, 3, 50, 106.44, -60.45, 0.60002, 51, -16.09, -62, 0.39185, 52, -107.32, 9.88, 0.00813, 3, 50, 82.19, -83.9, 0.57556, 51, -49.2, -69.01, 0.05644, 81, 95.55, -26.19, 0.368, 5, 49, 172.94, -73.23, 0.00612, 50, 50.02, -83.51, 0.6504, 51, -76.15, -51.44, 0.00946, 81, 63.93, -32.15, 0.30202, 80, 164.81, -31.61, 0.032, 4, 49, 135.14, -81.81, 0.08597, 50, 13.19, -85.84, 0.44996, 81, 28.29, -41.7, 0.31206, 80, 129.25, -41.46, 0.152, 4, 49, 89.15, -89.75, 0.3751, 50, -31.79, -86.54, 0.13741, 81, -15.68, -51.25, 0.19149, 80, 85.36, -51.38, 0.296, 4, 48, 153.7, -91.97, 0.01349, 49, 50.63, -88.44, 0.6714, 50, -70.4, -78.95, 0.0111, 80, 46.03, -51.88, 0.304, 3, 48, 108.57, -82.74, 0.21179, 49, 7.7, -80.48, 0.50021, 80, 0.41, -45.45, 0.288, 3, 48, 75.28, -76.23, 0.68783, 49, -23.99, -76.46, 0.19217, 80, -33.22, -41, 0.12, 2, 48, 31.08, -70.37, 0.99087, 49, -67.13, -72.67, 0.00913, 1, 48, -17.74, -64.35, 1, 2, 48, -63.03, -58.11, 0.424, 47, 2.83, -56.06, 0.576, 2, 48, 11.44, -41.78, 0.99714, 49, -87.73, -44.77, 0.00285, 3, 82, 65.68, -43.05, 0.00283, 83, 37.28, -27.19, 0.99606, 85, -72.65, -68.11, 0.00112, 1, 83, 5.74, -2.79, 1, 2, 48, -60.25, -12.85, 0.4, 47, 5.61, -10.8, 0.6, 5, 49, 183.85, -35.67, 0.00498, 50, 67.19, -48.33, 0.45263, 51, -42.68, -30.95, 0.1461, 52, -107.83, 50.95, 0.0123, 81, 73.83, 5.72, 0.384, 5, 49, 139.83, -45.72, 0.1312, 50, 22.57, -50.63, 0.55456, 51, -81.6, -8.96, 0.03159, 52, -123.72, 92.71, 0.00265, 81, 30.54, -5.33, 0.28, 5, 48, 179.54, -41.08, 0.00354, 49, 78.6, -38.01, 0.59021, 50, -37.46, -32.34, 0.09438, 81, -31.92, 0.76, 0.104, 80, 68.69, 0.5, 0.20787, 3, 48, 113.15, -45.37, 0.17715, 49, 13.87, -44.52, 0.37485, 80, 2.69, -7.87, 0.448, 2, 48, 53.63, -43.81, 0.87249, 49, -45.36, -45.23, 0.12751, 2, 48, 71.14, 28.47, 0.87915, 49, -30.32, 27.35, 0.12085, 3, 48, 121.67, 27.12, 0.25897, 49, 19.78, 27.2, 0.73693, 50, -84.86, 43.52, 0.00409, 3, 48, 97.76, 0.04, 0.5367, 49, -2.83, -0.08, 0.46249, 50, -112.43, 20.18, 0.00081, 3, 48, 172.18, -11.02, 0.03944, 49, 69.9, -8.72, 0.87196, 50, -40.4, -1.53, 0.0886, 4, 49, 145.72, -2.86, 0.07708, 50, 37.13, -9.46, 0.84064, 51, -47.19, 17.91, 0.07937, 52, -80.07, 91.5, 0.00291, 4, 49, 193.67, 5.26, 0.0025, 50, 85.16, -9.95, 0.45107, 51, -6.94, -8.24, 0.51754, 52, -65.79, 45.64, 0.02888, 2, 48, -65.79, 16.45, 0.7381, 47, 0.07, 18.49, 0.2619, 1, 48, -24.79, 17.78, 1, 3, 48, 2.28, 10.92, 0.95804, 49, -95.54, 25.75, 0.00087, 47, 68.14, 12.96, 0.04109, 3, 48, 12.47, -8.89, 0.98326, 49, -88.48, 4.62, 0.00164, 47, 78.33, -6.84, 0.01509, 3, 48, 7.07, -33.07, 0.95584, 49, -97.51, -18.45, 0.00242, 47, 72.92, -31.02, 0.04174, 3, 48, -32.01, -37.95, 0.65011, 49, -136.88, -17.33, 0.00105, 47, 33.85, -35.91, 0.34884, 2, 48, -75.37, -38.33, 0.37241, 47, -9.51, -36.29, 0.62759, 2, 48, -107.32, -39.89, 0.23191, 47, -41.47, -37.85, 0.76809], "hull": 34, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 66, 68, 68, 70, 70, 72, 72, 10, 76, 78, 78, 80, 80, 82, 82, 84, 86, 88, 88, 22, 86, 14, 4, 6, 6, 8, 6, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112], "width": 174, "height": 611}}}}], "animations": {"animation1": {"slots": {"yanjing2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}]}, "jiguang": {"color": [{"color": "dd39ff4a", "curve": 0.379, "c2": 0.52, "c3": 0.746}, {"time": 0.3, "color": "dd39ff2f", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "df3bff94"}, {"time": 1.0667, "color": "dd39ff45", "curve": 0.25, "c3": 0.75}, {"time": 1.6, "color": "dd39ff4d", "curve": "stepped"}, {"time": 1.9, "color": "dd39ff4d", "curve": 0.25, "c3": 0.75}, {"time": 2.3, "color": "dc39ffb4", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "dd39ff4a", "curve": 0.379, "c2": 0.52, "c3": 0.746}, {"time": 2.9667, "color": "dd39ff2f", "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "color": "df3bff94"}, {"time": 3.7333, "color": "dd39ff45", "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "color": "dd39ff4d", "curve": "stepped"}, {"time": 4.5667, "color": "dd39ff4d", "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "color": "dc39ffb4", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "dd39ff4a"}]}}, "bones": {"bone2": {"rotate": [{"angle": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.07}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.18, "y": -16.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.18, "y": -16.5, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "root": {"rotate": [{"angle": 0.03}]}, "bone": {"rotate": [{"angle": 0.03}]}, "bone3": {"rotate": [{"angle": 0.3, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.5, "angle": 0.03}, {"time": 1.8333, "angle": 1.19, "curve": 0.348, "c2": 0.38, "c3": 0.697, "c4": 0.77}, {"time": 2.6667, "angle": 0.3, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 3.1667, "angle": 0.03}, {"time": 4.5, "angle": 1.19, "curve": 0.348, "c2": 0.38, "c3": 0.697, "c4": 0.77}, {"time": 5.3333, "angle": 0.3}], "translate": [{"x": 0.75, "y": -0.44, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 2.24, "y": -1.32, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.75, "y": -0.44, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 2.24, "y": -1.32, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.75, "y": -0.44}]}, "bozo": {"rotate": [{"angle": 2.45, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.8333, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 4.75, "curve": 0.339, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 2.6667, "angle": 2.45, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 3.5, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 4.75, "curve": 0.339, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 5.3333, "angle": 2.45}], "translate": [{"x": 0.04, "y": -1.55, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 0.06, "y": -2.33, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 0.04, "y": -1.55, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5}, {"time": 4.8333, "x": 0.06, "y": -2.33, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 0.04, "y": -1.55}]}, "bone5": {"rotate": [{"angle": 0.03}], "shear": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.4, "y": -0.61, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 2.6667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 3.0667, "y": -0.61, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 5.3333}]}, "bone6": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone7": {"rotate": [{"angle": -2.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -19.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -19.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.61}]}, "bone8": {"rotate": [{"angle": -7.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -25.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -7.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -25.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -7.13}]}, "bone12": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone13": {"rotate": [{"angle": 6.87, "curve": 0.33, "c2": 0.32, "c3": 0.688, "c4": 0.73}, {"time": 0.5333, "angle": 1.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8667, "angle": 0.03}, {"time": 2.2, "angle": 9.86, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.5333, "angle": 8.04, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 2.6667, "angle": 6.87, "curve": 0.33, "c2": 0.32, "c3": 0.688, "c4": 0.73}, {"time": 3.2, "angle": 1.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5333, "angle": 0.03}, {"time": 4.8667, "angle": 9.86, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.2, "angle": 8.04, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 5.3333, "angle": 6.87}]}, "bone14": {"rotate": [{"angle": 9.29, "curve": 0.288, "c2": 0.18, "c3": 0.646, "c4": 0.6}, {"time": 0.5333, "angle": 4.62, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 1.1667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 9.86, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.5333, "angle": 9.82, "curve": 0.307, "c2": 0.12, "c3": 0.643, "c4": 0.47}, {"time": 2.6667, "angle": 9.29, "curve": 0.288, "c2": 0.18, "c3": 0.646, "c4": 0.6}, {"time": 3.2, "angle": 4.62, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.8333, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 9.86, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 5.2, "angle": 9.82, "curve": 0.307, "c2": 0.12, "c3": 0.643, "c4": 0.47}, {"time": 5.3333, "angle": 9.29}]}, "bone15": {"rotate": [{"angle": 8.04, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 1.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 9.86, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 8.04, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": 1.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 9.86, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 8.04}]}, "bone16": {"rotate": [{"angle": 9.82, "curve": 0.258, "c2": 0.05, "c3": 0.632, "c4": 0.53}, {"time": 0.6667, "angle": 4.62, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 1.3, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 9.86, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "angle": 9.82, "curve": 0.258, "c2": 0.05, "c3": 0.632, "c4": 0.53}, {"time": 3.3333, "angle": 4.62, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.9667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 9.86, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 5.3333, "angle": 9.82}]}, "bone24": {"rotate": [{"angle": 0.03}]}, "bone25": {"rotate": [{"angle": 0.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -8.1, "y": -3.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -8.1, "y": -3.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone26": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.73, "y": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.73, "y": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone27": {"rotate": [{"angle": 0.65, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4, "angle": 0.3, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.16, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "angle": 0.65, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 3.0667, "angle": 0.3, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 1.16, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.3333, "angle": 0.65}], "translate": [{"x": 0.22, "y": -2.19, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4, "x": 0.1, "y": -0.97, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 0.41, "y": -3.99, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "x": 0.22, "y": -2.19, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 3.0667, "x": 0.1, "y": -0.97, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": 0.41, "y": -3.99, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.3333, "x": 0.22, "y": -2.19}], "shear": [{"y": -0.29, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4, "y": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "y": -0.52, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "y": -0.29, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 3.0667, "y": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "y": -0.52, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.3333, "y": -0.29}]}, "bone28": {"rotate": [{"angle": 0.03}]}, "bone29": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.33, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone30": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone32": {"rotate": [{"angle": 0.03}]}, "bone31": {"rotate": [{"angle": -1.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 0.03}, {"time": 2, "angle": -2.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 0.03}, {"time": 4.6667, "angle": -2.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.17}]}, "bone67": {"rotate": [{"angle": 8.32, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "angle": 9.86, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.6667, "angle": 7.78, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 1.6333, "angle": 0.03, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": 8.32, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.9667, "angle": 9.86, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 3.3333, "angle": 7.78, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 4.3, "angle": 0.03, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.3333, "angle": 8.32}]}, "bone68": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.45, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone69": {"rotate": [{"angle": 10.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 0.03}, {"time": 1.3333, "angle": 13.54}, {"time": 2, "angle": 20.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 10.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 0.03}, {"time": 4, "angle": 13.54}, {"time": 4.6667, "angle": 20.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 10.03}]}, "bone70": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -5.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -5.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone71": {"rotate": [{"angle": 0.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -4.48, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 1.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -4.48, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 1.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.94}]}, "bone4": {"rotate": [{"angle": 0.74, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 0.7}, {"time": 2.0333, "angle": 1.88, "curve": 0.342, "c2": 0.36, "c3": 0.684, "c4": 0.72}, {"time": 2.6667, "angle": 0.74, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 3.3667}, {"time": 4.7, "angle": 1.88, "curve": 0.342, "c2": 0.36, "c3": 0.684, "c4": 0.72}, {"time": 5.3333, "angle": 0.74}], "translate": [{"x": -1.39, "y": 0.65, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7}, {"time": 2.0333, "x": -2.6, "y": 1.22, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "x": -1.39, "y": 0.65, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 3.3667}, {"time": 4.7, "x": -2.6, "y": 1.22, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.3333, "x": -1.39, "y": 0.65}], "scale": [{"x": 1.027, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 1.068, "curve": 0.342, "c2": 0.36, "c3": 0.684, "c4": 0.72}, {"time": 2.6667, "x": 1.027, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 1.068, "curve": 0.342, "c2": 0.36, "c3": 0.684, "c4": 0.72}, {"time": 5.3333, "x": 1.027}]}, "bozo2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -0.49, "y": -2.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.49, "y": -2.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bozo3": {"shear": [{"curve": "stepped"}, {"time": 0.4, "y": 0.16, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.0667, "y": 0.16, "curve": "stepped"}, {"time": 5.3333}]}, "bone74": {"rotate": [{"angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -19.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -19.24, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -7.21}]}, "bone76": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -14.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -14.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "scale": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.836, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.836, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone77": {"rotate": [{"angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -14.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": -14.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -6.63}], "scale": [{"x": 0.926, "y": 0.926, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 0.836, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.926, "y": 0.926, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": 0.836, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 0.926, "y": 0.926}]}, "bone83": {"rotate": [{"angle": -2.61}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 11.16, "y": -6.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 11.16, "y": -6.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bone84": {"rotate": [{"angle": -2.61, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.3333, "angle": 0.84, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.6667, "angle": -2.61, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4, "angle": 0.84, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.3333, "angle": -2.61}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.15, "y": -3.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.15, "y": -3.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone60": {"rotate": [{"angle": 10.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 10.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 7.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 10.82}]}, "bone59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone65": {"rotate": [{"angle": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.84}]}, "bone64": {"rotate": [{"angle": -3.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": -3.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.9667, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.3333, "angle": -3.11}]}, "bone63": {"rotate": [{"angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -3.69}]}, "bone62": {"rotate": [{"angle": -3.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.69, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -3.69, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.01}]}, "bone61": {"rotate": [{"angle": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.84}]}, "bone54": {"rotate": [{"angle": 4.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -12.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 4.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -12.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 4.59}]}, "bone55": {"rotate": [{"angle": -1.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -12.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -12.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.92}]}, "bone56": {"rotate": [{"angle": -8.42, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -12.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -8.42, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -12.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -8.42}]}, "bone72": {"rotate": [{"angle": -12.23, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -12.23, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -12.23}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone34": {"rotate": [{"angle": -1.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -10.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.97}]}, "bone35": {"rotate": [{"angle": -5.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -11.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -5.58}]}, "bone36": {"rotate": [{"angle": -24.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -29.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -24.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -29.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -24.45}]}, "bone73": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone90": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone48": {"rotate": [{"angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -7.82, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -7.82, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.91}]}, "bone52": {"rotate": [{"angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -7.82, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -7.82, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.91}]}, "bone51": {"rotate": [{"angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": -0.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "angle": -0.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.1}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 2.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "angle": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 2.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "angle": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone49": {"rotate": [{"angle": -2.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -5.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": -11.01, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 3, "angle": -5.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "angle": -11.01, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -2.01}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 12.66, "y": -30.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 12.66, "y": -30.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bone40": {"rotate": [{"angle": -4.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -4.82}]}, "bone39": {"rotate": [{"angle": -2.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -5.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.95}]}, "bone38": {"rotate": [{"angle": -1.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.91, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.91, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.09}]}, "bone87": {"translate": [{"x": -20.28, "y": 12, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -24.87, "y": 14.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -20.28, "y": 12, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -24.87, "y": 14.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": -20.28, "y": 12}]}, "bone86": {"translate": [{"x": -9.07, "y": 3.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -18.14, "y": 7.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -9.07, "y": 3.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -18.14, "y": 7.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -9.07, "y": 3.62}]}, "bone85": {"translate": [{"x": -3.74, "y": 1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -20.29, "y": 6.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -3.74, "y": 1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": -20.29, "y": 6.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": -3.74, "y": 1.15}]}, "bone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone43": {"translate": [{"x": -21.4, "y": 17.78, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -26.24, "y": 21.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -21.4, "y": 17.78, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -26.24, "y": 21.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": -21.4, "y": 17.78}]}, "bone42": {"translate": [{"x": -13.12, "y": 10.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -13.12, "y": 10.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -13.12, "y": 10.9}]}, "bone41": {"translate": [{"x": -13.12, "y": 10.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -13.12, "y": 10.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -13.12, "y": 10.9}]}, "bone47": {"translate": [{"x": 17.48, "y": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 34.96, "y": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 17.48, "y": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 34.96, "y": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 17.48, "y": -1.34}]}, "bone45": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 35.92, "y": -1.23, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 35.92, "y": -1.23, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone46": {"translate": [{"x": 3.21, "y": -0.53, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 17.4, "y": -2.87, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 3.21, "y": -0.53, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 17.4, "y": -2.87, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 3.21, "y": -0.53}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.61, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone10": {"rotate": [{"angle": 1.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 5.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 1.04}]}, "bone89": {"rotate": [{"angle": 2.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 13.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 13.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.52}]}, "bone11": {"rotate": [{"angle": 0.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.88}]}, "bone21": {"rotate": [{"angle": -6.75, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667}, {"time": 1.9, "angle": -16.83, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "angle": -6.75, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 3.2333}, {"time": 4.5667, "angle": -16.83, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 5.3333, "angle": -6.75}], "translate": [{"x": -4.19, "y": -5.83, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": -10.45, "y": -14.53, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "x": -4.19, "y": -5.83, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "x": -10.45, "y": -14.53, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 5.3333, "x": -4.19, "y": -5.83}]}, "bone22": {"rotate": [{"angle": -14.11, "curve": 0.327, "c2": 0.31, "c3": 0.688, "c4": 0.73}, {"time": 0.5667, "angle": -3.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -19.48, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -14.11, "curve": 0.327, "c2": 0.31, "c3": 0.688, "c4": 0.73}, {"time": 3.2333, "angle": -3.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -19.48, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "angle": -14.11}]}, "bone23": {"rotate": [{"angle": -24.67, "curve": 0.275, "c2": 0.12, "c3": 0.638, "c4": 0.56}, {"time": 0.5667, "angle": -12.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -25.3, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "angle": -24.67, "curve": 0.275, "c2": 0.12, "c3": 0.638, "c4": 0.56}, {"time": 3.2333, "angle": -12.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": -25.3, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 5.3333, "angle": -24.67}]}, "bone88": {"rotate": [{"angle": -24.57, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": -27.39, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5667, "angle": -22.34, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5667, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": -24.57, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.9, "angle": -27.39, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.2333, "angle": -22.34, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.2333, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 5.3333, "angle": -24.57}]}, "bone66": {"rotate": [{"angle": -26.82, "curve": 0.327, "c2": 0.31, "c3": 0.688, "c4": 0.73}, {"time": 0.5667, "angle": -6.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -37.03, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -26.82, "curve": 0.327, "c2": 0.31, "c3": 0.688, "c4": 0.73}, {"time": 3.2333, "angle": -6.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -37.03, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "angle": -26.82}]}, "bone91": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.22, "y": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.22, "y": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone99": {"translate": [{"x": 6.17, "y": -6.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 18.2, "y": -18.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1667, "x": 14.84, "y": -14.86, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 6.17, "y": -6.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 18.2, "y": -18.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.8333, "x": 14.84, "y": -14.86, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 6.17, "y": -6.18}]}, "bone98": {"translate": [{"x": 12.09, "y": -12.1, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 18.2, "y": -18.22, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 12.09, "y": -12.1, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 18.2, "y": -18.22, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 12.09, "y": -12.1}]}, "bone97": {"translate": [{"x": 1.07, "y": -1.07, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 18.2, "y": -18.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1667, "x": 9.1, "y": -9.11, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 2.6667, "x": 1.07, "y": -1.07, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 18.2, "y": -18.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8333, "x": 9.1, "y": -9.11, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 5.3333, "x": 1.07, "y": -1.07}]}, "bone96": {"translate": [{"x": 6.17, "y": -6.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 18.2, "y": -18.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1667, "x": 14.84, "y": -14.86, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 6.17, "y": -6.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 18.2, "y": -18.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.8333, "x": 14.84, "y": -14.86, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 6.17, "y": -6.18}]}, "bone95": {"translate": [{"x": 1.14, "y": -1.14, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "x": 18.2, "y": -18.22, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.1667, "x": 3.36, "y": -3.36, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 1.14, "y": -1.14, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.8333, "x": 18.2, "y": -18.22, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4.8333, "x": 3.36, "y": -3.36, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.1667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 1.14, "y": -1.14}]}, "bone94": {"translate": [{"x": 1.07, "y": -1.07, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 18.2, "y": -18.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1667, "x": 9.1, "y": -9.11, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 2.6667, "x": 1.07, "y": -1.07, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 18.2, "y": -18.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8333, "x": 9.1, "y": -9.11, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 5.3333, "x": 1.07, "y": -1.07}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone18": {"rotate": [{"angle": -2.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -11.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.04}]}, "bone19": {"rotate": [{"angle": -5.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -11.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -5.52}]}, "bone20": {"rotate": [{"angle": -9.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -11.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -9.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -11.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -9.01}]}, "bone57": {"rotate": [{"angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -11.05}]}, "bone58": {"rotate": [{"angle": -9.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -9.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -9.01}]}}, "deform": {"default": {"lian": {"lian": [{"time": 4.0333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.5667, "offset": 114, "vertices": [-3.0919, 0.26034, -3.09175, 0.26045, -17.17006, -0.73995, -17.16904, -0.73964, -13.64838, 0.25192, -13.64775, 0.25217, -7.99495, 0.52514, -7.99454, 0.52509, -5.1369, -2.90129, -5.136, -2.90129, -2.51942, -0.11621, -2.51891, -0.11617, -0.50655, -1.74167, -0.50667, -1.74171, 4.36395, -1.5369, 4.36436, -1.53685, 5.39645, -1.29882, 5.39705, -1.29893, 3.08905, -1.55747, 3.0891, -1.55747, 0, 0, 0, 0, -6.52477, -6.54827, -6.52347, -6.54798, -16.28825, -4.2868, -16.28595, -4.28644, -2.41429, -3.09814, -2.41182, -3.09714, -1.93993, -2.58175, -1.93893, -2.58187, -4.50845, -1.46584, -4.50793, -1.46586, -5.25977, 0.03676, -5.25932, 0.03696, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.42239, 0.77351, -1.42232, 0.77349, -19.07913, 2.04932, -19.0787, 2.04916, -14.62018, 3.74956, -14.62024, 3.74947, -7.16486, 2.50677, -7.16499, 2.50673, 0, 0, 0, 0, 4.91862, -0.12281, 4.91893, -0.12291, 7.93069, -2.60435, 7.93085, -2.60446, 7.07141, -3.08102, 7.07173, -3.08111, 3.48251, -2.80746, 3.48251, -2.80751, 0, 0, 0, 0, -9.213, 3.22208, -9.21265, 3.22196, -16.45108, 2.52879, -16.45093, 2.52866, -1.65636, 0.63535, -1.6562, 0.63533, -0.21022, -0.03073, -0.20959, -0.03081, -1.67258, 0.1619, -1.6725, 0.16188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.11427, 0.0348, -3.11417, 0.03477, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.46451, -0.24239, -3.46442, -0.24232, -1.7197, -0.22086, -1.71965, -0.22079, -1.48808, 0.31963, -1.48796, 0.31969, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.61588, 0.4179, -1.61578, 0.41788, -2.15613, 1.03423, -2.15605, 1.03421, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.7218, -1.89905, -5.72166, -1.89896, -3.73898, -0.91221, -3.73888, -0.91211, -1.18234, 0.31648, -1.18234, 0.31651, -1.48802, 0.31963, -1.48801, 0.31968, -1.47359, 0.07972, -1.47353, 0.07974, 0, 0, 0, 0, 1.67101, -0.16172, 1.67139, -0.16151, 0, 0, 0, 0, 0, 0, 0, 0, 2.01218, -0.85836, 2.01273, -0.85843, 2.22488, 1.26936, 2.22522, 1.26931, 1.17882, 0.53957, 1.17889, 0.53955, 0, 0, 0, 0, -8.88908, 2.01928, -8.88756, 2.019, -13.82278, 4.05299, -13.82115, 4.05265, -15.92947, 3.47995, -15.92831, 3.47973, -12.4968, 1.06955, -12.49597, 1.06937, -5.14258, 0.16763, -5.14233, 0.16758, 0, 0, 0, 0, 1.47063, -2.43985, 1.47079, -2.4399, 5.72008, -2.65258, 5.72043, -2.65264, 5.10864, -1.1146, 5.10963, -1.11475, 4.63333, -0.34921, 4.63362, -0.34927, 2.7043, -0.48187, 2.70438, -0.48188, -6.81757, -3.98662, -6.81401, -3.98531, -11.25223, -5.32013, -11.24968, -5.31975, -12.13579, -5.60106, -12.13411, -5.6009, -8.82242, -4.04783, -8.82114, -4.04742, 0.54388, 0.69651, 0.54442, 0.6965, 6.39313, -0.39668, 6.39349, -0.39674, 7.63025, 1.05875, 7.63121, 1.05861, 6.77202, -0.44156, 6.77257, -0.44162, 4.60378, 0.07106, 4.60403, 0.07104], "curve": 0.25, "c3": 0.75}, {"time": 5.0667}]}}}}, "animation2": {"slots": {"yanjing2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}], "attachment": [{"time": 0.3667, "name": "yanjing2"}]}, "jiguang": {"color": [{"color": "dd39ff4a", "curve": 0.379, "c2": 0.52, "c3": 0.746}, {"time": 0.3, "color": "dd39ff2f", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "df3bff94"}, {"time": 1.0667, "color": "dd39ff45", "curve": 0.25, "c3": 0.75}, {"time": 1.6, "color": "dd39ff4d", "curve": "stepped"}, {"time": 1.9, "color": "dd39ff4d", "curve": 0.25, "c3": 0.75}, {"time": 2.3, "color": "dc39ffb4", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "dd39ff4a", "curve": 0.379, "c2": 0.52, "c3": 0.746}, {"time": 2.9667, "color": "dd39ff2f", "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "color": "df3bff94"}, {"time": 3.7333, "color": "dd39ff45", "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "color": "dd39ff4d", "curve": "stepped"}, {"time": 4.5667, "color": "dd39ff4d", "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "color": "dc39ffb4", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "dd39ff4a"}], "attachment": [{"name": null}]}, "bg": {"attachment": [{"name": null}]}}, "bones": {"bone2": {"rotate": [{"angle": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.07}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.18, "y": -16.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.18, "y": -16.5, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "root": {"rotate": [{"angle": 0.03}]}, "bone": {"rotate": [{"angle": 0.03}]}, "bone3": {"rotate": [{"angle": 0.3, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 0.5, "angle": 0.03}, {"time": 1.8333, "angle": 1.19, "curve": 0.348, "c2": 0.38, "c3": 0.697, "c4": 0.77}, {"time": 2.6667, "angle": 0.3, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 3.1667, "angle": 0.03}, {"time": 4.5, "angle": 1.19, "curve": 0.348, "c2": 0.38, "c3": 0.697, "c4": 0.77}, {"time": 5.3333, "angle": 0.3}], "translate": [{"x": 0.75, "y": -0.44, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 2.24, "y": -1.32, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.75, "y": -0.44, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 2.24, "y": -1.32, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.75, "y": -0.44}]}, "bozo": {"rotate": [{"angle": 2.45, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.8333, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 4.75, "curve": 0.339, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 2.6667, "angle": 2.45, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 3.5, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 4.75, "curve": 0.339, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 5.3333, "angle": 2.45}], "translate": [{"x": 0.04, "y": -1.55, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 0.06, "y": -2.33, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 0.04, "y": -1.55, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5}, {"time": 4.8333, "x": 0.06, "y": -2.33, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 0.04, "y": -1.55}]}, "bone5": {"rotate": [{"angle": 0.03}], "shear": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.4, "y": -0.61, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 2.6667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 3.0667, "y": -0.61, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 5.3333}]}, "bone6": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone7": {"rotate": [{"angle": -2.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -19.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -19.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.61}]}, "bone8": {"rotate": [{"angle": -7.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -25.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -7.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 26.15, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -25.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -7.13}]}, "bone12": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.98, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone13": {"rotate": [{"angle": 6.87, "curve": 0.33, "c2": 0.32, "c3": 0.688, "c4": 0.73}, {"time": 0.5333, "angle": 1.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8667, "angle": 0.03}, {"time": 2.2, "angle": 9.86, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.5333, "angle": 8.04, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 2.6667, "angle": 6.87, "curve": 0.33, "c2": 0.32, "c3": 0.688, "c4": 0.73}, {"time": 3.2, "angle": 1.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5333, "angle": 0.03}, {"time": 4.8667, "angle": 9.86, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.2, "angle": 8.04, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 5.3333, "angle": 6.87}]}, "bone14": {"rotate": [{"angle": 9.29, "curve": 0.288, "c2": 0.18, "c3": 0.646, "c4": 0.6}, {"time": 0.5333, "angle": 4.62, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 1.1667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 9.86, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.5333, "angle": 9.82, "curve": 0.307, "c2": 0.12, "c3": 0.643, "c4": 0.47}, {"time": 2.6667, "angle": 9.29, "curve": 0.288, "c2": 0.18, "c3": 0.646, "c4": 0.6}, {"time": 3.2, "angle": 4.62, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.8333, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": 9.86, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 5.2, "angle": 9.82, "curve": 0.307, "c2": 0.12, "c3": 0.643, "c4": 0.47}, {"time": 5.3333, "angle": 9.29}]}, "bone15": {"rotate": [{"angle": 8.04, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 1.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 9.86, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 8.04, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": 1.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 9.86, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 8.04}]}, "bone16": {"rotate": [{"angle": 9.82, "curve": 0.258, "c2": 0.05, "c3": 0.632, "c4": 0.53}, {"time": 0.6667, "angle": 4.62, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 1.3, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 9.86, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "angle": 9.82, "curve": 0.258, "c2": 0.05, "c3": 0.632, "c4": 0.53}, {"time": 3.3333, "angle": 4.62, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 3.9667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 9.86, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 5.3333, "angle": 9.82}]}, "bone24": {"rotate": [{"angle": 0.03}]}, "bone25": {"rotate": [{"angle": 0.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -8.1, "y": -3.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -8.1, "y": -3.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone26": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.61, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.73, "y": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 1.73, "y": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone27": {"rotate": [{"angle": 0.65, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4, "angle": 0.3, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.16, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "angle": 0.65, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 3.0667, "angle": 0.3, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 1.16, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.3333, "angle": 0.65}], "translate": [{"x": 0.22, "y": -2.19, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4, "x": 0.1, "y": -0.97, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 0.41, "y": -3.99, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "x": 0.22, "y": -2.19, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 3.0667, "x": 0.1, "y": -0.97, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": 0.41, "y": -3.99, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.3333, "x": 0.22, "y": -2.19}], "shear": [{"y": -0.29, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4, "y": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "y": -0.52, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.6667, "y": -0.29, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 3.0667, "y": -0.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "y": -0.52, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.3333, "y": -0.29}]}, "bone28": {"rotate": [{"angle": 0.03}]}, "bone29": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.33, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone30": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone32": {"rotate": [{"angle": 0.03}]}, "bone31": {"rotate": [{"angle": -1.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 0.03}, {"time": 2, "angle": -2.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 0.03}, {"time": 4.6667, "angle": -2.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.17}]}, "bone67": {"rotate": [{"angle": 8.32, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "angle": 9.86, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.6667, "angle": 7.78, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 1.6333, "angle": 0.03, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": 8.32, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.9667, "angle": 9.86, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 3.3333, "angle": 7.78, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 4.3, "angle": 0.03, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.3333, "angle": 8.32}]}, "bone68": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.45, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone69": {"rotate": [{"angle": 10.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 0.03}, {"time": 1.3333, "angle": 13.54}, {"time": 2, "angle": 20.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 10.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 0.03}, {"time": 4, "angle": 13.54}, {"time": 4.6667, "angle": 20.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 10.03}]}, "bone70": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -5.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "angle": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -5.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "angle": 0.03}]}, "bone71": {"rotate": [{"angle": 0.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -4.48, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 1.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -4.48, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 1.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.94}]}, "bone4": {"rotate": [{"angle": 0.74, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 0.7}, {"time": 2.0333, "angle": 1.88, "curve": 0.342, "c2": 0.36, "c3": 0.684, "c4": 0.72}, {"time": 2.6667, "angle": 0.74, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 3.3667}, {"time": 4.7, "angle": 1.88, "curve": 0.342, "c2": 0.36, "c3": 0.684, "c4": 0.72}, {"time": 5.3333, "angle": 0.74}], "translate": [{"x": -1.39, "y": 0.65, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7}, {"time": 2.0333, "x": -2.6, "y": 1.22, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "x": -1.39, "y": 0.65, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 3.3667}, {"time": 4.7, "x": -2.6, "y": 1.22, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 5.3333, "x": -1.39, "y": 0.65}], "scale": [{"x": 1.027, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 1.068, "curve": 0.342, "c2": 0.36, "c3": 0.684, "c4": 0.72}, {"time": 2.6667, "x": 1.027, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 1.068, "curve": 0.342, "c2": 0.36, "c3": 0.684, "c4": 0.72}, {"time": 5.3333, "x": 1.027}]}, "bozo2": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -0.49, "y": -2.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.49, "y": -2.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bozo3": {"shear": [{"curve": "stepped"}, {"time": 0.4, "y": 0.16, "curve": "stepped"}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.0667, "y": 0.16, "curve": "stepped"}, {"time": 5.3333}]}, "bone74": {"rotate": [{"angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -19.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -7.21, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -19.24, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -7.21}]}, "bone76": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -14.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -14.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "scale": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.836, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.836, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone77": {"rotate": [{"angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -14.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": -14.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -6.63}], "scale": [{"x": 0.926, "y": 0.926, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 0.836, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.926, "y": 0.926, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": 0.836, "y": 0.836, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 0.926, "y": 0.926}]}, "bone83": {"rotate": [{"angle": -2.61}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 11.16, "y": -6.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 11.16, "y": -6.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bone84": {"rotate": [{"angle": -2.61, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 1.3333, "angle": 0.84, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.6667, "angle": -2.61, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 4, "angle": 0.84, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.3333, "angle": -2.61}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.15, "y": -3.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.15, "y": -3.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone60": {"rotate": [{"angle": 10.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 10.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 11.5, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 7.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 10.82}]}, "bone59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone65": {"rotate": [{"angle": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.84}]}, "bone64": {"rotate": [{"angle": -3.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "angle": -3.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.9667, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 5.3333, "angle": -3.11}]}, "bone63": {"rotate": [{"angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -3.69}]}, "bone62": {"rotate": [{"angle": -3.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.69, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -3.69, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.01}]}, "bone61": {"rotate": [{"angle": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.84}]}, "bone54": {"rotate": [{"angle": 4.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -12.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 4.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -12.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 4.59}]}, "bone55": {"rotate": [{"angle": -1.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -12.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -12.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.92}]}, "bone56": {"rotate": [{"angle": -8.42, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -12.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -8.42, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -12.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -8.42}]}, "bone72": {"rotate": [{"angle": -12.23, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -12.23, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -12.23}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone34": {"rotate": [{"angle": -1.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -10.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.97}]}, "bone35": {"rotate": [{"angle": -5.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -11.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -5.58}]}, "bone36": {"rotate": [{"angle": -24.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -29.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -24.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -29.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -24.45}]}, "bone73": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone90": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.21, "y": 32.84, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone48": {"rotate": [{"angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -7.82, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -7.82, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.91}]}, "bone52": {"rotate": [{"angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -7.82, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -3.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -7.82, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.91}]}, "bone51": {"rotate": [{"angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": -0.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -0.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "angle": -0.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.1}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 2.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "angle": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 2.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "angle": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone49": {"rotate": [{"angle": -2.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -5.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": -11.01, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.01, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 3, "angle": -5.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "angle": -11.01, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -2.01}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 12.66, "y": -30.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 12.66, "y": -30.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333}]}, "bone40": {"rotate": [{"angle": -4.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -5.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -4.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -4.82}]}, "bone39": {"rotate": [{"angle": -2.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -5.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.95}]}, "bone38": {"rotate": [{"angle": -1.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.91, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.91, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.09}]}, "bone87": {"translate": [{"x": -20.28, "y": 12, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -24.87, "y": 14.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -20.28, "y": 12, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -24.87, "y": 14.71, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": -20.28, "y": 12}]}, "bone86": {"translate": [{"x": -9.07, "y": 3.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -18.14, "y": 7.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -9.07, "y": 3.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -18.14, "y": 7.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -9.07, "y": 3.62}]}, "bone85": {"translate": [{"x": -3.74, "y": 1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -20.29, "y": 6.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -3.74, "y": 1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": -20.29, "y": 6.23, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": -3.74, "y": 1.15}]}, "bone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone43": {"translate": [{"x": -21.4, "y": 17.78, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -26.24, "y": 21.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -21.4, "y": 17.78, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -26.24, "y": 21.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": -21.4, "y": 17.78}]}, "bone42": {"translate": [{"x": -13.12, "y": 10.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -13.12, "y": 10.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -13.12, "y": 10.9}]}, "bone41": {"translate": [{"x": -13.12, "y": 10.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -13.12, "y": 10.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -26.24, "y": 21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -13.12, "y": 10.9}]}, "bone47": {"translate": [{"x": 17.48, "y": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 34.96, "y": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 17.48, "y": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 34.96, "y": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 17.48, "y": -1.34}]}, "bone45": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 35.92, "y": -1.23, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 35.92, "y": -1.23, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone46": {"translate": [{"x": 3.21, "y": -0.53, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 17.4, "y": -2.87, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 3.21, "y": -0.53, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 17.4, "y": -2.87, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 3.21, "y": -0.53}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.61, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone10": {"rotate": [{"angle": 1.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 5.61, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 1.04}]}, "bone89": {"rotate": [{"angle": 2.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 13.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 13.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 2.52}]}, "bone11": {"rotate": [{"angle": 0.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.88}]}, "bone21": {"rotate": [{"angle": -6.75, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667}, {"time": 1.9, "angle": -16.83, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "angle": -6.75, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 3.2333}, {"time": 4.5667, "angle": -16.83, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 5.3333, "angle": -6.75}], "translate": [{"x": -4.19, "y": -5.83, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": -10.45, "y": -14.53, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "x": -4.19, "y": -5.83, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "x": -10.45, "y": -14.53, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 5.3333, "x": -4.19, "y": -5.83}]}, "bone22": {"rotate": [{"angle": -14.11, "curve": 0.327, "c2": 0.31, "c3": 0.688, "c4": 0.73}, {"time": 0.5667, "angle": -3.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -19.48, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -14.11, "curve": 0.327, "c2": 0.31, "c3": 0.688, "c4": 0.73}, {"time": 3.2333, "angle": -3.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -19.48, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "angle": -14.11}]}, "bone23": {"rotate": [{"angle": -24.67, "curve": 0.275, "c2": 0.12, "c3": 0.638, "c4": 0.56}, {"time": 0.5667, "angle": -12.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -25.3, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "angle": -24.67, "curve": 0.275, "c2": 0.12, "c3": 0.638, "c4": 0.56}, {"time": 3.2333, "angle": -12.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": -25.3, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 5.3333, "angle": -24.67}]}, "bone88": {"rotate": [{"angle": -24.57, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": -27.39, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5667, "angle": -22.34, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5667, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": -24.57, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.9, "angle": -27.39, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.2333, "angle": -22.34, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.2333, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 5.3333, "angle": -24.57}]}, "bone66": {"rotate": [{"angle": -26.82, "curve": 0.327, "c2": 0.31, "c3": 0.688, "c4": 0.73}, {"time": 0.5667, "angle": -6.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -37.03, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -26.82, "curve": 0.327, "c2": 0.31, "c3": 0.688, "c4": 0.73}, {"time": 3.2333, "angle": -6.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -37.03, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "angle": -26.82}]}, "bone91": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.22, "y": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -2.22, "y": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone99": {"translate": [{"x": 6.17, "y": -6.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 18.2, "y": -18.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1667, "x": 14.84, "y": -14.86, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 6.17, "y": -6.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 18.2, "y": -18.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.8333, "x": 14.84, "y": -14.86, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 6.17, "y": -6.18}]}, "bone98": {"translate": [{"x": 12.09, "y": -12.1, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 18.2, "y": -18.22, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 12.09, "y": -12.1, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 18.2, "y": -18.22, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 12.09, "y": -12.1}]}, "bone97": {"translate": [{"x": 1.07, "y": -1.07, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 18.2, "y": -18.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1667, "x": 9.1, "y": -9.11, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 2.6667, "x": 1.07, "y": -1.07, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 18.2, "y": -18.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8333, "x": 9.1, "y": -9.11, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 5.3333, "x": 1.07, "y": -1.07}]}, "bone96": {"translate": [{"x": 6.17, "y": -6.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 18.2, "y": -18.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1667, "x": 14.84, "y": -14.86, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 6.17, "y": -6.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 18.2, "y": -18.22, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.8333, "x": 14.84, "y": -14.86, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 6.17, "y": -6.18}]}, "bone95": {"translate": [{"x": 1.14, "y": -1.14, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "x": 18.2, "y": -18.22, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.1667, "x": 3.36, "y": -3.36, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.5, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 1.14, "y": -1.14, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.8333, "x": 18.2, "y": -18.22, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4.8333, "x": 3.36, "y": -3.36, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.1667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 1.14, "y": -1.14}]}, "bone94": {"translate": [{"x": 1.07, "y": -1.07, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 18.2, "y": -18.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1667, "x": 9.1, "y": -9.11, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 2.6667, "x": 1.07, "y": -1.07, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 18.2, "y": -18.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8333, "x": 9.1, "y": -9.11, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 5.3333, "x": 1.07, "y": -1.07}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone18": {"rotate": [{"angle": -2.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -11.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.04}]}, "bone19": {"rotate": [{"angle": -5.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -11.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -5.52}]}, "bone20": {"rotate": [{"angle": -9.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -11.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -9.01, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -11.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -9.01}]}, "bone57": {"rotate": [{"angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -11.05}]}, "bone58": {"rotate": [{"angle": -9.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -9.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -9.01}]}}, "deform": {"default": {"lian": {"lian": [{"time": 0.1667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "offset": 118, "vertices": [-15.76987, 0.27637, -15.76999, 0.27628, -11.04561, 0.42087, -11.0457, 0.42081, -2.43477, 0.30981, -2.43477, 0.30979, 0, 0, 0, 0, 0, 0, 0, 0, 6.01273, -2.4892, 6.01222, -2.48937, 9.64281, -0.88434, 9.6427, -0.88441, 9.56245, -0.5141, 9.56236, -0.5142, 4.56775, -1.28141, 4.5677, -1.28147, 0, 0, 0, 0, -4.70135, -4.00844, -4.70142, -4.0085, -14.52103, -3.02904, -14.52112, -3.02911, 0, 0, 0, 0, -1.06187, 0.08456, -1.06187, 0.08455, -1.32719, -0.19055, -1.32716, -0.19059, -1.11858, -0.22834, -1.11856, -0.22836, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.62645, 1.68046, -16.62646, 1.68038, -14.11592, 3.52361, -14.11601, 3.52354, -7.16486, 2.50677, -7.16499, 2.50673, 0, 0, 0, 0, 6.03554, -0.73013, 6.03564, -0.73018, 9.44328, -3.28217, 9.44337, -3.28228, 7.78366, -4.43287, 7.78369, -4.43292, 3.48251, -2.80746, 3.48251, -2.80751, 0, 0, 0, 0, -8.1339, 3.16601, -8.13393, 3.16595, -15.0304, 2.47988, -15.03053, 2.47982], "curve": "stepped"}, {"time": 0.4667, "offset": 118, "vertices": [-15.76987, 0.27637, -15.76999, 0.27628, -11.04561, 0.42087, -11.0457, 0.42081, -2.43477, 0.30981, -2.43477, 0.30979, 0, 0, 0, 0, 0, 0, 0, 0, 6.01273, -2.4892, 6.01222, -2.48937, 9.64281, -0.88434, 9.6427, -0.88441, 9.56245, -0.5141, 9.56236, -0.5142, 4.56775, -1.28141, 4.5677, -1.28147, 0, 0, 0, 0, -4.70135, -4.00844, -4.70142, -4.0085, -14.52103, -3.02904, -14.52112, -3.02911, 0, 0, 0, 0, -1.06187, 0.08456, -1.06187, 0.08455, -1.32719, -0.19055, -1.32716, -0.19059, -1.11858, -0.22834, -1.11856, -0.22836, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -16.62645, 1.68046, -16.62646, 1.68038, -14.11592, 3.52361, -14.11601, 3.52354, -7.16486, 2.50677, -7.16499, 2.50673, 0, 0, 0, 0, 6.03554, -0.73013, 6.03564, -0.73018, 9.44328, -3.28217, 9.44337, -3.28228, 7.78366, -4.43287, 7.78369, -4.43292, 3.48251, -2.80746, 3.48251, -2.80751, 0, 0, 0, 0, -8.1339, 3.16601, -8.13393, 3.16595, -15.0304, 2.47988, -15.03053, 2.47982], "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": "stepped"}, {"time": 4.0333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.5667, "offset": 114, "vertices": [-3.0919, 0.26034, -3.09175, 0.26045, -17.17006, -0.73995, -17.16904, -0.73964, -13.64838, 0.25192, -13.64775, 0.25217, -7.99495, 0.52514, -7.99454, 0.52509, -5.1369, -2.90129, -5.136, -2.90129, -2.51942, -0.11621, -2.51891, -0.11617, -0.50655, -1.74167, -0.50667, -1.74171, 4.36395, -1.5369, 4.36436, -1.53685, 5.39645, -1.29882, 5.39705, -1.29893, 3.08905, -1.55747, 3.0891, -1.55747, 0, 0, 0, 0, -6.52477, -6.54827, -6.52347, -6.54798, -16.28825, -4.2868, -16.28595, -4.28644, -2.41429, -3.09814, -2.41182, -3.09714, -1.93993, -2.58175, -1.93893, -2.58187, -4.50845, -1.46584, -4.50793, -1.46586, -5.25977, 0.03676, -5.25932, 0.03696, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.42239, 0.77351, -1.42232, 0.77349, -19.07913, 2.04932, -19.0787, 2.04916, -14.62018, 3.74956, -14.62024, 3.74947, -7.16486, 2.50677, -7.16499, 2.50673, 0, 0, 0, 0, 4.91862, -0.12281, 4.91893, -0.12291, 7.93069, -2.60435, 7.93085, -2.60446, 7.07141, -3.08102, 7.07173, -3.08111, 3.48251, -2.80746, 3.48251, -2.80751, 0, 0, 0, 0, -9.213, 3.22208, -9.21265, 3.22196, -16.45108, 2.52879, -16.45093, 2.52866, -1.65636, 0.63535, -1.6562, 0.63533, -0.21022, -0.03073, -0.20959, -0.03081, -1.67258, 0.1619, -1.6725, 0.16188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.11427, 0.0348, -3.11417, 0.03477, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.46451, -0.24239, -3.46442, -0.24232, -1.7197, -0.22086, -1.71965, -0.22079, -1.48808, 0.31963, -1.48796, 0.31969, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.61588, 0.4179, -1.61578, 0.41788, -2.15613, 1.03423, -2.15605, 1.03421, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.7218, -1.89905, -5.72166, -1.89896, -3.73898, -0.91221, -3.73888, -0.91211, -1.18234, 0.31648, -1.18234, 0.31651, -1.48802, 0.31963, -1.48801, 0.31968, -1.47359, 0.07972, -1.47353, 0.07974, 0, 0, 0, 0, 1.67101, -0.16172, 1.67139, -0.16151, 0, 0, 0, 0, 0, 0, 0, 0, 2.01218, -0.85836, 2.01273, -0.85843, 2.22488, 1.26936, 2.22522, 1.26931, 1.17882, 0.53957, 1.17889, 0.53955, 0, 0, 0, 0, -8.88908, 2.01928, -8.88756, 2.019, -13.82278, 4.05299, -13.82115, 4.05265, -15.92947, 3.47995, -15.92831, 3.47973, -12.4968, 1.06955, -12.49597, 1.06937, -5.14258, 0.16763, -5.14233, 0.16758, 0, 0, 0, 0, 1.47063, -2.43985, 1.47079, -2.4399, 5.72008, -2.65258, 5.72043, -2.65264, 5.10864, -1.1146, 5.10963, -1.11475, 4.63333, -0.34921, 4.63362, -0.34927, 2.7043, -0.48187, 2.70438, -0.48188, -6.81757, -3.98662, -6.81401, -3.98531, -11.25223, -5.32013, -11.24968, -5.31975, -12.13579, -5.60106, -12.13411, -5.6009, -8.82242, -4.04783, -8.82114, -4.04742, 0.54388, 0.69651, 0.54442, 0.6965, 6.39313, -0.39668, 6.39349, -0.39674, 7.63025, 1.05875, 7.63121, 1.05861, 6.77202, -0.44156, 6.77257, -0.44162, 4.60378, 0.07106, 4.60403, 0.07104], "curve": 0.25, "c3": 0.75}, {"time": 5.0667}]}}}}}}