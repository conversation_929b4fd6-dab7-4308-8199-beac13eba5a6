import { _decorator, director, instantiate, v3, Node, tween, Vec3, Tween } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { NodeTool } from "../lib/utils/NodeTool";
import { Sleep } from "../game/GameDefine";
import FmUtils from "../lib/utils/FmUtils";
const { ccclass, property } = _decorator;

/**
 * 资源飞行
 */
@ccclass("TopGetRes")
export class TopGetRes extends BaseCtrl {
  @property(Node)
  nodeAni: Node;

  @property(Node)
  nodeRes: Node;

  @property(Node)
  nodeTypeList: Node[] = [];

  private _nodeUIMain: Node;
  private _itemId: number = 0;
  private _wPosition: Vec3;

  init(args: { startPos: Vec3; itemId: number }): void {
    this._itemId = args.itemId;
    this._wPosition = args.startPos;
  }

  protected onLoad(): void {
    this._nodeUIMain = NodeTool.findByName(director.getScene(), "UIMain");
  }

  protected async start(): Promise<void> {
    super.start();

    this.node.setWorldPosition(this._wPosition);

    // 默认值
    const posList = [v3(-8.8, 65, 0), v3(31.8, 24, 0), v3(22, -26, 0), v3(-21, 2, 0), v3(-12, -46, 0), v3(2, -86, 0)];

    // 随机阵型
    const nodeType = this.nodeTypeList[Math.floor(Math.random() * this.nodeTypeList.length)];
    if (nodeType) {
      for (let i = 0; i < nodeType.children.length; i++) {
        posList[i] = nodeType.children[i].getPosition();
      }
    }

    for (let i = 0; i < 6; i++) {
      const nodeRes = instantiate(this.nodeRes);
      nodeRes.active = true;
      nodeRes.parent = this.nodeAni;
      nodeRes.setPosition(i * 3, -i * 3, 0);

      tween(nodeRes)
        .to(0.3, { position: posList[i % posList.length] }, { easing: "quintOut" })
        .by(0.25, { position: v3(0, 20, 0) }, { easing: "sineInOut" })
        .by(0.25, { position: v3(0, -20, 0) }, { easing: "sineInOut" })
        .by(0.25, { position: v3(0, 20, 0) }, { easing: "sineInOut" })
        .by(0.25, { position: v3(0, -20, 0) }, { easing: "sineInOut" })
        .by(0.25, { position: v3(0, 20, 0) }, { easing: "sineInOut" })
        .start();

      await Sleep(0.05);
    }

    await Sleep(0.7);

    // 终点
    let posEnd: Vec3;
    if (this._itemId == 1) {
      posEnd = NodeTool.findByName(this._nodeUIMain, "lbl_item1").getWorldPosition();
    } else if (this._itemId == 6) {
      posEnd = NodeTool.findByName(this._nodeUIMain, "lbl_item6").getWorldPosition();
    }

    // 移动到目标位置
    for (let i = 0; i < this.nodeAni.children.length; i++) {
      Tween.stopAllByTarget(this.nodeAni.children[i]);
      tween(this.nodeAni.children[i])
        .to(1, { worldPosition: posEnd }, { easing: "quintOut" })
        .to(0.2, { scale: v3(0.6, 0.6, 1) })
        .delay(0.1)
        .destroySelf()
        .start();
      await Sleep(0.05);
    }

    await Sleep(2);
    this.closeBack();
  }
}
