import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>pi<PERSON><PERSON>lerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ClubSubCmd, PlayerSubCmd } from "../../game/net/cmd/CmdData";
import { ClubRankMessage } from "../../game/net/protocol/Club";
import { RewardMessage } from "../../game/net/protocol/Comm";
import { BoolValue } from "../../game/net/protocol/ExternalMessage";
import { PlayerRankBoardMessage, PlayerRankMessage } from "../../game/net/protocol/Player";
import { persistent } from "../../lib/decorators/persistent";
import { MainRankModule } from "./MainRankModule";

export class MainRankApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   console.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       console.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       console.log(`${errorCode}`);
  //       console.log(data);
  //     }
  //   );
  // }

  // 路由: 3 - 27  --- 【获取战力榜排行】 --- 【PlayerAction:573】【powerRank】
  //   方法返回值: PlayerRankMessage
  // powerRank = CmdMgr.getMergeCmd(MainCmd.playerCmd, 27),

  // 路由: 3 - 28  --- 【获取繁荣度榜排行】 --- 【PlayerAction:601】【energyRank】
  //     方法返回值: PlayerRankMessage
  // energyRank = CmdMgr.getMergeCmd(MainCmd.playerCmd, 28),

  // 路由: 3 - 31  --- 【获取排行榜点赞情况】 --- 【PlayerAction:678】【rankRewardInfo】
  //   方法参数: BoolValue
  //   方法返回值: PlayerRankBoardMessage
  // rankRewardInfo = CmdMgr.getMergeCmd(MainCmd.playerCmd, 31),
  // 路由: 3 - 32  --- 【点赞排行榜获取奖励】 --- 【PlayerAction:693】【takeRankReward】
  //     方法参数: BoolValue otherServer true-跨服 false-本服 目前不支持跨服
  //     方法返回值: RewardMessage
  // takeRankReward = CmdMgr.getMergeCmd(MainCmd.playerCmd, 32),
  public rankRewardInfo(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    //
    ApiHandler.instance.requestSync(
      PlayerRankBoardMessage,
      PlayerSubCmd.rankRewardInfo,
      BoolValue.encode({ value: false }),
      (data: PlayerRankBoardMessage) => {
        //
        MainRankModule.data.playerRankBoardMessage = data;
        success?.(data);
      },
      error
    );
  }
  public powerRank(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      PlayerRankMessage,
      PlayerSubCmd.powerRank,
      null,
      (data: PlayerRankMessage) => {
        //
        success?.(data);
      },
      error
    );
  }

  public energyRank(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      PlayerRankMessage,
      PlayerSubCmd.energyRank,
      null,
      (data: PlayerRankMessage) => {
        //
        success?.(data);
      },
      error
    );
  }
  // 路由: 23 - 29  --- 【仙盟战力排行榜】 --- 【ClubAction:793】【clubRank】
  //   方法返回值: ClubRankMessage
  public clubRank(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      ClubRankMessage,
      ClubSubCmd.clubRank,
      null,
      (data: ClubRankMessage) => {
        //
        success?.(data);
      },
      error
    );
  }
  // 路由: 28 - 4  --- 【点赞排行榜获取奖励】 --- 【RealmsAction:143】【takeRankReward】
  //     方法参数: BoolValue otherServer true-跨服 false-本服 目前不支持跨服
  //     方法返回值: RewardMessage
  public takeMainRankReward(success: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      RewardMessage,
      PlayerSubCmd.takeRankReward,
      BoolValue.encode({ value: false }),
      (data: RewardMessage) => {
        //
        this.rankRewardInfo(() => {
          success?.(data);
        });
      },
      error
    );
  }
}
