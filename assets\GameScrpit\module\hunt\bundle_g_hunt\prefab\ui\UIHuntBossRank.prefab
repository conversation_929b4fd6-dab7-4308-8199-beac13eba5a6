[{"__type__": "cc.Prefab", "_name": "UIHuntBossRank", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIHuntBossRank", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 360}], "_active": true, "_components": [{"__id__": 478}], "_prefab": {"__id__": 480}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 141}, {"__id__": 163}, {"__id__": 193}, {"__id__": 255}, {"__id__": 317}], "_active": true, "_components": [{"__id__": 357}], "_prefab": {"__id__": 359}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 10}, {"__id__": 92}], "_active": true, "_components": [{"__id__": 136}, {"__id__": 138}], "_prefab": {"__id__": 140}, "_lpos": {"__type__": "cc.Vec3", "x": 4, "y": 13, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "TY_bg_tanchuang_huodong_neikuang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -22.242, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 661, "height": 1108}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51vrfDd7BLCr90h65lQx71"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "250357c5-736c-4c9c-a75a-b1b8647d8cfd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31JsOJ+4FIEL3yDSBAqfPx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "065HgDXq1I0YxtgJd2F3AV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "rankScroll", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 11}, {"__id__": 37}, {"__id__": 49}, {"__id__": 69}, {"__id__": 77}], "_active": false, "_components": [{"__id__": 85}, {"__id__": 87}, {"__id__": 89}], "_prefab": {"__id__": 91}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": 448.25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "streamer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 12}, {"__id__": 18}, {"__id__": 24}], "_active": true, "_components": [{"__id__": 30}, {"__id__": 32}, {"__id__": 34}], "_prefab": {"__id__": 36}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 32.763, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": -238.015, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 14}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9pdsZU09JN4tPwJaIvU9Q"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 16}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "排名", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbWogGDy1GYqgIKMxvpVLo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07LkITrm5DCpkPfOeXN2mA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}], "_prefab": {"__id__": 23}, "_lpos": {"__type__": "cc.Vec3", "x": -3.888, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaLs6Fn5RCE77WaaXd10ma"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "玩家信息", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ebRg3KnRIWoeCIGBWbST9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cIduWl+dLBr05nm8HPes7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 25}, {"__id__": 27}], "_prefab": {"__id__": 29}, "_lpos": {"__type__": "cc.Vec3", "x": 196.933, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 26}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7duTQsnhhD44887WEJd3ca"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 28}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "积分", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73LEEZerlNJJv90s2u97g6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29nRPc4SdP+bkTIumdiaNl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 617, "height": 49}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff9ztEwa9KzJOl+QSX/Fm2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 33}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "63c261ce-11e2-4242-927c-84b35af2251c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eBbBIuIZG/LcdrUrAunlD"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 35}, "_alignFlags": 41, "_target": null, "_left": 6, "_right": 6, "_top": -57.263, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 400, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffUQmDdtdPEKedn4tbTtBH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f68PcJ9fRLApxWyUSGpLEl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 38}], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}], "_prefab": {"__id__": 48}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -470, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 37}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 41}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 38, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 40}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 37.799999999999955}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9gQ40A3JCCZ+57kWsc3Ps"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 42}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 121, "g": 159, "b": 201, "a": 255}, "_string": "活动暂未开始", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bjXp9Q5tBXZpqkR1Zf0YR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64sV6KZHRNp6Th7sH77Qqz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 45}, "_contentSize": {"__type__": "cc.Size", "width": 615, "height": 940}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecF3cY0p5LeYQpFcO8MwVS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 47}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2f256584-d6a2-44d0-8d3d-d805e825c218@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acDRXUmING9bd467meN/Ep"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c424/BwEdOyqRz3/AKnK5e", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>w", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 50}], "_active": true, "_components": [{"__id__": 60}, {"__id__": 62}, {"__id__": 64}, {"__id__": 66}], "_prefab": {"__id__": 68}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "rankContent", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 51}, {"__id__": 53}, {"__id__": 55}, {"__id__": 57}], "_prefab": {"__id__": 59}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 52}, "_contentSize": {"__type__": "cc.Size", "width": 620, "height": 16.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79+Wipiu9FO49o909DnFPE"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 54}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 5.5, "_paddingBottom": 20, "_spacingX": 0, "_spacingY": 9, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eb2XFbNA5HLbxIV5Eudree"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 56}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dZID8lLBCgL/xbz9+13El"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 58}, "_alignFlags": 41, "_target": null, "_left": 5072.55, "_right": 5072.55, "_top": 0, "_bottom": -562, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 650, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdTHVkmDRAMascJbSIuXH9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7C1dcdRNH4ZOkkVJN10bn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 61}, "_contentSize": {"__type__": "cc.Size", "width": 620, "height": 940}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "009jJGzjpG15zs/EtV+zkx"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 63}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eY/d/7bdJSYczMsc/+uDz"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 65}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3TWTQyE5MVKr1f9XI0uAe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 67}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "770LLk8J1HJpZj8cs74TV0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72kHncxnlNfIhJrsVcuwE8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bottom_crash", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 70}, {"__id__": 72}, {"__id__": 74}], "_prefab": {"__id__": 76}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -100900, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 71}, "_contentSize": {"__type__": "cc.Size", "width": 629, "height": 200000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7c9P0CeupChr+Z1HUCYvov"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 73}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -200000, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cb01hJbL1OtZWQ/A5Sq/lz"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eQ1apZHhEWZdbjCp9Sq3Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48yn3S8FRNNJEyh4JmOi9g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "top_carsh", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 78}, {"__id__": 80}, {"__id__": 82}], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 100000, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 79}, "_contentSize": {"__type__": "cc.Size", "width": 629, "height": 200000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93CwXJF19KnLF/Kfh38AyJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 81}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": -200000, "_bottom": -156, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73ThPBjzFClrq1Rdyxawjk"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 83}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7kpoBbfNOGbS0yx+tXYBN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acvBvcMFtEF7gb6kCmOBFO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 86}, "_contentSize": {"__type__": "cc.Size", "width": 629, "height": 940}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3wEeCenlFTINoc3PG+mYk"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 88}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 50}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdLckNpg5A87w6Ij1fVaTC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 90}, "_alignFlags": 40, "_target": null, "_left": 47, "_right": 53, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bnmYlw25JoaNT1JQpUi3+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c38hXFsERO+6AcSmn9KyCf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "awardScroll", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 93}, {"__id__": 113}, {"__id__": 121}], "_active": true, "_components": [{"__id__": 129}, {"__id__": 131}, {"__id__": 133}], "_prefab": {"__id__": 135}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 493.8710000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "awardView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [{"__id__": 94}], "_active": true, "_components": [{"__id__": 104}, {"__id__": 106}, {"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -495, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "awardContent", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 95}, {"__id__": 97}, {"__id__": 99}, {"__id__": 101}], "_prefab": {"__id__": 103}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 495, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 96}, "_contentSize": {"__type__": "cc.Size", "width": 527, "height": 16.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05Gbx293dJtZ/gPu9/fubb"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 98}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 5.5, "_paddingBottom": 20, "_spacingX": 0, "_spacingY": 9, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4l/bheH1Gh62ZKtlB0G+0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 100}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0arEyI04pCo6heCL9tPNO/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 102}, "_alignFlags": 41, "_target": null, "_left": 5584.05, "_right": 5584.05, "_top": 0, "_bottom": -562, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 650, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fF9BfFV9GF6o1ZHysg0aH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0174GaNQ5NjIS0D072syaj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 105}, "_contentSize": {"__type__": "cc.Size", "width": 629, "height": 990}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deKF2NGNhMoZIMcjZbj47A"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 107}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eVp/xbedPqZGFLpneq29F"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 109}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ec1akFaq9PKIJvIDAtWLcI"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 111}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fh7GDJ0NAQrkl5XUjgeXz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8H/GoOelOzpdOFu5+dNig", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bottom_crash", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [{"__id__": 114}, {"__id__": 116}, {"__id__": 118}], "_prefab": {"__id__": 120}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -100990, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 115}, "_contentSize": {"__type__": "cc.Size", "width": 629, "height": 200000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fDl5dtqNI47I0IhMr4dkg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 117}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -200000, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6jUund+VFCYJdjFTkPaYu"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 119}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55exRx2SdCBoWB+ijfawKS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eauUB9VrZEjKl8zQiKq4Uc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "top_carsh", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [{"__id__": 122}, {"__id__": 124}, {"__id__": 126}], "_prefab": {"__id__": 128}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 100000, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 123}, "_contentSize": {"__type__": "cc.Size", "width": 629, "height": 200000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ee2E0VAkNLb6GkTQC5U4Tl"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 125}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": -200000, "_bottom": -156, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65H9IL3C5ASIwSwtngWcNT"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 127}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "706+wZilhHGr1aPq/P25u0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "15OBcda1BIXaOkv7WYUlO+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": {"__id__": 130}, "_contentSize": {"__type__": "cc.Size", "width": 629, "height": 990}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fsnsQHVFNfJD80jFT8ZI0"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": {"__id__": 132}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 94}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94CGHMFqhHJ4WWv0pHfC1W"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": {"__id__": 134}, "_alignFlags": 40, "_target": null, "_left": 50, "_right": 50, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dGztTvTtDHLRQPc8H87cc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2R6WeUqRIuLvGe0D8n60h", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 137}, "_contentSize": {"__type__": "cc.Size", "width": 729, "height": 1195}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59HY1KMDpJVKQalaUz+H2i"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 139}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fd901f6d-8cd0-4bb6-8552-8b9d23889fc1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67PDKcwD5I3ZC4luat+JbF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5si/L2uFLT5SnVHl5o7q2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spr_title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 142}, {"__id__": 150}], "_active": true, "_components": [{"__id__": 158}, {"__id__": 160}], "_prefab": {"__id__": 162}, "_lpos": {"__type__": "cc.Vec3", "x": -156.378, "y": 539.337, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 141}, "_children": [], "_active": true, "_components": [{"__id__": 143}, {"__id__": 145}, {"__id__": 147}], "_prefab": {"__id__": 149}, "_lpos": {"__type__": "cc.Vec3", "x": -191.519, "y": -0.722, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": {"__id__": 144}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 124.74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4ej1bnoLZM6b+N6hXKFThw"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": {"__id__": 146}, "_lineHeight": 99, "_string": "<outline color=#ffa200 width=3><color=#ffffff><size=91>排</size></color></outline><outline color=#ffa200 width=3><color=#ffffff><size=77>行奖励</size></color></outline>", "_horizontalAlign": 1, "_verticalAlign": 2, "_fontSize": 99, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "059Y4cW9dNZKI/pc8HxqOB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": {"__id__": 148}, "_alignFlags": 8, "_target": null, "_left": 11.980999999999995, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12VdZMbRtEWKSBk+2ztc3R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44NwYpmYlFtYMeIPZchCvC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 141}, "_children": [], "_active": true, "_components": [{"__id__": 151}, {"__id__": 153}, {"__id__": 155}], "_prefab": {"__id__": 157}, "_lpos": {"__type__": "cc.Vec3", "x": 487.54, "y": 32.222, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 152}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8c3R5ibLtC1aZQbC5oxx59"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 154}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8e27d803-97dc-403c-bae5-609f4c45539b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4hE3vmyJIlrhP8iYiuBCy"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 156}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 150}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ai8EdSdVIuJjmWxxT3nk6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e92losN49NkJ81lkb8BEFn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 159}, "_contentSize": {"__type__": "cc.Size", "width": 407, "height": 176}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1Hjg7c/5K/be7zxnvyjWr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 161}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "41d4e0c2-61eb-4d08-94fe-e48ab611739d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5GFLvHh5Hdav5k7f50hhT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75QD+EBo1MRoZ8s6C4s+qI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "awardScroll_time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 164}, {"__id__": 170}], "_active": true, "_components": [{"__id__": 188}, {"__id__": 190}], "_prefab": {"__id__": 192}, "_lpos": {"__type__": "cc.Vec3", "x": 14.353, "y": 562.403, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 163}, "_children": [], "_active": true, "_components": [{"__id__": 165}, {"__id__": 167}], "_prefab": {"__id__": 169}, "_lpos": {"__type__": "cc.Vec3", "x": 54.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": {"__id__": 166}, "_contentSize": {"__type__": "cc.Size", "width": 109, "height": 48.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20E1SFvf5ArqFGfh40po6h"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 164}, "_enabled": true, "__prefab": {"__id__": 168}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "倒计时", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": false, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 6, "g": 135, "b": 9, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "875zrvdcRDw4iBtdd8YRp3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcB9AKmf9Hbrk27jr2olzc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 163}, "_prefab": {"__id__": 171}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 170}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 172}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "5bKsMGgxxO86lXJ3XSvOml", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 173}, {"__id__": 175}, {"__id__": 176}, {"__id__": 177}, {"__id__": 178}, {"__id__": 180}, {"__id__": 181}, {"__id__": 183}, {"__id__": 184}, {"__id__": 185}, {"__id__": 186}, {"__id__": 187}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_name"], "value": "LblCd"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 109, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100.25, "height": 48.1}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_horizontalAlign"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["73icu5TNJLZaYTvRIxQDjK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_actualFontSize"], "value": 35}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_fontSize"], "value": 35}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_lineHeight"], "value": 35}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 6, "g": 135, "b": 9, "a": 255}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 189}, "_contentSize": {"__type__": "cc.Size", "width": 209.25, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9PtLIBExK3JjUGldKK433"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 191}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85X2K21XBFipYuOVkoVwc2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eqmFkB+VFkqxPMcTdSfJk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_rank", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 194}, {"__id__": 212}, {"__id__": 230}], "_active": true, "_components": [{"__id__": 248}, {"__id__": 250}, {"__id__": 252}], "_prefab": {"__id__": 254}, "_lpos": {"__type__": "cc.Vec3", "x": 226.434, "y": -622, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 193}, "_children": [{"__id__": 195}, {"__id__": 201}], "_active": true, "_components": [{"__id__": 209}], "_prefab": {"__id__": 211}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 194}, "_children": [], "_active": true, "_components": [{"__id__": 196}, {"__id__": 198}], "_prefab": {"__id__": 200}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 197}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fJdtaLDdDgL6hSKYnNrBm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 199}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fHKNMGo5Ao6MBSzTdEyPa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62XOh/gvxIQq3vO5AkR9T7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 194}, "_children": [], "_active": true, "_components": [{"__id__": 202}, {"__id__": 204}, {"__id__": 206}], "_prefab": {"__id__": 208}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 8.691, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 203}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 48.84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9ckWquWVF2ppAEuPdFQzB"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 205}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "个人榜单", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 34, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28TcjWoNxD5oWkq+F/qC34"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 207}, "_args": [], "_messageKey": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cCEpfD4ZNbLtowa9wirLe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "45XMN3+YdLwp7axIxZIHkR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 194}, "_enabled": true, "__prefab": {"__id__": 210}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66OTZkH6ZN16MdhiEei80/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "77sOmzvMJBqatQqNudj5i0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 193}, "_children": [{"__id__": 213}, {"__id__": 219}], "_active": false, "_components": [{"__id__": 227}], "_prefab": {"__id__": 229}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 214}, {"__id__": 216}], "_prefab": {"__id__": 218}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": {"__id__": 215}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9X2UY8MdAWprIu6dQ5UaY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": {"__id__": 217}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22HqewbghGYY6dOAL9fbiJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4CyzQbE5OU6Cmf/2PrniI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 212}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 222}, {"__id__": 224}], "_prefab": {"__id__": 226}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 8.691, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 221}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 48.84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05J61JRJhLsYpkSmc/+DGG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 223}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "个人榜单", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 34, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33YlB4uhpCZKMvaQhWDhZ5"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 225}, "_args": [], "_messageKey": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0Bjqa06hHZp+zA13g7kAl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98Rsm7ZqVPS4n/pF4/NIQO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 228}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87qhS2L2xDjbRLSfUe5zPF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1gtqeOApFUoGy3DfWHvmB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_selected", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 193}, "_children": [{"__id__": 231}, {"__id__": 237}], "_active": false, "_components": [{"__id__": 245}], "_prefab": {"__id__": 247}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_xuanzhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [], "_active": true, "_components": [{"__id__": 232}, {"__id__": 234}], "_prefab": {"__id__": 236}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 233}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a23sLVZo9LRbreY59TLJgl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 235}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4f6079-50dd-47ea-982e-ad20f3c3c525@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79sOPAdLBNz7sgaERSNsBU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbOeoxoB9ITqTCk1Z1Azs/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [], "_active": true, "_components": [{"__id__": 238}, {"__id__": 240}, {"__id__": 242}], "_prefab": {"__id__": 244}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 8.691, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 239}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 48.84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eb8Q1eHp5NLaYbczyoPLJW"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 241}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "个人榜单", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 34, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5f56LZPeJCBpdPVBE9lqRz"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 243}, "_args": [], "_messageKey": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0408BAakdGqrwg096ax6Sj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2Gdpc3h1EUaL9Ji3tgxom", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 246}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16BOq9XERLpbINaAEsfV05"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92WNpUFqVF6Z/Fx1272chM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 193}, "_enabled": true, "__prefab": {"__id__": 249}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49eA1tbOJDIqOXmav+mZqT"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 193}, "_enabled": true, "__prefab": {"__id__": 251}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbP+0PzSpIcLNxRa30AH2c"}, {"__type__": "59b1da6uDxL2r5VRR8PWhCO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 193}, "_enabled": true, "__prefab": {"__id__": 253}, "enableNode": {"__id__": 194}, "disableNode": {"__id__": 212}, "selectedNode": {"__id__": 230}, "_messageKey": 0, "_args": [], "_selected": false, "_btnEnable": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dFOsGSFJEobN+wCMQipfY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8zmdQ82NLPIMF3UzPIs/I", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_award", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 256}, {"__id__": 274}, {"__id__": 292}], "_active": true, "_components": [{"__id__": 310}, {"__id__": 312}, {"__id__": 314}], "_prefab": {"__id__": 316}, "_lpos": {"__type__": "cc.Vec3", "x": 31.334, "y": -622, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 255}, "_children": [{"__id__": 257}, {"__id__": 263}], "_active": false, "_components": [{"__id__": 271}], "_prefab": {"__id__": 273}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 256}, "_children": [], "_active": true, "_components": [{"__id__": 258}, {"__id__": 260}], "_prefab": {"__id__": 262}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 257}, "_enabled": true, "__prefab": {"__id__": 259}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0Mc+gxk1M75UAMsz663Z9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 257}, "_enabled": true, "__prefab": {"__id__": 261}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e69zVs7bZBsozqehD2Sx3/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fahpzTKzlOa4I1b8m3xCyY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 256}, "_children": [], "_active": true, "_components": [{"__id__": 264}, {"__id__": 266}, {"__id__": 268}], "_prefab": {"__id__": 270}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.822, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 265}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 48.84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ejlTo56dBm6gC5+93/jln"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 267}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "排行奖励", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 34, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dxZM2qDZM9IYg36JMvFWQ"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 269}, "_args": [], "_messageKey": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49ljK6OpFGr4lj/ZszVpV0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfu91pjOFNV6RBPL9F2K/d", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 272}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8uXNF1ClGAbK42CgQuqJs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32my9g9jtOaL8J7nPh6uBC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 255}, "_children": [{"__id__": 275}, {"__id__": 281}], "_active": false, "_components": [{"__id__": 289}], "_prefab": {"__id__": 291}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 274}, "_children": [], "_active": true, "_components": [{"__id__": 276}, {"__id__": 278}], "_prefab": {"__id__": 280}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 277}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64QIjcCfVBEqHrW6xaZtrc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 279}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dqMyHTepLhq4g0aPLQFPq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c79X3hKBFBw6PTmIEUvhhQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 274}, "_children": [], "_active": true, "_components": [{"__id__": 282}, {"__id__": 284}, {"__id__": 286}], "_prefab": {"__id__": 288}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.822, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 281}, "_enabled": true, "__prefab": {"__id__": 283}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 48.84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9btuyl2xZMyrZbpcxp4H+L"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 281}, "_enabled": true, "__prefab": {"__id__": 285}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "排行奖励", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 34, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eC2yT1VpNVbIX17q8w7By"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 281}, "_enabled": true, "__prefab": {"__id__": 287}, "_args": [], "_messageKey": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3WrCEgS5OCpmr4sMAQTk1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6bMWgh0HlCmrWMnPPdDJaR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": {"__id__": 290}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40oGzKuqpD9Ycwb4Gow3z5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b6UePct8NCbJEMy+vQzsoF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_selected", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 255}, "_children": [{"__id__": 293}, {"__id__": 299}], "_active": true, "_components": [{"__id__": 307}], "_prefab": {"__id__": 309}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_xuanzhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 292}, "_children": [], "_active": true, "_components": [{"__id__": 294}, {"__id__": 296}], "_prefab": {"__id__": 298}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 295}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20hOZ/SAdF/o3rxvJ1A5w5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 297}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4f6079-50dd-47ea-982e-ad20f3c3c525@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6HbgoaQ1NXKEvtsA+X6Sq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "41Haw/KvxN1ZWT6rT8Izzj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 292}, "_children": [], "_active": true, "_components": [{"__id__": 300}, {"__id__": 302}, {"__id__": 304}], "_prefab": {"__id__": 306}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.822, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": {"__id__": 301}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 48.84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aSDOaDXxNgpZEvccWV/5i"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": {"__id__": 303}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "排行奖励", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 34, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3415YpNWFEgYjRGIpeMBQn"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 299}, "_enabled": true, "__prefab": {"__id__": 305}, "_args": [], "_messageKey": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6rq/WVSNNioPmqv++THNZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ebLvEA5plAgqru8SOlELXl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 308}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9neIxKLNH6YR1gdz+ZVrm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "83UCrRQjtCtYzrm9YkKmp2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 311}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62dB2maTZJvIFqvgsNAkr1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 313}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94a5dPP7RG6I7DcKYNy0C7"}, {"__type__": "59b1da6uDxL2r5VRR8PWhCO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 315}, "enableNode": {"__id__": 256}, "disableNode": {"__id__": 274}, "selectedNode": {"__id__": 292}, "_messageKey": 0, "_args": [], "_selected": true, "_btnEnable": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7d73+Qp1tHH62qeNnEwUR1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "45vKXFEkZIJJ0BADA3mleA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bottom_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 318}, {"__id__": 336}], "_active": true, "_components": [{"__id__": 354}], "_prefab": {"__id__": 356}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -525.442, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 317}, "_children": [{"__id__": 319}, {"__id__": 325}], "_active": true, "_components": [{"__id__": 331}, {"__id__": 333}], "_prefab": {"__id__": 335}, "_lpos": {"__type__": "cc.Vec3", "x": -293.973, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lab_myRank1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 318}, "_children": [], "_active": true, "_components": [{"__id__": 320}, {"__id__": 322}], "_prefab": {"__id__": 324}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 321}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03mvzMzJhPwbwoNHvqqd6X"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 323}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "我的排名：", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83jSsOBVlISpjCddFT0jqR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bc7csYVfRIU7vL7tjQs/fS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_myRank2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 318}, "_children": [], "_active": true, "_components": [{"__id__": 326}, {"__id__": 328}], "_prefab": {"__id__": 330}, "_lpos": {"__type__": "cc.Vec3", "x": 160, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 325}, "_enabled": true, "__prefab": {"__id__": 327}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8VxdTT7RJZrXUHrke9ptQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 325}, "_enabled": true, "__prefab": {"__id__": 329}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "未上榜", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dPuuP/iVECZ7klfMZRwg1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0bYExczhVJDrbkEYtCJFtv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 332}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acZn6mdQBD+bTfX0BZIP/7"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 334}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09Xkr8gyJHvIXDrZqx1ANP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11uw9D18NEo5TltcCEfXGI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 317}, "_children": [{"__id__": 337}, {"__id__": 343}], "_active": true, "_components": [{"__id__": 349}, {"__id__": 351}], "_prefab": {"__id__": 353}, "_lpos": {"__type__": "cc.Vec3", "x": 315, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lab_myHurt1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 336}, "_children": [], "_active": true, "_components": [{"__id__": 338}, {"__id__": 340}], "_prefab": {"__id__": 342}, "_lpos": {"__type__": "cc.Vec3", "x": -207.99998474121094, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 339}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4KYhfM8FJ77pvV+ESkdy2"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 341}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "造成伤害：", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1sa8WGd9HDbDH3HI+xR2q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67sR4TcaZIN5jzvqNafSbZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_myHurt2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 336}, "_children": [], "_active": true, "_components": [{"__id__": 344}, {"__id__": 346}], "_prefab": {"__id__": 348}, "_lpos": {"__type__": "cc.Vec3", "x": -63.99999237060547, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 343}, "_enabled": true, "__prefab": {"__id__": 345}, "_contentSize": {"__type__": "cc.Size", "width": 127.99998474121094, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07JuYnC/dFO58Uq2yNvSPu"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 343}, "_enabled": true, "__prefab": {"__id__": 347}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_string": "99900万", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66Kz+erLVMoJlH2ygrVdn0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23rDNwhh5GzbAyDcG+xMDG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 350}, "_contentSize": {"__type__": "cc.Size", "width": 287.99998474121094, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eGojBQSVAF71UH06pvqWC"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 352}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72k88KcBtAf7q3R22mg8B6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dG76zS5hHsaYZG15Yzg4g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 355}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1dr2IOsFF0KOYCbZex1EK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bMmg7zbBB/rQBl93tOirX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 358}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62X348H7NGNbtmO0XNq401"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fboBlSq11N+ZlIV9BUodbk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "clone_ins", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 361}, {"__id__": 405}, {"__id__": 424}], "_active": false, "_components": [{"__id__": 475}], "_prefab": {"__id__": 477}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "awardMessage", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 360}, "_children": [{"__id__": 362}, {"__id__": 394}], "_active": true, "_components": [{"__id__": 400}, {"__id__": 402}], "_prefab": {"__id__": 404}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_sklx_biaotidi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 361}, "_children": [{"__id__": 363}], "_active": true, "_components": [{"__id__": 387}, {"__id__": 389}, {"__id__": 391}], "_prefab": {"__id__": 393}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 60, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 362}, "_children": [{"__id__": 364}, {"__id__": 370}, {"__id__": 376}], "_active": true, "_components": [{"__id__": 382}, {"__id__": 384}], "_prefab": {"__id__": 386}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "line_fengexian_baioti_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 363}, "_children": [], "_active": true, "_components": [{"__id__": 365}, {"__id__": 367}], "_prefab": {"__id__": 369}, "_lpos": {"__type__": "cc.Vec3", "x": -121, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 366}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bkWUbHedOIqZox0YhroQD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 364}, "_enabled": true, "__prefab": {"__id__": 368}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 68, "g": 135, "b": 194, "a": 255}, "_spriteFrame": {"__uuid__": "e8bd324c-660f-4f00-b9e9-4b92e3b611c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05YP5A/f1MB7Mv+4aB4I1M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4e+mC3V/JNNYZzHumh+sdC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_awardRank", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 363}, "_children": [], "_active": true, "_components": [{"__id__": 371}, {"__id__": 373}], "_prefab": {"__id__": 375}, "_lpos": {"__type__": "cc.Vec3", "x": -38, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 372}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 50.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9liMmCGJCGJ7dYL365y3Z"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 374}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 234, "g": 247, "b": 255, "a": 255}, "_string": "奖励", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 24, "g": 98, "b": 178, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dZ2ZhzotBXaS3MrOFlM5r"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0tV8qBnpLlZWyBQWRuY6P", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_baioti_bai-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 363}, "_children": [], "_active": true, "_components": [{"__id__": 377}, {"__id__": 379}], "_prefab": {"__id__": 381}, "_lpos": {"__type__": "cc.Vec3", "x": 121, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 378}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0Kk3CXZxNd4k+a39bAp2V"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 380}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 68, "g": 135, "b": 194, "a": 255}, "_spriteFrame": {"__uuid__": "e8bd324c-660f-4f00-b9e9-4b92e3b611c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8lKDy1hVGfYOwMxwtS/wI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e3/0x9cXxIh5RbanM+hr/i", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 383}, "_contentSize": {"__type__": "cc.Size", "width": 338, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59JAohCb1Fy4uZczLS4Csk"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 385}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 35, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dffY3dX9VEiqwfBOF33OdW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25inlJjxNFypqbooBynI3X", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 388}, "_contentSize": {"__type__": "cc.Size", "width": 608, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfFOrc/7VPVo80nW7KyIyH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 390}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1c438bf4-9cff-47ea-a7cf-0798fe331b59@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cp8nFCRJPMYpW127JVyQp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 392}, "_alignFlags": 41, "_target": null, "_left": 2, "_right": 2, "_top": 3, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 602, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f1zHn2a1MK5ER/1raJltF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54YCCFln9GzZXabYqZjRc+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lay_award", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 361}, "_children": [], "_active": true, "_components": [{"__id__": 395}, {"__id__": 397}], "_prefab": {"__id__": 399}, "_lpos": {"__type__": "cc.Vec3", "x": -304.878, "y": -21.894, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 394}, "_enabled": true, "__prefab": {"__id__": 396}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "420T4PB8lEmpsL7BRBOXyp"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 394}, "_enabled": true, "__prefab": {"__id__": 398}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": -13, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b06KnbY2pATosXWvGyZBeY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5eoK7QmxxKEJ9K+U+j83WM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 401}, "_contentSize": {"__type__": "cc.Size", "width": 612, "height": 178}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8PrPbXKBJiqV7enDpQKJ1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 403}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5fca5cce-83b1-4ac1-a0a6-9abd0b00b04f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06ZhpOwN1Myop1gDzVFEou"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0V1GhWtFJoIBum/ROY7Gr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 360}, "_prefab": {"__id__": 406}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 405}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 407}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "b8GBAIolVKQJn6gJb5kd5P", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 408}, {"__id__": 410}, {"__id__": 411}, {"__id__": 412}, {"__id__": 413}, {"__id__": 414}, {"__id__": 415}, {"__id__": 417}, {"__id__": 419}, {"__id__": 421}, {"__id__": 423}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_name"], "value": "btn_item"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 416}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 418}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 420}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 422}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.Node", "_name": "rankMessage", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 360}, "_children": [{"__id__": 425}, {"__id__": 431}, {"__id__": 440}, {"__id__": 452}, {"__id__": 458}, {"__id__": 464}], "_active": false, "_components": [{"__id__": 470}, {"__id__": 472}], "_prefab": {"__id__": 474}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spr_rank", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 424}, "_children": [], "_active": false, "_components": [{"__id__": 426}, {"__id__": 428}], "_prefab": {"__id__": 430}, "_lpos": {"__type__": "cc.Vec3", "x": -267.779, "y": 1.663, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 425}, "_enabled": true, "__prefab": {"__id__": 427}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdAALZQSxEWYF8ac/hcAX3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 425}, "_enabled": true, "__prefab": {"__id__": 429}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c43f618a-983c-41b1-9ea0-ed76e506bef6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dW5FrlTNBa7oSGHEgA5Ml"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9bYGdi0FNIDpn+m3Dutm+J", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 424}, "_prefab": {"__id__": 432}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 431}, "asset": {"__uuid__": "9f3bd2e8-623f-4363-815e-8dbdf1ced8ba", "__expectedType__": "cc.Prefab"}, "fileId": "e3t6Sl+oNAFp8fYboS8QNH", "instance": {"__id__": 433}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "21rztJVy5DuqdWfarrf+Qc", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 434}, {"__id__": 436}, {"__id__": 437}, {"__id__": 438}, {"__id__": 439}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 435}, "propertyPath": ["_name"], "value": "btn_header"}, {"__type__": "cc.TargetInfo", "localID": ["e3t6Sl+oNAFp8fYboS8QNH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 435}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -122.147, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 435}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 435}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 435}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.78, "y": 0.78, "z": 1}}, {"__type__": "cc.Node", "_name": "spr_lab_rank_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 424}, "_children": [{"__id__": 441}], "_active": true, "_components": [{"__id__": 447}, {"__id__": 449}], "_prefab": {"__id__": 451}, "_lpos": {"__type__": "cc.Vec3", "x": -267.626, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lab_rank", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 440}, "_children": [], "_active": true, "_components": [{"__id__": 442}, {"__id__": 444}], "_prefab": {"__id__": 446}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 441}, "_enabled": true, "__prefab": {"__id__": 443}, "_contentSize": {"__type__": "cc.Size", "width": 33.983978271484375, "height": 60.480000000000004}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "473ZzMkQtItI5o6Uanuzy3"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 441}, "_enabled": true, "__prefab": {"__id__": 445}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 234, "g": 240, "b": 244, "a": 255}, "_string": "11111", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 11, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 2, "_enableWrapText": false, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7BaC074hDpqEMzkF1qr3F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9PaEcouxBjIWnl/Q88Jvn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 440}, "_enabled": true, "__prefab": {"__id__": 448}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 62}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5Nx2AEgpLHp4Zq5MgOfUC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 440}, "_enabled": true, "__prefab": {"__id__": 450}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "13f692bb-ee4a-4f53-922d-07311db9d3ed@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cYJnOh6FEgIoJFoHe1Xiv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cB8h8j5tCJrNiN32KjjXj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_Name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 424}, "_children": [], "_active": true, "_components": [{"__id__": 453}, {"__id__": 455}], "_prefab": {"__id__": 457}, "_lpos": {"__type__": "cc.Vec3", "x": -65.457, "y": 25.623, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 454}, "_contentSize": {"__type__": "cc.Size", "width": 108, "height": 45.36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79PTO7MFVNfqVyGwPCx5dM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 456}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "小仙桃", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 36, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 238, "g": 114, "b": 114, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fS1jzGUtIwbXC8u/6WFHg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "18IKOci4FHs6yORQXC5NMx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_level", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 424}, "_children": [], "_active": true, "_components": [{"__id__": 459}, {"__id__": 461}], "_prefab": {"__id__": 463}, "_lpos": {"__type__": "cc.Vec3", "x": -61.521, "y": -15.171, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 460}, "_contentSize": {"__type__": "cc.Size", "width": 168, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffHdVzUQZGd4LoEpjHPtpp"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 462}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 93, "g": 145, "b": 197, "a": 255}, "_string": "金仙中期二阶", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 238, "g": 114, "b": 114, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcDYjGtl1JzomhF69CWu7+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0/JojGTxKrLd7UjE/zDix", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_score", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 424}, "_children": [], "_active": true, "_components": [{"__id__": 465}, {"__id__": 467}], "_prefab": {"__id__": 469}, "_lpos": {"__type__": "cc.Vec3", "x": 134.231, "y": 3.111, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 464}, "_enabled": true, "__prefab": {"__id__": 466}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 34.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "180h/BK3tG64nvyw4AVjv3"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 464}, "_enabled": true, "__prefab": {"__id__": 468}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 225, "g": 117, "b": 57, "a": 255}, "_string": "16548", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 238, "g": 114, "b": 114, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90kzSKrKxBOakSYPQ6Qa05"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8aabuqll1KxqPySr6WyD9J", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 471}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 118}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98mvcK5qlAT6eh0axoKhz+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 473}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2f256584-d6a2-44d0-8d3d-d805e825c218@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1fart0+9CLKw57zSjU86k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52cNoh9/BNt56gplzJi1hv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 360}, "_enabled": true, "__prefab": {"__id__": 476}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3ojQVLuZFaKAScXdH48bE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dLl1zAxBMC4VaRQhd992N", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 479}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecUaaHWalB67793H8ccmeb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2n9pFcN5N25TZQurbe58S", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 431}, {"__id__": 405}, {"__id__": 170}]}]