import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { HeroPetViewHolder } from "./HeroPetViewHolder";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HeroPetAdapter extends ListAdapter {
  private item: Node;
  private expand: Node;
  private datas: any[] = [];
  private isOnlyUpdateData: boolean = false;
  constructor(item: Node, expand: Node) {
    super();
    this.item = item;
    this.expand = expand;
  }
  setData(data: any[]) {
    this.datas = data;
    this.notifyDataSetChanged();
  }
  updateDataOnly(data: any) {
    this.datas = data;
    // log.log("updateDataOnly---");
    this.isOnlyUpdateData = true;
    this.notifyDataSetChanged(true);
    // log.log("updateDataOnly---");
    this.isOnlyUpdateData = false;
  }
  onCreateView(viewType: number): Node {
    if (viewType == -1) {
      let item = instantiate(this.expand);
      return item;
    }
    let item = instantiate(this.item);
    item.getComponent(HeroPetViewHolder).init();
    return item;
  }
  onBindData(node: Node, position: number): void {
    // log.log("updateView---");
    node.getComponent(HeroPetViewHolder).updateData(this.datas[position], position, this.isOnlyUpdateData);
  }
  getCount(): number {
    return this.datas.length;
  }
}
