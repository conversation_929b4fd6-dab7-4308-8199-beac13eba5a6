{"skeleton": {"hash": "hRq+ktgVAN+6sFmuVfJGOP3Zi3Q=", "spine": "3.8.75", "x": -123.93, "y": -11.97, "width": 229.17, "height": 386.07, "images": "./images/", "audio": "D:/spine导出/主角换皮动画/主角/主角5"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -12.48}, {"name": "bone2", "parent": "bone", "length": 75.15, "rotation": 0.7, "x": 0.97, "y": 165.97, "color": "f3ff00ff"}, {"name": "bone3", "parent": "bone2", "length": 42.77, "rotation": 97.43, "x": 2.26, "y": 4.99}, {"name": "bone4", "parent": "bone3", "length": 34.8, "rotation": 0.62, "x": 42.77}, {"name": "bone5", "parent": "bone4", "length": 10.59, "rotation": -6.7, "x": 34.8}, {"name": "bone6", "parent": "bone5", "x": 27.33, "y": -15.43}, {"name": "bone13", "parent": "bone6", "x": 27.12, "y": -3.13}, {"name": "bone14", "parent": "bone13", "length": 11.27, "rotation": 71.99, "x": 2.29, "y": 3.62}, {"name": "bone15", "parent": "bone14", "length": 14.53, "rotation": 52.96, "x": 11.27}, {"name": "bone16", "parent": "bone15", "length": 15, "rotation": 50.69, "x": 14.53}, {"name": "bone18", "parent": "bone13", "length": 10.35, "rotation": -70.79, "x": 1.72, "y": -4.35}, {"name": "bone19", "parent": "bone18", "length": 13, "rotation": -89.12, "x": 10.35}, {"name": "bone20", "parent": "bone19", "length": 13, "rotation": -33.48, "x": 13.04, "y": -0.14}, {"name": "bone31", "parent": "bone4", "x": 21.73, "y": 31.05}, {"name": "bone36", "parent": "bone31", "length": 37.62, "rotation": 153.83, "x": -1.83, "y": 1.87}, {"name": "bone37", "parent": "bone36", "length": 44.81, "rotation": 18.1, "x": 37.62}, {"name": "bone38", "parent": "bone37", "length": 16.25, "rotation": 1.18, "x": 44.81}, {"name": "bone62", "parent": "bone38", "length": 42.77, "rotation": 69.27, "x": 8.88, "y": 0.27}, {"name": "bone32", "parent": "bone4", "x": 14.46, "y": -24.82}, {"name": "bone33", "parent": "bone32", "length": 35.85, "rotation": -173.49, "x": -8.17, "y": -1.39}, {"name": "bone34", "parent": "bone33", "length": 35.66, "rotation": -2.95, "x": 35.85}, {"name": "bone35", "parent": "bone34", "length": 15.47, "rotation": -1.56, "x": 35.66}, {"name": "bone44", "parent": "bone2", "length": 17.63, "rotation": -79.05, "x": 23.32, "y": -16.68, "scaleX": 1.5545, "color": "52f94bff"}, {"name": "bone45", "parent": "bone44", "length": 14.89, "rotation": 5.4, "x": 17.67, "y": 0.17, "scaleX": 1.3044, "color": "52f94bff"}, {"name": "bone46", "parent": "bone45", "length": 13.32, "rotation": -8.86, "x": 14.95, "y": 0.18, "scaleX": 1.3458, "color": "52f94bff"}, {"name": "bone49", "parent": "bone2", "length": 15.88, "rotation": -103, "x": -22.32, "y": -14.94, "scaleX": 1.495, "scaleY": 1.2559, "color": "52f94bff"}, {"name": "bone50", "parent": "bone49", "length": 15.85, "rotation": -4.68, "x": 15.88, "scaleX": 1.495, "scaleY": 1.2559, "color": "52f94bff"}, {"name": "bone51", "parent": "bone50", "length": 11.93, "rotation": -14.97, "x": 15.85, "scaleX": 1.495, "scaleY": 1.2559, "color": "52f94bff"}, {"name": "bone52", "parent": "bone2", "length": 73.79, "rotation": -93.46, "x": -17.36, "y": -3.86}, {"name": "bone53", "parent": "bone52", "length": 56.02, "rotation": -0.27, "x": 73.79}, {"name": "bone54", "parent": "bone53", "length": 20, "rotation": 30.81, "x": 56.63, "y": -0.26}, {"name": "bone56", "parent": "bone2", "length": 76.08, "rotation": -85.57, "x": 10.47, "y": -3.6}, {"name": "bone57", "parent": "bone56", "length": 55.47, "rotation": -8.81, "x": 76.08}, {"name": "bone58", "parent": "bone57", "length": 23, "rotation": 55.19, "x": 55.47}, {"name": "bone7", "parent": "bone2", "length": 32.5, "rotation": -89.97, "x": 4.98, "y": -7.99}, {"name": "b1one8", "parent": "bone7", "length": 30.64, "rotation": -0.34, "x": 32.5}, {"name": "b1one9", "parent": "b1one8", "length": 25.47, "rotation": -1.78, "x": 30.64}, {"name": "bone39", "parent": "bone37", "length": 26.56, "rotation": -12.47, "x": 39.02, "y": -13.26}, {"name": "bone40", "parent": "bone39", "length": 29.18, "rotation": -3.62, "x": 26.56}, {"name": "bone41", "parent": "bone40", "length": 37.87, "rotation": -4.96, "x": 29.18}, {"name": "bone10", "parent": "bone5", "length": 37.33, "rotation": 159.26, "x": 19.08, "y": 23.85}, {"name": "bone11", "parent": "bone10", "length": 48.44, "rotation": -5.85, "x": 37.33}, {"name": "bone12", "parent": "bone11", "length": 56.54, "rotation": 4.28, "x": 48.44}, {"name": "bone17", "parent": "bone5", "length": 39.74, "rotation": -174.97, "x": 14.96, "y": -15.47}, {"name": "bone21", "parent": "bone17", "length": 44.88, "rotation": -0.81, "x": 39.74}, {"name": "bone22", "parent": "bone21", "length": 52.53, "rotation": -4.19, "x": 44.88}, {"name": "bone23", "parent": "bone5", "length": 20.5, "rotation": 174.18, "x": 45.26, "y": 14.66}, {"name": "bone24", "parent": "bone23", "length": 20.67, "rotation": 5.92, "x": 20.5}, {"name": "bone25", "parent": "bone24", "length": 22.08, "rotation": 6.91, "x": 20.67}, {"name": "bone26", "parent": "bone25", "length": 20.11, "rotation": 17.76, "x": 22.08}, {"name": "bone27", "parent": "bone5", "length": 18.21, "rotation": -172.28, "x": 31.5, "y": -30.04}, {"name": "bone28", "parent": "bone27", "length": 18.35, "rotation": 4.86, "x": 18.21}, {"name": "bone29", "parent": "bone28", "length": 19.29, "rotation": 8.35, "x": 18.35}, {"name": "bone30", "parent": "bone29", "length": 19.99, "rotation": 13.79, "x": 19.29}, {"name": "f<PERSON><PERSON>an", "parent": "root", "x": 49.46, "y": 384.69, "scaleX": 1.7354, "scaleY": 1.7354}, {"name": "bone63", "parent": "bone2", "length": 74.63, "rotation": -77.29, "x": -13.26, "y": -3.17, "color": "f3ff00ff"}, {"name": "bone64", "parent": "bone63", "length": 63.48, "rotation": -38.01, "x": 74.63, "color": "f3ff00ff"}, {"name": "bone8", "parent": "bone2", "length": 81.96, "rotation": -69.42, "x": 11.43, "y": -2.36, "color": "f3ff00ff"}, {"name": "bone9", "parent": "bone8", "length": 62.45, "rotation": -47.21, "x": 81.96, "color": "f3ff00ff"}, {"name": "bone55", "parent": "bone", "length": 35.97, "rotation": -0.77, "x": -37.8, "y": 14.11}, {"name": "bone59", "parent": "bone55", "length": 31.28, "rotation": 92.55, "y": 0.32}, {"name": "rjio1", "parent": "bone59", "rotation": -91.78, "x": 73.19, "y": -20.33, "color": "ff3f00ff"}, {"name": "rjio2", "parent": "bone59", "rotation": -91.78, "x": 16.79, "y": -15.19, "color": "ff3f00ff"}, {"name": "rjio3", "parent": "rjio2", "x": 8.94, "y": -17.67, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone60", "parent": "bone", "length": 39.53, "rotation": 0.93, "x": 6.49, "y": 13.28}, {"name": "bone61", "parent": "bone60", "length": 32.65, "rotation": 90.48, "x": -0.15, "y": 0.8}, {"name": "ljio1", "parent": "bone61", "rotation": -91.4, "x": 72.07, "y": -13.65, "color": "ff3f00ff"}, {"name": "ljio2", "parent": "bone61", "rotation": -91.4, "x": 17.12, "y": -8.6, "color": "ff3f00ff"}, {"name": "ljio3", "parent": "ljio2", "x": 17.97, "y": -14.27, "transform": "noScale", "color": "ff3f00ff"}], "slots": [{"name": "ft2", "bone": "bone5", "attachment": "ft2"}, {"name": "m3", "bone": "bone27", "attachment": "m3"}, {"name": "s2", "bone": "root", "attachment": "s2"}, {"name": "pd5", "bone": "root", "attachment": "pd5"}, {"name": "j2", "bone": "root", "attachment": "j2"}, {"name": "j1", "bone": "root", "attachment": "j1"}, {"name": "yifu", "bone": "root", "attachment": "yifu"}, {"name": "pd1", "bone": "root", "attachment": "pd1"}, {"name": "pd4", "bone": "root", "attachment": "pd4"}, {"name": "pd3", "bone": "root", "attachment": "pd3"}, {"name": "ya<PERSON>i", "bone": "bone2", "attachment": "ya<PERSON>i"}, {"name": "tou", "bone": "bone5", "attachment": "tou"}, {"name": "ts1", "bone": "bone5", "attachment": "ts1"}, {"name": "wuqi3", "bone": "bone62", "attachment": "wuqi3"}, {"name": "s1", "bone": "root", "attachment": "s1"}, {"name": "biyan", "bone": "bone5"}, {"name": "m2", "bone": "bone23", "attachment": "m2"}, {"name": "m1", "bone": "root", "attachment": "m1"}, {"name": "faji", "bone": "bone5", "attachment": "faji"}, {"name": "mutou2", "bone": "f<PERSON><PERSON>an"}, {"name": "xuanz", "bone": "f<PERSON><PERSON>an", "color": "ffffff6f", "blend": "additive"}], "ik": [{"name": "ljio1", "order": 7, "bones": ["bone56"], "target": "ljio1", "compress": true, "stretch": true}, {"name": "ljio2", "order": 8, "bones": ["bone57"], "target": "ljio2", "compress": true, "stretch": true}, {"name": "ljio3", "order": 9, "bones": ["bone58"], "target": "ljio3"}, {"name": "ljio4", "order": 5, "bones": ["bone8", "bone9"], "target": "ljio2", "bendPositive": false}, {"name": "rjio1", "order": 2, "bones": ["bone52"], "target": "rjio1", "compress": true, "stretch": true}, {"name": "rjio2", "order": 3, "bones": ["bone53"], "target": "rjio2", "compress": true, "stretch": true}, {"name": "rjio3", "order": 4, "bones": ["bone54"], "target": "rjio3"}, {"name": "rjio4", "bones": ["bone63", "bone64"], "target": "rjio2", "bendPositive": false}], "transform": [{"name": "ljio5", "order": 6, "bones": ["ljio1"], "target": "bone9", "rotation": 116.32, "x": 11.46, "y": -21.09, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "rjio5", "order": 1, "bones": ["rjio1"], "target": "bone64", "rotation": 114.12, "x": 10.53, "y": -20.1, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"pd4": {"pd4": {"type": "mesh", "uvs": [0.13232, 0, 0.27991, 0.01267, 0.37127, 0.12374, 0.50479, 0.2595, 0.65238, 0.43639, 0.84213, 0.60094, 1, 0.74904, 0.90538, 0.84777, 0.7086, 0.94239, 0.40641, 1, 0.34316, 0.83132, 0.26585, 0.65031, 0.2026, 0.47959, 0.09016, 0.29652, 0.01285, 0.1402, 0, 0.03941, 0.18152, 0.13197, 0.27288, 0.28418, 0.44857, 0.45696, 0.60318, 0.61946, 0.68752, 0.81898], "triangles": [9, 10, 8, 10, 20, 8, 8, 20, 7, 7, 20, 6, 10, 11, 20, 11, 19, 20, 6, 19, 5, 6, 20, 19, 11, 12, 19, 5, 19, 4, 12, 18, 19, 19, 18, 4, 18, 12, 13, 18, 17, 4, 18, 13, 17, 17, 3, 4, 17, 14, 16, 17, 13, 14, 3, 16, 2, 3, 17, 16, 14, 15, 16, 1, 16, 0, 16, 1, 2, 0, 16, 15], "vertices": [1, 23, -10.45, 1.66, 1, 1, 23, -8.62, 6.49, 1, 1, 23, 0.41, 6.96, 1, 2, 23, 11.13, 8.01, 0.68772, 24, -3.32, 9.18, 0.31228, 1, 24, 6.97, 8.11, 1, 2, 24, 15.41, 11.9, 0.01015, 25, 0.88, 9.5, 0.98985, 1, 25, 7.72, 11.7, 1, 1, 25, 12.19, 6.12, 1, 1, 25, 16.42, -2.96, 1, 1, 25, 18.9, -14.91, 1, 1, 25, 11.15, -13.29, 1, 2, 24, 20.59, -14.04, 0.23261, 25, 1.93, -11.49, 0.76739, 3, 23, 28.48, -6.37, 0.02718, 24, 6.68, -8.91, 0.97076, 25, -7.24, -7.19, 0.00206, 1, 23, 12.49, -7.25, 1, 1, 23, 0.01, -6.09, 1, 1, 23, -7.86, -4.04, 1, 1, 23, 0.16, 0.06, 1, 1, 23, 12.39, -0.5, 1, 2, 23, 26.42, 2.84, 0.0015, 24, 6.91, 0.38, 0.9985, 2, 24, 17.95, 0.64, 0.05531, 25, 1.55, 0.68, 0.94469, 1, 25, 10.76, -0.89, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 36, "height": 123}}, "pd5": {"pd5": {"type": "mesh", "uvs": [0, 0.99698, 0.00903, 0.82241, 0.07172, 0.6023, 0.15232, 0.42773, 0.24636, 0.20762, 0.36502, 0.04823, 0.51279, 0, 0.69415, 0, 0.79938, 0.1216, 0.85311, 0.33412, 0.91132, 0.59218, 0.95162, 0.82241, 0.98297, 1, 0.80162, 0.95397, 0.61355, 0.92361, 0.40085, 0.92108, 0.18367, 0.96156, 0.51279, 0.21015, 0.50832, 0.43785, 0.50608, 0.69338, 0.73445, 0.67314, 0.68295, 0.38472, 0.65385, 0.12666, 0.22621, 0.74904, 0.30905, 0.44291, 0.39413, 0.18485], "triangles": [13, 14, 20, 11, 13, 20, 13, 11, 12, 20, 21, 10, 20, 10, 11, 14, 19, 20, 22, 6, 7, 22, 7, 8, 22, 17, 6, 21, 22, 8, 21, 8, 9, 17, 22, 21, 18, 17, 21, 21, 9, 10, 18, 21, 20, 1, 2, 23, 16, 1, 23, 16, 23, 15, 0, 1, 16, 24, 25, 18, 3, 4, 24, 19, 24, 18, 19, 18, 20, 24, 2, 3, 23, 24, 19, 23, 2, 24, 15, 23, 19, 15, 19, 14, 25, 5, 6, 17, 25, 6, 4, 5, 25, 18, 25, 17, 24, 4, 25], "vertices": [3, 28, 11.31, -5.59, 0.99995, 24, -2.83, -103.74, 3e-05, 25, -28.5, -98, 2e-05, 4, 27, 22.78, -13.62, 0.086, 28, 6.83, -9.05, 0.91399, 24, -10.02, -96.62, 1e-05, 25, -32.66, -89.57, 0, 3, 26, 33.03, -18.05, 0.00021, 27, 12.42, -13.21, 0.77268, 28, 0.07, -10.87, 0.22711, 3, 26, 20.32, -13.93, 0.16077, 27, 3.72, -10.76, 0.83847, 28, -5.97, -10.77, 0.00076, 2, 26, 4.42, -9.39, 0.96706, 27, -7.12, -8.2, 0.03294, 2, 26, -7.9, -1.66, 0.93659, 23, -14.17, -41.59, 0.06341, 2, 26, -13.43, 10.51, 0.61556, 23, -15.04, -24.26, 0.38444, 2, 26, -16.35, 26.45, 0.20623, 23, -12.37, -4.19, 0.79377, 2, 26, -10.1, 37.76, 0.00783, 23, -3.17, 5, 0.99217, 2, 23, 11, 6.65, 0.98741, 24, -3.7, 7.94, 0.01259, 1, 24, 8.54, 6.7, 1, 3, 27, 6.06, 54.25, 0.0011, 28, -15.7, 39.72, 0, 25, 3.18, 5.62, 0.9989, 2, 24, 31.65, 9.01, 0, 25, 9.84, 5.8, 1, 4, 27, 20.95, 49.96, 0.09106, 28, -5.33, 39.48, 0.00343, 24, 28.86, -15.97, 0.10898, 25, 6.62, -13.84, 0.79653, 4, 27, 22.3, 34.36, 0.36662, 28, -1.76, 27.76, 0.04688, 24, 20.23, -36.95, 0.27692, 25, -1.3, -33.85, 0.30957, 4, 27, 22.96, 16.82, 0.45248, 28, 1.69, 14.4, 0.36542, 24, 9.56, -59.71, 0.11263, 25, -12.56, -56.2, 0.06947, 4, 27, 26.05, 0.88, 0.00313, 28, 6.44, 2.77, 0.99401, 24, 1.64, -83.5, 0.00178, 25, -22.2, -79.06, 0.00108, 4, 26, 0.33, 14.07, 0.61835, 27, -11.14, 10.16, 0.03498, 23, -1.77, -28.52, 0.34141, 24, -19.97, -22.86, 0.00526, 5, 26, 15.98, 17.75, 0.22451, 27, -0.9, 14.1, 0.4389, 23, 13.19, -33.79, 0.21624, 24, -9.86, -31.67, 0.11556, 25, -22.76, -26, 0.00479, 6, 26, 35.25, 23.36, 0.00151, 27, 11.64, 19.8, 0.61959, 28, -6.14, 14.36, 0.02948, 23, 31.97, -39.05, 0.0323, 24, 3.11, -41.11, 0.23791, 25, -14.39, -37.04, 0.07922, 6, 26, 29.21, 43.63, 0.00084, 27, 6.51, 35.49, 0.16839, 28, -12.17, 25.38, 0.00204, 23, 33.31, -12.11, 0.01601, 24, 9.2, -15.48, 0.67413, 25, -6.16, -13.54, 0.13859, 5, 26, 9.23, 32.02, 0.08284, 27, -6.18, 24.98, 0.1016, 23, 11.94, -13.32, 0.69993, 24, -6.84, -11.6, 0.11555, 25, -17.57, -6.97, 9e-05, 3, 26, -7.43, 25.06, 0.22948, 27, -16.9, 18.37, 0.0043, 23, -4.99, -11.2, 0.76622, 4, 27, 16.28, 0, 0.18276, 28, 0.28, 0.09, 0.81712, 24, -6.15, -71.57, 8e-05, 25, -26.07, -65.82, 4e-05, 4, 27, 1.94, 0.28, 0.99871, 23, 9.88, -55.75, 0.00064, 24, -16.59, -52.16, 0.0006, 25, -30.75, -44.7, 4e-05, 4, 26, 0.56, 3.21, 0.95495, 27, -10.39, 1.55, 0.00102, 23, -5.13, -41.12, 0.04379, 24, -24.9, -34.21, 0.00023], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 12, 34, 34, 36, 36, 38, 26, 40, 40, 42, 42, 44, 32, 46, 46, 48, 48, 50], "width": 113, "height": 100}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [0.17955, 0, 0.30587, 0.02019, 0.48678, 0.05973, 0.63181, 0.10422, 0.74566, 0.06468, 0.88913, 0.02266, 0.91408, 0.17343, 0.92344, 0.35881, 0.95775, 0.57384, 0.97821, 0.73784, 1, 0.91247, 0.92188, 0.93224, 0.83923, 0.76169, 0.77373, 0.90752, 0.64429, 1, 0.49457, 0.8482, 0.37605, 0.68013, 0.25129, 0.83337, 0.0969, 0.90752, 0, 0.80866, 0, 0.72462, 0.05791, 0.63316, 0.11093, 0.45026, 0.13588, 0.29949, 0.16396, 0.14377, 0.28248, 0.35634, 0.43219, 0.38353, 0.61622, 0.43296, 0.80336, 0.386, 0.82675, 0.60104, 0.61934, 0.65047, 0.39476, 0.54666, 0.25597, 0.48734, 0.17175, 0.65294, 0.10625, 0.779, 0.90473, 0.73945, 0.77217, 0.21298, 0.6131, 0.24017, 0.44467, 0.21545, 0.29807, 0.18332], "triangles": [33, 22, 32, 21, 22, 33, 33, 32, 16, 34, 21, 33, 20, 21, 34, 19, 20, 34, 17, 33, 16, 34, 33, 17, 18, 19, 34, 18, 34, 17, 29, 7, 8, 29, 30, 27, 35, 29, 8, 35, 8, 9, 12, 29, 35, 30, 29, 12, 13, 30, 12, 14, 15, 30, 11, 35, 9, 11, 9, 10, 12, 35, 11, 13, 14, 30, 15, 16, 30, 1, 24, 0, 38, 39, 1, 39, 24, 1, 2, 38, 1, 39, 23, 24, 25, 23, 39, 26, 39, 38, 25, 39, 26, 32, 23, 25, 22, 23, 32, 32, 25, 31, 37, 2, 3, 38, 2, 37, 26, 38, 37, 31, 25, 26, 31, 26, 27, 16, 32, 31, 16, 31, 30, 36, 4, 5, 36, 5, 6, 36, 37, 3, 36, 3, 4, 28, 36, 6, 7, 28, 6, 27, 37, 36, 27, 36, 28, 27, 26, 37, 29, 28, 7, 29, 27, 28, 30, 31, 27], "vertices": [1, 3, 15.44, 33.17, 1, 2, 2, -22.03, 14.81, 0.01164, 3, 12.88, 22.82, 0.98836, 2, 2, -6.86, 12.53, 0.09273, 3, 8.65, 8.07, 0.90727, 3, 2, 5.29, 10.02, 0.13725, 3, 4.59, -3.66, 0.86275, 23, -19.06, -12.63, 0, 3, 2, 14.88, 12, 0.78305, 3, 5.32, -13.42, 0.21695, 23, -19.14, -2.84, 1e-05, 3, 2, 26.96, 14.08, 0.974, 3, 5.82, -25.67, 0.02595, 23, -18.98, 9.41, 5e-05, 3, 2, 28.95, 6.06, 0.99337, 3, -2.39, -26.61, 0.00656, 23, -13.67, 9.85, 7e-05, 3, 2, 29.62, -3.77, 0.82262, 3, -12.23, -26, 0, 23, -7.38, 8.64, 0.17738, 3, 2, 32.36, -15.21, 0.07381, 3, -23.92, -27.24, 0, 23, 0.18, 9.16, 0.92619, 2, 3, -32.76, -27.71, 0, 23, 5.88, 9.09, 1, 2, 2, 35.69, -33.2, 0, 23, 11.94, 9.01, 1, 2, 2, 29.12, -34.16, 0, 23, 11.75, 2.37, 1, 2, 2, 22.28, -25.04, 0.00746, 23, 5.16, -2.6, 0.99254, 2, 2, 16.69, -32.7, 0.05897, 23, 9.31, -9.55, 0.94103, 2, 2, 5.76, -37.47, 0.13294, 23, 10.99, -21.19, 0.86706, 2, 2, -6.72, -29.27, 0.33838, 23, 4.28, -31.88, 0.66162, 2, 2, -16.57, -20.24, 0.54771, 23, -2.62, -39.83, 0.45229, 1, 26, 9.39, -1.36, 1, 1, 26, 13.81, -10.79, 1, 1, 26, 11.54, -18.01, 1, 1, 26, 8.63, -18.76, 1, 1, 26, 4.77, -15.8, 1, 1, 3, -7.37, 42.25, 1, 1, 3, 0.24, 39.04, 1, 1, 3, 8.08, 35.54, 1, 1, 3, -4.48, 27.28, 1, 3, 2, -11.66, -4.58, 0.90242, 3, -7.69, 15.03, 0.0685, 23, -11.91, -32.04, 0.02908, 2, 2, 3.77, -7.39, 0.87309, 23, -8.25, -17.43, 0.12691, 3, 2, 19.52, -5.09, 0.81, 3, -12.23, -15.81, 0, 23, -7.78, -1.53, 0.19, 3, 2, 21.34, -16.51, 0.05832, 3, -23.79, -16.14, 0, 23, -0.35, -1.91, 0.94168, 2, 2, 3.89, -18.92, 0.46333, 23, -0.96, -19.5, 0.53667, 2, 2, -14.91, -13.19, 0.77567, 23, -6.87, -36.86, 0.22433, 1, 26, -2.65, -4.17, 1, 1, 26, 4.09, -8.18, 1, 1, 26, 9.24, -11.33, 1, 2, 3, -31.98, -21.59, 0, 23, 5.13, 3.03, 1, 3, 2, 17.01, 4.11, 0.94472, 3, -2.78, -14.51, 0.05527, 23, -13.9, -2.24, 0, 2, 2, 3.63, 2.83, 0.51447, 3, -2.32, -1.08, 0.48553, 2, 2, -10.5, 4.32, 0.42505, 3, 0.98, 12.74, 0.57495, 2, 2, -22.79, 6.17, 0.02817, 3, 4.41, 24.69, 0.97183], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 46, 50, 50, 52, 52, 54, 54, 56, 16, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 22, 70, 12, 72, 72, 74, 74, 76, 76, 78], "width": 84, "height": 53}}, "wuqi3": {"wuqi3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [91.85, -98.77, -38.51, 0.74, 33.7, 95.33, 164.06, -4.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 164, "height": 119}}, "j1": {"j1": {"type": "mesh", "uvs": [0.19373, 0.17163, 0.24016, 0.09864, 0.28638, 0.03228, 0.56873, 0, 0.73638, 0, 0.88197, 0.01694, 1, 0.08598, 1, 0.19081, 1, 0.31226, 1, 0.44265, 1, 0.55771, 0.91726, 0.6114, 0.73638, 0.62802, 0.66579, 0.69322, 0.6305, 0.77504, 0.62609, 0.84535, 0.77609, 0.8722, 0.84667, 0.9195, 0.8202, 0.97319, 0.67462, 1, 0.41432, 1, 0.25109, 0.9553, 0.12315, 0.90288, 0.13197, 0.83001, 0.17609, 0.7597, 0.16285, 0.68427, 0.12756, 0.61524, 0, 0.56538, 0, 0.45416, 0.07462, 0.36467, 0.15403, 0.25856, 0.65611, 0.0561, 0.62516, 0.12241, 0.57439, 0.22058, 0.54283, 0.29584, 0.51464, 0.38647, 0.4689, 0.47571, 0.42661, 0.57904, 0.41027, 0.62498, 0.40552, 0.69528, 0.38719, 0.77062, 0.37396, 0.83575, 0.44005, 0.90262, 0.61424, 0.93685], "triangles": [20, 43, 19, 19, 43, 18, 20, 42, 43, 20, 21, 42, 18, 43, 17, 21, 22, 42, 43, 16, 17, 43, 15, 16, 43, 42, 15, 22, 41, 42, 22, 23, 41, 42, 41, 15, 15, 41, 14, 41, 23, 40, 27, 28, 36, 36, 9, 10, 28, 29, 36, 36, 35, 9, 36, 29, 35, 35, 8, 9, 35, 34, 8, 35, 29, 34, 29, 30, 34, 34, 33, 8, 33, 7, 8, 34, 30, 33, 30, 0, 33, 33, 32, 7, 33, 0, 32, 7, 32, 6, 6, 32, 31, 31, 5, 6, 0, 1, 32, 31, 4, 5, 32, 1, 31, 31, 1, 2, 31, 2, 3, 31, 3, 4, 41, 40, 14, 40, 23, 24, 40, 39, 14, 14, 39, 13, 40, 24, 39, 24, 25, 39, 39, 38, 13, 39, 25, 38, 13, 38, 12, 25, 26, 38, 10, 11, 12, 12, 38, 37, 38, 26, 37, 26, 27, 37, 10, 12, 37, 37, 36, 10, 37, 27, 36], "vertices": [1, 29, 16.9, -21.42, 1, 1, 29, 3.92, -19.93, 1, 1, 29, -7.9, -18.36, 1, 1, 29, -14.54, -4.38, 1, 1, 29, -15.11, 4.15, 1, 1, 29, -12.64, 11.76, 1, 1, 29, -0.92, 18.59, 1, 1, 29, 17.5, 19.83, 1, 1, 29, 38.83, 21.27, 1, 2, 30, -12.14, 22.69, 0.15395, 29, 61.74, 22.81, 0.84605, 2, 30, 7.83, 24.26, 0.78281, 29, 81.95, 24.18, 0.21719, 2, 30, 17.47, 20.78, 0.93849, 29, 91.67, 20.6, 0.06151, 2, 30, 21.06, 11.81, 0.99508, 29, 95.21, 11.6, 0.00492, 1, 30, 32.65, 9.12, 1, 1, 30, 46.99, 8.44, 1, 2, 30, 59.21, 9.17, 0.32816, 31, 7.2, 6.63, 0.67184, 2, 30, 63.29, 17.17, 0.01459, 31, 14.89, 11.25, 0.98541, 1, 31, 23.86, 10.63, 1, 1, 31, 31.52, 5.11, 1, 1, 31, 32.22, -3.63, 1, 1, 31, 26.06, -15.39, 1, 1, 31, 15.3, -19.16, 1, 1, 31, 4.18, -20.72, 1, 2, 30, 58.48, -16.16, 0.17753, 31, -6.86, -14.46, 0.82247, 2, 30, 46.11, -14.88, 0.80073, 31, -16.67, -6.81, 0.19927, 3, 30, 33.07, -16.58, 0.99456, 29, 107.06, -16.92, 0.00038, 31, -28.63, -1.34, 0.00506, 2, 30, 21.22, -19.32, 0.94701, 29, 95.06, -19.54, 0.05299, 2, 30, 13.07, -26.48, 0.7829, 29, 86.73, -26.62, 0.2171, 2, 30, -6.24, -28, 0.28266, 29, 67.2, -27.93, 0.71734, 2, 30, -22.06, -25.43, 0.03005, 29, 51.22, -25.2, 0.96995, 1, 29, 32.31, -22.41, 1, 1, 29, -4.98, 0.73, 1, 1, 29, 6.77, -0.06, 1, 1, 29, 24.19, -1.48, 1, 1, 29, 37.52, -2.19, 1, 1, 29, 53.54, -2.55, 1, 2, 30, -4.33, -3.86, 0.07585, 29, 69.37, -3.82, 0.92415, 2, 30, 13.77, -4.6, 0.99116, 29, 87.67, -4.75, 0.00884, 2, 30, 21.81, -4.81, 0.99921, 29, 95.8, -5.03, 0.00079, 2, 30, 34.03, -4.09, 0.99933, 31, -21.19, 8.74, 0.00067, 2, 30, 47.18, -3.99, 0.93584, 31, -9.99, 1.85, 0.06416, 1, 31, -0.25, -3.99, 1, 1, 31, 11.64, -6.39, 1, 1, 31, 21.04, -1.27, 1], "hull": 31, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 2, 2, 4, 0, 60, 8, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 51, "height": 176}}, "j2": {"j2": {"type": "mesh", "uvs": [0.27304, 0, 0.53953, 0.01329, 0.6546, 0.12477, 0.71517, 0.292, 0.79996, 0.44577, 0.84841, 0.57071, 0.70911, 0.61876, 0.64855, 0.66682, 0.58798, 0.77254, 0.58192, 0.86864, 0.70305, 0.89748, 0.8787, 0.88402, 1, 0.87249, 1, 0.93976, 0.8787, 1, 0.58798, 0.99166, 0.42445, 0.98013, 0.1943, 0.98013, 0.07317, 0.9513, 0.11556, 0.88787, 0.11556, 0.77446, 0.06711, 0.64567, 0, 0.56302, 0, 0.40733, 0.01866, 0.26701, 0.01866, 0.14591, 0.06711, 0.02482, 0.30938, 0.14784, 0.32755, 0.27662, 0.36389, 0.42655, 0.36389, 0.55726, 0.3336, 0.65913, 0.32149, 0.78407, 0.33966, 0.87633, 0.36389, 0.92054, 0.58192, 0.94553, 0.80602, 0.95514, 0.90898, 0.9167], "triangles": [22, 29, 30, 22, 23, 29, 30, 29, 4, 29, 3, 4, 23, 28, 29, 29, 28, 3, 23, 24, 28, 28, 2, 3, 24, 27, 28, 28, 27, 2, 24, 25, 27, 25, 26, 27, 26, 0, 27, 27, 1, 2, 27, 0, 1, 15, 36, 14, 13, 36, 37, 13, 14, 36, 16, 35, 15, 15, 35, 36, 35, 10, 36, 37, 10, 11, 37, 36, 10, 35, 9, 10, 37, 12, 13, 37, 11, 12, 17, 34, 16, 16, 34, 35, 17, 18, 34, 34, 19, 33, 19, 34, 18, 34, 9, 35, 34, 33, 9, 19, 32, 33, 19, 20, 32, 33, 32, 9, 9, 32, 8, 8, 32, 31, 32, 20, 31, 20, 21, 31, 8, 31, 7, 7, 31, 6, 31, 30, 6, 31, 21, 30, 21, 22, 30, 6, 30, 5, 30, 4, 5], "vertices": [1, 32, -14.15, -0.7, 1, 1, 32, -10.71, 13.17, 1, 1, 32, 8.31, 17.61, 1, 1, 32, 36.32, 18.35, 1, 2, 33, -17.03, 18.16, 0.07303, 32, 62.21, 20.56, 0.92697, 2, 33, 3.72, 22.12, 0.69374, 32, 83.16, 21.28, 0.30626, 2, 33, 12.27, 15.29, 0.93725, 32, 90.48, 13.22, 0.06275, 2, 33, 20.54, 12.63, 0.99982, 32, 98.16, 9.32, 0.00018, 2, 33, 38.46, 10.61, 0.98023, 34, -0.95, 20.02, 0.01977, 2, 33, 54.58, 11.37, 0.20281, 34, 8.84, 7.2, 0.79719, 2, 33, 58.97, 18.1, 0.0029, 34, 16.87, 7.42, 0.9971, 1, 34, 22.74, 15, 1, 1, 34, 26.56, 20.53, 1, 1, 34, 33.59, 11.69, 1, 1, 34, 34.87, -0.23, 1, 1, 34, 21.96, -8.75, 1, 1, 34, 13.98, -12.65, 1, 2, 33, 74.66, -7.88, 0.07046, 34, 4.45, -20.26, 0.92954, 2, 33, 70.27, -14.61, 0.15364, 34, -3.58, -20.48, 0.84636, 2, 33, 59.49, -13.07, 0.51016, 34, -8.46, -10.75, 0.48984, 1, 33, 40.5, -14.35, 1, 2, 33, 19.11, -18.35, 0.94247, 32, 91.95, -21.07, 0.05753, 2, 33, 5.51, -22.82, 0.58814, 32, 77.93, -23.4, 0.41186, 2, 33, -20.57, -24.57, 0.01107, 32, 52.12, -21.11, 0.98893, 1, 32, 28.94, -18.06, 1, 1, 32, 8.86, -16.28, 1, 1, 32, -11, -11.94, 1, 1, 32, 10.53, -0.96, 1, 1, 32, 31.97, -1.89, 1, 1, 32, 57, -2.18, 1, 2, 33, 3.22, -3.65, 0.82409, 32, 78.67, -4.1, 0.17591, 2, 33, 20.39, -4.11, 0.99823, 32, 95.42, -7.2, 0.00177, 1, 33, 41.36, -3.35, 1, 2, 33, 56.75, -1.35, 0.4043, 34, -0.38, -1.82, 0.5957, 2, 33, 64.06, 0.42, 0.0225, 34, 5.24, -6.83, 0.9775, 1, 34, 16.88, -2.9, 1, 1, 34, 27.17, 3.26, 1, 1, 34, 27.41, 11.71, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74], "width": 53, "height": 167}}, "xuanz": {"xuanz": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-104.92, 12.46, -21.22, -104.34, 105.49, -13.54, 21.79, 103.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 256}}, "ts1": {"ts1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [36.72, -32.57, 38.07, 5.41, 53.06, 4.87, 51.71, -33.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 15}}, "mutou2": {"mutou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.4, -51.34, -68.62, 12.5, -48.9, 51.12, 76.12, -12.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 164, "height": 119}}, "tou": {"tou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-1.54, -39.21, 1.63, 49.73, 95.57, 46.38, 92.4, -42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 89, "height": 94}}, "m1": {"m1": {"type": "mesh", "uvs": [0.57152, 0.40324, 0.61591, 0.38703, 0.6455, 0.36594, 0.64961, 0.46973, 0.64057, 0.57351, 0.61673, 0.76648, 0.65207, 0.86865, 0.71419, 0.99777, 0.80514, 0.96534, 0.8709, 0.9329, 0.92898, 0.80101, 0.97172, 0.67345, 1, 0.58912, 0.98049, 0.35777, 0.95309, 0.15669, 0.86542, 0.13939, 0.78103, 0.09615, 0.69008, 0.08101, 0.62323, 0.18696, 0.57062, 0.31669, 0.4983, 0.16966, 0.41282, 0.03561, 0.30542, 0.09399, 0.19364, 0.0875, 0.07747, 0.00318, 0.07365, 0.22015, 0.01563, 0.39674, 0, 0.6322, 0.01232, 0.73685, 0.08856, 0.90364, 0.19299, 0.94288, 0.31896, 0.94615, 0.36869, 0.82188, 0.43665, 0.71396, 0.40847, 0.57334, 0.4151, 0.38693, 0.45985, 0.36077, 0.57187, 0.36589, 0.6368, 0.29329, 0.76233, 0.22283, 0.82185, 0.36375, 0.82402, 0.55805, 0.78518, 0.72236, 0.74179, 0.8829, 0.48547, 0.28087, 0.4041, 0.21398, 0.29019, 0.20328, 0.20475, 0.28087, 0.19254, 0.54041, 0.19661, 0.7839], "triangles": [32, 31, 49, 31, 30, 49, 30, 29, 49, 29, 28, 49, 33, 32, 34, 28, 48, 49, 34, 32, 48, 35, 34, 48, 48, 32, 49, 28, 27, 48, 27, 26, 48, 26, 25, 48, 35, 48, 47, 35, 47, 46, 48, 25, 47, 25, 23, 47, 47, 23, 46, 25, 24, 23, 46, 22, 45, 46, 23, 22, 45, 35, 46, 37, 36, 44, 35, 45, 36, 36, 45, 44, 44, 20, 19, 44, 45, 20, 45, 21, 20, 45, 22, 21, 7, 43, 8, 7, 6, 43, 9, 8, 42, 8, 43, 42, 9, 42, 10, 43, 6, 42, 6, 5, 42, 42, 41, 10, 10, 41, 11, 5, 4, 42, 42, 4, 41, 11, 41, 12, 12, 41, 13, 4, 3, 41, 3, 40, 41, 15, 13, 40, 15, 14, 13, 13, 41, 40, 40, 2, 39, 40, 3, 2, 40, 39, 15, 15, 39, 16, 39, 17, 16, 2, 1, 38, 1, 37, 38, 2, 38, 39, 19, 18, 38, 38, 17, 39, 38, 18, 17, 0, 36, 37, 0, 37, 1, 44, 19, 37, 37, 19, 38], "vertices": [3, 7, -2.15, -0.27, 0.94619, 11, -5.13, -2.32, 0.04399, 8, -5.08, 3.02, 0.00982, 4, 7, -1.67, -3.53, 0.44328, 11, -1.89, -2.93, 0.54055, 12, 2.74, -12.29, 0.00872, 13, -1.89, -15.81, 0.00745, 4, 7, -0.97, -5.72, 0.05684, 11, 0.41, -2.99, 0.80776, 12, 2.83, -9.99, 0.07719, 13, -3.08, -13.85, 0.05822, 3, 11, -0.71, -6.68, 0.46111, 12, 6.5, -11.16, 0.21288, 13, 0.63, -12.8, 0.32601, 3, 11, -2.71, -10.02, 0.24037, 12, 9.81, -13.22, 0.14304, 13, 4.53, -12.69, 0.61659, 3, 11, -6.92, -16.04, 0.08332, 12, 15.77, -17.52, 0.02285, 13, 11.87, -12.99, 0.89383, 3, 11, -5.89, -20.5, 0.0426, 12, 20.25, -16.55, 0.00411, 13, 15.07, -9.72, 0.95329, 2, 11, -3.39, -26.59, 0.00596, 13, 18.86, -4.33, 0.99404, 1, 13, 16.38, 1.94, 1, 1, 13, 14.25, 6.41, 1, 2, 12, 25.54, 3.11, 0.03157, 13, 8.64, 9.61, 0.96843, 2, 12, 22.35, 7.78, 0.2241, 13, 3.39, 11.74, 0.7759, 2, 12, 20.23, 10.87, 0.38066, 13, -0.07, 13.15, 0.61934, 2, 12, 11.77, 12.78, 0.83421, 13, -8.18, 10.07, 0.16579, 2, 12, 4.12, 13.73, 0.99484, 13, -15.08, 6.64, 0.00516, 1, 12, 1.12, 8.04, 1, 2, 11, 13.24, 2.73, 0.41152, 12, -2.68, 2.93, 0.58848, 1, 11, 7.26, 5.66, 1, 1, 11, 1.29, 3.77, 1, 3, 7, 1.05, -0.32, 0.84542, 11, -4.03, 0.69, 0.1121, 8, -4.13, -0.04, 0.04248, 1, 8, 2.44, -3.82, 1, 2, 8, 9.8, -6.87, 0.95514, 9, -6.37, -2.97, 0.04486, 2, 8, 16.75, -2.64, 0.0737, 9, 1.19, -5.96, 0.9263, 2, 9, 7.57, -11.06, 0.96188, 10, -12.97, -1.62, 0.03812, 2, 9, 12.46, -18.65, 0.89397, 10, -15.74, -10.22, 0.10603, 2, 9, 17.52, -12.41, 0.65364, 10, -7.71, -10.18, 0.34636, 2, 9, 24.83, -9.74, 0.17167, 10, -1.01, -14.14, 0.82833, 2, 9, 30.98, -3.47, 0.00465, 10, 7.75, -14.93, 0.99535, 1, 10, 11.58, -13.87, 1, 1, 10, 17.52, -8.06, 1, 1, 10, 18.66, -0.39, 1, 3, 8, 7.12, 27.41, 0.00086, 9, 19.37, 19.82, 0.03433, 10, 18.41, 8.81, 0.96481, 3, 8, 4.9, 21.99, 0.01326, 9, 13.71, 18.33, 0.12601, 10, 13.67, 12.25, 0.86073, 3, 8, 1.23, 16.78, 0.03912, 9, 7.34, 18.13, 0.24273, 10, 9.48, 17.04, 0.71815, 3, 8, 4.64, 12.35, 0.102, 9, 5.86, 12.73, 0.37054, 10, 4.36, 14.78, 0.52745, 3, 8, 6.07, 5.58, 0.58228, 9, 1.32, 7.52, 0.33043, 10, -2.55, 14.98, 0.08729, 4, 7, -0.29, 7.82, 0.00793, 8, 3.19, 3.75, 0.93762, 9, -1.87, 8.71, 0.04541, 10, -3.65, 18.2, 0.00904, 2, 7, -0.77, -0.35, 0.95878, 11, -4.6, -1.04, 0.04122, 3, 11, 0.79, -0.25, 0.99481, 12, 0.11, -9.57, 0.00316, 13, -5.59, -15, 0.00204, 3, 11, 10.27, -1.14, 0.00957, 12, 1.14, -0.1, 0.99041, 13, -9.94, -6.53, 2e-05, 1, 12, 7.61, 1.96, 1, 1, 13, 1.33, 0.33, 1, 3, 11, 5.13, -18.97, 0.00606, 12, 18.89, -5.51, 0.00243, 13, 7.85, -1.26, 0.99151, 2, 11, 0.02, -23.36, 0.00748, 13, 14.3, -3.19, 0.99252, 4, 7, 2.6, 5.84, 0.00311, 8, 2.21, 0.4, 0.99617, 9, -5.14, 7.47, 0.00061, 10, -6.68, 19.95, 0.00012, 1, 8, 8.6, -0.35, 1, 1, 9, 4.52, -3.4, 1, 2, 9, 11.23, -4.86, 0.94656, 10, -5.85, -0.52, 0.05344, 1, 10, 3.78, -1.02, 1, 1, 10, 12.77, -0.36, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 24, 82, 84, 84, 86, 74, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98], "width": 73, "height": 37}}, "s1": {"s1": {"type": "mesh", "uvs": [0.88885, 0.26784, 0.93445, 0.25555, 0.9202, 0.21755, 1, 0.1639, 1, 0.08678, 0.91165, 0.02643, 0.7207, 0, 0.4813, 0.00184, 0.55255, 0.05996, 0.44425, 0.07896, 0.3103, 0.08007, 0.2476, 0.10131, 0.2419, 0.14937, 0.33025, 0.1896, 0.4642, 0.21978, 0.43285, 0.26113, 0.3331, 0.29019, 0.21055, 0.33043, 0.2818, 0.36843, 0.34165, 0.3919, 0.25628, 0.46345, 0.13887, 0.58524, 0.09342, 0.69961, 0.08205, 0.80655, 0, 0.90309, 0, 0.95805, 0, 1, 0.10478, 0.98775, 0.29415, 0.92537, 0.44565, 0.81992, 0.50625, 0.70703, 0.53276, 0.66545, 0.68048, 0.67733, 0.85091, 0.66099, 0.85849, 0.60455, 0.77138, 0.55851, 0.77423, 0.51995, 0.80333, 0.4705, 0.87365, 0.43246, 0.92943, 0.41915, 0.89063, 0.35543, 0.86396, 0.3212, 0.90761, 0.29552, 0.76696, 0.07965, 0.67481, 0.15002, 0.64571, 0.23846, 0.56568, 0.3269, 0.55598, 0.42295, 0.55964, 0.38669, 0.57296, 0.50188, 0.57781, 0.55989, 0.64813, 0.616, 0.40806, 0.48096, 0.37168, 0.58747, 0.31591, 0.70159, 0.24073, 0.81286, 0.14616, 0.91841], "triangles": [23, 22, 55, 29, 55, 54, 56, 23, 55, 24, 23, 56, 28, 55, 29, 56, 55, 28, 25, 24, 56, 56, 26, 25, 27, 56, 28, 27, 26, 56, 54, 53, 31, 22, 21, 54, 30, 54, 31, 55, 22, 54, 30, 29, 54, 20, 19, 52, 53, 20, 52, 50, 53, 52, 21, 20, 53, 54, 21, 53, 35, 50, 36, 51, 50, 35, 51, 35, 34, 33, 51, 34, 51, 31, 53, 51, 53, 50, 32, 51, 33, 31, 51, 32, 48, 18, 17, 17, 16, 48, 46, 48, 16, 47, 19, 18, 40, 48, 46, 39, 48, 40, 48, 47, 18, 38, 47, 48, 39, 38, 48, 37, 47, 38, 52, 19, 47, 49, 47, 37, 52, 47, 49, 36, 49, 37, 36, 50, 49, 50, 52, 49, 8, 7, 6, 43, 6, 5, 8, 6, 43, 43, 5, 4, 44, 8, 43, 43, 4, 3, 44, 43, 3, 9, 12, 10, 12, 11, 10, 9, 13, 12, 2, 44, 3, 44, 14, 9, 44, 9, 8, 13, 9, 14, 45, 14, 44, 45, 44, 2, 0, 45, 2, 1, 0, 2, 41, 45, 0, 41, 0, 42, 45, 15, 14, 46, 45, 41, 46, 15, 45, 46, 16, 15, 46, 41, 40], "vertices": [2, 15, 17.91, 19.9, 0.93326, 16, -12.55, 25.04, 0.06674, 2, 15, 14.43, 22.63, 0.97148, 16, -15.02, 28.71, 0.02852, 2, 15, 7.37, 19.22, 0.9956, 16, -22.78, 27.66, 0.0044, 1, 15, -4.98, 22.03, 1, 1, 15, -19.99, 17.32, 1, 1, 15, -29.62, 6.89, 1, 1, 15, -30.19, -9.3, 1, 1, 15, -24.1, -27.46, 1, 1, 15, -14.49, -18.47, 1, 1, 15, -8.2, -25.58, 1, 1, 15, -4.77, -35.73, 1, 1, 15, 0.86, -39.22, 1, 1, 15, 10.35, -36.72, 1, 1, 15, 16.07, -27.52, 1, 2, 15, 18.73, -15.45, 0.99674, 16, -22.75, -8.82, 0.00326, 3, 15, 27.53, -15.32, 0.8779, 16, -14.35, -11.43, 0.12208, 38, -52.5, -9.74, 2e-05, 3, 15, 35.58, -21.15, 0.51546, 16, -8.51, -19.47, 0.47746, 38, -45.06, -16.33, 0.00708, 3, 15, 46.34, -28.05, 0.29226, 16, -0.42, -29.37, 0.6855, 38, -35.03, -24.25, 0.02224, 3, 15, 52.03, -20.29, 0.19724, 16, 7.4, -23.76, 0.7468, 38, -28.6, -17.09, 0.05595, 3, 15, 55.17, -14.29, 0.06658, 16, 12.24, -19.03, 0.76622, 38, -24.9, -11.42, 0.1672, 2, 16, 26.76, -26.03, 0.23392, 38, -9.21, -15.12, 0.76608, 2, 38, 17.03, -19.23, 0.81067, 39, -8.29, -19.8, 0.18933, 3, 38, 40.61, -18.02, 0.04603, 39, 15.16, -17.1, 0.93896, 40, -12.48, -18.25, 0.015, 2, 39, 36.44, -12.17, 0.12772, 40, 8.28, -11.5, 0.87228, 1, 40, 29.03, -10.79, 1, 1, 40, 39.54, -6.89, 1, 1, 40, 47.56, -3.91, 1, 1, 40, 42.3, 3.08, 1, 1, 40, 25.09, 12.85, 1, 2, 39, 31.33, 16.59, 0.44606, 40, 0.71, 16.72, 0.55394, 3, 17, 31.73, -7.27, 0.13609, 38, 35.34, 14.62, 0.01863, 39, 7.84, 15.14, 0.84528, 3, 17, 23.32, -4.88, 0.58843, 38, 26.6, 14.96, 0.11819, 39, -0.9, 14.93, 0.29338, 2, 17, 26.12, 6.85, 0.9987, 39, -1.71, 26.97, 0.0013, 2, 16, 67.61, 21.06, 0.00019, 17, 23.23, 20.59, 0.99981, 2, 16, 56.1, 21.8, 0.04183, 17, 11.74, 21.57, 0.95817, 2, 16, 46.63, 14.95, 0.34984, 17, 2.13, 14.9, 0.65016, 2, 16, 38.77, 15.27, 0.78253, 17, -5.72, 15.39, 0.21747, 3, 15, 59.41, 25.75, 0.01218, 16, 28.71, 17.71, 0.97355, 17, -15.73, 18.04, 0.01426, 2, 15, 50.32, 28.8, 0.08429, 16, 21.01, 23.43, 0.91571, 2, 15, 46.39, 32.24, 0.11721, 16, 18.35, 27.92, 0.88279, 2, 15, 34.92, 25.39, 0.35058, 16, 5.32, 24.97, 0.64942, 2, 15, 28.9, 21.26, 0.65593, 16, -1.69, 22.92, 0.34407, 2, 15, 22.85, 23.02, 0.83764, 16, -6.89, 26.47, 0.16236, 1, 15, -15.8, -0.9, 1, 1, 15, 0.11, -3.64, 1, 1, 15, 18.02, -0.45, 1, 3, 15, 37.15, -1.16, 0.62201, 16, -0.81, -0.96, 0.37772, 38, -41.54, 3.41, 0.00027, 2, 16, 18.78, -1.96, 0.98228, 38, -22.2, 6.66, 0.01772, 2, 16, 11.38, -1.58, 0.99418, 38, -29.5, 5.43, 0.00582, 2, 16, 34.89, -0.79, 0.98032, 38, -6.72, 11.28, 0.01968, 3, 16, 46.73, -0.54, 0.0452, 17, 1.92, -0.58, 0.93798, 38, 4.79, 14.08, 0.01682, 2, 16, 58.24, 4.95, 0.00483, 17, 13.54, 4.67, 0.99517, 2, 16, 30.47, -13.93, 0.34963, 38, -8.2, -2.51, 0.65037, 2, 38, 13.67, -0.91, 0.99856, 39, -12.81, -1.72, 0.00144, 2, 17, 30.13, -22.46, 0.00018, 39, 10.82, 0.17, 0.99982, 2, 39, 34.3, 0.41, 0.00226, 40, 5.07, 0.85, 0.99774, 1, 40, 27.89, 1.26, 1], "hull": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 0, 84, 10, 86, 86, 88, 88, 90, 90, 92, 92, 96, 96, 94, 34, 96, 96, 78, 36, 94, 94, 98, 98, 100, 100, 102, 104, 106, 106, 108, 108, 110, 110, 112, 112, 52], "width": 80, "height": 204}}, "s2": {"s2": {"type": "mesh", "uvs": [0.18501, 0, 0.4231, 0.00264, 0.55538, 0.11581, 0.68765, 0.24514, 0.79347, 0.33136, 0.84197, 0.47866, 0.78465, 0.54692, 0.80669, 0.68344, 0.84197, 0.78942, 0.99188, 0.85049, 1, 0.93312, 0.89047, 0.99779, 0.69647, 1, 0.50247, 0.99419, 0.40988, 0.91695, 0.44074, 0.81636, 0.34374, 0.67266, 0.27319, 0.52895, 0.17179, 0.49842, 0.07919, 0.37268, 0, 0.23975, 0, 0.1176, 0.0351, 0.03318, 0.26878, 0.10503, 0.34374, 0.21999, 0.44074, 0.34573, 0.51128, 0.48405, 0.53333, 0.5541, 0.57301, 0.7014, 0.65238, 0.81995, 0.73174, 0.94569], "triangles": [12, 30, 11, 12, 13, 30, 11, 30, 10, 13, 14, 30, 29, 14, 15, 14, 29, 30, 30, 9, 10, 9, 29, 8, 9, 30, 29, 8, 29, 28, 29, 15, 28, 15, 16, 28, 28, 7, 8, 7, 28, 27, 28, 16, 27, 27, 6, 7, 16, 17, 27, 17, 26, 27, 27, 26, 6, 6, 26, 5, 17, 18, 26, 18, 25, 26, 26, 4, 5, 18, 19, 25, 26, 25, 4, 19, 24, 25, 19, 20, 24, 25, 3, 4, 25, 24, 3, 24, 2, 3, 20, 23, 24, 20, 21, 23, 24, 23, 2, 21, 22, 23, 23, 1, 2, 22, 0, 23, 23, 0, 1], "vertices": [1, 20, -13.16, -2.49, 1, 1, 20, -10.13, 7.54, 1, 1, 20, 3.19, 9.94, 1, 2, 20, 18.2, 11.88, 0.99986, 21, -18.24, 10.95, 0.00014, 2, 20, 28.41, 13.92, 0.8933, 21, -8.15, 13.52, 0.1067, 2, 20, 44.32, 11.79, 0.08903, 21, 7.85, 12.21, 0.91097, 2, 20, 50.76, 7.42, 9e-05, 21, 14.52, 8.18, 0.99991, 2, 21, 29.13, 5.99, 0.99833, 22, -6.7, 5.81, 0.00167, 2, 21, 40.64, 5.06, 0.00676, 22, 4.84, 5.2, 0.99324, 1, 22, 12.55, 10.45, 1, 1, 22, 21.38, 9.14, 1, 1, 22, 27.34, 3.1, 1, 1, 22, 25.99, -5.33, 1, 1, 22, 23.78, -13.6, 1, 2, 21, 50.05, -16.45, 0.0007, 22, 14.83, -16.05, 0.9993, 2, 21, 39.72, -12.8, 0.19367, 22, 4.41, -12.69, 0.80633, 2, 21, 23.65, -13.67, 0.96569, 22, -11.64, -13.99, 0.03431, 2, 20, 42.97, -13.78, 0.17798, 21, 7.83, -13.39, 0.82202, 2, 20, 38.62, -17.22, 0.42118, 21, 3.65, -17.05, 0.57882, 2, 20, 24.44, -17.57, 0.92019, 21, -10.48, -18.14, 0.07981, 1, 20, 9.68, -17.16, 1, 1, 20, -3.05, -13.69, 1, 1, 20, -11.44, -9.8, 1, 1, 20, -1.25, -1.92, 1, 1, 20, 11.6, -2.01, 1, 2, 20, 25.82, -1.46, 0.99969, 21, -9.94, -1.98, 0.00031, 2, 20, 41.05, -2.4, 0.0066, 21, 5.32, -2.13, 0.9934, 2, 20, 48.6, -3.45, 0, 21, 12.92, -2.79, 1, 2, 21, 28.83, -4.47, 0.95047, 22, -6.71, -4.66, 0.04953, 2, 21, 42.09, -3.79, 0.01478, 22, 6.52, -3.61, 0.98522, 1, 22, 20.52, -2.71, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 44, "height": 108}}, "ft2": {"ft2": {"type": "mesh", "uvs": [0.59197, 0, 0.70432, 0.00462, 0.77232, 0.06058, 0.81223, 0.14517, 0.81223, 0.25059, 0.8551, 0.36642, 0.9231, 0.47443, 1, 0.57595, 0.86988, 0.6267, 0.77823, 0.75815, 0.70875, 0.87007, 0.67771, 0.9885, 0.58606, 1, 0.41606, 0.95076, 0.27562, 0.87528, 0.16919, 0.75554, 0.15145, 0.60718, 0.06719, 0.59547, 0, 0.55122, 0.13075, 0.49396, 0.24754, 0.40936, 0.33475, 0.28833, 0.3791, 0.1712, 0.39241, 0.06578, 0.49293, 0.00852, 0.60527, 0.11264, 0.59197, 0.24668, 0.58458, 0.40286, 0.52841, 0.56163, 0.48554, 0.73082, 0.5151, 0.87788, 0.74571, 0.58636, 0.73684, 0.40416, 0.70875, 0.23237, 0.69397, 0.10873, 0.29484, 0.66705, 0.36728, 0.46663, 0.43232, 0.30655, 0.47075, 0.19723, 0.48406, 0.08921], "triangles": [8, 31, 6, 7, 8, 6, 9, 31, 8, 27, 31, 28, 31, 29, 28, 29, 31, 9, 10, 29, 9, 30, 29, 10, 10, 12, 30, 11, 12, 10, 13, 30, 12, 32, 4, 5, 27, 26, 32, 6, 31, 32, 6, 32, 5, 31, 27, 32, 1, 25, 0, 34, 1, 2, 34, 25, 1, 34, 2, 3, 33, 34, 3, 25, 34, 33, 26, 25, 33, 33, 3, 4, 32, 33, 4, 26, 33, 32, 17, 18, 19, 36, 16, 19, 17, 19, 16, 35, 16, 36, 35, 36, 28, 29, 35, 28, 15, 16, 35, 14, 15, 35, 14, 35, 29, 14, 29, 30, 13, 14, 30, 21, 22, 37, 27, 37, 26, 36, 21, 37, 36, 37, 27, 20, 21, 36, 28, 36, 27, 36, 19, 20, 39, 23, 24, 22, 23, 39, 39, 24, 0, 38, 22, 39, 0, 25, 39, 25, 38, 39, 26, 38, 25, 37, 22, 38, 37, 38, 26], "vertices": [2, 41, -26.16, 9.81, 0.56914, 44, -26.85, -18.59, 0.43086, 2, 41, -31.88, 29.7, 0.15205, 44, -23.35, 1.8, 0.84795, 2, 41, -24.81, 45.3, 0.00696, 44, -10.2, 12.78, 0.99304, 1, 44, 8.25, 17.89, 1, 2, 44, 30.11, 15.17, 0.82713, 45, -9.84, 15.04, 0.17287, 3, 44, 55.11, 20.02, 0.02238, 45, 15.08, 20.24, 0.96979, 46, -31.19, 18.01, 0.00783, 2, 45, 38.89, 30.21, 0.55838, 46, -8.18, 29.69, 0.44162, 2, 45, 61.52, 41.96, 0.18412, 46, 13.54, 43.06, 0.81588, 2, 45, 69.45, 17, 0.03242, 46, 23.27, 18.75, 0.96758, 1, 46, 50.11, 0.9, 1, 2, 43, 51.1, 94.83, 0.1673, 46, 73.03, -12.72, 0.8327, 2, 43, 76.3, 98.05, 0.25445, 46, 97.56, -19.32, 0.74555, 2, 43, 84.39, 83.06, 0.3014, 46, 99.35, -36.26, 0.6986, 2, 43, 85.57, 50.15, 0.5351, 46, 87.93, -67.15, 0.4649, 2, 43, 79.72, 20.45, 0.81231, 46, 71.23, -92.4, 0.18769, 2, 43, 63.03, -6.59, 0.99952, 46, 45.51, -111.07, 0.00048, 1, 43, 35.07, -20.39, 1, 1, 43, 38.15, -35.79, 1, 2, 42, 85.88, -47.92, 0, 43, 33.76, -50.59, 1, 2, 42, 65, -31.01, 0.04044, 43, 14.19, -32.16, 0.95956, 2, 42, 39.99, -18.81, 0.79014, 43, -9.83, -18.13, 0.20986, 2, 41, 46.09, -15.7, 0.10501, 42, 10.31, -14.73, 0.89499, 2, 41, 20.29, -15.82, 0.97199, 42, -15.34, -17.47, 0.02801, 1, 41, -1.37, -20.56, 1, 2, 41, -18.63, -6.88, 0.91755, 44, -27.33, -36.9, 0.08245, 2, 41, -4.65, 19.68, 0.48093, 44, -3.19, -19.06, 0.51907, 4, 41, 22.67, 26.34, 0.3906, 42, -17.27, 24.71, 0.0899, 44, 24.31, -24.95, 0.42774, 45, -15.08, -25.16, 0.09176, 6, 41, 54.03, 35.52, 0.03077, 42, 12.99, 37.03, 0.38446, 43, -32.59, 39.58, 0.01183, 44, 56.54, -30.32, 0.03264, 45, 17.22, -30.07, 0.50409, 46, -25.38, -32.01, 0.03621, 4, 42, 47.47, 41.42, 0.26778, 43, 2.12, 41.38, 0.25121, 45, 49.08, -43.97, 0.15906, 46, 7.4, -43.54, 0.32194, 4, 42, 82.91, 48.94, 0.02915, 43, 38.02, 46.22, 0.50809, 45, 83.37, -55.67, 0.00779, 46, 42.45, -52.71, 0.45496, 3, 42, 108.6, 66.65, 0.00055, 43, 64.97, 61.97, 0.4128, 46, 73.37, -48.39, 0.58665, 4, 42, 35.55, 79.93, 0.00611, 43, -6.88, 80.67, 0.00545, 45, 58.58, -4.79, 0.00427, 46, 14.02, -3.77, 0.98418, 4, 41, 45.3, 62.14, 0.0005, 42, 1.59, 62.63, 0.00805, 43, -42.04, 65.95, 0.0002, 45, 20.55, -2.26, 0.99126, 4, 41, 12.95, 45.73, 0.01973, 42, -28.92, 43.01, 0.00357, 44, 23.99, -3.25, 0.9748, 45, -15.7, -3.48, 0.0019, 2, 41, -10.65, 34.87, 0.04669, 44, -1.99, -2.77, 0.95331, 4, 42, 85.36, 11.48, 0.00176, 43, 37.67, 8.69, 0.96409, 45, 66.29, -89.09, 0.00088, 46, 27.86, -87.29, 0.03327, 4, 42, 41.72, 6.2, 0.89098, 43, -6.24, 6.69, 0.08066, 45, 26.11, -71.27, 0.01607, 46, -13.52, -72.45, 0.01229, 5, 41, 43.94, 2.52, 0.00955, 42, 6.32, 3.19, 0.97332, 44, 33.11, -55.64, 0.0048, 45, -5.84, -55.73, 0.01217, 46, -46.52, -59.28, 0.00016, 4, 41, 20.03, 1.9, 0.9885, 42, -17.4, 0.13, 0.0002, 44, 11.31, -45.81, 0.01003, 45, -27.78, -46.2, 0.00126, 1, 41, -2.14, -3.02, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 18, 62, 62, 64, 64, 66, 66, 68, 28, 70, 70, 72, 72, 74, 74, 76, 76, 78], "width": 184, "height": 209}}, "m2": {"m2": {"type": "mesh", "uvs": [0.27055, 0, 0.40324, 0.04157, 0.38514, 0.19638, 0.36705, 0.37329, 0.42736, 0.55021, 0.52989, 0.73115, 0.75305, 0.84775, 1, 0.96838, 0.72289, 0.97642, 0.43942, 0.97039, 0.3485, 0.91323, 0.22833, 0.8377, 0.10771, 0.69094, 0.07152, 0.51201, 0.04739, 0.35319, 0.04739, 0.17225, 0.10771, 0.0275, 0.24643, 0.05967, 0.18009, 0.20442, 0.14993, 0.3552, 0.22231, 0.52005, 0.31278, 0.69898, 0.48166, 0.84172], "triangles": [22, 11, 21, 5, 22, 21, 22, 5, 6, 10, 11, 22, 9, 10, 22, 8, 22, 6, 8, 6, 7, 9, 22, 8, 20, 13, 19, 20, 3, 4, 12, 13, 20, 21, 12, 20, 4, 21, 20, 21, 4, 5, 11, 12, 21, 19, 14, 18, 3, 19, 2, 13, 14, 19, 3, 20, 19, 17, 16, 0, 17, 0, 1, 15, 16, 17, 2, 17, 1, 18, 15, 17, 2, 18, 17, 14, 15, 18, 2, 19, 18], "vertices": [1, 47, -5.21, 0.82, 1, 1, 47, -1.51, 5.32, 1, 1, 47, 13.36, 5.72, 1, 1, 48, 10.43, 5.21, 1, 2, 48, 27.48, 6.5, 0.00493, 49, 7.54, 5.64, 0.99507, 2, 49, 25.21, 6.15, 0.06785, 50, 4.85, 4.9, 0.93215, 1, 50, 18.07, 6.22, 1, 1, 50, 31.97, 8.05, 1, 1, 50, 28.65, -0.21, 1, 1, 50, 24.05, -8.04, 1, 1, 50, 17.84, -8.17, 1, 1, 50, 9.63, -8.33, 1, 2, 49, 19.28, -6.59, 0.83863, 50, -4.68, -5.42, 0.16137, 2, 48, 23.39, -4.74, 0.17118, 49, 2.13, -5.03, 0.82882, 1, 48, 8.12, -4.94, 1, 1, 47, 11.76, -5.22, 1, 1, 47, -2.24, -4.21, 1, 1, 47, 0.55, 0.42, 1, 1, 47, 14.56, -0.78, 1, 1, 48, 8.44, -1.67, 1, 1, 49, 3.65, -0.39, 1, 2, 49, 21.07, -0.23, 0.9931, 50, -1.04, 0.09, 0.0069, 1, 50, 13.63, -1.27, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 16], "width": 32, "height": 96}}, "m3": {"m3": {"type": "mesh", "uvs": [0.10892, 0, 0.21692, 0.0368, 0.24092, 0.17754, 0.28592, 0.31976, 0.35792, 0.47383, 0.45691, 0.60569, 0.57991, 0.72865, 0.75091, 0.8368, 0.95191, 0.91976, 0.93691, 0.98791, 0.75091, 0.99087, 0.55591, 0.9242, 0.40292, 0.8205, 0.28892, 0.71383, 0.16892, 0.56865, 0.08792, 0.41902, 0.03992, 0.27087, 0.01592, 0.15532, 0.03992, 0.02198, 0.12392, 0.04124, 0.12392, 0.16717, 0.14492, 0.3005, 0.21392, 0.4368, 0.30392, 0.57161, 0.39992, 0.68865, 0.53192, 0.81161, 0.69392, 0.88865], "triangles": [25, 24, 6, 12, 24, 25, 25, 6, 7, 26, 25, 7, 26, 7, 8, 11, 25, 26, 12, 25, 11, 9, 26, 8, 10, 26, 9, 11, 26, 10, 23, 14, 22, 23, 4, 5, 24, 23, 5, 13, 14, 23, 13, 23, 24, 24, 5, 6, 13, 24, 12, 15, 16, 21, 22, 15, 21, 3, 22, 21, 22, 3, 4, 14, 15, 22, 4, 23, 22, 19, 0, 1, 18, 0, 19, 17, 18, 19, 20, 17, 19, 20, 19, 1, 2, 20, 1, 16, 17, 20, 21, 20, 2, 16, 20, 21, 21, 2, 3], "vertices": [1, 51, -5.93, 2.74, 1, 1, 51, -2.26, 6.49, 1, 1, 51, 9.14, 5.51, 1, 2, 51, 20.8, 5.33, 0.13744, 52, 3.03, 5.09, 0.86256, 2, 52, 15.83, 4.72, 0.83768, 53, -1.81, 5.04, 0.16232, 1, 53, 9.57, 4.52, 1, 2, 53, 20.66, 5.16, 0.19359, 54, 2.56, 4.68, 0.80641, 1, 54, 13.68, 4.92, 1, 1, 54, 23.87, 7.34, 1, 1, 54, 27.94, 3.56, 1, 1, 54, 23.67, -2.55, 1, 1, 54, 14.68, -5.56, 1, 2, 53, 24.75, -4.26, 0.00342, 54, 4.29, -5.44, 0.99658, 2, 53, 15.01, -5.09, 0.98117, 54, -5.36, -3.92, 0.01883, 2, 52, 21.35, -4.53, 0.1154, 53, 2.31, -4.92, 0.8846, 1, 52, 8.81, -4.61, 1, 2, 51, 15.23, -3.7, 0.96564, 52, -3.29, -3.43, 0.03436, 1, 51, 5.84, -3.06, 1, 1, 51, -4.64, -0.28, 1, 1, 51, -2.54, 2.76, 1, 1, 51, 7.52, 1.04, 1, 2, 51, 18.3, 0.03, 0.44835, 52, 0.09, 0.02, 0.55165, 1, 52, 11.47, -0.09, 1, 1, 53, 4.64, -0.04, 1, 1, 53, 14.87, -0.2, 1, 1, 54, 6.8, -0.88, 1, 1, 54, 15.68, 0.58, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 18], "width": 40, "height": 81}}, "biyan": {"biyan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.67, -34.78, 17.49, 16.19, 38.47, 15.44, 36.65, -35.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 51, "height": 21}}, "yifu": {"yifu": {"type": "mesh", "uvs": [0.31757, 0.1428, 0.32159, 0.0878, 0.35781, 0.0328, 0.45239, 0, 0.57513, 0.02613, 0.59324, 0.07113, 0.58922, 0.10613, 0.70391, 0.12613, 0.83671, 0.17613, 1, 0.21613, 0.94738, 0.29447, 0.87092, 0.3678, 0.81257, 0.4178, 0.84678, 0.51447, 0.87293, 0.6228, 0.84275, 0.72447, 0.89708, 0.82447, 0.84275, 0.91113, 0.72604, 0.99947, 0.59324, 1, 0.40007, 0.98947, 0.23708, 0.93947, 0.12641, 0.84447, 0.11233, 0.7328, 0.06806, 0.5778, 0.00367, 0.46447, 0, 0.33447, 0.058, 0.21947, 0.19684, 0.14447, 0.45641, 0.08947, 0.46446, 0.17113, 0.75824, 0.26613, 0.15257, 0.2878, 0.49062, 0.35113, 0.52483, 0.54613, 0.553, 0.72113, 0.54092, 0.88447, 0.74013, 0.85447, 0.69989, 0.69947, 0.68379, 0.53613, 0.65763, 0.3728, 0.61537, 0.22947, 0.33769, 0.86113, 0.31153, 0.72947, 0.27129, 0.55613, 0.26727, 0.39113, 0.28739, 0.26447], "triangles": [18, 37, 17, 18, 19, 37, 20, 36, 19, 21, 42, 20, 32, 27, 28, 46, 32, 28, 26, 27, 32, 45, 32, 46, 25, 26, 32, 25, 32, 45, 24, 25, 45, 44, 24, 45, 41, 6, 7, 31, 7, 8, 41, 7, 31, 10, 8, 9, 31, 8, 10, 11, 31, 10, 40, 41, 31, 12, 40, 31, 11, 12, 31, 39, 40, 12, 39, 12, 13, 29, 3, 4, 29, 4, 5, 2, 3, 29, 1, 2, 29, 6, 29, 5, 30, 29, 6, 30, 6, 41, 0, 1, 29, 0, 29, 30, 46, 28, 0, 33, 30, 41, 33, 41, 40, 34, 33, 40, 46, 0, 30, 46, 30, 33, 33, 45, 46, 33, 34, 45, 34, 40, 39, 45, 34, 44, 14, 38, 39, 14, 39, 13, 38, 35, 34, 38, 34, 39, 15, 38, 14, 34, 43, 44, 43, 34, 35, 23, 24, 44, 23, 44, 43, 22, 23, 43, 37, 38, 15, 37, 15, 16, 42, 43, 35, 22, 43, 42, 36, 42, 35, 35, 38, 37, 17, 37, 16, 21, 22, 42, 20, 42, 36, 37, 36, 35, 37, 19, 36], "vertices": [3, 4, 38.82, 8.21, 0.2617, 5, 3.03, 8.62, 0.61951, 14, 17.08, -22.85, 0.11879, 3, 4, 44.15, 7.05, 0.01559, 5, 8.46, 8.09, 0.96568, 14, 22.41, -24, 0.01873, 2, 5, 13.79, 4.93, 0.99942, 14, 27.34, -27.76, 0.00058, 2, 5, 16.76, -2.93, 0.99633, 19, 36.64, 19.95, 0.00367, 3, 4, 47.02, -14.43, 0.00657, 5, 13.82, -12.9, 0.94506, 19, 32.56, 10.4, 0.04837, 3, 4, 42.39, -15.22, 0.04269, 5, 9.31, -14.23, 0.86724, 19, 27.93, 9.61, 0.09007, 3, 4, 39.02, -14.36, 0.12805, 5, 5.86, -13.77, 0.68575, 19, 24.55, 10.46, 0.18621, 3, 4, 35.63, -23.36, 0.14987, 5, 3.55, -23.1, 0.26538, 19, 21.17, 1.47, 0.58475, 3, 4, 29.08, -33.37, 0.02461, 5, -1.79, -33.81, 0.06097, 19, 14.62, -8.54, 0.91442, 1, 19, 8.67, -21.18, 1, 2, 5, -13.82, -42.46, 3e-05, 19, 1.66, -15.73, 0.99997, 2, 3, 53.02, -33.15, 0.01581, 19, -4.56, -8.43, 0.98419, 3, 3, 48.8, -27.71, 0.1587, 4, 5.73, -27.77, 0.02835, 19, -8.73, -2.95, 0.81295, 3, 3, 38.93, -29.13, 0.52185, 4, -4.15, -29.09, 0.02326, 19, -18.61, -4.27, 0.45489, 3, 3, 28.01, -29.74, 0.75382, 4, -15.08, -29.58, 0.00055, 19, -29.54, -4.76, 0.24563, 2, 3, 18.39, -25.87, 0.89712, 19, -39.11, -0.78, 0.10288, 2, 3, 7.96, -28.88, 0.9629, 19, -49.57, -3.68, 0.0371, 1, 2, 25.3, 8.09, 1, 1, 2, 15.63, -0.53, 1, 1, 2, 4.74, -0.45, 1, 1, 2, -11.09, 0.78, 1, 1, 2, -24.39, 5.9, 1, 4, 3, 14.94, 33.96, 0.73191, 4, -27.46, 34.26, 0.0006, 14, -49.19, 3.21, 0.09149, 2, -33.35, 15.41, 0.176, 3, 3, 26.05, 33.54, 0.76905, 4, -16.36, 33.72, 0.02113, 14, -38.09, 2.67, 0.20982, 3, 3, 41.75, 34.97, 0.3771, 4, -0.64, 34.98, 0.08288, 14, -22.37, 3.93, 0.54002, 3, 3, 53.61, 38.61, 0.10761, 4, 11.25, 38.49, 0.02771, 14, -10.48, 7.44, 0.86467, 3, 3, 66.39, 37.08, 0.00122, 5, -15.01, 35.32, 0.00101, 14, 2.29, 5.78, 0.99777, 3, 4, 34.55, 30.4, 0.12746, 5, -3.8, 30.16, 0.04277, 14, 12.82, -0.65, 0.82977, 3, 4, 40.16, 18.02, 0.35359, 5, 3.22, 18.52, 0.18628, 14, 18.42, -13.04, 0.46013, 2, 5, 7.9, -2.95, 0.98898, 19, 27.84, 20.97, 0.01102, 3, 4, 34.21, -3.27, 0.44542, 5, -0.21, -3.32, 0.52763, 19, 19.75, 21.55, 0.02695, 3, 4, 21.25, -25.65, 0.05643, 5, -10.46, -27.06, 0.04835, 19, 6.79, -0.83, 0.89522, 4, 3, 69.19, 24.05, 0.00013, 4, 26.68, 23.76, 0.22332, 5, -10.84, 22.65, 0.03253, 14, 4.95, -7.29, 0.74402, 3, 4, 16.27, -2.68, 0.95606, 5, -18.09, -4.83, 0.00121, 19, 1.81, 22.14, 0.04274, 3, 3, 39.56, -2.55, 0.89074, 4, -3.23, -2.52, 0.08859, 19, -17.7, 22.3, 0.02067, 2, 3, 22.08, -2.39, 0.99538, 19, -35.17, 22.65, 0.00462, 3, 3, 6.21, 0.88, 0.89582, 14, -58.28, -29.78, 0.00018, 2, 0.59, 11.04, 0.104, 3, 3, 6.84, -15.72, 0.85595, 19, -50.55, 9.5, 0.01605, 2, 16.96, 13.81, 0.128, 3, 3, 22.5, -14.62, 0.91621, 4, -20.42, -14.4, 0.0001, 19, -34.88, 10.42, 0.08369, 3, 3, 38.7, -15.6, 0.59679, 4, -4.24, -15.55, 0.11481, 19, -18.7, 9.27, 0.28841, 4, 3, 55.01, -15.76, 0.05353, 4, 12.07, -15.89, 0.35861, 5, -20.72, -18.44, 0.00516, 19, -2.39, 8.93, 0.5827, 3, 4, 26.62, -14.63, 0.39346, 5, -6.42, -15.48, 0.18548, 19, 12.16, 10.2, 0.42105, 3, 3, 10.86, 17.05, 0.73744, 14, -53.46, -13.66, 0.03056, 2, -16.05, 13.55, 0.232, 3, 3, 24.06, 17.33, 0.88818, 4, -18.51, 17.53, 0.01199, 14, -40.25, -13.52, 0.09983, 3, 3, 41.52, 18.17, 0.47075, 4, -1.05, 18.18, 0.25014, 14, -22.79, -12.87, 0.2791, 4, 3, 57.74, 16.18, 0.04505, 4, 15.14, 16.02, 0.55401, 5, -21.39, 13.62, 0.00052, 14, -6.59, -15.03, 0.40042, 3, 4, 27.29, 12.48, 0.6335, 5, -8.92, 11.52, 0.06257, 14, 5.55, -18.57, 0.30393], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 6, 58, 58, 60, 60, 66, 66, 68, 68, 70, 70, 72, 36, 74, 74, 76, 76, 78, 78, 80, 80, 82, 40, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 82, "height": 99}}, "faji": {"faji": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [71.37, -42.81, 74.41, 42.13, 140.36, 39.78, 137.33, -45.17], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 85, "height": 66}}, "pd1": {"pd1": {"type": "mesh", "uvs": [0, 0.05509, 0.25552, 0, 0.57879, 0, 0.88099, 0.0149, 1, 0.07874, 1, 0.23716, 1, 0.45233, 1, 0.6604, 1, 0.87321, 0.81071, 0.95123, 0.53663, 1, 0.23443, 0.95123, 0.00252, 0.87321, 0.01657, 0.67695, 0, 0.45233, 0.0236, 0.23952, 0.55068, 0.07401, 0.54366, 0.24189, 0.51554, 0.46179, 0.53663, 0.66513, 0.52257, 0.88266], "triangles": [11, 20, 10, 10, 20, 9, 9, 20, 8, 11, 12, 20, 12, 13, 20, 20, 19, 8, 20, 13, 19, 19, 7, 8, 13, 18, 19, 19, 18, 7, 13, 14, 18, 18, 6, 7, 6, 18, 17, 18, 14, 15, 17, 18, 15, 17, 5, 6, 5, 17, 16, 17, 15, 16, 16, 0, 1, 16, 15, 0, 5, 16, 4, 16, 3, 4, 16, 2, 3, 16, 1, 2], "vertices": [1, 35, 1.12, -19, 1, 1, 35, -4.66, -9.73, 1, 1, 35, -4.51, 1.91, 1, 1, 35, -2.78, 12.76, 1, 1, 35, 4.1, 16.96, 1, 2, 35, 21.05, 16.75, 0.88276, 36, -11.55, 16.68, 0.11724, 3, 35, 44.07, 16.45, 0.02905, 36, 11.47, 16.52, 0.96977, 37, -19.67, 15.92, 0.00118, 2, 36, 33.74, 16.37, 0.39211, 37, 2.59, 16.46, 0.60789, 1, 37, 25.35, 17.02, 1, 1, 37, 33.87, 10.41, 1, 1, 37, 39.32, 0.67, 1, 1, 37, 34.37, -10.33, 1, 2, 36, 56.26, -19.69, 0.00027, 37, 26.23, -18.88, 0.99973, 2, 36, 35.27, -19.04, 0.29239, 37, 5.22, -18.89, 0.70761, 3, 35, 43.62, -19.54, 0.05467, 36, 11.23, -19.48, 0.9269, 37, -18.79, -20.07, 0.01843, 2, 35, 20.86, -18.4, 0.86064, 36, -11.53, -18.47, 0.13936, 1, 35, 3.39, 0.79, 1, 2, 35, 21.35, 0.31, 0.99998, 36, -11.15, 0.25, 2e-05, 1, 36, 12.37, -0.93, 1, 2, 36, 34.13, -0.31, 0.00187, 37, 3.5, -0.2, 0.99813, 1, 37, 26.78, -0.14, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 4, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 36, "height": 107}}, "pd3": {"pd3": {"type": "mesh", "uvs": [0.89992, 0, 1, 0.08917, 0.93543, 0.26903, 0.89548, 0.45681, 0.84666, 0.67027, 0.7934, 0.86991, 0.75345, 1, 0.50045, 0.97269, 0.25633, 0.89363, 0, 0.75724, 0.02996, 0.66237, 0.18087, 0.47262, 0.34954, 0.29275, 0.4827, 0.13067, 0.62917, 0.00417, 0.80227, 0.06347, 0.69575, 0.20183, 0.58478, 0.36984, 0.50045, 0.56552, 0.38949, 0.74934], "triangles": [6, 7, 5, 5, 7, 19, 7, 8, 19, 8, 9, 19, 5, 19, 4, 9, 10, 19, 19, 18, 4, 19, 10, 18, 10, 11, 18, 4, 18, 3, 18, 17, 3, 18, 11, 17, 11, 12, 17, 3, 17, 2, 17, 12, 16, 17, 16, 2, 12, 13, 16, 2, 16, 1, 1, 16, 15, 16, 13, 15, 13, 14, 15, 15, 0, 1, 15, 14, 0], "vertices": [1, 26, -10.98, 0.84, 1, 1, 26, -4.33, 7.21, 1, 2, 26, 11.24, 8.25, 0.88343, 27, -3.54, 6.25, 0.11657, 3, 26, 27.27, 10.55, 0.00155, 27, 7.02, 9.12, 0.99163, 28, -7.28, 5.2, 0.00682, 2, 27, 19.05, 12.26, 0.17416, 28, -0.05, 10.09, 0.82584, 1, 28, 6.8, 14.48, 1, 1, 28, 11.32, 17.2, 1, 1, 28, 13.26, 10.08, 1, 1, 28, 13.48, 1.7, 1, 1, 28, 12.04, -8.63, 1, 2, 27, 25.02, -16.23, 0.02282, 28, 8.73, -10.6, 0.97718, 3, 26, 34.39, -20.79, 2e-05, 27, 13.48, -15.3, 0.63595, 28, 1.11, -12.25, 0.36404, 3, 26, 17.98, -17.22, 0.29877, 27, 2.34, -13.53, 0.69874, 28, -6.39, -13.18, 0.00249, 2, 26, 3.34, -14.83, 0.96868, 27, -7.55, -12.58, 0.03132, 1, 26, -8.43, -11.08, 1, 1, 26, -4.88, -2.12, 1, 2, 26, 7.56, -3.84, 0.99194, 27, -5.33, -3.58, 0.00806, 3, 26, 22.52, -5.11, 0.01838, 27, 4.71, -3.62, 0.9816, 28, -6.57, -5.08, 2e-05, 2, 27, 16.05, -2.11, 0.34251, 28, 0.5, -1.59, 0.65749, 1, 28, 7.48, 0.9, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 57, "height": 128}}}}], "events": {"atk": {}, "hurt": {}}, "animations": {"boss_attack1": {"bones": {"bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -20.44, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -36.08, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -9.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -80.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -113.18, "curve": "stepped"}, {"time": 0.3667, "angle": -113.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.27}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -92.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.81, "curve": "stepped"}, {"time": 0.3667, "angle": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.44}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -30.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 74.78, "curve": "stepped"}, {"time": 0.3667, "angle": 74.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.62}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": "stepped"}, {"time": 0.1, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -30.18, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -3.87}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -7.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": 3.04}]}, "bone44": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -38.41, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.5}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -34.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.6}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.51, "curve": "stepped"}, {"time": 0.3667, "angle": -46.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 68.61, "curve": "stepped"}, {"time": 0.3667, "angle": 68.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.77}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": "stepped"}, {"time": 0.1, "angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 58.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 44.1, "curve": "stepped"}, {"time": 0.4667, "angle": 44.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -4.1}]}, "bone49": {"rotate": [{"angle": 7.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "angle": -30.76, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 7.66}]}, "bone5": {"rotate": [{"angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24}]}, "bone19": {"rotate": [{"angle": -2.76}]}, "bone4": {"rotate": [{"angle": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.71, "curve": "stepped"}, {"time": 0.3667, "angle": 17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 117.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.39, "curve": "stepped"}, {"time": 0.3667, "angle": 31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.22}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": "stepped"}, {"time": 0.1, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -27.71, "curve": 0.349, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 0.5, "angle": 10.7}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -18.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -70.9, "curve": "stepped"}, {"time": 0.3667, "angle": -70.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.48}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": "stepped"}, {"time": 0.2, "angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -30.75, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.5, "angle": 7.67}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 76.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 38.17, "curve": "stepped"}, {"time": 0.3667, "angle": 38.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.08}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 30.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.55, "curve": "stepped"}, {"time": 0.3667, "angle": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.52}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 54.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -19.21, "curve": "stepped"}, {"time": 0.3667, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.69}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -24.9, "y": -51.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 36.43, "y": -2.04, "curve": "stepped"}, {"time": 0.3667, "x": 36.43, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -30.75, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.1333, "angle": -23.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 7.67}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 28.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -57.6, "curve": "stepped"}, {"time": 0.3667, "angle": -57.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.235, "y": 1.832, "curve": "stepped"}, {"time": 0.3667, "x": 1.235, "y": 1.832, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone55": {"translate": [{"time": 0.1667, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.3, "x": 78.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5}]}, "bone59": {"translate": [{"time": 0.1667, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.3, "y": 65.75, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.5}]}, "bone10": {"rotate": [{"angle": -4.61, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.37, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -4.61}]}, "bone11": {"rotate": [{"angle": -10.52, "curve": 0.29, "c2": 0.19, "c3": 0.652, "c4": 0.62}, {"time": 0.1, "angle": -4.61, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.37, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -10.52}]}, "bone12": {"rotate": [{"angle": -9.27, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -11.37, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.1, "angle": -10.49, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.3, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -9.27}]}}, "events": [{"time": 0.2667, "name": "atk"}]}, "boss_attack3": {"slots": {"wuqi3": {"attachment": [{"time": 0.2667, "name": null}]}, "xuanz": {"attachment": [{"time": 0.2667, "name": "xuanz"}, {"time": 0.5333, "name": null}]}, "mutou2": {"attachment": [{"time": 0.2667, "name": "mutou"}, {"time": 0.5333, "name": null}]}}, "bones": {"feijian": {"rotate": [{"time": 0.2667}, {"time": 0.3333, "angle": -120}, {"time": 0.4, "angle": 120}, {"time": 0.4333}, {"time": 0.5333, "angle": -120}, {"time": 0.5667, "angle": 120}, {"time": 0.6667}], "translate": [{"x": -6.78, "y": -184.11, "curve": "stepped"}, {"time": 0.2667, "x": -6.78, "y": -184.11, "curve": 0.321, "c2": 0.51, "c3": 0.75}, {"time": 0.6667, "x": 777.72, "y": -196.09}]}, "bone2": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 5.42, "curve": "stepped"}, {"time": 0.2, "angle": 5.42, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": -28, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -11.2, "y": -18.17, "curve": "stepped"}, {"time": 0.2, "x": -11.2, "y": -18.17, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "x": 8.84, "y": -18.08, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 9.89, "curve": "stepped"}, {"time": 0.2, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone4": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone5": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 9.26, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "angle": 3.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -25.46, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone20": {"rotate": [{"angle": -8.19, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -25.46, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.7, "angle": -8.19}]}, "bone27": {"rotate": [{"angle": -14.11, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -17.84, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.7, "angle": -14.11}]}, "bone33": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 15.99, "curve": "stepped"}, {"time": 0.2, "angle": 15.99, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": -39.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone34": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 62.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "angle": -10.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone36": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 37.6, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.1667, "angle": -177.28, "curve": "stepped"}, {"time": 0.2, "angle": -177.28, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 71.15, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.7}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.74, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": 18.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7}]}, "bone40": {"rotate": [{"angle": -12.09, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -27.47, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.7, "angle": -12.09}]}, "bone41": {"rotate": [{"angle": -26.54, "curve": 0.278, "c2": 0.14, "c3": 0.644, "c4": 0.59}, {"time": 0.1667, "angle": -12.09, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -27.47, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 0.7, "angle": -26.54}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone45": {"rotate": [{"angle": -8.63, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -26.82, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.7, "angle": -8.63}]}, "bone46": {"rotate": [{"angle": -25.88, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -26.82, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 0.7, "angle": -25.88}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone50": {"rotate": [{"angle": -11.8, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -26.82, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.7, "angle": -11.8}]}, "bone51": {"rotate": [{"angle": -25.88, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -26.82, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 0.7, "angle": -25.88}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -37.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone10": {"rotate": [{"angle": -3.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.41, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.7, "angle": -3.49}]}, "bone11": {"rotate": [{"angle": -10.91, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": -5.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -14.41, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.7, "angle": -10.91}]}, "bone12": {"rotate": [{"angle": -13.84, "curve": 0.289, "c2": 0.17, "c3": 0.637, "c4": 0.56}, {"time": 0.1, "angle": -9.11, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -14.41, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.7, "angle": -13.84}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -18.45, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone21": {"rotate": [{"angle": -6.79, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.45, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7, "angle": -6.79}]}, "bone22": {"rotate": [{"angle": -11.66, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -18.45, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7, "angle": -11.66}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -27.73, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone25": {"rotate": [{"angle": -6.72, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -27.73, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.7, "angle": -6.72}]}, "bone26": {"rotate": [{"angle": -26.55, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -27.73, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.7, "angle": -26.55}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -27.73, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone29": {"rotate": [{"angle": -6.72, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -27.73, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.7, "angle": -6.72}]}, "bone30": {"rotate": [{"angle": -26.55, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -27.73, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.7, "angle": -26.55}]}, "bone55": {"translate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.2, "x": -25.16, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.3333}]}}, "deform": {"default": {"mutou2": {"mutou": [{"time": 0.2667, "vertices": [-35.04689, -20.0926, 17.61178, 36.35671, 35.04676, 20.09225, -17.61174, -36.35686]}]}}}, "events": [{"time": 0.4667, "name": "atk"}]}, "boss_idle": {"slots": {"biyan": {"attachment": [{"time": 1.0667, "name": "biyan"}, {"time": 1.1667, "name": null}]}}, "bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.8, "y": -0.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.51, "y": -0.08}]}, "bone5": {"rotate": [{"angle": 1.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.52, "y": -0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.09, "y": -0.24}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -9.39, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.64, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.87}]}, "bone19": {"rotate": [{"angle": -2.76, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -13.64, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -2.76}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -13.64, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -8.6}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.63, "y": 2.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.82, "y": 1.09}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.22, "y": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.11, "y": -1.69}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.48}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.22}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.62}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.72, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.1}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.04}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.67}]}, "bone49": {"rotate": [{"angle": -17.23, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -27.61, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -17.23}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10.7}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 7.67}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone39": {"rotate": [{"angle": 5.08, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": 10.04, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.3667, "angle": -6.59, "curve": 0.243, "c3": 0.646, "c4": 0.59}, {"time": 2, "angle": 5.08}]}, "bone40": {"rotate": [{"angle": -2.35, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 0.6667, "angle": 10.04, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.7, "angle": -6.59, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -2.35}]}, "bone41": {"rotate": [{"angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 10.04, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.59}]}, "b1one8": {"rotate": [{"angle": -2.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.67}]}, "b1one9": {"rotate": [{"angle": -6.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.39, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.73}]}, "bone23": {"rotate": [{"angle": -5.39, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.63, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.39}]}, "bone24": {"rotate": [{"angle": -3.69, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": -5.39, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 1.63, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -3.69}]}, "bone25": {"rotate": [{"angle": -0.66, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "angle": -5.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 1.63, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -0.66}]}, "bone26": {"rotate": [{"angle": 1.63, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.39, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.63}]}, "bone27": {"rotate": [{"angle": -4.22, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -5.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 1.63, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -4.22}]}, "bone28": {"rotate": [{"angle": -1.59, "curve": 0.338, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 0.2333, "angle": -3.69, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5333, "angle": -5.39, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 1.63, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -1.59}]}, "bone29": {"rotate": [{"angle": 1.17, "curve": 0.3, "c2": 0.21, "c3": 0.643, "c4": 0.58}, {"time": 0.2333, "angle": -0.66, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.8667, "angle": -5.39, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 1.63, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 1.17}]}, "bone30": {"rotate": [{"angle": 0.46, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": 1.63, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -5.39, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 0.46}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.29, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone11": {"rotate": [{"angle": -2.35, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.35}]}, "bone12": {"rotate": [{"angle": -5.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.94}]}, "bone17": {"rotate": [{"angle": -2.01, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -8.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -2.01}]}, "bone21": {"rotate": [{"angle": -5.58, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.3, "angle": -2.35, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -8.29, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -5.58}]}, "bone22": {"rotate": [{"angle": -8.24, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 0.3, "angle": -5.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -8.29, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": -8.24}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.2, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -23.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 52.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 103.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 84.48}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 11.86, "y": -31.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.74, "y": 42.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.74, "y": 57.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.74, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.7, "y": -124.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 3.7, "y": -99.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 3.7, "y": -124.49}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone33": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 45.03, "curve": "stepped"}, {"time": 0.3333, "angle": 45.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -24.05}]}, "bone34": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 30.81, "curve": "stepped"}, {"time": 0.3667, "angle": 30.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.76}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 73.21, "curve": "stepped"}, {"time": 0.3, "angle": 73.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 22.07}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.22, "curve": "stepped"}, {"time": 0.3, "angle": -15.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.67}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 20.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone45": {"rotate": [{"angle": 5.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 20.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6333, "angle": 5.77}]}, "bone46": {"rotate": [{"angle": 17.21, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 20.34, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.6333, "angle": 17.21}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 20.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "bone50": {"rotate": [{"angle": 5.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 20.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6333, "angle": 5.77}]}, "bone51": {"rotate": [{"angle": 17.21, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 20.34, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.6333, "angle": 17.21}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.73}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 16.4}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.98}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.81}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 49.39}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -45.22, "curve": "stepped"}, {"time": 0.3, "angle": -45.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -72.27}]}, "bone55": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 177.46}]}, "bone60": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 139.15}]}, "bone59": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 82.57}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 7.16, "y": 100.94, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 7.16, "y": 132.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 7.16, "y": 55.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 7.16, "y": 3.52}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 82.57}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2413, "x": 7.16, "y": 100.94, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "x": 7.16, "y": 124.74, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "x": 7.16, "y": 51.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 7.16, "y": 5.73}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.1667, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.12, "y": -8.93, "curve": "stepped"}, {"time": 0.2, "x": -1.12, "y": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone4": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone33": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone36": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 62, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone37": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -52.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 29.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 35.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -40.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -39.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -27.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 22.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone40": {"rotate": [{"angle": 14.09, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -27.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 22.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 14.09}]}, "bone41": {"rotate": [{"angle": 8.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 22.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -27.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 8.29}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone11": {"rotate": [{"angle": -3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -3.49}]}, "bone12": {"rotate": [{"angle": -8.81, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -12.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -8.81}]}}}, "run1": {"bones": {"bone60": {"translate": [{"x": 33.29, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -41.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -90.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -50.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 33.29}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -67.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -50.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"x": 0.48, "y": 17.65, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.79, "y": 55.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.48, "y": 17.65}]}, "bone55": {"translate": [{"x": -58.4, "y": -0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -34.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 85.88, "y": 0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -5.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -58.4, "y": -0.4}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 60.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -58.5}], "translate": [{"x": -1.86, "y": 44.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 16.3, "y": 58.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.7, "y": 37.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.86, "y": 44.51}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone2": {"rotate": [{"angle": -22.85}]}, "bone": {"rotate": [{"angle": -4.76}], "translate": [{"y": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 7.43}]}, "bone44": {"rotate": [{"angle": 47.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 47.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 47.09}], "translate": [{"x": -2.28, "y": 20.48}]}, "bone16": {"rotate": [{"angle": -16.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -21.34, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -5.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -16.17}]}, "bone17": {"rotate": [{"angle": -5.17, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -21.34, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -16.17, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -5.17}]}, "bone20": {"rotate": [{"angle": -16.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -21.34, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -5.17, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -16.17}]}, "bone19": {"rotate": [{"angle": -21.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -21.34}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone3": {"rotate": [{"angle": 5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.55}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.82}]}, "bone36": {"rotate": [{"angle": -15.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 17.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -15.28}]}, "bone37": {"rotate": [{"angle": 33.81, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 29.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 61.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 33.81}]}, "bone38": {"rotate": [{"angle": 16.21}]}, "bone35": {"rotate": [{"angle": 35.12}]}, "bone33": {"rotate": [{"angle": -5.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -69.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.06}]}, "bone34": {"rotate": [{"angle": 74.36, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 79.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 65.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 74.36}]}, "ljio3": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 13.97, "y": -7.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone15": {"rotate": [{"angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.85}]}, "bone49": {"rotate": [{"angle": 4.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 11.47, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 4.22}]}, "bone50": {"rotate": [{"angle": 7.71, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.0667, "angle": 3.49, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": -3.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 7.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 7.71}]}, "bone51": {"rotate": [{"angle": -5.51, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.0667, "angle": 0.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.78, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4333, "angle": 1.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -9.78, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -5.51}]}, "bone7": {"rotate": [{"angle": 29.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 17.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 29.97}]}, "b1one8": {"rotate": [{"angle": -3, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -12.37, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -3}]}, "b1one9": {"rotate": [{"angle": -7.82, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.37, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.82}]}, "bone10": {"rotate": [{"angle": -7.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.16, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.05}]}, "bone11": {"rotate": [{"angle": -11.16, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2, "angle": -4.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.16}]}, "bone12": {"rotate": [{"angle": -7.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -11.16, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2, "angle": -9.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -7.05}]}, "bone45": {"rotate": [{"angle": 1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 11.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -15.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -15.84, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 1.56}]}, "bone46": {"rotate": [{"angle": -12.26, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 11.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -15.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -15.84, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -12.26}]}}}, "run2": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 75.4}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone46": {"rotate": [{"angle": -12.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -1.35, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -38.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 28.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3, "angle": -38.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -12.03}]}, "bone45": {"rotate": [{"angle": -12.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 14.55, "curve": 0.347, "c2": 0.66, "c3": 0.68}, {"time": 0.1667, "angle": -23.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 14.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -12.4}]}, "bone44": {"rotate": [{"angle": 93.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 55.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 93.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 55.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 93.2}], "translate": [{"x": 1.8, "y": 34.1}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone7": {"rotate": [{"angle": 28.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 28.76}], "translate": [{"x": 9.54, "y": 5.79}], "scale": [{"y": 1.54}]}, "b1one8": {"rotate": [{"angle": 8.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2333, "angle": -3.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": 8.59}]}, "b1one9": {"rotate": [{"angle": 5.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2667, "angle": -3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 5.21}]}, "bone49": {"rotate": [{"angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.05}], "translate": [{"x": 9.31, "y": 10.06}]}, "bone50": {"rotate": [{"angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -10.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.77}]}, "bone51": {"rotate": [{"angle": -1.73, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 15.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 15.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.73}]}}}, "show_time": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 75.4}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone46": {"rotate": [{"angle": -27.04, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -12.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -40.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -12.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -40.99, "curve": 0.345, "c2": 0.39, "c3": 0.679, "c4": 0.72}, {"time": 0.3667, "angle": -27.04}]}, "bone45": {"rotate": [{"angle": -11.14, "curve": 0.347, "c2": 0.66, "c3": 0.68}, {"time": 0.0333, "angle": -7.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -35.47, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.2, "angle": -7.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3, "angle": -35.47, "curve": 0.354, "c2": 0.48, "c3": 0.688, "c4": 0.82}, {"time": 0.3667, "angle": -11.14}]}, "bone44": {"rotate": [{"angle": 67.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": 39.28, "curve": 0.324, "c2": 0.3, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "angle": 67.5, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2667, "angle": 39.28, "curve": 0.324, "c2": 0.3, "c3": 0.664, "c4": 0.66}, {"time": 0.3667, "angle": 67.5}], "translate": [{"x": 1.8, "y": 34.1}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone7": {"rotate": [{"angle": 28.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 14.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 28.76}], "translate": [{"x": 9.54, "y": 5.79}], "scale": [{"y": 1.54}]}, "b1one8": {"rotate": [{"angle": 8.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2333, "angle": -3.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": 8.59}]}, "b1one9": {"rotate": [{"angle": 5.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 10.45, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2667, "angle": -3.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 5.21}]}, "bone49": {"rotate": [{"angle": -0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 23.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 23.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.43}], "translate": [{"x": 9.31, "y": 10.06}]}, "bone50": {"rotate": [{"angle": 2.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -3.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": 20.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 20.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 2.94}]}, "bone51": {"rotate": [{"angle": 7.43, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -9.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 14.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 14.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 7.43}]}}}}}