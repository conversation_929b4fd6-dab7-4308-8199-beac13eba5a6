import { _decorator, Component, EditBox, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

@ccclass("TopConfirm")
export class TopConfirm extends BaseCtrl {
  edit_num1: EditBox;

  onLoad() {
    this.edit_num1 = this.getNode("edit_num1").getComponent(EditBox);
  }

  on_click_btn_yes() {
    if (this.edit_num1.string) {
      this.closeBack({ code: 200, param1: this.edit_num1.string });
    }
  }
}
