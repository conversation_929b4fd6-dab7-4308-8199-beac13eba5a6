import TimeTicker from "./TimeTicker";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);

export default class TickerMgr {
  public static readonly instance: TickerMgr = new TickerMgr();
  private _tickerList: Array<TimeTicker> = [];
  private _tickerLength: number = 0;
  public get length(): number {
    return this._tickerLength;
  }
  public doTick(dtime: number): void {
    for (var i: number = 0; i < this._tickerLength; i += 1) {
      this._tickerList[i].doTick(dtime);
    }
  }
  public addTicker(ticker: TimeTicker): void {
    this._tickerList[this._tickerLength] = ticker;
    this._tickerLength += 1;
  }
  public removeTicker(ticker: TimeTicker): void {
    var index: number = this._tickerList.indexOf(ticker);
    if (index != -1) {
      this._tickerList.splice(index, 1);
      this._tickerLength -= 1;
    }
  }

  //实用的静态方法
  public static sTickerId: number = 0;
  public static sTickerMap: Object = Object.create(null);
  /**
   * 设置一个指定时间后回调的定时器
   *
   * @param delay:延迟时间
   * @param callback:回调
   */
  public static setTimeout(delay: number, callback: Function, ...args): number {
    var ticker: TimeTicker = new TimeTicker(delay, 1, null, onTimeout);
    ticker.setTickerMgr(TickerMgr.instance);
    ticker.start();
    var id: number = ++this.sTickerId;
    this.sTickerMap[id] = ticker;
    return id;
    function onTimeout(): void {
      clearTimeout(id);
      callback.apply(null, args);
    }
  }
  /**
   * 移除一个定时器
   *
   * @param id:由setTimeout返回的id
   */
  public static clearTimeout(id: number): void {
    if (id == 0) {
      return;
    }
    var ticker: TimeTicker = this.sTickerMap[id];
    if (ticker) {
      ticker.dispose();
      delete this.sTickerMap[id];
    }
  }
  /**
   * 设置一个定时器，间隔delay执行一次
   *
   * @param delay:间隔时间
   * @param callback:回调
   * @param immediately:是否立即回调一次
   *
   */
  public static setInterval(delay: number, callback: Function, immediately: boolean, ...args): number {
    var ticker: TimeTicker = new TimeTicker(delay, 0, callback);
    ticker.setTickerMgr(TickerMgr.instance);
    if (immediately) callback();
    ticker.start();
    ticker.updateParams = args;
    var id: number = ++this.sTickerId;
    this.sTickerMap[id] = ticker;
    return id;
  }
  /**
   * 移除一个定时器
   *
   * @param id:由setInterval返回的id
   */
  public static clearInterval(id: number): void {
    if (id == 0) {
      return;
    }
    var ticker: TimeTicker = this.sTickerMap[id];
    if (ticker) {
      ticker.dispose();
      delete this.sTickerMap[id];
    }
  }
  /**
   * 设置一个指定次数的定时器
   *
   * @param delay:间隔时间
   * @param repeatCount:次数
   * @param callback:回调
   * @param complete:完成后的回调
   */
  public static setTicker(
    delay: number,
    repeatCount: number,
    callback: Function,
    compFunc: Function = null,
    updateParams: any = null,
    completeParams: any = null
  ): number {
    var ticker: TimeTicker = new TimeTicker(delay, repeatCount, callback, onComplete);
    ticker.setTickerMgr(TickerMgr.instance);
    ticker.start();
    ticker.updateParams = updateParams;
    var id: number = ++this.sTickerId;
    this.sTickerMap[id] = ticker;
    return id;
    function onComplete(): void {
      if (compFunc != null) {
        compFunc.apply(null, completeParams);
      }
      TickerMgr.clearTicker(id);
    }
  }
  /**
   * 移除一个定时器
   *
   * @param id:由setTicker返回的id
   */
  public static clearTicker(id: number): void {
    if (id == 0) {
      return;
    }
    var ticker: TimeTicker = this.sTickerMap[id];
    if (ticker) {
      ticker.dispose();
      delete this.sTickerMap[id];
    }
  }
  /**
   * 重置定时器
   * @param id
   *
   */
  public static resetTimer(id: number): void {
    if (id == 0) {
      return;
    }
    var ticker: TimeTicker = this.sTickerMap[id];
    if (ticker) {
      ticker.reset();
    }
  }
  /**
   * 重置一个定时器的延时
   *
   * @param id:ID
   * @param delay:新时间
   */
  public static resetTime(id: number, delay_: number): void {
    if (id == 0) {
      return;
    }
    var ticker: TimeTicker = this.sTickerMap[id];
    if (ticker) {
      ticker.delay = delay_;
    }
  }

  /**立刻执行定时器的事件 */
  public static carryTicker(id: number) {
    if (!this.sTickerMap) {
      return;
    }
    let ticker = this.sTickerMap[id];
    ticker.delay = 0;
  }

  /**
   * 移除一个定时器
   *
   * @param id:由setTicker返回的id
   */
  public static clearAllTicker(): void {
    const keys: string[] = Object.keys(this.sTickerMap);
    log.log("ticker count: " + keys.length);
    for (let key of keys) {
      let id = parseInt(key);
      var ticker: TimeTicker = this.sTickerMap[id];
      if (ticker) {
        ticker.dispose();
        delete this.sTickerMap[id];
      }
    }
    log.log("ticker count: " + Object.keys(this.sTickerMap));
  }
}
