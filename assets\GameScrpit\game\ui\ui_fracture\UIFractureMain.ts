import { _decorator, EventTouch } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { FractureModule } from "../../../module/fracture/FractureModule";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
import { ActivityModule } from "../../../module/activity/ActivityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { FractureFloorMap } from "../../../module/fracture/FractureConstant";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { UIFractureSearch } from "./UIFractureSearch";
import { UIFractureTipAttr } from "./UIFractureTipAttr";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
/**
 * ivan_huang
 * Mon May 19 2025 20:02:34 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_fracture/UIFractureMain.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIFractureMain")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureMain",
  nextHop: [
    {
      via: "btn_tab1",
      des: UIFractureSearch,
    },
  ],
  exit: "dialog_close",
})
export class UIFractureMain extends BaseCtrl {
  public playShowAni: boolean = true;
  // @property(Node)
  // btn_close: Node = null;
  //=================================================
  public init(args: any): void {
    super.init(args);
    let activity = ActivityModule.data.allActivityConfig[11501];
    log.log("活动数据", activity);
  }

  protected start(): void {
    super.start();
    this.getNode("btn_tab1").getComponent(FmButton).btnEnable = true;
    this.getNode("btn_tab2").getComponent(FmButton).btnEnable = FractureModule.service.getUnlockedDifficulty() >= 2;
    this.getNode("btn_tab3").getComponent(FmButton).btnEnable = FractureModule.service.getUnlockedDifficulty() >= 3;
    this.getNode("btn_tab1").getComponent(FmButton).selected = FractureModule.service.getSelectedDifficulty() == 1;
    this.getNode("btn_tab2").getComponent(FmButton).selected = FractureModule.service.getSelectedDifficulty() == 2;
    this.getNode("btn_tab3").getComponent(FmButton).selected = FractureModule.service.getSelectedDifficulty() == 3;
    let isFinish1 =
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[1][0]] == 0 &&
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[1][1]] == 0;
    let isFinish2 =
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[2][0]] == 0 &&
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[2][1]] == 0;
    let isFinish3 =
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[3][0]] == 0 &&
      FractureModule.data.fractureData.remainFloorCountMap[FractureFloorMap[3][1]] == 0;
    this.getNode("btn_tab1").getChildByName("node_yitongguan").active = isFinish1;
    this.getNode("btn_tab2").getChildByName("node_yitongguan").active = isFinish2;
    this.getNode("btn_tab3").getChildByName("node_yitongguan").active = isFinish3;
  }

  private on_click_btn_sklx_xiezhu_test(e: EventTouch) {
    //
    // ClubModule.api.allAssistList((data) => {
    //   log.log("allAssistList", data);
    // });
    // ClubModule.api.doAssist
  }

  private onClickClose() {
    this.closeBack();
  }
  private on_click_btn_tab1(e: EventTouch) {
    if (e.target.getComponent(FmButton).btnEnable == false) return;
    if (e.target.getComponent(FmButton).selected) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureSearch);
    } else {
      FractureModule.api.travelFloor(
        FractureFloorMap[1][0],
        () => {
          RouteManager.uiRouteCtrl.showRoute(UIFractureSearch);
          this.getNode("btn_tab1").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 1;
          this.getNode("btn_tab2").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 2;
          this.getNode("btn_tab3").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 3;
        },
        () => {
          return false;
        }
      );
    }
  }
  private on_click_btn_tab2(e: EventTouch) {
    if (e.target.getComponent(FmButton).btnEnable == false) return;
    if (e.target.getComponent(FmButton).selected) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureSearch);
    } else {
      FractureModule.api.travelFloor(
        FractureFloorMap[2][0],
        () => {
          RouteManager.uiRouteCtrl.showRoute(UIFractureSearch);
          this.getNode("btn_tab1").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 1;
          this.getNode("btn_tab2").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 2;
          this.getNode("btn_tab3").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 3;
        },
        () => {
          return false;
        }
      );
    }
  }
  private on_click_btn_tab3(e: EventTouch) {
    if (e.target.getComponent(FmButton).btnEnable == false) return;
    if (e.target.getComponent(FmButton).selected) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureSearch);
    } else {
      FractureModule.api.travelFloor(
        FractureFloorMap[3][0],
        () => {
          RouteManager.uiRouteCtrl.showRoute(UIFractureSearch);
          this.getNode("btn_tab1").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 1;
          this.getNode("btn_tab2").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 2;
          this.getNode("btn_tab3").getComponent(FmButton).selected =
            FractureModule.service.getSelectedDifficulty() == 3;
        },
        () => {
          return false;
        }
      );
    }
  }

  private on_click_btn_test(e: EventTouch) {
    // FractureModule.api.travelFloor(FractureFloorMap[1][0], () => {
    // RouteManager.uiRouteCtrl.showRoute(FractureRoute.route.UIFractureAnswerSuccess, {
    //   payload: UIFractureAnswerSuccess.Param.create(5501, [1072, 1], "rewardMsg1"),
    // });
    // MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
    // RouteManager.uiRouteCtrl.showRoute(FractureRoute.route.UIFractureDrawPreview);
    // let path = RouteTableManager.instance.findPath(
    //   "prefab/ui/UIFractureMain",
    //   FractureRoute.route.UIFractureDrawPreview.url,
    //   null,
    //   "baoxiang"
    // );
    let path = RouteManager.uiRouteCtrl.getRoutePath("prefab/ui/UIFractureDrawPreview", "baoxiang");
    log.log(path);
  }
}
