import { _decorator, Component, Node } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("FarmRewardWorker")
export class FarmRewardWorker extends BaseCtrl {
  start() {
    super.start();
    TipsMgr.setEnableTouch(false, 0.3);
  }

  on_click_btn_close() {
    this.closeBack();
  }
}
