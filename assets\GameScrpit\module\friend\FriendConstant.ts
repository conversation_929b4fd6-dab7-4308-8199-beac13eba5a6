import { BundleEnum } from "../../game/bundleEnum/BundleEnum";

export enum FriendSort {
  DEFAULT = 1,
  TALENT,
  FRIENDSHIP,
}
export enum FriendType {
  ALL,
  PERSON,
  GOD,
  YAO,
  MING,
  WU,
}
export const FriendBellesColorHorizontalURL = `${BundleEnum.BUNDLE_COMMON_FRIENDICON}?patterns/friendBellesIcon2`;
export const FriendBellesColorHorizontal = {
  1: "bg_meiminglu_1",
  2: "bg_meiminglu_2",
  3: "bg_meiminglu_3",
  4: "bg_meiminglu_4",
  5: "bg_meiminglu_5",
  6: "bg_meiminglu_6",
  7: "bg_meiminglu_7",
  8: "bg_meiminglu_8",
  9: "bg_meiminglu_9",
  10: "bg_meiminglu_10",
  11: "bg_meiminglu_11",
  12: "bg_meiminglu_12",
};
export const FriendBellesColorVerticalURL = `${BundleEnum.BUNDLE_COMMON_FRIENDICON}?patterns/friendBellesIcon1`;
export const FriendBellesColorVertical = {
  1: "bg_meiming<PERSON>hao_1",
  2: "bg_meimingchenghao_2",
  3: "bg_meiming<PERSON>hao_3",
  4: "bg_meimingchenghao_4",
  5: "bg_meimingchenghao_5",
  6: "bg_meiming<PERSON>hao_6",
  7: "bg_meimingchenghao_7",
  8: "bg_meimingchenghao_8",
  9: "bg_meimingchenghao_9",
  10: "bg_meimingchenghao_10",
  11: "bg_meimingchenghao_11",
  12: "bg_meimingchenghao_12",
};
export const FriendRaceColorFG = {
  1: "XY_bg_pinzhi1",
  2: "XY_bg_pinzhi2",
  3: "XY_bg_pinzhi3",
  4: "XY_bg_pinzhi4",
  5: "XY_bg_pinzhi5",
};
export const FriendRaceColorBG = {
  1: "XY_bg_pinzhi1_1",
  2: "XY_bg_pinzhi2_1",
  3: "XY_bg_pinzhi3_1",
  4: "XY_bg_pinzhi4_1",
  5: "XY_bg_pinzhi5_1",
};
export const FriendRaceIcon = {
  1: "icon_judianjineng_4",
  2: "icon_judianjineng_5",
  3: "icon_judianjineng_2",
  4: "icon_judianjineng_1",
  5: "icon_judianjineng_3",
};
