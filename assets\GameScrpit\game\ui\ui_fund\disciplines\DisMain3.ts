import { _decorator, Component, Label, Node, Tween } from "cc";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { DisciplinesModule } from "db://assets/GameScrpit/module/disciplines/DisciplinesModule";
import { ActivityTakeRequest, DayTaskMessage } from "../../../net/protocol/Activity";
import { MainTaskModule } from "db://assets/GameScrpit/module/mainTask/MainTaskModule";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { GoodsRouteName } from "db://assets/GameScrpit/module/goods/GoodsRoute";
import GameHttpApi from "../../../httpNet/GameHttpApi";

import { ListView } from "../../../common/ListView";
import { DisTaskAdapter } from "../adapter_disciplines/DisTaskAdapter";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { DisciplinesAudioName } from "db://assets/GameScrpit/module/disciplines/DisciplinesConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "db://assets/GameScrpit/ext_guide/GuideMgr";
import { AchieveVO, DayTaskVO, LeaderFundVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("DisMain3")
export class DisMain3 extends Component {
  private _achieveVO: AchieveVO = null;
  private _taskVO: DayTaskVO = null;
  private _dayTask: DayTaskMessage = null;
  private _achievementId1: number = null;
  private _disciplinesdb: LeaderFundVO;

  private _tickMgrIdList: number[] = [];

  @property(Node)
  btn_main3_goumai: Node = null;

  @property(Node)
  btn_yigoumai3: Node = null;

  @property(Node)
  lbl_main3_money: Node = null;

  @property(Node)
  lbl_renwu_bar: Node = null;

  @property(Node)
  list_view_level: Node = null;

  @property(Node)
  renwu_item: Node = null;

  @property(Node)
  award_item: Node = null;

  protected onEnable(): void {}

  protected onDisable(): void {}

  protected onLoad(): void {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_FUND_DAILY_TASK_UPDATE, this.initMain3, this);
  }

  start() {
    this.initMain3();
  }

  private async initMain3() {
    this._disciplinesdb = await DisciplinesModule.data.getdb();
    this._taskVO = await DisciplinesModule.data.getDisTaskVO();
    this._dayTask = DisciplinesModule.data.getDayTask();

    this._achieveVO = await DisciplinesModule.data.getDisFundOV();
    this._achievementId1 = this._achieveVO.id;
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);

    this.btn_main3_goumai.active = !AchieveData.paid;
    this.btn_yigoumai3.active = AchieveData.paid;

    this.lbl_main3_money.getComponent(Label).string = (this._achieveVO.price % 10000) + "元";
    this.loadListMain3();
  }

  private async loadListMain3() {
    let takeList = this._taskVO.takeList;
    let rewardList = this._taskVO.rewardList;
    this.lbl_renwu_bar.getComponent(Label).string = this._dayTask.takeList.length + "/" + takeList.length;
    let list0 = [];
    let list1 = [];
    let list2 = [];
    for (let i = 0; i < takeList.length; i++) {
      let obj = {
        index: i,
        takeList: takeList[i],
        rewardList: rewardList[i],
        dayTask: this._dayTask.takeList[i],
        dayVal: this._dayTask.targetValList[i],
        state: -1,
      };
      //0是进行中
      //1是可领取
      //2是已领取
      /**按钮的显示状态，是否已经领取过 */
      if (this._dayTask.takeList.indexOf(i) != -1) {
        obj.state = 2;
        list2.push(obj);
      } else {
        /**按钮的显示状态，是还在进行还是达到了可以领的进度 */
        if (obj.takeList[1] > obj.dayVal) {
          /**还在进行中 */
          obj.state = 0;
          list0.push(obj);
        } else {
          obj.state = 1;
          list1.push(obj);
        }
      }
    }
    let list = list1.concat(list0, list2);

    let adapter = new DisTaskAdapter(this.renwu_item);
    this.list_view_level.getComponent(ListView).setAdapter(adapter);
    adapter.setData(list);
  }

  private on_click_btn_renwu_get(event) {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击领取按钮);
    let itemIndex = event.target.parent["objInfo"].index;
    let param: ActivityTakeRequest = {
      activityId: this._disciplinesdb.id,
      index: itemIndex,
      takeAll: false,
    };
    DisciplinesModule.api.takeDayTaskReward(param, (rs) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
      this.initMain3();
    });
  }
  private on_click_btn_renwu_go(event) {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击前往按钮);
    let itemIndex = event.target.parent["objInfo"].index;
    let takeList = this._taskVO.takeList;
    let info = MainTaskModule.data.getConfigTask(takeList[itemIndex][0]);
    GuideMgr.startGuide({ stepId: info.guideType });
  }
  private on_click_btn_main3_goumai() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let data = {
      goodsId: this._achieveVO.id,
      goodsType: this._disciplinesdb.buyType,
      playerId: PlayerModule.data.playerId,
      orderAmount: (this._achieveVO.price % 10000) % 10000,
      goodsName: this._disciplinesdb.name,
      platformType: "TEST",
    };

    GameHttpApi.pay(data).then((resp: any) => {
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  update(deltaTime: number) {}

  protected onDestroy(): void {
    Tween.stopAllByTarget(this.node);
    MsgMgr.off(MsgEnum.ON_ACTIVITY_FUND_DAILY_TASK_UPDATE, this.initMain3, this);
  }
}
