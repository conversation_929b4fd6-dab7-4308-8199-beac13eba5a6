import { _decorator, instantiate, isValid, Label, Node, Prefab, sp, UIOpacity, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { CityCtrl } from "../ui_gameMap/CityCtrl";
import ToolExt from "../../common/ToolExt";
const { ccclass, property } = _decorator;

@ccclass("UICityReel")
export class UICityReel extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UICityReel`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _cityId: number = 0;

  public init(args: any): void {
    super.init(args);
    this._cityId = args.curCityId;
  }

  protected async onEvtShow() {
    let db = JsonMgr.instance.jsonList.c_build;
    let info = db[this._cityId];
    let unlockLvList = info.unlockLvList;

    this.getNode("lbl_main_title").getComponent(Label).string = info.name;

    // 遍历不同等级
    for (let i = 0; i < unlockLvList.length; i++) {
      let level = unlockLvList[i];
      // 创建 item 节点
      let itemNode = ToolExt.clone(this.getNode("item"), this);
      itemNode.active = true;
      // 动态加载预制体
      let pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_GAME_MAP, `prefab/building/city_${this._cityId}`);
      if (pb) {
        let nodeCity: Node = instantiate(pb);
        nodeCity.getComponent(CityCtrl).setOnlyShow({
          hideUI: true,
          cityLevel: level,
        });
        nodeCity.setPosition(0, 0);
        itemNode["city_point"].addChild(nodeCity);
        nodeCity.walk((val) => {
          val.layer = this.node.layer;
        });

        this.getNode("content").addChild(itemNode);

        itemNode["city_name_main"].getComponent(Label).string = info.name;
        itemNode["city_lv_main"].getComponent(Label).string = `${level}级`;
      }
    }
  }
}
