import { _decorator, Component, instantiate, Label, Node, sp, Sprite, tween, UIOpacity, Vec3 } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { DialogZero } from "../../GameDefine";
import { FriendModule } from "../../../module/friend/FriendModule";
import ResMgr from "../../../lib/common/ResMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import FmUtils from "../../../lib/utils/FmUtils";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ToolExt from "../../common/ToolExt";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Wed Jun 26 2024 20:48:15 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendHeartTalk.ts
 *
 */

@ccclass("UIFriendHeartTalk")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRIEND,
  url: "prefab/ui/UIFriendHeartTalk",
  nextHop: [],
  exit: "dialog_close",
  transparent: true,
})
export class UIFriendHeartTalk extends BaseCtrl {
  private _valueAdd: Label;
  private _addNums: number;

  private _friendId: number;
  private _time: number = 0;

  //=================================================
  public init(args: RouteShowArgs): void {
    super.init(args);
    this._friendId = args.payload.friendId;
    this._addNums = args.payload.chatAdd;
  }
  public tick(dt: any): void {
    if (this._time > 1) {
      return;
    }
    this._time += dt;
    let friend = FriendModule.config.getFriendById(this._friendId);
    if (this._time > 0.1 && this.getNode("chat_content").getComponent(Label).string.length < friend.chat.length) {
      let index = this.getNode("chat_content").getComponent(Label).string.length;
      this.getNode("chat_content").getComponent(Label).string += friend.chat[index];
      this._time = 0;
    }
  }
  protected start(): void {
    super.start();
    this.getNode("chat_content").getComponent(Label).string = "";
    this._valueAdd = this.getNode("value_add").getComponent(Label);
    this._valueAdd.string = `+${this._addNums}`;
    let friendMessage = FriendModule.data.getFriendMessage(this._friendId);
    let friend = FriendModule.config.getFriendById(this._friendId);
    let friendImg = this.getNode("bg_friend_img");

    this.getNode("sk_hudong")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "hdcg") {
          this.getNode("btn_avatar1").active = false;
          this.getNode("btn_avatar2").active = false;
          this.closeBack();
        }
      });

    let data = ToolExt.newPlayerBaseMessage();
    tween(this.getNode("btn_avatar1").getComponent(UIOpacity)).to(0.5, { opacity: 255 }).start();
    tween(this.getNode("btn_avatar2").getComponent(UIOpacity)).to(0.5, { opacity: 255 }).start();

    data.avatarList = PlayerModule.data.getMyAvatarList();
    data.vipLevel = PlayerModule.data.getPlayerInfo().vipLevel;
    FmUtils.setHeaderNode(this.getNode("btn_avatar1"), data);

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_ITEM,
      `autoItem/item_fr_${friend.id}`,
      this.getNode("bg_friend_avatar").getComponent(Sprite)
    );

    ResMgr.setNodePrefab(BundleEnum.BUNDLE_COMMON_FRIEND, `prefab/friend_${friend.id}`, friendImg, (item: Node) => {
      friendImg.destroyAllChildren();
    });
    this.getNode("friend_name").getComponent(Label).string = friend.name;
    tween(this.getNode("add"))
      .hide()
      .delay(0.6)
      .show()
      .by(
        1.0,
        { position: new Vec3(0, 100, 0) },
        {
          easing: "sineOut",
          onUpdate: (target, ratio) => {
            this.getNode("add").getComponent(UIOpacity).opacity = 255 * this.transform(ratio);
          },
        }
      )
      .start();
  }
  private transform(ratio) {
    if (ratio < 0.8) {
      return 1;
    }
    return 1 - (ratio - 0.8) / 0.2;
  }
  private on_click_background() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let friend = FriendModule.config.getFriendById(this._friendId);
    if (friend.chat.length <= this.getNode("chat_content").getComponent(Label).string.length) {
      this.closeBack();
      return;
    }
    this.getNode("chat_content").getComponent(Label).string = friend.chat;
  }
}
