import { _decorator, instantiate, Label, math, Node, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ClubModule } from "../../../../module/club/ClubModule";
import { ClubJoinResponse, ClubMessage } from "../../../net/protocol/Club";
import TipMgr from "../../../../lib/tips/TipMgr";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { Net_Code } from "../../../mgr/ApiHandler";
import ToolExt from "../../../common/ToolExt";
import { ClubRouteItem } from "../../../../module/club/ClubRoute";
import { UIClubAvatar } from "../UIClubAvatar";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("ClubListViewHolder")
export class ClubListViewHolder extends ViewHolder {
  @property(Label)
  club_name: Label;
  @property(Label)
  club_members: Label;
  @property(Node)
  btn_apply: Node;
  @property(Node)
  icon_applyed: Node;
  @property(Label)
  club_level: Label;
  @property(Label)
  apply_condition;
  @property(Node)
  clubAvatar: Node;

  _data: ClubMessage;
  public init() {
    this.btn_apply.on(Node.EventType.TOUCH_END, () => {
      log.log(this._data);

      let leftTime = (ClubModule.data?.clubFormMessage?.joinColdStamp ?? 0) - new Date().getTime();
      if (leftTime > 0) {
        TipMgr.showTip("冷却时间未到，不可以加入或创建新的战盟");
      } else {
        ClubModule.api.joinClub(
          this._data.id,
          (data: ClubJoinResponse) => {
            if (data.code == 2) {
              TipMgr.showTip("申请被拒绝");
              return;
            }
            TipMgr.showTip("申请成功");
            if (data.clubMessage) {
              UIMgr.instance.back();
              UIMgr.instance.showPage(ClubRouteItem.UIClubMain);
            }
            log.log(data);
          },
          (errorCode: Net_Code, msg: string[], data: any) => {
            // TipMgr.showTip(msg);
            log.log(msg);
            return false;
          }
        );
      }
    });
  }
  public updateData(data: ClubMessage) {
    if (this.position % 2 == 0) {
      this.node.getComponent(Sprite).color = math.color("#d7f1ff");
    } else {
      this.node.getComponent(Sprite).color = math.color("#bedff6");
    }
    this._data = data;
    let activeVal = 0;
    data.memberList.forEach((item) => {
      activeVal += item.activeVal;
    });
    this.getNode("lbl_active").getComponent(Label).string = activeVal + "";
    let applyDate = ClubModule.data.clubFormMessage.applyMap[this._data.id];
    if (applyDate && applyDate - new Date().getTime() > 0) {
      this.icon_applyed.active = true;
      this.btn_apply.active = false;
    } else {
      this.icon_applyed.active = false;
      this.btn_apply.active = true;
    }
    this.club_level.string = `LV.${this._data.level}`;
    this.club_name.string = this._data.name;
    let maxMembers = ClubModule.config.getUnionMaxNumber(this._data.level);
    this.club_members.string = `${this._data.memberList.length}/${maxMembers}`;
    if (
      !this._data.auditOption.manual &&
      this._data.auditOption.levelLowerLimit == 0 &&
      this._data.auditOption.powerLowerLimit == 0
    ) {
      this.apply_condition.string = "无";
    } else {
      this.apply_condition.string = `需审核`;
    }
    this.clubAvatar.getComponent(UIClubAvatar).setAvatar(this._data.avatar);
  }
}
export class ClubListAdapter extends ListAdapter {
  private item: Node;
  private data: ClubMessage[];
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: ClubMessage[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }

  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(ClubListViewHolder).init();
    return item;
  }
  onBindData(view: Node, position: number): void {
    view.getComponent(ClubListViewHolder).updateData(this.data[position]);
  }
  getCount(): number {
    return this.data.length;
  }
}
