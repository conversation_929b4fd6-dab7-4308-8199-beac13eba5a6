{"skeleton": {"hash": "sfs+BPwE6ilimrsU/P2zwXN1Gis=", "spine": "3.8.75", "x": -66.24, "y": -18.74, "width": 135.88, "height": 146.92, "images": "./images/", "audio": "D:/spine导出/弟子spine/鸡弟子"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 165.65, "rotation": 0.28, "scaleX": 0.365, "scaleY": 0.365}, {"name": "bone2", "parent": "bone", "length": 113.83, "rotation": -1.74, "x": -18.1, "y": 93.67}, {"name": "bone3", "parent": "bone2", "length": 47.58, "rotation": 73.76, "x": -24.74, "y": 5.64}, {"name": "bone4", "parent": "bone3", "length": 40.76, "rotation": 6.79, "x": 47.58}, {"name": "bone5", "parent": "bone4", "length": 27.64, "rotation": 16.92, "x": 40.76}, {"name": "bone6", "parent": "bone4", "x": 39.35, "y": -66.56}, {"name": "bone7", "parent": "bone4", "x": 23.15, "y": 37.85}, {"name": "bone8", "parent": "bone6", "length": 54.66, "rotation": -127.67, "x": -0.12, "y": -17.21}, {"name": "bone9", "parent": "bone8", "length": 50.67, "rotation": -43.61, "x": 54.66}, {"name": "bone10", "parent": "bone9", "length": 37.14, "rotation": -27.38, "x": 50.67}, {"name": "bone11", "parent": "bone7", "length": 44.89, "rotation": 166.82, "x": -6.58, "y": 6.1}, {"name": "bone12", "parent": "bone11", "length": 41.37, "rotation": -16.66, "x": 44.89}, {"name": "bone13", "parent": "bone12", "length": 41.94, "rotation": -29.78, "x": 41.37}, {"name": "bone15", "parent": "bone2", "x": 58.46, "y": -36.27}, {"name": "bone16", "parent": "bone2", "length": 35.19, "rotation": -90.3, "x": -30.2, "y": -37.09}, {"name": "bone17", "parent": "bone16", "length": 17.26, "rotation": -1.82, "x": 35.19}, {"name": "bone18", "parent": "bone17", "length": 18.06, "rotation": -65.47, "x": 17.24, "y": 0.36}, {"name": "bone20", "parent": "bone2", "length": 46.63, "rotation": -69.83, "x": 62.17, "y": -32.22}, {"name": "bone21", "parent": "bone20", "length": 21.87, "rotation": -2.64, "x": 46.63}, {"name": "bone22", "parent": "bone21", "length": 17.41, "rotation": 43.16, "x": 23.79, "y": 5.38}, {"name": "bone24", "parent": "bone5", "length": 23.12, "rotation": -21.26, "x": 115.21, "y": 11.86}, {"name": "bone25", "parent": "bone5", "length": 26.4, "rotation": -53.5, "x": 115.02, "y": -17.06}, {"name": "bone26", "parent": "bone5", "length": 25.21, "rotation": -70.27, "x": 95.17, "y": -46.78}, {"name": "bone27", "parent": "bone5", "length": 16.86, "rotation": 21.17, "x": 89.12, "y": 31.33}, {"name": "bone28", "parent": "bone5", "x": 13.82, "y": 28.5, "color": "abe323ff"}, {"name": "bone29", "parent": "bone5", "x": 79.61, "y": 11.48}, {"name": "bone30", "parent": "bone5", "length": 11.66, "rotation": -126.54, "x": 14.73, "y": -56.27}, {"name": "bone31", "parent": "bone30", "length": 13.69, "rotation": 34.85, "x": 11.66}, {"name": "bone32", "parent": "bone5", "length": 12.5, "rotation": -153.63, "x": -3.2, "y": -15.02}, {"name": "bone33", "parent": "bone32", "length": 15.3, "rotation": 11.26, "x": 12.5}, {"name": "bone34", "parent": "bone5", "length": 17.53, "rotation": 163.84, "x": -1.82, "y": 32.48}, {"name": "bone35", "parent": "bone34", "length": 20.87, "rotation": 8.03, "x": 17.44, "y": 0.51}, {"name": "bone36", "parent": "bone5", "length": 14.52, "rotation": 148.79, "x": 1.95, "y": 48.66}, {"name": "bone37", "parent": "bone36", "length": 14.81, "rotation": -14.86, "x": 14.41, "y": 0.23}, {"name": "bone14", "parent": "bone2", "length": 21.8, "rotation": 1.98, "x": -11.49, "y": -22.02}, {"name": "bone38", "parent": "bone14", "length": 24.81, "rotation": -29.98, "x": 21.8}, {"name": "bone39", "parent": "bone38", "length": 24.74, "rotation": -9.64, "x": 24.71, "y": 0.17}, {"name": "bone40", "parent": "bone14", "length": 17.6, "rotation": -36.74, "x": 3.95, "y": -5.04}, {"name": "bone41", "parent": "bone40", "length": 20.48, "rotation": -21.95, "x": 17.76, "y": 0.12}, {"name": "bone42", "parent": "bone41", "length": 15.66, "rotation": -6.9, "x": 20.58, "y": 0.17}, {"name": "bone43", "parent": "bone2", "length": 13.09, "rotation": -139.75, "x": -43.28, "y": -15.02}, {"name": "bone44", "parent": "bone43", "length": 17.71, "rotation": 25.8, "x": 13.09}, {"name": "bone45", "parent": "bone44", "length": 15.12, "rotation": -2.17, "x": 17.71}, {"name": "bone46", "parent": "bone", "length": 84.49, "rotation": 179.49, "x": -52.68, "y": 3.13}, {"name": "target1", "parent": "bone46", "rotation": -179.78, "x": -0.68, "y": -2.04, "color": "ff3f00ff"}, {"name": "target2", "parent": "bone46", "rotation": -179.78, "x": 16.39, "y": 5.81, "color": "ff3f00ff"}, {"name": "bone47", "parent": "bone", "length": 70.59, "rotation": 179.21, "x": 62.79, "y": -4.79}, {"name": "target3", "parent": "bone47", "rotation": -179.49, "x": -0.61, "y": 0.92, "color": "ff3f00ff"}, {"name": "target4", "parent": "bone47", "rotation": -179.49, "x": -22.98, "y": 8.33, "color": "ff3f00ff"}], "slots": [{"name": "sd", "bone": "root", "color": "ffffff87", "attachment": "sd"}, {"name": "j3", "bone": "bone16", "attachment": "j3"}, {"name": "j4", "bone": "bone20", "attachment": "j4"}, {"name": "j1", "bone": "bone", "attachment": "j1"}, {"name": "j2", "bone": "bone", "attachment": "j2"}, {"name": "c112", "bone": "bone11", "attachment": "c111"}, {"name": "b222", "bone": "bone2", "attachment": "b222"}, {"name": "m2", "bone": "bone", "attachment": "m2"}, {"name": "m1", "bone": "bone", "attachment": "m1"}, {"name": "tou1", "bone": "bone5"}, {"name": "tou22", "bone": "bone5", "attachment": "tou22"}, {"name": "c222", "bone": "bone8", "attachment": "c222"}, {"name": "c111", "bone": "bone11"}], "ik": [{"name": "target1", "bones": ["bone16", "bone17"], "target": "target1"}, {"name": "target2", "order": 3, "bones": ["bone17", "bone18"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 1, "bones": ["bone20", "bone21"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 2, "bones": ["bone21", "bone22"], "target": "target4", "stretch": true}], "transform": [{"name": "face", "order": 4, "bones": ["bone29"], "target": "bone28", "x": 65.79, "y": -17.02, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "s1", "order": 5, "bones": ["bone6"], "target": "bone28", "rotation": -16.92, "x": -34.54, "y": -91.78, "shearY": 360, "rotateMix": -0.199, "translateMix": -0.199, "scaleMix": -0.199, "shearMix": -0.199}, {"name": "s2", "order": 6, "bones": ["bone7"], "target": "bone28", "rotation": -16.92, "x": -19.65, "y": 12.84, "shearY": 360, "rotateMix": -0.199, "translateMix": -0.199, "scaleMix": -0.199, "shearMix": -0.199}, {"name": "ss1", "order": 7, "bones": ["bone14"], "target": "bone28", "rotation": -95.48, "x": -125, "y": -11, "shearY": -13.2, "rotateMix": 0.328, "translateMix": 0.328, "scaleMix": 0.328, "shearMix": 0.328}, {"name": "ss2", "order": 8, "bones": ["bone43"], "target": "bone28", "rotation": 122.79, "x": -114.45, "y": 23.55, "rotateMix": 0.247, "translateMix": 0.247, "scaleMix": 0.247, "shearMix": 0.247}], "skins": [{"name": "default", "attachments": {"j1": {"j1": {"type": "mesh", "uvs": [0.53931, 0.13502, 0.54213, 0.02593, 0.65076, 0.05038, 0.77631, 0.08988, 0.74245, 0.17829, 0.72553, 0.28174, 0.71847, 0.36262, 0.77772, 0.40776, 0.86095, 0.41717, 0.93854, 0.46043, 0.97663, 0.54695, 0.97804, 0.65793, 0.93431, 0.74069, 0.88776, 0.65416, 0.79606, 0.63535, 0.69731, 0.65416, 0.62254, 0.67862, 0.55342, 0.74821, 0.46031, 0.79335, 0.35028, 0.80276, 0.25999, 0.87047, 0.19651, 0.93254, 0.15137, 0.98145, 0.06531, 0.94571, 0.00889, 0.89681, 0.03146, 0.74445, 0.10905, 0.61466, 0.20215, 0.52626, 0.32065, 0.49993, 0.40812, 0.49993, 0.48429, 0.42281, 0.52238, 0.34757, 0.53649, 0.23283, 0.63963, 0.16267, 0.62299, 0.26574, 0.61614, 0.36489, 0.61125, 0.51231, 0.61372, 0.43792, 0.69051, 0.51231, 0.77857, 0.51492, 0.86174, 0.53449, 0.92436, 0.58015, 0.93414, 0.65321, 0.52319, 0.5658, 0.41849, 0.62059, 0.32065, 0.63233, 0.2277, 0.68974, 0.14355, 0.76279, 0.09267, 0.86716], "triangles": [39, 7, 8, 39, 8, 40, 38, 6, 7, 36, 37, 38, 36, 30, 37, 40, 8, 9, 41, 40, 9, 43, 30, 36, 29, 30, 43, 10, 41, 9, 44, 29, 43, 45, 27, 28, 45, 28, 29, 44, 45, 29, 14, 39, 40, 11, 42, 41, 15, 39, 14, 13, 40, 41, 13, 41, 42, 14, 40, 13, 11, 41, 10, 15, 16, 36, 15, 36, 38, 43, 36, 16, 46, 27, 45, 26, 27, 46, 12, 42, 11, 13, 42, 12, 17, 43, 16, 44, 43, 17, 47, 26, 46, 25, 26, 47, 18, 44, 17, 19, 45, 44, 19, 44, 18, 46, 45, 19, 48, 25, 47, 20, 46, 19, 47, 46, 20, 21, 48, 47, 24, 25, 48, 20, 21, 47, 23, 24, 48, 22, 23, 48, 21, 22, 48, 15, 38, 39, 38, 37, 6, 38, 7, 39, 0, 1, 2, 33, 0, 2, 4, 33, 2, 3, 4, 2, 32, 0, 33, 34, 32, 33, 5, 33, 4, 34, 33, 5, 31, 32, 34, 6, 34, 5, 35, 31, 34, 35, 34, 6, 37, 31, 35, 30, 31, 37, 6, 37, 35], "vertices": [1, 16, 2.54, -3.77, 1, 1, 16, -2.71, -1.83, 1, 1, 16, -5.98, 3.19, 1, 1, 16, -1.62, 8.51, 1, 1, 16, 3.69, 7.7, 1, 1, 16, 8.08, 7.01, 1, 1, 17, -8.43, -1.89, 1, 1, 17, -10.6, 1.26, 1, 1, 17, -14.63, 3.63, 1, 1, 17, -17.76, 7.15, 1, 1, 17, -18.11, 11.35, 1, 1, 17, -16.16, 15.59, 1, 1, 17, -12.45, 17.66, 1, 1, 17, -11.67, 13.25, 1, 1, 17, -7.38, 10.32, 1, 1, 17, -2.06, 8.64, 1, 1, 17, 2.16, 7.75, 1, 1, 17, 6.92, 8.71, 1, 1, 17, 12.44, 8.16, 1, 1, 17, 18.16, 5.85, 1, 1, 17, 23.95, 6.22, 1, 1, 17, 28.28, 7.03, 1, 1, 17, 31.45, 7.79, 1, 1, 17, 35.15, 4.35, 1, 1, 17, 37.1, 1.13, 1, 1, 17, 33.19, -4.09, 1, 1, 17, 26.92, -7.13, 1, 1, 17, 20.61, -8.21, 1, 1, 17, 14.15, -6.34, 1, 2, 16, 18.28, -10.2, 0.01701, 17, 9.73, -4.22, 0.98299, 2, 16, 14.79, -6.13, 0.46951, 17, 4.49, -5.29, 0.53049, 2, 16, 11.51, -4.19, 0.9484, 17, 1.2, -7.21, 0.0516, 1, 16, 6.65, -3.68, 1, 1, 16, 1, 2.1, 1, 1, 16, 7.75, 1.23, 1, 2, 16, 11.93, 1.1, 0.98919, 17, -3.22, -4.28, 0.01081, 1, 17, -0.29, 1.18, 1, 2, 16, 15, 1.14, 0.79049, 17, -1.77, -1.58, 0.20951, 1, 17, -4.29, 3.1, 1, 1, 17, -8.69, 5.33, 1, 1, 17, -12.53, 8.09, 1, 1, 17, -14.86, 11.34, 1, 1, 17, -14.03, 14.34, 1, 1, 17, 5.12, 1.07, 1, 1, 17, 11.41, 0.6, 1, 1, 17, 16.56, -1.32, 1, 1, 17, 22.29, -1.41, 1, 1, 17, 27.87, -0.68, 1, 1, 17, 32.34, 2.04, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 4, 66, 66, 68, 68, 70, 70, 74, 74, 72, 72, 76, 76, 78, 78, 80, 80, 82, 82, 84, 72, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96], "width": 56, "height": 42}}, "j2": {"j2": {"type": "mesh", "uvs": [0.26055, 0.18373, 0.20478, 0.13861, 0.14106, 0.09725, 0.17451, 0.05213, 0.33065, 0.02205, 0.47085, 0.01453, 0.5059, 0.07281, 0.48041, 0.12357, 0.47722, 0.18937, 0.53777, 0.30029, 0.61743, 0.42813, 0.74807, 0.43753, 0.91377, 0.47325, 0.9998, 0.62365, 0.94563, 0.78533, 0.89465, 0.91505, 0.79428, 1, 0.68753, 0.83797, 0.54733, 0.71577, 0.38163, 0.73645, 0.26533, 0.64433, 0.2478, 0.56349, 0.13468, 0.59921, 0.00085, 0.65561, 0.00245, 0.48641, 0.06139, 0.35105, 0.18567, 0.31721, 0.23824, 0.32661, 0.28604, 0.24013, 0.34685, 0.09147, 0.37657, 0.22078, 0.41279, 0.32817, 0.42579, 0.49475, 0.58166, 0.56464, 0.73025, 0.61505, 0.85098, 0.70272, 0.24919, 0.43313, 0.11175, 0.47916, 0.04859, 0.55587], "triangles": [36, 37, 26, 19, 20, 32, 18, 19, 32, 37, 25, 26, 24, 25, 37, 38, 24, 37, 33, 32, 10, 22, 37, 21, 38, 37, 22, 34, 10, 11, 34, 11, 12, 33, 10, 34, 23, 24, 38, 23, 38, 22, 35, 34, 12, 35, 12, 13, 18, 32, 33, 18, 33, 34, 14, 35, 13, 17, 18, 34, 17, 34, 35, 15, 35, 14, 17, 35, 15, 16, 17, 15, 21, 37, 36, 20, 21, 32, 36, 26, 27, 31, 36, 27, 21, 36, 32, 29, 4, 5, 7, 5, 6, 29, 5, 7, 1, 3, 4, 1, 4, 29, 2, 3, 1, 0, 1, 29, 7, 30, 29, 8, 30, 7, 0, 29, 30, 28, 0, 30, 31, 30, 8, 31, 8, 9, 28, 30, 31, 32, 31, 9, 32, 9, 10, 31, 27, 28, 32, 36, 31], "vertices": [1, 19, 4.2, -5.71, 1, 1, 19, 1.15, -8.29, 1, 1, 19, -7.91, -10.33, 1, 1, 19, -13.88, -5.51, 1, 1, 19, -13.02, 1.02, 1, 1, 19, -9.99, 7.29, 1, 1, 19, 2.67, 9.72, 1, 2, 19, 4.72, 7.6, 0.9975, 20, -10.68, 15.95, 0.0025, 2, 19, 7.85, 6.55, 0.97264, 20, -9.45, 12.89, 0.02736, 2, 19, 14.14, 8.53, 0.69228, 20, -3.86, 9.39, 0.30772, 2, 19, 21.55, 11.37, 0.03998, 20, 3.11, 5.59, 0.96002, 1, 20, 10.28, 8.44, 1, 1, 20, 19.89, 10.98, 1, 1, 20, 27.68, 6.33, 1, 1, 20, 28.22, -2.35, 1, 1, 20, 28.25, -9.5, 1, 1, 20, 24.7, -15.86, 1, 2, 19, 42.4, 9.95, 0.00018, 20, 15.55, -11.2, 0.99982, 2, 19, 34.32, 3.58, 0.15128, 20, 5.47, -9.18, 0.84872, 1, 20, -2.94, -14.27, 1, 2, 19, 26.48, -11.52, 0.008, 20, -11.11, -13.01, 0.992, 1, 20, -13.76, -9.79, 1, 1, 20, -19.05, -14.25, 1, 1, 20, -25, -20.15, 1, 1, 20, -28.5, -12.45, 1, 1, 20, -28.23, -4.85, 1, 1, 20, -22.31, -0.2, 1, 1, 20, -19.3, 0.69, 1, 1, 19, 7.31, -5, 1, 1, 19, -4.36, 0.84, 1, 2, 19, 7.79, 0.4, 0.9995, 20, -14.16, 8.95, 0.0005, 2, 19, 13.54, 1.05, 0.9833, 20, -9.95, 4.99, 0.0167, 1, 19, 21.77, -0.41, 1, 2, 19, 27.57, 7.53, 0.01417, 20, 4.1, -1.48, 0.98583, 1, 20, 13.1, -0.04, 1, 1, 20, 21.41, -0.98, 1, 2, 19, 16.05, -9.65, 0.008, 20, -16.46, -3.86, 0.992, 1, 20, -22.82, -9.39, 1, 1, 20, -24.56, -14.44, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 8, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 64, 72, 72, 74, 74, 76], "width": 59, "height": 50}}, "j3": {"j3": {"x": 1.64, "y": 1.53, "rotation": 94.05, "width": 76, "height": 76}}, "j4": {"j4": {"x": 6.69, "y": -1.39, "rotation": 70.3, "width": 78, "height": 79}}, "c112": {"c111": {"type": "mesh", "uvs": [0.55309, 0.3431, 0.51221, 0.25173, 0.50248, 0.1646, 0.65042, 0.08173, 0.81003, 0.01373, 0.87622, 0, 1, 0, 1, 0.14973, 0.96576, 0.2751, 0.89958, 0.41748, 0.8295, 0.56198, 0.79057, 0.55773, 0.76526, 0.71285, 0.70492, 0.89135, 0.61343, 1, 0.42461, 1, 0.22023, 1, 0.00416, 0.99335, 0, 0.7851, 0, 0.57898, 0, 0.43873, 0.21439, 0.43023, 0.39736, 0.3856, 0.89605, 0.10283, 0.7811, 0.2477, 0.64081, 0.43585, 0.48463, 0.64927, 0.23287, 0.80767, 0.1106, 0.90639, 0.17258, 0.53107, 0.39131, 0.53722, 0.52978, 0.43104, 0.59877, 0.83213, 0.66767, 0.66684], "triangles": [15, 32, 14, 14, 32, 13, 16, 27, 15, 15, 26, 32, 15, 27, 26, 17, 28, 16, 16, 28, 27, 17, 18, 28, 28, 18, 27, 18, 29, 27, 27, 30, 26, 27, 29, 30, 18, 19, 29, 19, 20, 29, 29, 21, 30, 30, 21, 22, 29, 20, 21, 13, 32, 12, 12, 32, 33, 32, 26, 33, 12, 33, 11, 26, 25, 33, 33, 25, 11, 26, 31, 25, 26, 30, 31, 10, 11, 9, 11, 25, 9, 30, 22, 31, 31, 0, 25, 31, 22, 0, 25, 24, 9, 25, 0, 24, 9, 24, 8, 0, 1, 24, 24, 23, 8, 8, 23, 7, 1, 3, 24, 1, 2, 3, 24, 4, 23, 4, 5, 23, 24, 3, 4, 23, 6, 7, 23, 5, 6], "vertices": [3, 11, 43.31, -23.7, 0.46923, 12, 5.28, -23.16, 0.48784, 13, -19.83, -38.03, 0.04293, 3, 11, 35.31, -32.91, 0.83892, 12, 0.25, -34.28, 0.15834, 13, -18.67, -50.17, 0.00274, 2, 11, 26.18, -38.17, 0.91055, 12, -6.98, -41.93, 0.08945, 2, 11, 9.46, -24.22, 0.9906, 12, -27.01, -33.36, 0.0094, 1, 11, -6.22, -8.16, 1, 1, 11, -11.13, -0.82, 1, 1, 11, -17.46, 14.11, 1, 1, 11, -0.92, 21.13, 1, 1, 11, 14.68, 22.88, 1, 2, 11, 33.79, 21.57, 0.91428, 12, -16.82, 17.48, 0.08572, 2, 11, 53.34, 19.89, 0.34949, 12, 2.39, 21.48, 0.65051, 2, 11, 54.86, 15, 0.20011, 12, 5.25, 17.23, 0.79989, 2, 12, 21.7, 26.56, 0.96899, 13, -30.26, 13.28, 0.03101, 2, 12, 43.23, 34.14, 0.605, 13, -15.34, 30.56, 0.395, 2, 12, 60.92, 33.23, 0.30423, 13, 0.46, 38.55, 0.69577, 2, 12, 76.7, 14.19, 0.02683, 13, 23.62, 29.86, 0.97317, 1, 13, 48.69, 20.46, 1, 1, 13, 74.91, 9.77, 1, 1, 13, 66.64, -13.82, 1, 2, 12, 73.31, -60.88, 0.0142, 13, 57.95, -36.98, 0.9858, 2, 12, 60.35, -71.62, 0.02641, 13, 52.04, -52.74, 0.97359, 2, 12, 41.64, -50.65, 0.16188, 13, 25.39, -43.83, 0.83812, 3, 11, 55.97, -40.49, 0.02275, 12, 22.22, -35.61, 0.5658, 13, 1.06, -40.42, 0.41145, 1, 11, -0.78, 6.39, 1, 2, 11, 21.1, -0.68, 0.99992, 12, -22.6, -7.47, 8e-05, 3, 11, 49.07, -8.77, 0.17256, 12, 6.51, -7.21, 0.81982, 13, -26.68, -23.57, 0.00762, 2, 12, 39.28, -6.62, 0.52852, 13, 1.48, -6.78, 0.47148, 2, 12, 74.97, -19.88, 9e-05, 13, 39.03, -0.57, 0.99991, 1, 13, 58.19, 4.9, 1, 2, 12, 54.45, -47.14, 0.08058, 13, 34.77, -34.42, 0.91942, 3, 11, 73.03, -34.11, 0.00096, 12, 36.73, -24.61, 0.41602, 13, 8.2, -23.66, 0.58302, 3, 11, 54.22, -22.39, 0.11939, 12, 15.35, -18.78, 0.76589, 13, -13.26, -29.22, 0.11472, 2, 12, 46.64, 18.9, 0.5008, 13, -4.81, 19.02, 0.4992, 2, 12, 25.6, 13.19, 0.99026, 13, -20.23, 3.62, 0.00974], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 12, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 40, 58, 58, 60, 60, 62, 28, 64, 64, 66], "width": 131, "height": 120}}, "b222": {"b222": {"type": "mesh", "uvs": [0.09497, 0.30903, 0.03942, 0.37357, 0, 0.42563, 0, 0.55056, 0, 0.68173, 0.01643, 0.82748, 0.10838, 0.92742, 0.27312, 0.98364, 0.5049, 1, 0.76158, 1, 0.92249, 0.9066, 1, 0.72337, 0.99911, 0.51099, 0.92441, 0.30695, 0.83629, 0.20492, 0.84204, 0.10706, 0.81905, 0, 0.75009, 0, 0.67347, 0.0446, 0.56428, 0.09873, 0.53746, 0.02586, 0.43402, 0, 0.31718, 0, 0.26929, 0.07375, 0.22331, 0.05085, 0.11987, 0.08832, 0.06241, 0.14871, 0.07773, 0.23407, 0.35697, 0.12867, 0.31494, 0.32935, 0.28372, 0.56419, 0.2832, 0.82721, 0.62808, 0.23363, 0.68813, 0.37526, 0.71285, 0.57591, 0.68697, 0.82512, 0.49613, 0.22821, 0.49708, 0.41138, 0.49733, 0.63628, 0.50871, 0.8373, 0.20662, 0.19268, 0.20112, 0.31436, 0.13967, 0.54096, 0.15553, 0.79083, 0.0868, 0.4209, 0.80252, 0.34308, 0.72666, 0.16743, 0.12251, 0.16417], "triangles": [41, 44, 0, 44, 1, 0, 41, 40, 29, 40, 41, 27, 40, 27, 47, 27, 41, 0, 27, 26, 47, 24, 40, 25, 26, 25, 47, 40, 47, 25, 10, 35, 11, 11, 34, 12, 34, 11, 35, 34, 45, 12, 45, 13, 12, 37, 33, 34, 34, 33, 45, 37, 32, 33, 37, 36, 32, 45, 33, 46, 33, 32, 46, 45, 14, 13, 45, 46, 14, 36, 19, 32, 46, 19, 18, 46, 32, 19, 14, 46, 15, 46, 17, 15, 17, 16, 15, 46, 18, 17, 29, 40, 28, 36, 28, 19, 19, 21, 20, 21, 19, 28, 40, 23, 28, 40, 24, 23, 23, 22, 28, 28, 22, 21, 29, 36, 37, 29, 28, 36, 9, 8, 35, 8, 39, 35, 9, 35, 10, 8, 7, 39, 39, 7, 31, 7, 6, 31, 31, 6, 43, 6, 5, 43, 31, 38, 39, 39, 38, 35, 5, 4, 43, 31, 30, 38, 31, 43, 30, 35, 38, 34, 4, 42, 43, 43, 42, 30, 4, 3, 42, 30, 37, 38, 38, 37, 34, 42, 41, 30, 30, 29, 37, 30, 41, 29, 3, 44, 42, 3, 2, 44, 42, 44, 41, 2, 1, 44], "vertices": [3, 3, 48.16, 51.93, 0.23357, 4, 6.72, 51.49, 0.0045, 7, -16.43, 13.64, 0.76193, 2, 3, 31.84, 60.19, 0.52038, 7, -31.66, 23.77, 0.47962, 2, 3, 19.01, 65.69, 0.61918, 7, -43.75, 30.75, 0.38082, 2, 3, -5.82, 58.46, 0.79005, 7, -69.26, 26.5, 0.20995, 2, 3, -31.89, 50.86, 0.92299, 7, -96.04, 22.04, 0.07701, 3, 3, -59.82, 38.87, 0.9814, 6, -141.39, 117.86, 8e-05, 7, -125.19, 13.44, 0.01853, 3, 3, -73.89, 13.22, 0.98897, 6, -158.4, 94.05, 0.00907, 7, -142.2, -10.37, 0.00196, 2, 3, -74.7, -25.62, 0.93194, 6, -163.79, 55.58, 0.06806, 2, 3, -63.36, -76.64, 0.747, 6, -158.57, 3.58, 0.253, 2, 3, -47.2, -132.08, 0.5408, 6, -149.08, -53.39, 0.4592, 2, 3, -18.51, -161.43, 0.44563, 6, -124.06, -85.92, 0.55437, 2, 3, 22.78, -167.57, 0.35211, 6, -83.78, -96.9, 0.64789, 2, 3, 64.93, -155.08, 0.23486, 6, -40.45, -89.48, 0.76514, 2, 3, 100.78, -127.13, 0.10234, 6, -1.55, -65.96, 0.89766, 2, 3, 115.51, -102.18, 0.02464, 6, 16.03, -42.93, 0.97536, 2, 3, 135.32, -97.76, 0.00115, 6, 36.22, -40.88, 0.99885, 2, 5, 24.68, -110.68, 8e-05, 6, 57.23, -32.14, 0.99992, 2, 5, 26.7, -95.29, 0.00534, 6, 54.68, -16.83, 0.99466, 2, 5, 19.78, -77, 0.04394, 6, 42.74, -1.35, 0.95606, 3, 4, 67, -45.52, 0.04019, 5, 11.86, -51.19, 0.36619, 6, 27.65, 21.05, 0.59362, 3, 4, 80.89, -37.09, 0.01537, 5, 27.6, -47.16, 0.65354, 6, 41.54, 29.48, 0.3311, 2, 5, 35.94, -24.78, 0.87507, 6, 43, 53.31, 0.12493, 3, 5, 39.35, 1.29, 0.99819, 6, 38.68, 79.25, 0.00132, 7, 54.88, -25.17, 0.00049, 3, 4, 61.2, 20.8, 0.01193, 5, 25.61, 13.95, 0.77261, 7, 38.05, -17.05, 0.21546, 3, 4, 64.18, 31.79, 0.00201, 5, 31.66, 23.59, 0.52761, 7, 41.03, -6.07, 0.47038, 2, 5, 26.99, 47.68, 0.19085, 7, 29.56, 15.62, 0.80915, 2, 5, 16.28, 62.12, 0.07353, 7, 15.1, 26.32, 0.92647, 3, 3, 61.97, 59.99, 0.02049, 5, -1.69, 61, 0.01125, 7, -1.76, 20.01, 0.96825, 2, 5, 11.78, -4.13, 0.98582, 6, 13.88, 66.04, 0.01418, 3, 3, 57.97, 3.23, 0.01528, 4, 10.7, 1.98, 0.97013, 7, -12.45, -35.87, 0.01459, 2, 3, 9.33, -3.62, 0.9954, 6, -77.76, 67.49, 0.0046, 2, 3, -42.97, -18.74, 0.94412, 6, -131.48, 58.66, 0.05588, 3, 4, 41.82, -64.26, 0.00932, 5, -17.69, -61.79, 0.01583, 6, 2.47, 2.3, 0.97484, 3, 3, 72.33, -80.04, 0.13275, 4, 15.12, -82.41, 0.0274, 6, -24.23, -15.84, 0.83985, 3, 3, 34.01, -97, 0.364, 4, -24.94, -94.72, 0.0147, 6, -64.29, -28.15, 0.6213, 3, 3, -17.14, -105.84, 0.55402, 4, -76.78, -97.45, 0.00046, 6, -116.13, -30.88, 0.44552, 4, 3, 89.47, -30.05, 0.01362, 4, 38.05, -34.79, 0.33773, 5, -12.72, -32.5, 0.20933, 6, -1.3, 31.77, 0.43933, 4, 3, 53.13, -40.86, 0.32951, 4, 0.68, -41.23, 0.29272, 5, -50.34, -27.78, 0.00065, 6, -38.67, 25.33, 0.37711, 3, 3, 8.45, -53.94, 0.69239, 4, -45.23, -48.94, 0.02689, 6, -84.58, 17.63, 0.28071, 3, 3, -30.78, -68.04, 0.73797, 4, -85.86, -58.3, 0.00032, 6, -125.2, 8.27, 0.26171, 3, 4, 34.6, 30.67, 0.10837, 5, 3.04, 31.13, 0.1794, 7, 11.45, -7.18, 0.71223, 3, 3, 53.78, 28.69, 0.19679, 4, 9.55, 27.75, 0.21668, 7, -13.59, -10.1, 0.58653, 2, 3, 4.88, 28.84, 0.85122, 7, -62.14, -4.17, 0.14878, 3, 3, -43.78, 10.95, 0.98504, 6, -128.77, 88.23, 0.00663, 7, -112.57, -16.18, 0.00833, 3, 3, 25.41, 47.21, 0.58009, 4, -16.43, 49.5, 0.00253, 7, -39.58, 11.65, 0.41738, 3, 3, 85.93, -102.89, 0.09652, 4, 25.92, -106.7, 0.0004, 6, -13.43, -40.14, 0.90307, 3, 3, 116.06, -76.33, 0.00125, 5, -6.98, -85.56, 0.00749, 6, 19.63, -17.33, 0.99126, 2, 5, 11.35, 49.13, 0.09618, 7, 14.17, 12.45, 0.90382], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 42, 56, 56, 58, 58, 60, 60, 62, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86], "width": 225, "height": 207}}, "sd": {"sd": {"x": -1.3, "y": -2.3, "scaleX": 0.6577, "scaleY": 0.6577, "width": 125, "height": 50}}, "tou1": {"tou1": {"x": 53.64, "y": 9.75, "rotation": -96.01, "width": 80, "height": 52}}, "tou22": {"tou22": {"type": "mesh", "uvs": [0.25578, 0.27926, 0.26911, 0.19743, 0.32701, 0.10637, 0.40915, 0.03376, 0.45897, 0, 0.49398, 0, 0.47917, 0.05682, 0.46301, 0.12136, 0.4482, 0.15363, 0.5263, 0.12597, 0.61786, 0.10061, 0.71885, 0.09024, 0.71212, 0.11329, 0.63941, 0.16861, 0.58959, 0.22163, 0.66364, 0.20895, 0.77271, 0.21356, 0.74848, 0.3, 0.70879, 0.34632, 0.67442, 0.38644, 0.73232, 0.46021, 0.78214, 0.51898, 0.87236, 0.5478, 1, 0.49586, 1, 0.55434, 0.9127, 0.61917, 0.80577, 0.64332, 0.85775, 0.67383, 0.79686, 0.70815, 0.67359, 0.71578, 0.67656, 0.77425, 0.64686, 0.85052, 0.5633, 0.86792, 0.48201, 0.88485, 0.36023, 0.91027, 0.26072, 0.93951, 0.17607, 0.91535, 0.06765, 0.88612, 0.1033, 0.81112, 0.16567, 0.71069, 0.04537, 0.68527, 0, 0.65349, 0, 0.62425, 0.07508, 0.61408, 0.14339, 0.59756, 0.12112, 0.54925, 0.14636, 0.47807, 0.18201, 0.42722, 0.19834, 0.37002, 0.1924, 0.309, 0.20429, 0.25561, 0.44681, 0.04844, 0.38678, 0.12045, 0.32499, 0.21825, 0.29878, 0.31762, 0.22756, 0.31681, 0.24995, 0.37452, 0.28781, 0.40661, 0.35505, 0.39917, 0.38637, 0.32331, 0.44947, 0.23022, 0.54368, 0.16211, 0.64431, 0.12221, 0.44248, 0.41541, 0.51184, 0.35991, 0.59782, 0.29288, 0.70955, 0.24373, 0.61919, 0.50419, 0.74395, 0.58652, 0.85488, 0.58896, 0.94449, 0.55245, 0.50661, 0.69639, 0.54519, 0.7707, 0.59932, 0.81353, 0.30517, 0.69996, 0.26704, 0.7734, 0.26011, 0.86713, 0.07018, 0.65859, 0.16352, 0.64675, 0.23255, 0.52442, 0.31614, 0.52302, 0.49097, 0.55667, 0.58857, 0.62345], "triangles": [78, 44, 79, 44, 45, 79, 79, 47, 57, 45, 46, 79, 63, 64, 67, 81, 63, 67, 80, 58, 63, 80, 63, 81, 79, 57, 80, 74, 79, 80, 63, 59, 64, 82, 81, 67, 74, 80, 81, 46, 47, 79, 58, 59, 63, 36, 37, 38, 76, 38, 75, 38, 39, 75, 40, 77, 39, 77, 78, 39, 39, 78, 74, 78, 79, 74, 40, 41, 77, 78, 43, 44, 78, 77, 43, 43, 77, 42, 77, 41, 42, 36, 76, 35, 35, 76, 34, 76, 36, 38, 34, 76, 75, 33, 34, 75, 75, 74, 72, 75, 39, 74, 33, 75, 72, 33, 72, 32, 32, 73, 31, 32, 72, 73, 31, 73, 30, 73, 72, 30, 72, 29, 30, 72, 74, 71, 71, 74, 81, 72, 71, 29, 71, 82, 29, 71, 81, 82, 28, 29, 26, 28, 26, 27, 26, 29, 68, 26, 69, 25, 26, 68, 69, 25, 70, 24, 25, 69, 70, 69, 22, 70, 68, 21, 69, 69, 21, 22, 70, 23, 24, 70, 22, 23, 29, 82, 68, 82, 67, 68, 68, 67, 21, 21, 67, 20, 67, 19, 20, 67, 64, 19, 64, 65, 19, 19, 65, 18, 65, 64, 60, 18, 66, 17, 18, 65, 66, 17, 66, 16, 65, 15, 66, 65, 14, 15, 66, 15, 16, 59, 60, 64, 65, 60, 14, 60, 61, 14, 60, 8, 61, 14, 61, 13, 13, 62, 12, 13, 61, 62, 61, 10, 62, 8, 9, 61, 61, 9, 10, 12, 62, 11, 11, 62, 10, 54, 53, 59, 59, 53, 60, 0, 1, 53, 53, 8, 60, 53, 52, 8, 53, 2, 52, 53, 1, 2, 8, 52, 7, 52, 51, 7, 7, 51, 6, 52, 3, 51, 52, 2, 3, 6, 51, 5, 51, 4, 5, 51, 3, 4, 56, 47, 48, 47, 56, 57, 80, 57, 58, 58, 57, 54, 57, 56, 54, 58, 54, 59, 48, 55, 56, 56, 55, 54, 48, 49, 55, 55, 0, 54, 54, 0, 53, 0, 55, 50, 55, 49, 50], "vertices": [6, 24, 11.78, -7.87, 0.7531, 21, -17.36, 10.81, 0.2419, 22, -43.59, 17.25, 0.0035, 23, -67.98, 32.65, 0.00115, 27, -120.42, 20.56, 0.00024, 29, -114.31, 8.39, 0.00011, 3, 24, 27.87, -18.88, 0.03393, 21, 1.94, 13.55, 0.96607, 30, -135.06, 48.09, 0, 2, 21, 25.82, 8.24, 1, 30, -142.85, 71.29, 0, 2, 21, 46.87, -2.98, 0.9981, 22, 18.1, 39.84, 0.0019, 1, 21, 57.31, -10.45, 1, 1, 21, 59.26, -17.25, 1, 2, 21, 45.55, -18.08, 0.98234, 22, 25.03, 26.37, 0.01766, 2, 21, 30.01, -19.14, 0.81196, 22, 12.45, 17.17, 0.18804, 2, 21, 21.86, -18.37, 0.43983, 22, 5.15, 13.48, 0.56017, 2, 21, 32.49, -31.73, 0.00494, 22, 21.26, 7.85, 0.99506, 2, 22, 39.05, 0, 1, 30, -103.87, 115.27, 0, 2, 22, 55.9, -11.76, 1, 30, -91.79, 131.88, 0, 2, 22, 51.26, -14.91, 1, 30, -88.72, 127.18, 0, 3, 22, 31.61, -14.87, 0.96996, 23, 13.29, 23.59, 0.03004, 30, -89.15, 107.54, 0, 6, 24, -6.05, -74.31, 0.00068, 22, 15.77, -17.51, 0.55938, 23, -1.12, 16.5, 0.43987, 29, -90.54, 72.94, 7e-05, 30, -86.83, 91.65, 0, 31, -127.93, 38.54, 0, 4, 22, 28.92, -25.23, 0.04583, 23, 13.71, 12.89, 0.95417, 27, -58.63, 77.53, 0, 30, -78.84, 104.65, 0, 4, 22, 44.64, -40.72, 1e-05, 23, 33.22, 2.6, 0.99999, 27, -39.24, 88.05, 0, 30, -63.05, 120.05, 0, 5, 24, -36.89, -94.83, 0.00016, 22, 27.4, -52.68, 0, 23, 20.16, -13.82, 0.98668, 27, -32.83, 68.07, 0.01193, 29, -57.97, 90.57, 0.00122, 6, 24, -43.11, -82.78, 0.0081, 23, 8.28, -20.34, 0.90138, 27, -34, 54.57, 0.07603, 28, -6.3, 70.87, 5e-05, 29, -52.86, 78.02, 0.01359, 31, -103.6, 67.76, 0.00085, 7, 24, -48.5, -72.35, 0.03127, 22, 2.65, -57.93, 0.00415, 23, -2.01, -25.99, 0.66241, 27, -35.02, 42.87, 0.24631, 28, -13.81, 61.85, 0.00335, 29, -48.44, 67.14, 0.04805, 31, -92.98, 62.73, 0.00446, 6, 24, -69.3, -75.08, 0.02459, 23, 1.24, -46.71, 0.28767, 27, -15.98, 34.07, 0.56305, 28, -3.22, 43.75, 0.06932, 29, -27.48, 67.97, 0.05289, 31, -78.11, 77.52, 0.00249, 7, 24, -86.2, -77.92, 0.00614, 23, 4.5, -63.54, 0.09353, 27, -0.18, 27.44, 0.51751, 28, 5.97, 29.28, 0.37248, 29, -10.39, 69.27, 0.01028, 30, -8.93, 72.41, 0, 31, -66.39, 90.02, 6e-05, 4, 23, 18.15, -77.4, 0.00589, 27, 18.93, 31.1, 0.06128, 28, 23.73, 21.37, 0.93275, 30, 8.45, 81.14, 8e-05, 2, 28, 50.23, 32, 0.99984, 30, 17.01, 108.37, 0.00016, 2, 28, 49.37, 18.23, 0.99985, 30, 27.12, 98.98, 0.00015, 2, 28, 30.82, 4.05, 0.9999, 30, 26.34, 75.65, 0.0001, 1, 28, 8.91, -0.3, 1, 1, 28, 18.94, -8.14, 1, 4, 27, 25.55, -9.16, 0.2188, 28, 6.17, -15.46, 0.76724, 29, 29.18, 48.4, 0.00107, 30, 25.8, 44.21, 0.01289, 4, 27, 5.21, -23.63, 0.53044, 28, -18.8, -15.71, 0.0036, 29, 17.66, 26.25, 0.13645, 30, 10.18, 24.74, 0.32951, 3, 27, 12.89, -35.12, 0.13911, 29, 29.73, 19.53, 0.02743, 30, 20.7, 15.79, 0.83346, 3, 27, 17.11, -53.61, 0.00192, 30, 29.81, -0.86, 0.99635, 32, 9.02, 77, 0.00173, 4, 29, 36.56, -11.55, 0.0111, 30, 21.34, -16.02, 0.89041, 31, 22.84, 62.21, 0.02592, 32, 13.97, 60.35, 0.07256, 4, 29, 31.35, -27.62, 0.09264, 30, 13.09, -30.77, 0.53011, 31, 29.87, 46.85, 0.1004, 32, 18.79, 44.15, 0.27684, 4, 29, 23.56, -51.72, 0.04446, 30, 0.75, -52.88, 0.11788, 31, 40.42, 23.83, 0.07061, 32, 26.02, 19.88, 0.76705, 4, 29, 18.9, -72.45, 9e-05, 30, -7.87, -72.31, 0.002, 32, 33.92, 0.15, 0.97615, 34, 18.62, 40.13, 0.02176, 3, 30, -23.68, -80.96, 0, 32, 29.09, -17.21, 0.58119, 34, 25.49, 23.46, 0.41881, 3, 30, -43.64, -92.31, 0, 32, 23.3, -39.43, 0.09291, 34, 34.59, 2.38, 0.90709, 2, 30, -51.71, -74.99, 0, 34, 16.48, -3.74, 1, 3, 24, -71.31, 53.73, 0.00704, 30, -60.51, -49.64, 0, 33, 2.55, -6.66, 0.99296, 3, 24, -55.13, 72.82, 0.00652, 30, -81.44, -63.36, 0, 33, 7.8, -31.13, 0.99348, 2, 30, -93.17, -64.98, 0, 33, 5.07, -42.66, 1, 3, 24, -38.15, 74.62, 0.00015, 30, -98.23, -60.28, 0, 33, -1.14, -45.68, 0.99985, 5, 24, -42.75, 59.98, 0.03476, 29, -66.16, -64.12, 0.00137, 30, -89.67, -47.53, 0, 31, -17.31, -45.97, 0.00462, 33, -9.93, -33.09, 0.95925, 8, 24, -45.4, 45.88, 0.05298, 23, -120.28, -25.86, 0.00011, 27, -100.8, -55.42, 7e-05, 29, -62.25, -50.32, 0.00623, 30, -83.13, -34.77, 0, 31, -23.75, -33.16, 0.02058, 33, -19.48, -22.39, 0.19202, 5, 30.22, 57.71, 0.728, 8, 24, -33.19, 44.84, 0.07474, 23, -119.55, -13.63, 2e-05, 27, -110.57, -48.02, 3e-05, 29, -74.31, -48.18, 0.00743, 30, -94.55, -30.31, 0, 31, -34.09, -39.73, 0.02013, 33, -27.76, -31.43, 0.09765, 5, 41.98, 61.15, 0.8, 8, 24, -20.41, 32.8, 0.12334, 23, -107.83, -0.55, 0.00039, 27, -114.94, -31.01, 0.00045, 29, -85.95, -35.03, 0.00963, 30, -103.39, -15.14, 0, 31, -51.55, -37.91, 0.02078, 33, -45.1, -34.2, 0.06941, 5, 58.25, 54.54, 0.776, 9, 24, -12.87, 21.01, 0.25658, 22, -77.96, 1.12, 5e-05, 23, -96.24, 7.29, 0.00149, 27, -115.02, -17.02, 0.00142, 29, -92.39, -22.61, 0.0128, 30, -107.29, -1.7, 0, 31, -64.7, -33.11, 0.02228, 33, -59.04, -32.98, 0.05737, 5, 69.54, 46.27, 0.648, 7, 24, -2.24, 12.05, 0.93402, 23, -87.55, 18.13, 0.00076, 27, -119.21, -3.77, 0.00093, 29, -102.16, -12.72, 0.00963, 30, -114.94, 9.9, 0, 31, -78.58, -32.43, 0.0163, 33, -72.62, -35.92, 0.03836, 2, 24, 11.19, 6.71, 1, 30, -126.31, 18.82, 0, 4, 24, 21.4, -1.04, 0.99998, 21, -14.87, 22.35, 2e-05, 22, -47.64, 28.34, 0, 23, -75.06, 42.1, 0, 2, 21, 45.64, -11.25, 0.98908, 22, 21.47, 32.19, 0.01092, 2, 21, 25.96, -4.28, 0.94897, 22, 1.1, 27.59, 0.05103, 3, 24, 18.45, -26.8, 0.00132, 21, 0.33, 1.35, 0.99868, 30, -123.78, 53.02, 0, 8, 24, -0.2, -11.62, 0.71255, 21, -23.67, -0.04, 0.20747, 22, -43.14, 4.71, 0.0436, 23, -63.93, 20.77, 0.01862, 27, -108.29, 17.34, 0.00628, 29, -102.05, 11.04, 0.00802, 31, -94.56, -14.85, 0.00292, 33, -92.62, -23.1, 0.00055, 3, 24, 6.38, 1.18, 0.99993, 30, -120.12, 22.77, 0, 33, -86.49, -36.12, 7e-05, 9, 24, -7.83, 3.18, 0.9093, 21, -39.3, 5.74, 0.00232, 22, -59.44, 1.26, 0.00648, 23, -78.54, 12.76, 0.0083, 27, -109.75, 0.74, 0.00511, 29, -95.78, -4.4, 0.01654, 30, -107.06, 16.82, 0, 31, -79.51, -21.99, 0.01897, 33, -76.23, -26.09, 0.03298, 8, 24, -18.02, -0.29, 0.73681, 21, -44.47, -3.7, 0.0123, 22, -58.78, -9.49, 0.02855, 23, -74.81, 2.67, 0.03602, 27, -99.28, -1.76, 0.02209, 29, -85.33, -1.86, 0.05383, 31, -73.52, -13.05, 0.04934, 33, -72.77, -15.89, 0.06105, 9, 24, -22.49, -13.24, 0.29308, 21, -39.03, -16.27, 0.02522, 22, -47.48, -17.22, 0.04786, 23, -61.76, -1.48, 0.05296, 27, -88.58, 6.8, 0.02813, 29, -79.7, 10.63, 0.04513, 31, -77.82, -0.04, 0.02701, 33, -80.29, -4.45, 0.01661, 5, 72.93, 10.86, 0.464, 8, 24, -9.27, -26.87, 0.35324, 21, -20.08, -17.41, 0.2201, 22, -30.83, -8.08, 0.23828, 23, -48.46, 12.08, 0.10789, 27, -92.48, 25.38, 0.03084, 29, -91.63, 25.4, 0.03426, 31, -96.59, 2.78, 0.01295, 33, -99.16, -6.6, 0.00245, 7, 24, 4.73, -48.06, 0.01947, 21, 4.56, -23.6, 0.13878, 22, -6.7, -0.17, 0.80351, 23, -27.63, 26.61, 0.03182, 27, -93, 50.77, 0.00305, 29, -103.66, 47.77, 0.00277, 31, -120.58, 11.13, 0.00061, 3, 22, 18.2, -0.85, 0.9956, 23, -3.59, 33.15, 0.0044, 30, -103.43, 94.41, 0, 3, 22, 39.64, -7.36, 0.99893, 23, 18.81, 33.1, 0.00107, 30, -96.5, 115.71, 0, 9, 24, -33.78, -27.35, 0.09808, 21, -37.84, -34.3, 0.00811, 22, -36.85, -31.84, 0.04067, 23, -47.37, -12.41, 0.07404, 27, -71.5, 12.69, 0.04085, 29, -67.18, 23.66, 0.04397, 31, -77.4, 18.03, 0.01745, 33, -84.58, 13.1, 0.00484, 5, 67.5, -6.38, 0.672, 8, 24, -28.29, -45.73, 0.14218, 21, -21.39, -44.16, 0.01873, 22, -17.68, -31.4, 0.21395, 23, -29.14, -6.45, 0.42357, 27, -66.33, 31.16, 0.11156, 29, -70.99, 42.46, 0.0704, 31, -92.91, 29.3, 0.01833, 33, -102.49, 19.97, 0.00128, 6, 24, -21.85, -68.32, 0.0145, 22, 5.81, -31.16, 0.12582, 23, -6.71, 0.55, 0.82872, 27, -59.71, 53.7, 0.02104, 29, -75.35, 65.54, 0.00848, 31, -111.73, 43.36, 0.00144, 3, 22, 30.38, -37.53, 0.00071, 23, 18.64, 1.54, 0.99929, 30, -66.52, 105.86, 0, 9, 24, -68.43, -50, 0.01878, 22, -24.17, -71.24, 0.00319, 23, -23.86, -46.47, 0.0817, 27, -30.12, 13.33, 0.1708, 28, -26.67, 34.81, 0.00215, 29, -30.62, 43.07, 0.05356, 31, -63.58, 57.04, 0.00577, 33, -81.37, 54.37, 7e-05, 5, 43.37, -40.01, 0.664, 6, 24, -97.04, -63.92, 0.00203, 23, -9.22, -74.73, 0.03032, 27, 1.51, 9.81, 0.69806, 28, -2.72, 13.85, 0.26656, 29, -0.86, 54.35, 0.00299, 31, -49.28, 85.47, 4e-05, 4, 23, 10.85, -84.71, 0.00387, 27, 20.96, 20.96, 0.0446, 28, 19.61, 11.89, 0.95148, 30, 13.16, 71.94, 6e-05, 4, 23, 30.9, -84.55, 1e-05, 27, 31.95, 37.73, 0.0005, 28, 38.21, 19.37, 0.99935, 30, 19.17, 91.07, 0.00014, 7, 24, -98.93, -9.45, 0.00687, 22, -71.34, -89.94, 0.00038, 23, -63.63, -77.99, 0.00446, 27, -25.99, -37.24, 0.02408, 29, -3.92, -0.07, 0.92692, 31, -14.74, 43.3, 0.03674, 32, -25.88, 46.87, 0.00056, 4, 29, 15.1, -2.63, 0.20348, 30, 2.03, -3.09, 0.77372, 31, 1, 54.28, 0.01094, 32, -8.76, 55.54, 0.01186, 3, 30, 16.88, -1.95, 0.99396, 31, 8.86, 66.93, 0.00145, 32, 0.79, 66.97, 0.00459, 8, 24, -81.58, 27.37, 0.02216, 22, -102.26, -63.47, 0.00027, 23, -100.87, -61.57, 0.00258, 27, -60.32, -59.1, 0.00207, 29, -24.53, -35.17, 0.09693, 30, -43.19, -27.26, 0.00043, 31, -6.21, 3.51, 0.83093, 33, -12.06, 17.57, 0.04463, 3, 31, 12.27, -0.77, 0.97284, 33, 6.89, 18.23, 0.02383, 34, -11.88, 15.47, 0.00333, 2, 32, 16.86, -0.83, 0.98881, 34, 5.78, 28.87, 0.01119, 5, 24, -51.72, 65.53, 0.01555, 29, -57.73, -70.47, 0.0001, 30, -82.64, -55.4, 0, 31, -6.81, -44.95, 0.00019, 33, -0.06, -29.38, 0.98416, 7, 24, -57.6, 47.41, 0.08367, 23, -121.5, -38.1, 0.00037, 27, -91.3, -63.23, 0.00018, 29, -50.23, -52.95, 0.01089, 30, -71.86, -39.69, 0, 31, -13.12, -26.96, 0.05366, 33, -10.82, -13.66, 0.85123, 9, 24, -37.95, 22.07, 0.19268, 22, -85.62, -22.8, 0.00151, 23, -96.67, -17.82, 0.0058, 27, -94.38, -31.32, 0.00544, 29, -67.5, -25.94, 0.03541, 30, -83.53, -9.82, 0, 31, -44.11, -18.74, 0.065, 33, -42.88, -13.76, 0.13417, 5, 45.76, 38.2, 0.56, 10, 24, -45.17, 6.8, 0.16514, 21, -69.29, -16.78, 3e-05, 22, -72.8, -33.79, 0.00669, 23, -81.22, -24.65, 0.02015, 27, -80.12, -22.26, 0.02026, 29, -58.93, -11.39, 0.07456, 30, -72.28, 2.77, 0, 31, -47.63, -2.22, 0.08409, 33, -50.57, 1.28, 0.06908, 5, 44.54, 21.36, 0.56, 8, 24, -67.99, -21.29, 0.04258, 22, -51.74, -63.23, 0.00578, 23, -52.57, -46.76, 0.03864, 27, -45.82, -10.71, 0.08183, 29, -33.65, 14.52, 0.13466, 31, -46.52, 33.96, 0.03641, 33, -58.89, 36.51, 0.0041, 5, 33.41, -13.08, 0.656, 8, 24, -90.88, -31.93, 0.0249, 22, -47.53, -88.12, 0.00207, 23, -41.35, -69.37, 0.04319, 27, -20.78, -13.93, 0.46953, 29, -9.9, 23.05, 0.42544, 30, -17.47, 26.98, 0.0199, 31, -34.78, 56.3, 0.01494, 33, -53.36, 61.13, 4e-05], "hull": 51, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 62, 64, 64, 66, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 0, 100, 10, 102, 102, 104, 104, 106, 106, 108, 100, 110, 110, 112, 108, 114, 116, 118, 118, 120, 120, 122, 122, 124, 126, 128, 128, 130, 130, 132, 134, 136, 136, 138, 138, 140, 142, 144, 144, 146, 148, 150, 150, 152, 84, 154, 154, 156], "width": 202, "height": 236}}, "m1": {"m1": {"type": "mesh", "uvs": [0, 0.08324, 0.03787, 0.00507, 0.1761, 0, 0.35921, 0.0124, 0.55129, 0.08568, 0.6985, 0.24201, 0.84391, 0.30551, 0.99829, 0.35681, 0.99111, 0.54489, 0.94264, 0.69633, 0.83672, 0.81358, 0.68054, 0.9064, 0.51539, 0.97723, 0.36998, 1, 0.29997, 0.78671, 0.29279, 0.5571, 0.20662, 0.37391, 0.07377, 0.22247, 0, 0.16384, 0.12619, 0.10845, 0.27829, 0.19469, 0.43884, 0.30391, 0.55714, 0.44763, 0.65854, 0.64884, 0.12787, 0.1837, 0.26928, 0.2997, 0.38365, 0.42986, 0.45019, 0.58831, 0.49386, 0.79204, 0.15074, 0.0592, 0.32958, 0.1073, 0.5001, 0.19784, 0.64775, 0.35064, 0.76628, 0.46947], "triangles": [33, 5, 6, 32, 5, 33, 8, 6, 7, 33, 6, 8, 33, 22, 32, 23, 22, 33, 9, 33, 8, 10, 23, 33, 9, 10, 33, 11, 23, 10, 32, 31, 5, 22, 31, 32, 31, 3, 4, 30, 3, 31, 31, 4, 5, 21, 30, 31, 21, 31, 22, 26, 21, 22, 20, 30, 21, 19, 0, 29, 24, 0, 19, 20, 19, 29, 0, 17, 18, 20, 24, 19, 30, 2, 3, 29, 1, 2, 0, 1, 29, 29, 2, 30, 20, 29, 30, 27, 22, 23, 28, 27, 23, 14, 27, 28, 28, 23, 11, 12, 28, 11, 13, 14, 28, 13, 28, 12, 27, 26, 22, 14, 15, 27, 25, 20, 21, 26, 25, 21, 15, 16, 26, 15, 26, 27, 24, 17, 0, 25, 24, 20, 16, 24, 25, 17, 24, 16, 26, 16, 25], "vertices": [2, 38, -11.7, -0.41, 0.01887, 35, -5.66, 1.64, 0.98113, 1, 35, -2.48, 6.38, 1, 1, 35, 9, 6.58, 1, 2, 35, 24.19, 5.68, 0.44914, 36, -0.77, 6.12, 0.55085, 2, 36, 15.31, 10.07, 0.90941, 37, -10.92, 8.18, 0.09059, 1, 37, 4.57, 8.48, 1, 1, 37, 16.38, 13.09, 1, 1, 37, 28.3, 18.74, 1, 1, 37, 35.07, 9.46, 1, 2, 40, 20.93, 37.87, 0.02132, 37, 37.78, -0.25, 0.97868, 2, 40, 23.71, 26.88, 0.19964, 37, 35.46, -11.34, 0.80036, 3, 40, 23.38, 12.74, 0.65649, 36, 49.27, -28.25, 2e-05, 37, 28.97, -23.91, 0.34349, 3, 39, 41.77, -3.91, 0.00029, 40, 21.52, -1.51, 0.99197, 37, 21.06, -35.91, 0.00774, 2, 39, 36.58, -14.9, 0.06114, 40, 17.69, -13.04, 0.93886, 2, 39, 22.46, -12.98, 0.5417, 40, 3.44, -12.82, 0.4583, 2, 38, 24.99, -9.37, 0.04467, 39, 10.25, -6.1, 0.95533, 1, 38, 12.61, -4.58, 1, 2, 38, -1.74, -3.64, 0.83846, 35, 0.38, -6.91, 0.16154, 2, 38, -8.79, -4.37, 0.13976, 35, -5.71, -3.28, 0.86024, 2, 38, -2.34, 4.54, 0.00292, 35, 4.8, 0, 0.99708, 4, 38, 10.95, 7.76, 0.16663, 39, -9.17, 4.54, 0.22955, 35, 17.37, -5.37, 0.45475, 36, -1.15, -6.87, 0.14907, 3, 39, 3.52, 12.35, 0.29848, 40, -18.41, 10.04, 0.0088, 36, 13.73, -6.11, 0.69272, 4, 39, 16.14, 16.07, 0.11072, 40, -6.32, 15.25, 0.17419, 36, 26.59, -8.92, 0.53132, 37, 3.38, -8.65, 0.18378, 3, 40, 8.36, 17.71, 0.41608, 36, 39.95, -15.47, 0.04977, 37, 17.65, -12.87, 0.53415, 2, 38, 0.49, 0.92, 0.89189, 35, 4.89, -4.59, 0.10811, 4, 38, 14.14, 2.15, 0.4816, 39, -4.12, 0.53, 0.4322, 35, 16.57, -11.77, 0.03894, 36, 1.35, -12.81, 0.04726, 3, 39, 7.63, 4.41, 0.82345, 40, -13.37, 2.65, 0.0167, 36, 13.52, -15.06, 0.15985, 4, 39, 18.75, 4, 0.41798, 40, -2.28, 3.58, 0.48753, 36, 23.08, -20.76, 0.07539, 37, 1.9, -20.91, 0.0191, 3, 40, 10.52, 1.63, 0.98376, 36, 32.35, -29.79, 0.00228, 37, 12.55, -28.27, 0.01395, 1, 35, 6.86, 2.99, 1, 4, 38, 11.24, 14.57, 8e-05, 39, -11.45, 10.97, 0.00117, 35, 21.68, -0.08, 0.55067, 36, -0.06, -0.13, 0.44808, 2, 36, 14.97, 2.02, 0.99181, 37, -9.91, 0.19, 0.00819, 1, 37, 5.48, 0.68, 1, 1, 37, 17.69, 1.26, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 83, "height": 61}}, "m2": {"m2": {"type": "mesh", "uvs": [1, 0.04165, 1, 0.14959, 0.83912, 0.2525, 0.70733, 0.46838, 0.71905, 0.69179, 0.71612, 0.99301, 0.47891, 1, 0.20948, 0.96791, 0.01619, 0.85997, 0, 0.70183, 0.19191, 0.51608, 0.36762, 0.38806, 0.48769, 0.22238, 0.69269, 0.04416, 0.86841, 0, 0.76225, 0.14676, 0.61783, 0.29347, 0.49213, 0.45852, 0.36643, 0.69234, 0.29155, 0.85052, 0.54027, 0.87573], "triangles": [7, 19, 6, 6, 20, 5, 6, 19, 20, 5, 20, 4, 7, 8, 19, 19, 18, 20, 20, 18, 4, 8, 9, 19, 4, 18, 17, 19, 9, 18, 9, 10, 18, 17, 18, 10, 4, 17, 3, 17, 10, 11, 17, 16, 3, 3, 16, 2, 17, 11, 16, 11, 12, 16, 16, 12, 15, 16, 15, 2, 2, 15, 1, 12, 13, 15, 15, 0, 1, 15, 14, 0, 15, 13, 14], "vertices": [1, 41, -4.98, 1.66, 1, 1, 41, -1.66, 5.79, 1, 2, 41, 6.76, 5.48, 0.916, 42, -3.31, 7.69, 0.084, 3, 41, 17.7, 10.26, 0.00765, 42, 8.62, 7.23, 0.97741, 43, -9.36, 6.88, 0.01494, 2, 42, 18.3, 12.37, 0.36115, 43, 0.12, 12.38, 0.63885, 2, 42, 31.68, 18.59, 0.00214, 43, 13.25, 19.11, 0.99786, 1, 43, 18.17, 10.44, 1, 1, 43, 22.02, -0.32, 1, 1, 43, 21.09, -9.97, 1, 2, 42, 31.7, -14.7, 0.00168, 43, 14.53, -14.16, 0.99832, 2, 42, 20.02, -11.32, 0.2579, 43, 2.73, -11.23, 0.7421, 2, 42, 11.19, -7.35, 0.9465, 43, -6.24, -7.59, 0.0535, 2, 41, 17.34, -4.92, 0.11534, 42, 1.69, -6.28, 0.88466, 1, 41, 5.16, -6.33, 1, 1, 41, -1.95, -3.39, 1, 1, 41, 6.03, -0.58, 1, 1, 42, 2.49, 0.16, 1, 1, 42, 12.06, -1.14, 1, 2, 42, 24.68, -1, 0.00052, 43, 7, -0.73, 0.99948, 1, 43, 15.32, 0.07, 1, 2, 42, 29.66, 9.45, 0.01002, 43, 11.58, 9.9, 0.98998], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 42, "height": 49}}, "c222": {"c222": {"type": "mesh", "uvs": [0.00361, 0.14611, 0.05596, 0.2394, 0.13273, 0.32321, 0.22172, 0.38804, 0.18856, 0.54933, 0.14669, 0.71694, 0.09085, 0.91143, 0.09085, 1, 0.18159, 1, 0.33164, 0.95255, 0.48867, 0.86716, 0.65443, 0.78019, 0.77483, 0.72959, 0.91938, 0.61208, 1, 0.47733, 1, 0.30601, 0.75795, 0.28098, 0.58377, 0.22708, 0.48606, 0.19436, 0.50517, 0.09233, 0.32675, 0.06153, 0.15894, 0.02688, 0.04849, 0.01918, 0.006, 0.06538, 0.10539, 0.12818, 0.21356, 0.18178, 0.34418, 0.26681, 0.41564, 0.387, 0.40754, 0.58857, 0.33413, 0.76427, 0.23417, 0.88265, 0.57326, 0.36264, 0.70247, 0.45538, 0.64402, 0.61444, 0.54168, 0.71386], "triangles": [8, 30, 9, 8, 7, 30, 7, 6, 30, 9, 29, 10, 9, 30, 29, 6, 5, 30, 30, 5, 29, 10, 29, 34, 5, 4, 29, 10, 34, 11, 12, 11, 33, 29, 28, 34, 29, 4, 28, 11, 34, 33, 12, 33, 13, 34, 28, 33, 31, 28, 27, 28, 31, 33, 33, 32, 13, 33, 31, 32, 13, 32, 14, 4, 3, 28, 28, 3, 27, 32, 16, 14, 16, 15, 14, 32, 31, 16, 31, 27, 18, 31, 17, 16, 3, 26, 27, 3, 2, 26, 27, 26, 18, 31, 18, 17, 2, 25, 26, 2, 1, 25, 18, 26, 20, 1, 24, 25, 1, 0, 24, 26, 25, 20, 18, 20, 19, 24, 21, 25, 25, 21, 20, 0, 23, 24, 23, 22, 24, 24, 22, 21], "vertices": [1, 8, -5.12, -15.04, 1, 1, 8, 11.09, -19.23, 1, 2, 8, 28.51, -19.76, 0.98246, 9, -5.31, -32.34, 0.01754, 3, 8, 44.83, -16.95, 0.702, 9, 4.57, -19.06, 0.29763, 10, -32.17, -38.12, 0.00036, 3, 8, 60.99, -37.63, 0.05329, 9, 30.54, -22.88, 0.7984, 10, -7.35, -29.58, 0.14831, 2, 9, 57.57, -27.94, 0.1875, 10, 18.98, -21.63, 0.8125, 1, 10, 50.04, -13.33, 1, 1, 10, 62.37, -6.34, 1, 1, 10, 55.88, 5.1, 1, 1, 10, 38.55, 20.29, 1, 2, 9, 79.71, 22.52, 0.12752, 10, 15.44, 33.36, 0.87248, 2, 9, 64.9, 46.01, 0.71348, 10, -8.52, 47.41, 0.28652, 2, 9, 56.15, 63.15, 0.9067, 10, -24.17, 58.6, 0.0933, 2, 9, 36.57, 83.39, 0.98985, 10, -50.86, 67.56, 0.01015, 3, 8, 130.23, 58.19, 0.00458, 9, 14.58, 94.25, 0.99541, 10, -75.39, 67.1, 2e-05, 2, 8, 109.68, 76.33, 0.01711, 9, -12.81, 93.22, 0.98289, 2, 8, 83.45, 52.67, 0.13804, 9, -15.48, 57.99, 0.86196, 2, 8, 60.27, 39.45, 0.54591, 9, -23.15, 32.43, 0.45409, 2, 8, 46.96, 32.29, 0.90574, 9, -27.84, 18.07, 0.09426, 2, 8, 36.56, 45.17, 0.99976, 9, -44.26, 20.23, 0.00024, 1, 8, 15.74, 29.04, 1, 1, 8, -4.52, 14.47, 1, 1, 8, -16.04, 3.28, 1, 1, 8, -14.57, -6.23, 1, 1, 8, 2.49, -2.08, 1, 1, 8, 19.3, 4, 1, 1, 8, 42.04, 9.2, 1, 2, 8, 63.31, 4.24, 0.11969, 9, 3.34, 9.04, 0.88031, 1, 9, 35.62, 9.08, 1, 2, 9, 64.11, -0.49, 0.00041, 10, 12.17, 5.75, 0.99959, 2, 9, 83.58, -14.26, 3e-05, 10, 35.79, 2.48, 0.99997, 2, 8, 75.52, 23.95, 0.25852, 9, -1.42, 31.73, 0.74148, 3, 8, 99.04, 28.17, 0.0485, 9, 12.7, 51.01, 0.95061, 10, -57.17, 27.84, 0.00089, 3, 8, 112.51, 4.98, 0.00023, 9, 38.45, 43.5, 0.93737, 10, -30.85, 33.01, 0.06239, 2, 9, 54.91, 29.28, 0.6731, 10, -9.69, 27.95, 0.3269], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 14, 62, 64, 64, 66, 66, 68], "width": 145, "height": 160}}}}], "animations": {"idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.44}], "translate": [{"x": 0.86, "y": 0.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.04, "y": 2.24, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.86, "y": 0.64}]}, "bone5": {"rotate": [{"angle": -1.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.11}], "translate": [{"x": 1.23, "y": 0.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.72, "y": 0.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.23, "y": 0.31}]}, "bone6": {"translate": [{"x": 2.11, "y": -0.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 4.22, "y": -1.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 2.11, "y": -0.89}]}, "bone7": {"translate": [{"x": -0.67, "y": 2.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -1.34, "y": 4.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.67, "y": 2.11}]}, "bone8": {"rotate": [{"angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 8.04}]}, "bone9": {"rotate": [{"angle": 5.76, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.76}]}, "bone10": {"rotate": [{"angle": 2.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.28}]}, "bone11": {"rotate": [{"angle": -9.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -9.99, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -9.41}]}, "bone12": {"rotate": [{"angle": -1.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.11}]}, "bone13": {"rotate": [{"angle": -0.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -0.44}]}, "bone16": {"rotate": [{"angle": -2.29}]}, "bone17": {"rotate": [{"angle": 2.49}]}, "bone18": {"rotate": [{"angle": 4.5}]}, "bone20": {"rotate": [{"angle": 0.98}]}, "bone21": {"rotate": [{"angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.74}]}, "bone22": {"rotate": [{"angle": 6.39, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 6.39}]}, "bone24": {"rotate": [{"angle": -5.2, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -12.61, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -5.2}]}, "bone25": {"rotate": [{"angle": -3.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -12.61, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -3.05}]}, "bone26": {"rotate": [{"angle": -0.54, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -12.61, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -0.54}]}, "bone27": {"rotate": [{"angle": -1.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": -12.61, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -1.64}]}, "bone28": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.47, "y": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone30": {"rotate": [{"angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 9.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.77}]}, "bone31": {"rotate": [{"angle": 7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.77, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7}]}, "bone32": {"rotate": [{"angle": 3.27, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1333, "angle": 2.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 7.18, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 3.27}]}, "bone33": {"rotate": [{"angle": 6.26, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.1333, "angle": 5.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 7.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 6.26}]}, "bone34": {"rotate": [{"angle": -1.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.73}]}, "bone35": {"rotate": [{"angle": -4.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.36}]}, "bone36": {"rotate": [{"angle": -5.96, "curve": 0.267, "c2": 0.1, "c3": 0.657, "c4": 0.63}, {"time": 0.6, "angle": -1.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -6.09, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -5.96}]}, "bone37": {"rotate": [{"angle": -4.85, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -6.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "angle": -4.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2667, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -4.85}]}, "bone14": {"rotate": [{"angle": 5.77, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 8.55, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 5.77}]}, "bone38": {"rotate": [{"angle": 8.48, "curve": 0.256, "c2": 0.06, "c3": 0.653, "c4": 0.62}, {"time": 0.6333, "angle": 2.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 8.55, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": 8.48}]}, "bone39": {"rotate": [{"angle": 6.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 8.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": 6.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 6.48}]}, "bone40": {"rotate": [{"angle": 8.48, "curve": 0.256, "c2": 0.06, "c3": 0.653, "c4": 0.62}, {"time": 0.6333, "angle": 2.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 8.55, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": 8.48}]}, "bone41": {"rotate": [{"angle": 6.48, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 8.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": 6.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": 6.48}]}, "bone42": {"rotate": [{"angle": 2.78, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "angle": 8.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 2.78}]}, "bone43": {"rotate": [{"angle": -7.08, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -10.49, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -7.08}]}, "bone44": {"rotate": [{"angle": -10.4, "curve": 0.256, "c2": 0.06, "c3": 0.653, "c4": 0.62}, {"time": 0.6333, "angle": -2.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -10.49, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": -10.4}]}, "bone45": {"rotate": [{"angle": -7.95, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": -10.49, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "angle": -7.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -7.95}]}}}}}