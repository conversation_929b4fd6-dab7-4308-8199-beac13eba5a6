import { _decorator, EventTouch, instantiate, Label, Node, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { FractureLogFightResponse, FractureLogMessage } from "../../../net/protocol/Activity";
import { JsonMgr } from "../../../mgr/JsonMgr";
import ToolExt from "../../../common/ToolExt";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { ItemCost } from "../../../common/ItemCost";
import { FmButton } from "../../../../../platform/src/core/ui/components/FmButton";
import { TimeUtils } from "db://assets/GameScrpit/lib/utils/TimeUtils";
import { ItemCtrl } from "../../../common/ItemCtrl";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { FractureModule } from "db://assets/GameScrpit/module/fracture/FractureModule";
import { BoolValue } from "../../../net/protocol/ExternalMessage";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { FightData } from "../../../fight/FightDefine";
import { RewardMessage } from "../../../net/protocol/Comm";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import ResMgr from "db://assets/GameScrpit/lib/common/ResMgr";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { UIFractureFight } from "../UIFractureFight";
import { UIFractureGift } from "../UIFractureGift";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("FractureLogViewholder")
export class FractureLogViewholder extends ViewHolder {
  @property(Node)
  private lblName: Node = null;
  @property(Node)
  private bgProcess: Node;
  @property(Node)
  private lblProcess: Node;
  @property(Node)
  private lblPower: Node;
  @property(Node)
  private itemFightCost: Node;
  @property(Node)
  private itemLayout: Node;
  @property(Node)
  private btnClaimable: Node = null; // 可领取按钮
  @property(Node)
  private btnUnclaimable: Node = null; // 不可领取按钮
  @property(Node)
  private btnClaimed: Node = null; // 已领取按钮
  @property(Node)
  private btnFight: Node = null; // 战斗按钮
  @property(Node)
  private btnHelp: Node = null; // 求助按钮

  @property(Node)
  private btnHeader: Node = null; // 头像按钮

  @property(Node)
  private lblFightCd: Node = null; // 可战斗cd

  @property(Node)
  private bgMonsterHeader: Node = null; // 头像背景

  private _data: FractureLogMessage = null;
  start() {}

  update(deltaTime: number) {}

  updateData(data: FractureLogMessage) {
    this._data = data;
    // JsonMgr.instance.jsonList.c_fracture[data.monsterId];
    let monster = JsonMgr.instance.jsonList.c_monster[data.monsterId];
    let fracture = JsonMgr.instance.jsonList.c_fracture[data.fractureId];
    if (monster) {
      let monsterShow = JsonMgr.instance.jsonList.c_monsterShow[monster.monsterShowId];
      this.lblName.getComponent(Label).string = monsterShow.name;
      let process = data.remainHp / monster.attr[0][1];
      if (process < 0) {
        process = 0;
      } else if (process > 1) {
        process = 1;
      }
      this.bgProcess.getComponent(Sprite).fillRange = process;
      this.lblProcess.getComponent(Label).string = `${Math.floor(process * 100)}%`;
      this.lblPower.getComponent(Label).string = Formate.format(ToolExt.levelBossPower(monster.attr));
      // this.bgProcess.getComponent(Label).string = `${data}/${data.total}`;
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_ITEM,
        `autoItem/${monsterShow.iconId}`,
        this.bgMonsterHeader.getComponent(Sprite)
      );
      this.btnHeader.active = false;
      this.bgMonsterHeader.active = true;
    } else {
      this.btnHeader.active = true;
      this.bgMonsterHeader.active = false;
      this.lblName.getComponent(Label).string = `${data.npcMessage.nickname}`;
      let process = data.remainHp / data.npcMessage.battleAttrMap[1];
      if (process < 0) {
        process = 0;
      } else if (process > 1) {
        process = 1;
      }
      this.bgProcess.getComponent(Sprite).fillRange = process;
      this.lblProcess.getComponent(Label).string = `${Math.floor(process * 100)}%`;
      this.lblPower.getComponent(Label).string = Formate.format(data.npcMessage.power);
      FmUtils.setHeaderNode(this.btnHeader, data.npcMessage);
    }

    if (data.fightCount == 0) {
      this.itemFightCost.active = false;
    } else {
      this.itemFightCost.active = true;
      this.itemFightCost.getComponent(ItemCost).setItemId(fracture.cost02[0], fracture.cost02[1]);
    }

    if (data.remainHp > 0) {
      this.btnHelp.getComponent(FmButton).selected = false;
      this.btnHelp.getComponent(FmButton).btnEnable = !data.assist;
    } else {
      this.btnHelp.getComponent(FmButton).selected = true;
    }
    if (data.coldTime > TimeUtils.serverTime) {
      this.lblFightCd.active = true;
      this.btnFight.getComponent(FmButton).selected = true;
      FmUtils.setCd(this.lblFightCd, data.coldTime, true, () => {
        this.updateData(data);
      });
    } else {
      FmUtils.setCd(this.lblFightCd, 0, true);
      this.lblFightCd.active = false;
      if (data.remainHp > 0) {
        this.btnFight.getComponent(FmButton).btnEnable = true;
      } else {
        this.btnFight.getComponent(FmButton).btnEnable = false;
      }
    }
    // 判断是否有奖励需要领取
    if (data.remainHp > 0) {
      this.btnClaimed.active = false;
      this.btnClaimable.active = false;
      this.btnUnclaimable.active = true;
    } else {
      if (data.take) {
        this.btnClaimed.active = true;
        this.btnClaimable.active = false;
        this.btnUnclaimable.active = false;
      } else {
        this.btnClaimed.active = false;
        this.btnClaimable.active = true;
        this.btnUnclaimable.active = false;
      }
    }
    let i = 0;
    for (; i < fracture.reward02List.length; i++) {
      let item = this.itemLayout.children[i];
      if (!item) {
        item = instantiate(this.itemLayout.children[0]);
        item.parent = this.itemLayout;
        item.active = true;
      }
      item.getComponent(ItemCtrl).setItemId(fracture.reward02List[i][0], fracture.reward02List[i][1]);
    }
    for (; i < this.itemLayout.children.length; i++) {
      this.itemLayout.children[i].active = false;
    }
  }

  onClickFight(e: EventTouch) {
    log.log("onClickFight");
    AudioMgr.instance.playEffect(1775);
    if (!e.target.getComponent(FmButton).btnEnable) {
      log.log("onClickFight--disable");
      return;
    }
    if (this.itemFightCost.active && !this.itemFightCost.getComponent(ItemCost).isEnough()) {
      //
      RouteManager.uiRouteCtrl.showRoute(UIFractureGift);
      return;
    }
    let bossCallback = (res: FractureLogFightResponse) => {
      log.log("boss战斗的数据====", res);
      if (res.error) {
        this._data = res.fractureLogMessage;
        return;
      }
      let data = JSON.parse(res.replay);
      let args: FightData = {
        fightData: data,
        win: res.win,
        clubBossInfo: res,
        // resAddList: res.resAddList,
        // buddyList: this._buddyList,
      };
      this.updateData(res.fractureLogMessage);
      if (res.error) {
        return;
      }
      RouteManager.uiRouteCtrl.showRoute(UIFractureFight, {
        payload: { data: args, bossId: this._data.monsterId },
        onCloseBack: (args) => {
          this.updateData(this._data);
        },
      });
    };
    // 发送战斗请求
    FractureModule.api.fightFractureLog(this._data.id, bossCallback);
  }
  onClickHelp(e: EventTouch) {
    AudioMgr.instance.playEffect(1776);
    log.log("onClickHelp");
    if (!e.target.getComponent(FmButton).btnEnable || e.target.getComponent(FmButton).selected) {
      log.log("onClickHelp--disable");
      return;
    }
    // 发送战斗请求
    FractureModule.api.assistLogRequest(this._data.id, (data: BoolValue) => {
      //
      this._data.assist = data.value;
      this.updateData(this._data);
    });
  }
  /**领取奖励 */
  onClickClaimable(e: EventTouch) {
    //
    FractureModule.api.takeLogReward(this._data.id, (data: RewardMessage) => {
      this._data.take = true;
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.updateData(this._data);
    });
  }
}

export class FractureLogAdapter extends ListAdapter {
  constructor(item: Node) {
    super();
    this._item = item;
  }
  private _item: Node = null;
  private _datas: any[] = [];
  public setDatas(datas: FractureLogMessage[]) {
    // 添加排序逻辑
    this._datas = datas.sort((a, b) => {
      // 定义优先级计算函数
      const getPriority = (data: FractureLogMessage) => {
        if (data.remainHp > 0 && !data.assist && data.fightCount === 0) return 0; // 最高优先级
        if (data.remainHp > 0 && !data.assist) return 1;
        if (data.remainHp > 0) return 2;
        if (!data.take) return 3;
        return 4; // 最低优先级
      };

      return getPriority(a) - getPriority(b);
    });
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true; // 设置为active状态
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(FractureLogViewholder).updateData(this._datas[position]);
  }
  getCount(): number {
    return this._datas.length;
  }
}
