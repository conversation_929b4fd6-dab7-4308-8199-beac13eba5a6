import { UIShengDianChengjiu } from "../../game/ui/ui_shengdian/UIShengDianChengjiu";
import { UIShengDianGong } from "../../game/ui/ui_shengdian/UIShengDianGong";
import { UIShengDianMain } from "../../game/ui/ui_shengdian/UIShengDianMain";
import { UIShengDianRongyaoge } from "../../game/ui/ui_shengdian/UIShengDianRongyaoge";
import { UIShengDianShenji } from "../../game/ui/ui_shengdian/UIShengDianShenji";
import { UIShengDianShow } from "../../game/ui/ui_shengdian/UIShengDianShow";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum ShengDianRouteItem {
  UIShengDianChengjiu = "UIShengDianChengjiu",
  UIShengDianMain = "UIShengDianMain",
  UIShengDianShenji = "UIShengDianShenji",
  UIShengDianGong = "UIShengDianGong",
  UIShengDianRongyaoge = "UIShengDianRongyaoge",
  UIShengDianShow = "UIShengDianShow",
}
export class ShengDianRoute {
  rotueTables: Recording[] = [
    {
      node: UIShengDianChengjiu,
      uiName: ShengDianRouteItem.UIShengDianChengjiu,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIShengDianMain,
      uiName: ShengDianRouteItem.UIShengDianMain,
      keep: false,
      relevanceUIList: [],
      music: 1121,
    },
    {
      node: UIShengDianShenji,
      uiName: ShengDianRouteItem.UIShengDianShenji,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIShengDianGong,
      uiName: ShengDianRouteItem.UIShengDianGong,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIShengDianRongyaoge,
      uiName: ShengDianRouteItem.UIShengDianRongyaoge,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIShengDianShow,
      uiName: ShengDianRouteItem.UIShengDianShow,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
