import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { ChengjiuAdapter } from "./adapter/ChengjiuAdapter";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { ListLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/ListLayoutManager";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Tue Jan 14 2025 15:52:06 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_shengdian/UIShengDianChengjiu.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIShengDianChengjiu")
export class UIShengDianChengjiu extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SHENGDIAN}?prefab/ui/UIShengDianChengjiu`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    // do something
    let adapter = new ChengjiuAdapter(this.getNode("tree_viewholder"));
    this.getNode("node_list").addComponent(ListLayoutManager);
    this.getNode("node_list").getComponent(AdapterView).setAdapter(adapter);
    adapter.notifyDataSetChanged();
    this.getNode("node_list").once(Node.EventType.SIZE_CHANGED, () => {
      adapter.notifyDataSetChanged();
    });
  }
  private on_click_btn_back() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
