import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { AdventureTaskVO, RedeemPackVO, AdventureBigPrizeSubVO } from "../activity/ActivityConfig";
import { PlayerModule } from "../player/PlayerModule";
import { Check, SONHAI_LJAWARD_State, SonhaiMsgEnum } from "./SonhaiConfig";
import { sonhai_achieveId, sonhai_activityId, SonhaiModule } from "./SonhaiModule";

/**
 * 模块逻辑处理
 */
export class SonhaiService {
  private _tickId: number = null;

  public async init() {
    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }
    MsgMgr.off(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE, this.updatePopover, this);
    MsgMgr.on(SonhaiMsgEnum.SONHAIMSGENUM_RED_DOT_UPDATE, this.updatePopover, this);

    // this.addTaskRed();
    // this.addXslbRed();

    this._tickId = TickerMgr.setInterval(3, this.updatePopover.bind(this), false);
  }

  // private async addTaskRed() {
  //   let redNameList = [];
  //   let taskIndexList = SonhaiModule.data.adventureMessage.taskIndexList;
  //   for (let i = 0; i < taskIndexList.length; i++) {
  //     let index = taskIndexList[i];
  //     redNameList.push("task_item_" + index);
  //   }
  //   BadgeMgr.instance.addListBadgeItem(BadgeType.UITerritory.btn_fd_shtx.btn_shtx_renwu, redNameList);
  // }
  // private async addXslbRed() {
  //   let redNameList = [];
  //   const LbId: number = 11101201;
  //   let db = await SonhaiModule.data.getSonhaidb(sonhai_activityId);
  //   let lbList = null;
  //   for (let i = 0; i < db.redeemList.length; i++) {
  //     let list = db.redeemList[i];
  //     let id = list[0].id;
  //     if (LbId == id) {
  //       lbList = list;
  //     }
  //   }
  //   for (let i = 0; i < lbList.length; i++) {
  //     let info: RedeemPackVO = lbList[i];
  //     redNameList.push("SONHAI_XSLB" + info.id);
  //   }
  //   BadgeMgr.instance.addListBadgeItem(BadgeType.UITerritory.btn_fd_shtx.btn_xslb, redNameList);
  // }

  /**
   * 红点更新方法
   */
  private updatePopover() {
    this.getFreeBigPrizeBubInfo();
    this.getTaskIsOk();
    this.getLeijiBigAward();
    this.getchouJiangRed();
  }

  /**判断狮子是否出现红点 */
  private async getchouJiangRed() {
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    let id = db.costList[0];
    let num = PlayerModule.data.getItemNum(id);

    if (num > 0) {
      BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_fd_shtx.xiezhi.id, true);
    } else {
      BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_fd_shtx.xiezhi.id, false);
    }
  }

  /**判断累计大奖是否可以领取 */
  private async getLeijiBigAward() {
    let list = await this.newLjList();
    let isBool = false;
    for (let i = 0; i < list.length; i++) {
      let state = list[i].state;
      if (state == SONHAI_LJAWARD_State.可以领取) {
        isBool = true;
        break;
      }
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_fd_shtx.leiji_award_item.id, isBool);
  }

  /**判断活动任务是否有完成的 */
  private async getTaskIsOk() {
    let taskIndexList = SonhaiModule.data.adventureMessage.taskIndexList;
    let targetValList = SonhaiModule.data.adventureMessage.targetValList;
    let taskTakeList = SonhaiModule.data.adventureMessage.taskTakeList;
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    let taskVO: AdventureTaskVO = db.taskVO;
    let isBool = false;
    for (let i = 0; i < taskIndexList.length; i++) {
      let index = taskIndexList[i];
      let avTaskInfo = taskVO.taskList[index];
      let val = targetValList[i];
      //let isBool = false;
      if (taskTakeList.indexOf(i) == -1) {
        if (val >= avTaskInfo[1]) {
          isBool = true;
          break;
        }
      }
      //BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_fd_shtx.btn_xslb["task_item_" + index].id, isBool);
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_fd_shtx.btn_shtx_renwu.id, isBool);
  }

  /**判断限时礼包里是否有免费领取的礼包 */
  private async getFreeBigPrizeBubInfo() {
    const LbId: number = 11101201;
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    let lbList = null;
    for (let i = 0; i < db.redeemList.length; i++) {
      let list = db.redeemList[i];
      let id = list[0].id;
      if (LbId == id) {
        lbList = list;
      }
    }

    let isBool = false;
    for (let i = 0; i < lbList.length; i++) {
      let info: RedeemPackVO = lbList[i];
      //let isBool = false;
      if (info.price == 0 && !info.cost) {
        let redem_num = SonhaiModule.data.redeemMap[info.id] || 0;
        if (redem_num < info.max) {
          isBool = true;
          break;
        }
      }
      // BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_fd_shtx.btn_xslb["SONHAI_XSLB" + info.id].id, isBool);
    }
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_fd_shtx.btn_xslb.id, isBool);
  }

  /**根据id，获取某个大奖信息配置 */
  public async getBigPrizeBubInfo(id: number): Promise<AdventureBigPrizeSubVO> {
    let bigPrizeVO = (await SonhaiModule.data.getSonhaiDb(sonhai_activityId)).bigPrizeVO;
    let subList = bigPrizeVO.subList;

    for (let i = 0; i < subList.length; i++) {
      let info = subList[i];

      if (info.id == id) {
        return info;
      }
    }
  }

  public async newLjList(): Promise<any> {
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    let achieveVO = null;
    for (let i = 0; i < db.achieveVOList.length; i++) {
      if (db.achieveVOList[i].id == sonhai_achieveId) {
        achieveVO = db.achieveVOList[i];
        break;
      }
    }

    let basicRewardList = achieveVO.basicRewardList;
    let requireList = achieveVO.requireList;

    let arr1 = [];
    let arr2 = [];
    let arr3 = [];
    let arr4 = [];
    for (let i = 0; i < basicRewardList.length; i++) {
      let param = new Check();
      param.index = i;
      param.require = requireList[i];
      let state = SonhaiModule.service.checkGet(param);
      let obj = {
        basicReward: basicRewardList[i],
        require: requireList[i],
        param: param,
        state: state,
      };
      if (state == SONHAI_LJAWARD_State.可以领取) {
        arr1.push(obj);
      } else if (state == SONHAI_LJAWARD_State.未达成) {
        arr2.push(obj);
      } else if (state == SONHAI_LJAWARD_State.领取过) {
        arr3.push(obj);
      } else if (state == SONHAI_LJAWARD_State.异常状态) {
        arr4.push(obj);
      }
    }

    let list = arr1.concat(arr2, arr3, arr4);

    return list;
  }

  public checkGet(info: Check) {
    let achieveTakeList = SonhaiModule.data.getAchieveMap(sonhai_achieveId).basicTakeList;
    let curCostCount = SonhaiModule.data.getAchieveMap(sonhai_achieveId).targetVal;

    if (info.require > curCostCount) {
      return SONHAI_LJAWARD_State.未达成; //不行
    }

    if (achieveTakeList.indexOf(info.index) != -1) {
      return SONHAI_LJAWARD_State.领取过; //已领取
    }

    if (curCostCount >= info.require) {
      return SONHAI_LJAWARD_State.可以领取; //可以领
    }

    return SONHAI_LJAWARD_State.异常状态; //不知道
  }
}
