import { Layers, Node, _decorator, isValid } from "cc";
import { Section } from "../../../lib/object/Section";
import GameObject, { GO_STATE } from "../../../lib/object/GameObject";
import ShakeSection from "../../../lib/object/ShakeSection";

const { ccclass, property } = _decorator;

@ccclass("ManagerSection")
export default class ManagerSection extends Section {
  public root: GameObject = null;
  public objects: Array<GameObject> = new Array<GameObject>();
  public static sectionName(): string {
    return "MgrSection";
  }
  protected createRoot(name: string, parent: Node): GameObject {
    if (parent == null) {
      return;
    }
    this.root = new GameObject();
    // this.root.layer = Layers.Enum["UILayer"];
    this.root.name = name;
    parent.addChild(this.root);
    this.root.createSection(ShakeSection);

    this.root.walk((child) => (child.layer = parent.layer));
    return this.root;
  }

  public addChild(node: GameObject, parent: Node = null) {
    if (node == null) {
      return;
    }
    // node.layer = Layers.Enum["UILayer"];
    this.objects.push(node);
    if (node.parent != null) {
      return;
    }
    if (parent) {
      parent.addChild(node);
    } else {
      this.root && this.root.addChild(node);
    }

    node.walk((child) => (child.layer = node.parent.layer));
  }

  public onRemove() {
    super.onRemove();
    for (let i = 0; i < this.objects.length; i++) {
      this.objects[i].remove();
    }
    this.root && this.root.removeFromParent();
    this.root && this.root.destroy();
  }

  public updateSelf(dt) {
    super.updateSelf(dt);
    this.updateRoot(dt);
    this.updateObjects(dt);
  }

  protected updateRoot(dt) {
    if (this.root == null) {
      return;
    }
    this.root.tick(dt);
  }

  protected updateObjects(dt) {
    for (let i = this.objects.length - 1; i >= 0; i--) {
      if (this.objects[i].getState() == GO_STATE.REMOVE) {
        if (isValid(this.objects[i])) {
          this.objects[i].destroy();
        }
        this.objects.splice(i, 1);
      } else {
        this.objects[i].tick(dt);
      }
    }
  }

  public getRoot() {
    return this.root;
  }

  public shake(shakeId: number) {
    // let shakeConfig = DBMgr.data.shakeDB[shakeId];
    // if (shakeConfig == null) { return; }
    // this.root.getSection(ShakeSection).shake(shakeConfig);
  }

  public pauseAllObject() {
    this.setPause(true);
    for (let i = 0; i < this.objects.length; i++) {
      this.objects[i].setPause(true);
    }
  }

  public resumeAllObject() {
    this.setPause(false);
    for (let i = 0; i < this.objects.length; i++) {
      this.objects[i].setPause(false);
    }
  }

  public objectsPush(node) {
    this.objects.push(node);
  }
}
