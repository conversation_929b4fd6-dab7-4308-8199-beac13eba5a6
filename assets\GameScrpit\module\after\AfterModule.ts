import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { ActivityModule } from "../activity/ActivityModule";
import { AfterApi } from "./AfterApi";
import { AfterConfig } from "./AfterConfig";
import { AfterData } from "./AfterData";
import { AfterRoute } from "./AfterRoute";
import { AfterService } from "./AfterService";
import { AfterSubscriber } from "./AfterSubscriber";
import { AfterViewModel } from "./AfterViewModel";

export class AfterModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): AfterModule {
    if (!GameData.instance.AfterModule) {
      GameData.instance.AfterModule = new AfterModule();
    }
    return GameData.instance.AfterModule;
  }
  private _data = new AfterData();
  private _api = new AfterApi();
  private _config = new AfterConfig();
  private _viewModel = new AfterViewModel();
  private _route = new AfterRoute();
  private _service = new AfterService();
  private _subscriber = new AfterSubscriber();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }
  public static get service() {
    return this.instance._service;
  }

  public static get route() {
    return this.instance._route;
  }

  public init(data?: any) {
    // 初始化模块
    this._data = new AfterData();
    this._api = new AfterApi();
    this._config = new AfterConfig();
    this._viewModel = new AfterViewModel();
    this._route = new AfterRoute();
    this._service = new AfterService();
    this._subscriber = new AfterSubscriber();

    // 初始化数据
    // if (ActivityModule.service.checkActivityUnlock(10901)) {
    // }
    AfterModule.api.topUpInfo(10901);
    this._subscriber.register();
    this._route.init();
    this._service.init();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
