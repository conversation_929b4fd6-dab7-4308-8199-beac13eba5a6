{"skeleton": {"hash": "8a8Qd/a1UWMbMDn4ci3VfyiDbBA=", "spine": "3.8.75", "x": -174.46, "y": -128.65, "width": 348.43, "height": 259.19, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "rotation": -10.33, "x": -1.3, "scaleX": 1.1449, "scaleY": 2.1323}, {"name": "bone2", "parent": "root", "x": -0.24, "y": 1.13, "scaleX": 1.361, "scaleY": 0.9682}, {"name": "bone3", "parent": "bone2"}, {"name": "line1", "parent": "root", "x": -12.49, "y": -33.49}, {"name": "bone5", "parent": "line1", "length": 18.63, "rotation": 89.88, "x": -228.18, "y": 21.52}, {"name": "bone6", "parent": "bone5", "length": 18.63, "x": 18.63}, {"name": "bone7", "parent": "bone6", "length": 18.63, "x": 18.63}, {"name": "bone8", "parent": "bone7", "length": 18.63, "x": 18.63}, {"name": "bone9", "parent": "bone8", "length": 18.63, "x": 18.63}, {"name": "bone10", "parent": "bone9", "length": 18.63, "x": 18.63}, {"name": "bone11", "parent": "bone10", "length": 18.63, "x": 18.63}, {"name": "bone12", "parent": "bone11", "length": 18.63, "x": 18.63}, {"name": "bone13", "parent": "bone12", "length": 18.63, "x": 18.63}, {"name": "bone14", "parent": "bone13", "length": 18.63, "x": 18.63}, {"name": "line2", "parent": "root", "x": -7.63, "y": -35.92, "scaleX": -1}, {"name": "bone16", "parent": "line2", "length": 18.63, "rotation": 89.88, "x": -228.18, "y": 21.52}, {"name": "bone17", "parent": "bone16", "length": 18.63, "x": 18.63}, {"name": "bone18", "parent": "bone17", "length": 18.63, "x": 18.63}, {"name": "bone19", "parent": "bone18", "length": 18.63, "x": 18.63}, {"name": "bone20", "parent": "bone19", "length": 18.63, "x": 18.63}, {"name": "bone21", "parent": "bone20", "length": 18.63, "x": 18.63}, {"name": "bone22", "parent": "bone21", "length": 18.63, "x": 18.63}, {"name": "bone23", "parent": "bone22", "length": 18.63, "x": 18.63}, {"name": "bone24", "parent": "bone23", "length": 18.63, "x": 18.63}, {"name": "bone25", "parent": "bone24", "length": 18.63, "x": 18.63}, {"name": "line3", "parent": "root", "x": -18.83, "y": -38.77}, {"name": "bone15", "parent": "line3", "length": 18.63, "rotation": 89.88, "x": -228.18, "y": 21.52}, {"name": "bone26", "parent": "bone15", "length": 18.63, "x": 18.63}, {"name": "bone27", "parent": "bone26", "length": 18.63, "x": 18.63}, {"name": "bone28", "parent": "bone27", "length": 18.63, "x": 18.63}, {"name": "bone29", "parent": "bone28", "length": 18.63, "x": 18.63}, {"name": "bone30", "parent": "bone29", "length": 18.63, "x": 18.63}, {"name": "bone31", "parent": "bone30", "length": 18.63, "x": 18.63}, {"name": "bone32", "parent": "bone31", "length": 18.63, "x": 18.63}, {"name": "bone33", "parent": "bone32", "length": 18.63, "x": 18.63}, {"name": "bone34", "parent": "bone33", "length": 18.63, "x": 18.63}, {"name": "line4", "parent": "root", "x": -1.3, "y": -41.2, "scaleX": -1}, {"name": "bone35", "parent": "line4", "length": 18.63, "rotation": 89.88, "x": -228.18, "y": 21.52}, {"name": "bone36", "parent": "bone35", "length": 18.63, "x": 18.63}, {"name": "bone37", "parent": "bone36", "length": 18.63, "x": 18.63}, {"name": "bone38", "parent": "bone37", "length": 18.63, "x": 18.63}, {"name": "bone39", "parent": "bone38", "length": 18.63, "x": 18.63}, {"name": "bone40", "parent": "bone39", "length": 18.63, "x": 18.63}, {"name": "bone41", "parent": "bone40", "length": 18.63, "x": 18.63}, {"name": "bone42", "parent": "bone41", "length": 18.63, "x": 18.63}, {"name": "bone43", "parent": "bone42", "length": 18.63, "x": 18.63}, {"name": "bone44", "parent": "bone43", "length": 18.63, "x": 18.63}, {"name": "line5", "parent": "root", "x": 2.8, "y": -30.75}, {"name": "bone45", "parent": "line5", "length": 45.7, "rotation": 89.8, "x": 17.8, "y": -14.52}, {"name": "bone46", "parent": "bone45", "length": 45.7, "x": 45.7}, {"name": "bone47", "parent": "bone46", "length": 45.7, "x": 45.7}, {"name": "bone48", "parent": "bone47", "length": 45.7, "x": 45.7}, {"name": "qiu", "parent": "root", "x": -0.24, "y": 42.08, "scaleX": 1.1832, "scaleY": 1.1832}], "slots": [{"name": "yzzl_skill_zd_00000", "bone": "qiu"}, {"name": "dishangshui00", "bone": "bone", "color": "00fff8a6", "dark": "30c6d1", "attachment": "dishangshui18"}, {"name": "A_ring_glow", "bone": "bone3", "color": "0094ffa1", "attachment": "A_ring_glow", "blend": "additive"}, {"name": "bujian11", "bone": "root"}, {"name": "linelight11", "bone": "line1", "dark": "00fff8", "attachment": "linelight11", "blend": "additive"}, {"name": "line1", "bone": "line1", "dark": "00fff8", "attachment": "line1", "blend": "additive"}, {"name": "line3", "bone": "line3", "dark": "00fff8", "attachment": "line1", "blend": "additive"}, {"name": "line2", "bone": "line2", "dark": "00fff8", "attachment": "line1", "blend": "additive"}, {"name": "line4", "bone": "line4", "dark": "00fff8", "attachment": "line1", "blend": "additive"}, {"name": "linelight12", "bone": "root", "dark": "00fff8", "attachment": "linelight11", "blend": "additive"}, {"name": "linelight13", "bone": "root", "dark": "00fff8", "attachment": "linelight11", "blend": "additive"}, {"name": "linelight14", "bone": "root", "dark": "00fff8", "attachment": "linelight11", "blend": "additive"}, {"name": "linelight15", "bone": "line5", "dark": "00fff8", "attachment": "linelight11", "blend": "additive"}, {"name": "bone4", "bone": "line5", "attachment": "bone4"}], "path": [{"name": "bone4", "order": 4, "bones": ["bone45", "bone46", "bone47", "bone48"], "target": "bone4", "rotation": -11, "position": -0.1724, "spacing": -42.8}, {"name": "line1", "bones": ["bone5", "bone6", "bone7", "bone8", "bone9", "bone10", "bone11", "bone12", "bone13", "bone14"], "target": "line1", "spacingMode": "percent"}, {"name": "line2", "order": 1, "bones": ["bone16", "bone17", "bone18", "bone19", "bone20", "bone21", "bone22", "bone23", "bone24", "bone25"], "target": "line2", "spacingMode": "percent"}, {"name": "line3", "order": 3, "bones": ["bone15", "bone26", "bone27", "bone28", "bone29", "bone30", "bone31", "bone32", "bone33", "bone34"], "target": "line3", "spacingMode": "percent"}, {"name": "line4", "order": 2, "bones": ["bone35", "bone36", "bone37", "bone38", "bone39", "bone40", "bone41", "bone42", "bone43", "bone44"], "target": "line4", "spacingMode": "percent"}], "skins": [{"name": "default", "attachments": {"line2": {"line1": {"type": "path", "lengths": [140.16, 294.71, 465.03], "vertexCount": 9, "vertices": [7.03, 11.32, 7.03, 11.32, 7.03, 11.32, -96.51, 15.94, -100.78, 83.48, -104.64, 144.64, 4.87, 161.71, 10.55, 180.79, 21.16, 216.47]}}, "linelight11": {"linelight11": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.94118, 0, 0.88235, 0, 0.82353, 0, 0.76471, 0, 0.70588, 0, 0.64706, 0, 0.58824, 0, 0.52941, 0, 0.47059, 0, 0.41176, 0, 0.35294, 0, 0.29412, 0, 0.23529, 0, 0.17647, 0, 0.11765, 0, 0.05882, 0, 0, 0.5, 0, 1, 0, 1, 0.05882, 1, 0.11765, 1, 0.17647, 1, 0.23529, 1, 0.29412, 1, 0.35294, 1, 0.41176, 1, 0.47059, 1, 0.52941, 1, 0.58824, 1, 0.64706, 1, 0.70588, 1, 0.76471, 1, 0.82353, 1, 0.88235, 1, 0.94118, 0.5, 0.94118, 0.5, 0.88235, 0.5, 0.82353, 0.5, 0.76471, 0.5, 0.70588, 0.5, 0.64706, 0.5, 0.58824, 0.5, 0.52941, 0.5, 0.47059, 0.5, 0.41176, 0.5, 0.35294, 0.5, 0.29412, 0.5, 0.23529, 0.5, 0.17647, 0.5, 0.11765, 0.5, 0.05882], "triangles": [51, 52, 23, 23, 52, 22, 52, 53, 22, 22, 53, 21, 53, 20, 21, 16, 17, 52, 52, 17, 53, 17, 18, 53, 53, 18, 20, 18, 19, 20, 50, 51, 24, 24, 51, 23, 15, 16, 51, 51, 16, 52, 48, 49, 26, 26, 49, 25, 49, 50, 25, 25, 50, 24, 13, 14, 49, 49, 14, 50, 14, 15, 50, 50, 15, 51, 28, 47, 27, 47, 48, 27, 27, 48, 26, 47, 12, 48, 12, 13, 48, 48, 13, 49, 45, 46, 29, 29, 46, 28, 46, 47, 28, 10, 11, 46, 46, 11, 47, 11, 12, 47, 31, 44, 30, 44, 45, 30, 30, 45, 29, 44, 9, 45, 9, 10, 45, 45, 10, 46, 42, 43, 32, 32, 43, 31, 43, 44, 31, 7, 8, 43, 43, 8, 44, 8, 9, 44, 34, 41, 33, 41, 42, 33, 33, 42, 32, 41, 6, 42, 6, 7, 42, 42, 7, 43, 39, 40, 35, 35, 40, 34, 40, 41, 34, 4, 5, 40, 40, 5, 41, 5, 6, 41, 0, 1, 37, 1, 38, 37, 37, 38, 36, 38, 39, 36, 36, 39, 35, 1, 2, 38, 2, 3, 38, 38, 3, 39, 3, 4, 39, 39, 4, 40], "vertices": [1, 5, -10.34, -16.85, 1, 1, 5, -10.37, -0.35, 1, 1, 5, -10.41, 16.14, 1, 2, 5, 1.71, 16.17, 0.99138, 6, -16.91, 16.17, 0.00862, 2, 5, 13.83, 16.19, 0.74935, 6, -4.8, 16.19, 0.25065, 3, 5, 25.95, 16.22, 0.18274, 6, 7.32, 16.22, 0.7532, 7, -11.3, 16.22, 0.06406, 4, 5, 38.06, 16.24, 0.00401, 6, 19.44, 16.24, 0.46604, 7, 0.81, 16.24, 0.52325, 8, -17.81, 16.24, 0.00669, 3, 6, 31.56, 16.27, 0.05052, 7, 12.93, 16.27, 0.732, 8, -5.69, 16.27, 0.21748, 3, 7, 25.05, 16.29, 0.2212, 8, 6.42, 16.29, 0.72244, 9, -12.2, 16.29, 0.05636, 4, 7, 37.17, 16.32, 0.00805, 8, 18.54, 16.32, 0.4947, 9, -0.08, 16.32, 0.49249, 10, -18.71, 16.32, 0.00476, 3, 8, 30.66, 16.34, 0.06062, 9, 12.03, 16.34, 0.74136, 10, -6.59, 16.34, 0.19802, 3, 9, 24.15, 16.36, 0.23604, 10, 5.53, 16.36, 0.71885, 11, -13.1, 16.36, 0.04511, 4, 9, 36.27, 16.39, 0.00842, 10, 17.64, 16.39, 0.53527, 11, -0.98, 16.39, 0.45351, 12, -19.61, 16.39, 0.0028, 3, 10, 29.76, 16.41, 0.07264, 11, 11.14, 16.41, 0.76232, 12, -7.49, 16.41, 0.16504, 3, 11, 23.25, 16.44, 0.28697, 12, 4.63, 16.44, 0.68016, 13, -14, 16.44, 0.03287, 4, 11, 35.37, 16.46, 0.01586, 12, 16.75, 16.46, 0.59878, 13, -1.88, 16.46, 0.38511, 14, -20.5, 16.46, 0.00024, 3, 12, 28.86, 16.49, 0.11432, 13, 10.24, 16.49, 0.77351, 14, -8.39, 16.49, 0.11217, 3, 12, 40.98, 16.51, 0.00132, 13, 22.36, 16.51, 0.35035, 14, 3.73, 16.51, 0.64832, 2, 13, 34.47, 16.53, 0.02752, 14, 15.85, 16.53, 0.97248, 1, 14, 27.97, 16.56, 1, 1, 14, 28, 0.06, 1, 1, 14, 28.03, -16.44, 1, 2, 13, 34.54, -16.47, 0.0261, 14, 15.92, -16.47, 0.9739, 3, 12, 41.05, -16.49, 0.0007, 13, 22.42, -16.49, 0.34188, 14, 3.8, -16.49, 0.65743, 3, 12, 28.93, -16.51, 0.10693, 13, 10.31, -16.51, 0.77391, 14, -8.32, -16.51, 0.11916, 4, 11, 35.44, -16.54, 0.0103, 12, 16.81, -16.54, 0.60345, 13, -1.81, -16.54, 0.38573, 14, -20.44, -16.54, 0.00051, 3, 11, 23.32, -16.56, 0.25924, 12, 4.69, -16.56, 0.71283, 13, -13.93, -16.56, 0.02793, 3, 10, 29.83, -16.59, 0.06633, 11, 11.2, -16.59, 0.74026, 12, -7.42, -16.59, 0.19341, 4, 9, 36.34, -16.61, 0.01092, 10, 17.71, -16.61, 0.50571, 11, -0.92, -16.61, 0.47636, 12, -19.54, -16.61, 0.00701, 3, 9, 24.22, -16.64, 0.24696, 10, 5.59, -16.64, 0.69212, 11, -13.03, -16.64, 0.06092, 3, 8, 30.73, -16.66, 0.06657, 9, 12.1, -16.66, 0.73187, 10, -6.53, -16.66, 0.20156, 4, 7, 37.23, -16.68, 0.00977, 8, 18.61, -16.68, 0.4885, 9, -0.02, -16.68, 0.49472, 10, -18.64, -16.68, 0.00701, 3, 7, 25.12, -16.71, 0.2327, 8, 6.49, -16.71, 0.69659, 9, -12.14, -16.71, 0.07071, 4, 6, 31.62, -16.73, 0.05536, 7, 13, -16.73, 0.72792, 8, -5.63, -16.73, 0.21672, 9, -24.25, -16.73, 1e-05, 4, 5, 38.13, -16.76, 0.00288, 6, 19.51, -16.76, 0.46954, 7, 0.88, -16.76, 0.51928, 8, -17.75, -16.76, 0.0083, 3, 5, 26.01, -16.78, 0.16553, 6, 7.39, -16.78, 0.76322, 7, -11.24, -16.78, 0.07126, 2, 5, 13.9, -16.81, 0.72201, 6, -4.73, -16.81, 0.27799, 2, 5, 1.78, -16.83, 0.9857, 6, -16.85, -16.83, 0.0143, 1, 5, 1.74, -0.33, 1, 1, 5, 13.86, -0.31, 1, 1, 6, 7.35, -0.28, 1, 2, 6, 19.47, -0.26, 0.27723, 7, 0.85, -0.26, 0.72277, 1, 7, 12.96, -0.23, 1, 2, 8, 6.46, -0.21, 0.99995, 9, -12.17, -0.21, 5e-05, 2, 8, 18.57, -0.18, 0.51337, 9, -0.05, -0.18, 0.48663, 2, 9, 12.07, -0.16, 0.99984, 10, -6.56, -0.16, 0.00016, 2, 10, 5.56, -0.14, 0.99999, 11, -13.07, -0.14, 1e-05, 2, 10, 17.68, -0.11, 0.7479, 11, -0.95, -0.11, 0.2521, 1, 11, 11.17, -0.09, 1, 1, 12, 4.66, -0.06, 1, 2, 12, 16.78, -0.04, 0.96428, 13, -1.85, -0.04, 0.03572, 1, 13, 10.27, -0.01, 1, 1, 14, 3.76, 0.01, 1, 1, 14, 15.88, 0.03, 1], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 0], "width": 33, "height": 206}}, "linelight12": {"linelight11": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.9375, 0, 0.875, 0, 0.8125, 0, 0.75, 0, 0.6875, 0, 0.625, 0, 0.5625, 0, 0.5, 0, 0.4375, 0, 0.375, 0, 0.3125, 0, 0.25, 0, 0.1875, 0, 0.125, 0, 0.0625, 0, 0, 0.5, 0, 1, 0, 1, 0.0625, 1, 0.125, 1, 0.1875, 1, 0.25, 1, 0.3125, 1, 0.375, 1, 0.4375, 1, 0.5, 1, 0.5625, 1, 0.625, 1, 0.6875, 1, 0.75, 1, 0.8125, 1, 0.875, 1, 0.9375, 0.5, 0.9375, 0.5, 0.875, 0.5, 0.8125, 0.5, 0.75, 0.5, 0.6875, 0.5, 0.625, 0.5, 0.5625, 0.5, 0.5, 0.5, 0.4375, 0.5, 0.375, 0.5, 0.3125, 0.5, 0.25, 0.5, 0.1875, 0.5, 0.125, 0.5, 0.0625], "triangles": [17, 18, 19, 50, 17, 19, 16, 17, 50, 49, 16, 50, 15, 16, 49, 48, 15, 49, 14, 15, 48, 47, 14, 48, 13, 14, 47, 46, 13, 47, 12, 13, 46, 45, 12, 46, 11, 12, 45, 44, 11, 45, 10, 11, 44, 43, 10, 44, 9, 10, 43, 42, 9, 43, 8, 9, 42, 41, 8, 42, 7, 8, 41, 40, 7, 41, 6, 7, 40, 39, 6, 40, 5, 6, 39, 38, 5, 39, 4, 5, 38, 37, 4, 38, 3, 4, 37, 36, 3, 37, 2, 3, 36, 1, 2, 36, 50, 19, 20, 21, 50, 20, 49, 50, 21, 22, 49, 21, 48, 49, 22, 23, 48, 22, 47, 48, 23, 24, 47, 23, 46, 47, 24, 25, 46, 24, 45, 46, 25, 26, 45, 25, 44, 45, 26, 27, 44, 26, 43, 44, 27, 28, 43, 27, 42, 43, 28, 29, 42, 28, 41, 42, 29, 30, 41, 29, 40, 41, 30, 31, 40, 30, 39, 40, 31, 32, 39, 31, 38, 39, 32, 33, 38, 32, 37, 38, 33, 34, 37, 33, 36, 37, 34, 35, 36, 34, 1, 36, 35, 0, 1, 35], "vertices": [1, 16, -9.35, 16.1, 1, 1, 16, -9.32, -0.4, 1, 1, 16, -9.29, -16.9, 1, 2, 16, 3.59, -16.87, 0.95802, 17, -15.04, -16.87, 0.04198, 3, 16, 16.46, -16.84, 0.56363, 17, -2.16, -16.84, 0.43362, 18, -20.79, -16.84, 0.00274, 3, 16, 29.34, -16.82, 0.06257, 17, 10.71, -16.82, 0.76807, 18, -7.91, -16.82, 0.16936, 3, 17, 23.59, -16.79, 0.26472, 18, 4.96, -16.79, 0.69685, 19, -13.66, -16.79, 0.03844, 4, 17, 36.46, -16.77, 0.00892, 18, 17.84, -16.77, 0.53134, 19, -0.79, -16.77, 0.45493, 20, -19.41, -16.77, 0.0048, 3, 18, 30.71, -16.74, 0.05885, 19, 12.09, -16.74, 0.73633, 20, -6.54, -16.74, 0.20482, 3, 19, 24.96, -16.71, 0.21995, 20, 6.34, -16.71, 0.72469, 21, -12.29, -16.71, 0.05536, 4, 19, 37.84, -16.69, 0.00611, 20, 19.21, -16.69, 0.47144, 21, 0.59, -16.69, 0.51278, 22, -18.04, -16.69, 0.00967, 3, 20, 32.09, -16.66, 0.04212, 21, 13.46, -16.66, 0.69356, 22, -5.16, -16.66, 0.26433, 3, 21, 26.34, -16.64, 0.1619, 22, 7.71, -16.64, 0.76274, 23, -10.91, -16.64, 0.07537, 4, 21, 39.21, -16.61, 0.00206, 22, 20.59, -16.61, 0.40558, 23, 1.96, -16.61, 0.57233, 24, -16.66, -16.61, 0.02003, 4, 22, 33.46, -16.59, 0.02837, 23, 14.84, -16.59, 0.64494, 24, -3.79, -16.59, 0.32666, 25, -22.41, -16.59, 3e-05, 3, 23, 27.71, -16.56, 0.1228, 24, 9.09, -16.56, 0.77006, 25, -9.54, -16.56, 0.10714, 3, 23, 40.59, -16.53, 0.00063, 24, 21.96, -16.53, 0.33285, 25, 3.34, -16.53, 0.66652, 2, 24, 34.84, -16.51, 0.01636, 25, 16.21, -16.51, 0.98364, 1, 25, 29.09, -16.48, 1, 1, 25, 29.05, 0.02, 1, 1, 25, 29.02, 16.52, 1, 2, 24, 34.77, 16.49, 0.01352, 25, 16.15, 16.49, 0.98648, 3, 23, 40.52, 16.47, 0.00099, 24, 21.9, 16.47, 0.31308, 25, 3.27, 16.47, 0.68594, 3, 23, 27.65, 16.44, 0.13819, 24, 9.02, 16.44, 0.73908, 25, -9.6, 16.44, 0.12274, 4, 22, 33.4, 16.41, 0.03205, 23, 14.77, 16.41, 0.67308, 24, -3.85, 16.41, 0.29449, 25, -22.48, 16.41, 0.00039, 4, 21, 39.15, 16.39, 0.00084, 22, 20.52, 16.39, 0.41501, 23, 1.9, 16.39, 0.57285, 24, -16.73, 16.39, 0.01131, 3, 21, 26.27, 16.36, 0.14626, 22, 7.65, 16.36, 0.77853, 23, -10.98, 16.36, 0.0752, 3, 20, 32.02, 16.34, 0.04835, 21, 13.4, 16.34, 0.68374, 22, -5.23, 16.34, 0.26791, 4, 19, 37.77, 16.31, 0.00546, 20, 19.15, 16.31, 0.47354, 21, 0.52, 16.31, 0.51178, 22, -18.1, 16.31, 0.00922, 3, 19, 24.9, 16.29, 0.21703, 20, 6.27, 16.29, 0.72863, 21, -12.35, 16.29, 0.05434, 3, 18, 30.65, 16.26, 0.05783, 19, 12.02, 16.26, 0.74319, 20, -6.6, 16.26, 0.19898, 4, 17, 36.4, 16.23, 0.00474, 18, 17.77, 16.23, 0.52596, 19, -0.85, 16.23, 0.46509, 20, -19.48, 16.23, 0.00421, 4, 16, 42.15, 16.21, 5e-05, 17, 23.52, 16.21, 0.23185, 18, 4.9, 16.21, 0.72521, 19, -13.73, 16.21, 0.04289, 3, 16, 29.27, 16.18, 0.09577, 17, 10.65, 16.18, 0.73046, 18, -7.98, 16.18, 0.17377, 3, 16, 16.4, 16.16, 0.63995, 17, -2.23, 16.16, 0.35783, 18, -20.85, 16.16, 0.00222, 2, 16, 3.52, 16.13, 0.98343, 17, -15.1, 16.13, 0.01657, 2, 16, 3.56, -0.37, 0.99998, 17, -15.07, -0.37, 2e-05, 1, 16, 16.43, -0.34, 1, 1, 17, 10.68, -0.32, 1, 1, 18, 4.93, -0.29, 1, 2, 18, 17.81, -0.27, 0.71411, 19, -0.82, -0.27, 0.28589, 1, 19, 12.06, -0.24, 1, 1, 20, 6.31, -0.21, 1, 2, 20, 19.18, -0.19, 0.35737, 21, 0.56, -0.19, 0.64263, 1, 21, 13.43, -0.16, 1, 1, 22, 7.68, -0.14, 1, 3, 22, 20.56, -0.11, 0.01626, 23, 1.93, -0.11, 0.98373, 24, -16.7, -0.11, 0, 1, 23, 14.8, -0.09, 1, 1, 24, 9.05, -0.06, 1, 1, 25, 3.3, -0.03, 1, 1, 25, 16.18, -0.01, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 0], "width": 33, "height": 206}}, "linelight13": {"linelight11": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.9375, 0, 0.875, 0, 0.8125, 0, 0.75, 0, 0.6875, 0, 0.625, 0, 0.5625, 0, 0.5, 0, 0.4375, 0, 0.375, 0, 0.3125, 0, 0.25, 0, 0.1875, 0, 0.125, 0, 0.0625, 0, 0, 0.5, 0, 1, 0, 1, 0.0625, 1, 0.125, 1, 0.1875, 1, 0.25, 1, 0.3125, 1, 0.375, 1, 0.4375, 1, 0.5, 1, 0.5625, 1, 0.625, 1, 0.6875, 1, 0.75, 1, 0.8125, 1, 0.875, 1, 0.9375, 0.5, 0.9375, 0.5, 0.875, 0.5, 0.8125, 0.5, 0.75, 0.5, 0.6875, 0.5, 0.625, 0.5, 0.5625, 0.5, 0.5, 0.5, 0.4375, 0.5, 0.375, 0.5, 0.3125, 0.5, 0.25, 0.5, 0.1875, 0.5, 0.125, 0.5, 0.0625], "triangles": [17, 18, 19, 50, 17, 19, 16, 17, 50, 49, 16, 50, 15, 16, 49, 48, 15, 49, 14, 15, 48, 47, 14, 48, 13, 14, 47, 46, 13, 47, 12, 13, 46, 45, 12, 46, 11, 12, 45, 44, 11, 45, 10, 11, 44, 43, 10, 44, 9, 10, 43, 42, 9, 43, 8, 9, 42, 41, 8, 42, 7, 8, 41, 40, 7, 41, 6, 7, 40, 39, 6, 40, 5, 6, 39, 38, 5, 39, 4, 5, 38, 37, 4, 38, 3, 4, 37, 36, 3, 37, 2, 3, 36, 1, 2, 36, 50, 19, 20, 21, 50, 20, 49, 50, 21, 22, 49, 21, 48, 49, 22, 23, 48, 22, 47, 48, 23, 24, 47, 23, 46, 47, 24, 25, 46, 24, 45, 46, 25, 26, 45, 25, 44, 45, 26, 27, 44, 26, 43, 44, 27, 28, 43, 27, 42, 43, 28, 29, 42, 28, 41, 42, 29, 30, 41, 29, 40, 41, 30, 31, 40, 30, 39, 40, 31, 32, 39, 31, 38, 39, 32, 33, 38, 32, 37, 38, 33, 34, 37, 33, 36, 37, 34, 35, 36, 34, 1, 36, 35, 0, 1, 35], "vertices": [1, 38, -9.05, 15.97, 1, 1, 38, -9.01, -0.53, 1, 2, 38, -8.98, -17.03, 1, 39, -27.61, -17.03, 0, 2, 38, 3.89, -17, 0.96574, 39, -14.73, -17, 0.03426, 3, 38, 16.77, -16.98, 0.59389, 39, -1.86, -16.98, 0.40433, 40, -20.48, -16.98, 0.00178, 3, 38, 29.64, -16.95, 0.08693, 39, 11.02, -16.95, 0.75832, 40, -7.61, -16.95, 0.15475, 4, 38, 42.52, -16.93, 0, 39, 23.89, -16.93, 0.2917, 40, 5.27, -16.93, 0.66477, 41, -13.36, -16.93, 0.04353, 4, 39, 36.77, -16.9, 0.01755, 40, 18.14, -16.9, 0.51897, 41, -0.48, -16.9, 0.45382, 42, -19.11, -16.9, 0.00966, 3, 40, 31.02, -16.87, 0.06435, 41, 12.39, -16.87, 0.70396, 42, -6.23, -16.87, 0.23169, 3, 41, 25.27, -16.85, 0.20889, 42, 6.64, -16.85, 0.7197, 43, -11.98, -16.85, 0.07141, 4, 41, 38.14, -16.82, 0.00676, 42, 19.52, -16.82, 0.42705, 43, 0.89, -16.82, 0.55705, 44, -17.73, -16.82, 0.00914, 3, 42, 32.39, -16.8, 0.03366, 43, 13.77, -16.8, 0.70169, 44, -4.86, -16.8, 0.26465, 3, 43, 26.64, -16.77, 0.16804, 44, 8.02, -16.77, 0.7798, 45, -10.61, -16.77, 0.05216, 4, 43, 39.52, -16.75, 0.00285, 44, 20.89, -16.75, 0.48718, 45, 2.27, -16.75, 0.47811, 46, -16.36, -16.75, 0.03187, 3, 44, 33.77, -16.72, 0.06697, 45, 15.14, -16.72, 0.56485, 46, -3.48, -16.72, 0.36818, 4, 44, 46.64, -16.69, 0.00027, 45, 28.02, -16.69, 0.09993, 46, 9.39, -16.69, 0.79642, 47, -9.23, -16.69, 0.10338, 2, 46, 22.27, -16.67, 0.3471, 47, 3.64, -16.67, 0.6529, 2, 46, 35.14, -16.64, 0.02103, 47, 16.52, -16.64, 0.97897, 1, 47, 29.39, -16.62, 1, 1, 47, 29.36, -0.12, 1, 1, 47, 29.33, 16.38, 1, 2, 46, 35.08, 16.36, 0.01491, 47, 16.45, 16.36, 0.98509, 3, 45, 40.83, 16.33, 0.0024, 46, 22.2, 16.33, 0.3268, 47, 3.58, 16.33, 0.6708, 3, 45, 27.95, 16.31, 0.15109, 46, 9.33, 16.31, 0.7455, 47, -9.3, 16.31, 0.10341, 3, 44, 33.7, 16.28, 0.00911, 45, 15.08, 16.28, 0.71062, 46, -3.55, 16.28, 0.28027, 4, 43, 39.45, 16.25, 0.00301, 44, 20.83, 16.25, 0.30841, 45, 2.2, 16.25, 0.68085, 46, -16.42, 16.25, 0.00773, 3, 43, 26.58, 16.23, 0.1734, 44, 7.95, 16.23, 0.70139, 45, -10.67, 16.23, 0.12521, 4, 42, 32.33, 16.2, 0.03051, 43, 13.7, 16.2, 0.72321, 44, -4.92, 16.2, 0.24517, 45, -23.55, 16.2, 0.00112, 4, 41, 38.08, 16.18, 0.00347, 42, 19.45, 16.18, 0.43348, 43, 0.83, 16.18, 0.55618, 44, -17.8, 16.18, 0.00688, 3, 41, 25.2, 16.15, 0.20022, 42, 6.58, 16.15, 0.73246, 43, -12.05, 16.15, 0.06732, 3, 40, 30.95, 16.13, 0.06302, 41, 12.33, 16.13, 0.73793, 42, -6.3, 16.13, 0.19904, 4, 39, 36.7, 16.1, 0.00587, 40, 18.08, 16.1, 0.54886, 41, -0.55, 16.1, 0.44243, 42, -19.17, 16.1, 0.00284, 3, 39, 23.83, 16.07, 0.24999, 40, 5.2, 16.07, 0.719, 41, -13.42, 16.07, 0.031, 3, 38, 29.58, 16.05, 0.07043, 39, 10.95, 16.05, 0.77937, 40, -7.67, 16.05, 0.1502, 3, 38, 16.7, 16.02, 0.59979, 39, -1.92, 16.02, 0.39961, 40, -20.55, 16.02, 0.0006, 2, 38, 3.83, 16, 0.97787, 39, -14.8, 16, 0.02213, 1, 38, 3.86, -0.5, 1, 2, 38, 16.74, -0.48, 0.96861, 39, -1.89, -0.48, 0.03139, 3, 38, 29.61, -0.45, 0.00025, 39, 10.99, -0.45, 0.99941, 40, -7.64, -0.45, 0.00034, 2, 39, 23.86, -0.43, 0.00013, 40, 5.24, -0.43, 0.99987, 2, 40, 18.11, -0.4, 0.6325, 41, -0.51, -0.4, 0.3675, 3, 40, 30.99, -0.37, 0, 41, 12.36, -0.37, 0.99941, 42, -6.26, -0.37, 0.00058, 2, 41, 25.24, -0.35, 0.00154, 42, 6.61, -0.35, 0.99846, 2, 42, 19.49, -0.32, 0.26726, 43, 0.86, -0.32, 0.73274, 1, 43, 13.74, -0.3, 1, 1, 44, 7.99, -0.27, 1, 2, 45, 2.23, -0.25, 0.99994, 46, -16.39, -0.25, 6e-05, 2, 44, 33.74, -0.22, 1e-05, 45, 15.11, -0.22, 0.99999, 1, 46, 9.36, -0.19, 1, 1, 47, 3.61, -0.17, 1, 1, 47, 16.48, -0.14, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 0], "width": 33, "height": 206}}, "linelight14": {"linelight11": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.9375, 0, 0.875, 0, 0.8125, 0, 0.75, 0, 0.6875, 0, 0.625, 0, 0.5625, 0, 0.5, 0, 0.4375, 0, 0.375, 0, 0.3125, 0, 0.25, 0, 0.1875, 0, 0.125, 0, 0.0625, 0, 0, 0.5, 0, 1, 0, 1, 0.0625, 1, 0.125, 1, 0.1875, 1, 0.25, 1, 0.3125, 1, 0.375, 1, 0.4375, 1, 0.5, 1, 0.5625, 1, 0.625, 1, 0.6875, 1, 0.75, 1, 0.8125, 1, 0.875, 1, 0.9375, 0.5, 0.9375, 0.5, 0.875, 0.5, 0.8125, 0.5, 0.75, 0.5, 0.6875, 0.5, 0.625, 0.5, 0.5625, 0.5, 0.5, 0.5, 0.4375, 0.5, 0.375, 0.5, 0.3125, 0.5, 0.25, 0.5, 0.1875, 0.5, 0.125, 0.5, 0.0625], "triangles": [17, 18, 19, 50, 17, 19, 16, 17, 50, 49, 16, 50, 15, 16, 49, 48, 15, 49, 14, 15, 48, 47, 14, 48, 13, 14, 47, 46, 13, 47, 12, 13, 46, 45, 12, 46, 11, 12, 45, 44, 11, 45, 10, 11, 44, 43, 10, 44, 9, 10, 43, 42, 9, 43, 8, 9, 42, 41, 8, 42, 7, 8, 41, 40, 7, 41, 6, 7, 40, 39, 6, 40, 5, 6, 39, 38, 5, 39, 4, 5, 38, 37, 4, 38, 3, 4, 37, 36, 3, 37, 2, 3, 36, 1, 2, 36, 50, 19, 20, 21, 50, 20, 49, 50, 21, 22, 49, 21, 48, 49, 22, 23, 48, 22, 47, 48, 23, 24, 47, 23, 46, 47, 24, 25, 46, 24, 45, 46, 25, 26, 45, 25, 44, 45, 26, 27, 44, 26, 43, 44, 27, 28, 43, 27, 42, 43, 28, 29, 42, 28, 41, 42, 29, 30, 41, 29, 40, 41, 30, 31, 40, 30, 39, 40, 31, 32, 39, 31, 38, 39, 32, 33, 38, 32, 37, 38, 33, 34, 37, 33, 36, 37, 34, 35, 36, 34, 1, 36, 35, 0, 1, 35], "vertices": [1, 27, -10.64, -16.86, 1, 1, 27, -10.67, -0.36, 1, 1, 27, -10.71, 16.14, 1, 2, 27, 2.17, 16.17, 0.98671, 28, -16.46, 16.17, 0.01329, 3, 27, 15.04, 16.19, 0.67774, 28, -3.58, 16.19, 0.3215, 29, -22.21, 16.19, 0.00076, 3, 27, 27.92, 16.22, 0.10866, 28, 9.29, 16.22, 0.76086, 29, -9.33, 16.22, 0.13048, 4, 27, 40.79, 16.24, 7e-05, 28, 22.17, 16.24, 0.30082, 29, 3.54, 16.24, 0.68896, 30, -15.08, 16.24, 0.01014, 4, 28, 35.04, 16.27, 0.01007, 29, 16.42, 16.27, 0.66769, 30, -2.21, 16.27, 0.32085, 31, -20.83, 16.27, 0.00138, 3, 29, 29.29, 16.29, 0.12378, 30, 10.67, 16.29, 0.71642, 31, -7.96, 16.29, 0.1598, 4, 29, 42.17, 16.32, 0.0011, 30, 23.54, 16.32, 0.23916, 31, 4.92, 16.32, 0.72782, 32, -13.71, 16.32, 0.03192, 4, 30, 36.42, 16.35, 0.00561, 31, 17.79, 16.35, 0.56667, 32, -0.83, 16.35, 0.42481, 33, -19.46, 16.35, 0.00291, 3, 31, 30.67, 16.37, 0.07498, 32, 12.04, 16.37, 0.73393, 33, -6.58, 16.37, 0.19109, 3, 32, 24.92, 16.4, 0.21264, 33, 6.29, 16.4, 0.73638, 34, -12.33, 16.4, 0.05098, 4, 32, 37.79, 16.42, 0.00447, 33, 19.17, 16.42, 0.46747, 34, 0.54, 16.42, 0.50995, 35, -18.08, 16.42, 0.01811, 3, 33, 32.04, 16.45, 0.03505, 34, 13.42, 16.45, 0.67922, 35, -5.21, 16.45, 0.28573, 3, 34, 26.29, 16.48, 0.16193, 35, 7.67, 16.48, 0.76442, 36, -10.96, 16.48, 0.07365, 3, 34, 39.17, 16.5, 0.00221, 35, 20.54, 16.5, 0.39879, 36, 1.91, 16.5, 0.599, 2, 35, 33.42, 16.53, 0.02635, 36, 14.79, 16.53, 0.97365, 1, 36, 27.66, 16.55, 1, 1, 36, 27.7, 0.05, 1, 1, 36, 27.73, -16.45, 1, 2, 35, 33.48, -16.47, 0.02828, 36, 14.86, -16.47, 0.97172, 3, 34, 39.23, -16.5, 0.00109, 35, 20.61, -16.5, 0.39829, 36, 1.98, -16.5, 0.60063, 3, 34, 26.36, -16.52, 0.14943, 35, 7.73, -16.52, 0.76622, 36, -10.89, -16.52, 0.08436, 3, 33, 32.11, -16.55, 0.0484, 34, 13.48, -16.55, 0.68034, 35, -5.14, -16.55, 0.27127, 4, 32, 37.86, -16.58, 0.00589, 33, 19.23, -16.58, 0.48925, 34, 0.61, -16.58, 0.49513, 35, -18.02, -16.58, 0.00973, 3, 32, 24.98, -16.6, 0.21969, 33, 6.36, -16.6, 0.73081, 34, -12.27, -16.6, 0.0495, 3, 31, 30.73, -16.63, 0.06047, 32, 12.11, -16.63, 0.73805, 33, -6.52, -16.63, 0.20149, 4, 30, 36.48, -16.65, 0.00827, 31, 17.86, -16.65, 0.53341, 32, -0.77, -16.65, 0.45399, 33, -19.39, -16.65, 0.00433, 3, 30, 23.61, -16.68, 0.26707, 31, 4.98, -16.68, 0.69721, 32, -13.64, -16.68, 0.03572, 3, 29, 29.36, -16.71, 0.06159, 30, 10.73, -16.71, 0.78593, 31, -7.89, -16.71, 0.15248, 4, 28, 35.11, -16.73, 0.01904, 29, 16.48, -16.73, 0.52665, 30, -2.14, -16.73, 0.45292, 31, -20.77, -16.73, 0.0014, 3, 28, 22.23, -16.76, 0.33436, 29, 3.61, -16.76, 0.61395, 30, -15.02, -16.76, 0.05169, 3, 27, 27.98, -16.78, 0.08695, 28, 9.36, -16.78, 0.79342, 29, -9.27, -16.78, 0.11963, 3, 27, 15.11, -16.81, 0.61639, 28, -3.52, -16.81, 0.38301, 29, -22.14, -16.81, 0.00059, 2, 27, 2.23, -16.83, 0.96626, 28, -16.39, -16.83, 0.03374, 2, 27, 2.2, -0.33, 0.99993, 28, -16.42, -0.33, 7e-05, 1, 27, 15.08, -0.31, 1, 1, 28, 9.32, -0.28, 1, 2, 29, 3.57, -0.26, 0.9999, 30, -15.05, -0.26, 0.0001, 1, 29, 16.45, -0.23, 1, 1, 30, 10.7, -0.21, 1, 1, 31, 4.95, -0.18, 1, 2, 31, 17.82, -0.15, 0.70293, 32, -0.8, -0.15, 0.29707, 2, 32, 12.07, -0.13, 1, 33, -6.55, -0.13, 0, 1, 33, 6.32, -0.1, 1, 2, 33, 19.2, -0.08, 0.34886, 34, 0.57, -0.08, 0.65114, 2, 34, 13.45, -0.05, 0.99801, 35, -5.18, -0.05, 0.00199, 1, 35, 7.7, -0.02, 1, 2, 35, 20.57, 0, 0.0155, 36, 1.95, 0, 0.9845, 1, 36, 14.82, 0.03, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 0], "width": 33, "height": 206}}, "linelight15": {"linelight11": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.9375, 0, 0.875, 0, 0.8125, 0, 0.75, 0, 0.6875, 0, 0.625, 0, 0.5625, 0, 0.5, 0, 0.4375, 0, 0.375, 0, 0.3125, 0, 0.25, 0, 0.1875, 0, 0.125, 0, 0.0625, 0, 0, 0.5, 0, 1, 0, 1, 0.0625, 1, 0.125, 1, 0.1875, 1, 0.25, 1, 0.3125, 1, 0.375, 1, 0.4375, 1, 0.5, 1, 0.5625, 1, 0.625, 1, 0.6875, 1, 0.75, 1, 0.8125, 1, 0.875, 1, 0.9375, 0.5, 0.9375, 0.5, 0.875, 0.5, 0.8125, 0.5, 0.75, 0.5, 0.6875, 0.5, 0.625, 0.5, 0.5625, 0.5, 0.5, 0.5, 0.4375, 0.5, 0.375, 0.5, 0.3125, 0.5, 0.25, 0.5, 0.1875, 0.5, 0.125, 0.5, 0.0625], "triangles": [0, 1, 35, 1, 36, 35, 35, 36, 34, 36, 37, 34, 34, 37, 33, 37, 38, 33, 33, 38, 32, 38, 39, 32, 32, 39, 31, 39, 40, 31, 31, 40, 30, 40, 41, 30, 30, 41, 29, 41, 42, 29, 29, 42, 28, 42, 43, 28, 28, 43, 27, 43, 44, 27, 27, 44, 26, 44, 45, 26, 26, 45, 25, 45, 46, 25, 25, 46, 24, 46, 47, 24, 24, 47, 23, 47, 48, 23, 23, 48, 22, 48, 49, 22, 22, 49, 21, 49, 50, 21, 21, 50, 20, 50, 19, 20, 1, 2, 36, 2, 3, 36, 36, 3, 37, 3, 4, 37, 37, 4, 38, 4, 5, 38, 38, 5, 39, 5, 6, 39, 39, 6, 40, 6, 7, 40, 40, 7, 41, 7, 8, 41, 41, 8, 42, 8, 9, 42, 42, 9, 43, 9, 10, 43, 43, 10, 44, 10, 11, 44, 44, 11, 45, 11, 12, 45, 45, 12, 46, 12, 13, 46, 46, 13, 47, 13, 14, 47, 47, 14, 48, 14, 15, 48, 48, 15, 49, 15, 16, 49, 49, 16, 50, 16, 17, 50, 50, 17, 19, 17, 18, 19], "vertices": [2, 49, -12.5, -17.19, 0.99268, 50, -58.2, -17.19, 0.00732, 2, 49, -12.56, -0.69, 0.99835, 50, -58.26, -0.69, 0.00165, 2, 49, -12.62, 15.81, 0.99278, 50, -58.32, 15.81, 0.00722, 2, 49, 0.26, 15.86, 0.96786, 50, -45.45, 15.86, 0.03214, 3, 49, 13.13, 15.9, 0.90308, 50, -32.57, 15.9, 0.09679, 51, -78.28, 15.9, 0.00013, 3, 49, 26.01, 15.95, 0.78093, 50, -19.7, 15.95, 0.21648, 51, -65.4, 15.95, 0.00259, 3, 49, 38.88, 15.99, 0.60408, 50, -6.82, 15.99, 0.38036, 51, -52.53, 15.99, 0.01556, 4, 49, 51.76, 16.04, 0.40415, 50, 6.05, 16.04, 0.53918, 51, -39.65, 16.04, 0.05667, 52, -85.36, 16.04, 0, 4, 49, 64.63, 16.08, 0.22552, 50, 18.93, 16.08, 0.62699, 51, -26.78, 16.08, 0.1469, 52, -72.48, 16.08, 0.0006, 4, 49, 77.51, 16.13, 0.10082, 50, 31.8, 16.13, 0.60123, 51, -13.9, 16.13, 0.29192, 52, -59.61, 16.13, 0.00603, 4, 49, 90.38, 16.17, 0.03389, 50, 44.68, 16.17, 0.47492, 51, -1.03, 16.17, 0.46289, 52, -46.73, 16.17, 0.0283, 4, 49, 103.26, 16.22, 0.00775, 50, 57.55, 16.22, 0.30583, 51, 11.85, 16.22, 0.59812, 52, -33.86, 16.22, 0.08831, 4, 49, 116.13, 16.26, 0.0009, 50, 70.43, 16.26, 0.15731, 51, 24.72, 16.26, 0.63714, 52, -20.98, 16.26, 0.20465, 4, 49, 129.01, 16.31, 1e-05, 50, 83.3, 16.31, 0.06227, 51, 37.6, 16.31, 0.56042, 52, -8.11, 16.31, 0.3773, 3, 50, 96.18, 16.36, 0.01764, 51, 50.47, 16.36, 0.40536, 52, 4.77, 16.36, 0.577, 3, 50, 109.05, 16.4, 0.00309, 51, 63.35, 16.4, 0.23741, 52, 17.64, 16.4, 0.7595, 3, 50, 121.93, 16.45, 0.00018, 51, 76.22, 16.45, 0.10985, 52, 30.52, 16.45, 0.88997, 2, 51, 89.1, 16.49, 0.03813, 52, 43.39, 16.49, 0.96187, 2, 51, 101.97, 16.54, 0.00912, 52, 56.27, 16.54, 0.99088, 2, 51, 102.03, 0.04, 0.00229, 52, 56.33, 0.04, 0.99771, 2, 51, 102.09, -16.46, 0.00898, 52, 56.38, -16.46, 0.99102, 2, 51, 89.21, -16.51, 0.03764, 52, 43.51, -16.51, 0.96236, 3, 50, 122.04, -16.55, 0.00023, 51, 76.34, -16.55, 0.10863, 52, 30.63, -16.55, 0.89114, 3, 50, 109.17, -16.6, 0.0034, 51, 63.46, -16.6, 0.23507, 52, 17.76, -16.6, 0.76153, 3, 50, 96.29, -16.64, 0.01865, 51, 50.59, -16.64, 0.40162, 52, 4.89, -16.64, 0.57973, 4, 49, 129.12, -16.69, 2e-05, 50, 83.42, -16.69, 0.06451, 51, 37.71, -16.69, 0.55532, 52, -7.99, -16.69, 0.38015, 4, 49, 116.25, -16.73, 0.00098, 50, 70.54, -16.73, 0.1609, 51, 24.84, -16.73, 0.63113, 52, -20.86, -16.73, 0.20699, 4, 49, 103.37, -16.78, 0.00808, 50, 57.67, -16.78, 0.31007, 51, 11.97, -16.78, 0.59207, 52, -33.74, -16.78, 0.08978, 4, 49, 90.5, -16.83, 0.03474, 50, 44.8, -16.83, 0.47844, 51, -0.91, -16.83, 0.45781, 52, -46.61, -16.83, 0.029, 4, 49, 77.62, -16.87, 0.10231, 50, 31.92, -16.87, 0.60293, 51, -13.78, -16.87, 0.28849, 52, -59.49, -16.87, 0.00626, 4, 49, 64.75, -16.92, 0.22747, 50, 19.05, -16.92, 0.62674, 51, -26.66, -16.92, 0.14514, 52, -72.36, -16.92, 0.00064, 4, 49, 51.88, -16.96, 0.40602, 50, 6.17, -16.96, 0.53792, 51, -39.53, -16.96, 0.05606, 52, -85.24, -16.96, 0, 3, 49, 39, -17.01, 0.60537, 50, -6.7, -17.01, 0.37916, 51, -52.41, -17.01, 0.01547, 3, 49, 26.13, -17.05, 0.78146, 50, -19.58, -17.05, 0.21592, 51, -65.28, -17.05, 0.00261, 3, 49, 13.25, -17.1, 0.9031, 50, -32.45, -17.1, 0.09675, 51, -78.16, -17.1, 0.00015, 2, 49, 0.38, -17.14, 0.96771, 50, -45.33, -17.14, 0.03229, 1, 49, 0.32, -0.64, 1, 1, 49, 13.19, -0.6, 1, 1, 49, 26.07, -0.55, 1, 1, 49, 38.94, -0.51, 1, 1, 50, 6.11, -0.46, 1, 1, 50, 18.99, -0.42, 1, 1, 50, 31.86, -0.37, 1, 2, 50, 44.74, -0.33, 0.74152, 51, -0.97, -0.33, 0.25848, 1, 51, 11.91, -0.28, 1, 1, 51, 24.78, -0.23, 1, 1, 51, 37.66, -0.19, 1, 1, 52, 4.83, -0.14, 1, 1, 52, 17.7, -0.1, 1, 1, 52, 30.58, -0.05, 1, 1, 52, 43.45, -0.01, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 0], "width": 33, "height": 206}}, "line4": {"line1": {"type": "path", "lengths": [111.25, 252.96, 453.2], "vertexCount": 9, "vertices": [-4.45, 13.7, -4.45, 13.7, -4.45, 13.7, -72.65, 26.2, -78.49, 87.42, -77.53, 143.91, -16.25, 132.67, -23.77, 213, -27.24, 250.06]}}, "dishangshui00": {"dishangshui00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}, "dishangshui02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}, "dishangshui04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}, "dishangshui06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}, "dishangshui08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}, "dishangshui10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}, "dishangshui12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}, "dishangshui14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}, "dishangshui18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [126, -49, -125, -49, -125, 50, 126, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 99}}, "bone4": {"bone4": {"type": "path", "lengths": [114.29, 220.09, 394.27], "vertexCount": 9, "vertices": [10.94, -10.19, -14.28, 5.73, -59.12, 34.02, -80.53, 69.21, -71.95, 96.47, -55.96, 147.23, -13.43, 142.73, 6.28, 162.62, 21.97, 178.46]}}, "yzzl_skill_zd_00000": {"yzzl_skill_zd_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "yzzl_skill_zd_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}}, "line1": {"line1": {"type": "path", "lengths": [128.08, 281.24, 503.77], "vertexCount": 9, "vertices": [7.03, 11.32, 7.03, 11.32, 7.03, 11.32, -75.37, 26.83, -79.63, 94.37, -83.49, 155.52, -31.82, 214.39, -15.45, 229.1, 12.24, 253.98]}}, "A_ring_glow": {"A_ring_glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [128, -128, -128, -128, -128, 128, 128, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 256}}, "line3": {"line1": {"type": "path", "lengths": [116.39, 245.28, 432.4], "vertexCount": 9, "vertices": [7.03, 11.32, 7.03, 11.32, 7.03, 11.32, -78.33, 35.75, -48.48, 96.48, -25.94, 142.32, 26.26, 142.06, 25.21, 197.42, 24.5, 234.64]}}}}], "animations": {"111": {"slots": {"yzzl_skill_zd_00000": {"attachment": [{"name": "yzzl_skill_zd_00000"}, {"time": 0.0667, "name": "yzzl_skill_zd_00001"}, {"time": 0.1333, "name": "yzzl_skill_zd_00002"}, {"time": 0.2, "name": "yzzl_skill_zd_00003"}, {"time": 0.2333, "name": "yzzl_skill_zd_00004"}, {"time": 0.3, "name": "yzzl_skill_zd_00005"}, {"time": 0.3667, "name": "yzzl_skill_zd_00006"}, {"time": 0.4333, "name": "yzzl_skill_zd_00007"}, {"time": 0.5, "name": "yzzl_skill_zd_00008"}, {"time": 0.5667, "name": "yzzl_skill_zd_00009"}, {"time": 0.6, "name": "yzzl_skill_zd_00000"}, {"time": 0.6667, "name": "yzzl_skill_zd_00001"}, {"time": 0.7333, "name": "yzzl_skill_zd_00002"}, {"time": 0.8, "name": "yzzl_skill_zd_00003"}, {"time": 0.8667, "name": "yzzl_skill_zd_00004"}, {"time": 0.9333, "name": "yzzl_skill_zd_00005"}, {"time": 0.9667, "name": "yzzl_skill_zd_00006"}, {"time": 1.0333, "name": "yzzl_skill_zd_00007"}, {"time": 1.1, "name": "yzzl_skill_zd_00008"}, {"time": 1.1667, "name": "yzzl_skill_zd_00009"}, {"time": 1.2333, "name": "yzzl_skill_zd_00000"}, {"time": 1.3, "name": "yzzl_skill_zd_00001"}, {"time": 1.3333, "name": "yzzl_skill_zd_00002"}, {"time": 1.4, "name": "yzzl_skill_zd_00003"}, {"time": 1.4667, "name": "yzzl_skill_zd_00004"}, {"time": 1.5333, "name": "yzzl_skill_zd_00005"}, {"time": 1.6, "name": "yzzl_skill_zd_00006"}, {"time": 1.6667, "name": "yzzl_skill_zd_00007"}, {"time": 1.7333, "name": "yzzl_skill_zd_00008"}, {"time": 1.7667, "name": "yzzl_skill_zd_00009"}, {"time": 1.8333, "name": "yzzl_skill_zd_00000"}, {"time": 1.9, "name": "yzzl_skill_zd_00001"}, {"time": 1.9667, "name": "yzzl_skill_zd_00002"}, {"time": 2.0333, "name": "yzzl_skill_zd_00003"}, {"time": 2.1, "name": "yzzl_skill_zd_00004"}, {"time": 2.1333, "name": "yzzl_skill_zd_00005"}, {"time": 2.2, "name": "yzzl_skill_zd_00006"}, {"time": 2.2667, "name": "yzzl_skill_zd_00007"}, {"time": 2.3333, "name": "yzzl_skill_zd_00008"}, {"time": 2.4, "name": "yzzl_skill_zd_00009"}, {"time": 2.4667, "name": "yzzl_skill_zd_00000"}, {"time": 2.5, "name": "yzzl_skill_zd_00001"}, {"time": 2.5667, "name": "yzzl_skill_zd_00002"}, {"time": 2.6333, "name": "yzzl_skill_zd_00003"}, {"time": 2.7, "name": "yzzl_skill_zd_00004"}, {"time": 2.7667, "name": "yzzl_skill_zd_00005"}, {"time": 2.8333, "name": "yzzl_skill_zd_00006"}, {"time": 2.9, "name": "yzzl_skill_zd_00007"}, {"time": 2.9333, "name": "yzzl_skill_zd_00008"}, {"time": 3, "name": "yzzl_skill_zd_00009"}]}}}, "yao_zu_shui_jing": {"slots": {"dishangshui00": {"attachment": [{"time": 0.0667, "name": "dishangshui14"}, {"time": 0.1333, "name": "dishangshui12"}, {"time": 0.2, "name": "dishangshui10"}, {"time": 0.2667, "name": "dishangshui08"}, {"time": 0.3333, "name": "dishangshui04"}, {"time": 0.4, "name": "dishangshui02"}, {"time": 0.4667, "name": "dishangshui00"}, {"time": 0.5333, "name": "dishangshui18"}, {"time": 0.6, "name": "dishangshui14"}, {"time": 0.6667, "name": "dishangshui12"}, {"time": 0.7333, "name": "dishangshui10"}, {"time": 0.8, "name": "dishangshui08"}, {"time": 0.8667, "name": "dishangshui04"}, {"time": 0.9333, "name": "dishangshui02"}, {"time": 1, "name": "dishangshui00"}, {"time": 1.0667, "name": "dishangshui18"}, {"time": 1.1333, "name": "dishangshui14"}, {"time": 1.2, "name": "dishangshui12"}, {"time": 1.2667, "name": "dishangshui10"}, {"time": 1.3333, "name": "dishangshui08"}, {"time": 1.4, "name": "dishangshui04"}, {"time": 1.4667, "name": "dishangshui02"}, {"time": 1.5333, "name": "dishangshui00"}, {"time": 1.6, "name": "dishangshui18"}, {"time": 1.6667, "name": "dishangshui14"}, {"time": 1.7333, "name": "dishangshui12"}, {"time": 1.8, "name": "dishangshui10"}, {"time": 1.8667, "name": "dishangshui08"}, {"time": 1.9333, "name": "dishangshui04"}, {"time": 2, "name": "dishangshui02"}, {"time": 2.0667, "name": "dishangshui00"}, {"time": 2.1333, "name": "dishangshui18"}, {"time": 2.2, "name": "dishangshui14"}, {"time": 2.2667, "name": "dishangshui12"}, {"time": 2.3333, "name": "dishangshui10"}, {"time": 2.4, "name": "dishangshui08"}, {"time": 2.4667, "name": "dishangshui04"}, {"time": 2.5333, "name": "dishangshui02"}, {"time": 2.6, "name": "dishangshui00"}, {"time": 2.6667, "name": "dishangshui18"}, {"time": 2.7333, "name": "dishangshui14"}, {"time": 2.8, "name": "dishangshui12"}, {"time": 2.8667, "name": "dishangshui10"}, {"time": 2.9333, "name": "dishangshui08"}, {"time": 3, "name": "dishangshui04"}]}, "linelight15": {"twoColor": [{"light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.7667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.5, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 2.2667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 3, "light": "ffffff00", "dark": "000000"}]}, "linelight13": {"twoColor": [{"light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 2.1667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.5, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "light": "ffffff00", "dark": "000000"}]}, "linelight14": {"twoColor": [{"light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.4, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.9, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.2, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "light": "ffffff00", "dark": "000000"}]}, "linelight11": {"twoColor": [{"light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.3, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.5, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.8, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 2.1667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "light": "ffffff00", "dark": "000000"}]}, "yzzl_skill_zd_00000": {"attachment": [{"name": "yzzl_skill_zd_00000"}, {"time": 0.0667, "name": "yzzl_skill_zd_00001"}, {"time": 0.1333, "name": "yzzl_skill_zd_00002"}, {"time": 0.2, "name": "yzzl_skill_zd_00003"}, {"time": 0.2333, "name": "yzzl_skill_zd_00004"}, {"time": 0.3, "name": "yzzl_skill_zd_00005"}, {"time": 0.3667, "name": "yzzl_skill_zd_00006"}, {"time": 0.4333, "name": "yzzl_skill_zd_00007"}, {"time": 0.5, "name": "yzzl_skill_zd_00008"}, {"time": 0.5667, "name": "yzzl_skill_zd_00009"}, {"time": 0.6, "name": "yzzl_skill_zd_00000"}, {"time": 0.6667, "name": "yzzl_skill_zd_00001"}, {"time": 0.7333, "name": "yzzl_skill_zd_00002"}, {"time": 0.8, "name": "yzzl_skill_zd_00003"}, {"time": 0.8667, "name": "yzzl_skill_zd_00004"}, {"time": 0.9333, "name": "yzzl_skill_zd_00005"}, {"time": 0.9667, "name": "yzzl_skill_zd_00006"}, {"time": 1.0333, "name": "yzzl_skill_zd_00007"}, {"time": 1.1, "name": "yzzl_skill_zd_00008"}, {"time": 1.1667, "name": "yzzl_skill_zd_00009"}, {"time": 1.2333, "name": "yzzl_skill_zd_00000"}, {"time": 1.3, "name": "yzzl_skill_zd_00001"}, {"time": 1.3333, "name": "yzzl_skill_zd_00002"}, {"time": 1.4, "name": "yzzl_skill_zd_00003"}, {"time": 1.4667, "name": "yzzl_skill_zd_00004"}, {"time": 1.5333, "name": "yzzl_skill_zd_00005"}, {"time": 1.6, "name": "yzzl_skill_zd_00006"}, {"time": 1.6667, "name": "yzzl_skill_zd_00007"}, {"time": 1.7333, "name": "yzzl_skill_zd_00008"}, {"time": 1.7667, "name": "yzzl_skill_zd_00009"}, {"time": 1.8333, "name": "yzzl_skill_zd_00000"}, {"time": 1.9, "name": "yzzl_skill_zd_00001"}, {"time": 1.9667, "name": "yzzl_skill_zd_00002"}, {"time": 2.0333, "name": "yzzl_skill_zd_00003"}, {"time": 2.1, "name": "yzzl_skill_zd_00004"}, {"time": 2.1333, "name": "yzzl_skill_zd_00005"}, {"time": 2.2, "name": "yzzl_skill_zd_00006"}, {"time": 2.2667, "name": "yzzl_skill_zd_00007"}, {"time": 2.3333, "name": "yzzl_skill_zd_00008"}, {"time": 2.4, "name": "yzzl_skill_zd_00009"}, {"time": 2.4667, "name": "yzzl_skill_zd_00000"}, {"time": 2.5, "name": "yzzl_skill_zd_00001"}, {"time": 2.5667, "name": "yzzl_skill_zd_00002"}, {"time": 2.6333, "name": "yzzl_skill_zd_00003"}, {"time": 2.7, "name": "yzzl_skill_zd_00004"}, {"time": 2.7667, "name": "yzzl_skill_zd_00005"}, {"time": 2.8333, "name": "yzzl_skill_zd_00006"}, {"time": 2.9, "name": "yzzl_skill_zd_00007"}, {"time": 2.9333, "name": "yzzl_skill_zd_00008"}, {"time": 3, "name": "yzzl_skill_zd_00009"}]}, "linelight12": {"twoColor": [{"light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.2, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.8667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.2, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.5, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.7, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 2.3667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 3, "light": "ffffff00", "dark": "000000"}]}}, "bones": {"bone3": {"rotate": [{}, {"time": 1, "angle": 120}, {"time": 2, "angle": -120}, {"time": 3}]}, "bone44": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 4.53}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.4, "y": 0.29}]}, "bone30": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 4.02}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.15, "y": 0.18}]}, "bone31": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 4.86}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.16, "y": 0.23}]}, "bone21": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 0.63}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.58, "y": 0.02}]}, "bone22": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.03}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.87, "y": 0.04}]}, "bone10": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -2.47}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.51, "y": -0.16}]}, "bone11": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -2.09}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.5, "y": -0.13}]}, "bone39": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.58}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.8, "y": 0.33}]}, "bone40": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.89}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.95, "y": 0.34}]}, "bone41": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.98}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.79, "y": 0.36}]}, "bone42": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.46}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.92, "y": 0.33}]}, "bone5": {"rotate": [{"angle": -89.88, "curve": "stepped"}, {"time": 1.5, "angle": -89.88, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -32.24}], "translate": [{"x": 235.22, "y": -10.2, "curve": "stepped"}, {"time": 1.5, "x": 235.22, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 175.66, "y": 115.93}]}, "bone6": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -4.46}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.5, "y": -0.28}]}, "bone7": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -3.88}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.53, "y": -0.25}]}, "bone8": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -3.37}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.5, "y": -0.21}]}, "bone9": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -2.9}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.49, "y": -0.19}]}, "bone12": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -1.74}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.46, "y": -0.11}]}, "bone16": {"rotate": [{"angle": 90.12, "curve": "stepped"}, {"time": 1.5, "angle": 90.12, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -64.36}], "translate": [{"x": 235.22, "y": -10.2, "curve": "stepped"}, {"time": 1.5, "x": 235.22, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 250.47, "y": 137.98}], "scale": [{"curve": 0.27, "c3": 0.619, "c4": 0.41}, {"time": 0.2333, "y": 2.319, "curve": 0.322, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.6667, "curve": 0.337, "c2": 0.35, "c3": 0.681, "c4": 0.71}, {"time": 0.9333, "y": 3.056, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 1.5, "curve": 0.27, "c3": 0.619, "c4": 0.41}, {"time": 1.7333, "y": 2.171, "curve": 0.32, "c2": 0.29, "c3": 0.66, "c4": 0.65}, {"time": 2.2667, "curve": 0.344, "c2": 0.37, "c3": 0.686, "c4": 0.73}, {"time": 2.4333, "y": 2.162, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 3}]}, "bone17": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -0.3}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.65, "y": -0.01}]}, "bone18": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -0.13}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.68, "y": -0.01}]}, "bone19": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 0.06}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.84}]}, "bone20": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 0.29}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.76, "y": 0.01}]}, "bone23": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.75}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.7, "y": 0.07}]}, "bone15": {"rotate": [{"angle": -89.88, "curve": "stepped"}, {"time": 1.5, "angle": -89.88, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -52.51}], "translate": [{"x": 235.22, "y": -10.2, "curve": "stepped"}, {"time": 1.5, "x": 235.22, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 218.42, "y": 116.44}]}, "bone26": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 0.55}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.15, "y": 0.02}]}, "bone27": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.33}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.16, "y": 0.06}]}, "bone28": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 2.19}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.14, "y": 0.1}]}, "bone29": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 3.07}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.18, "y": 0.14}]}, "bone32": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.5}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.21, "y": 0.26}]}, "bone35": {"rotate": [{"angle": 90.12, "curve": "stepped"}, {"time": 1.5, "angle": 90.12, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -46.05}], "translate": [{"x": 223.73, "y": -7.82, "curve": "stepped"}, {"time": 1.5, "x": 223.73, "y": -7.82, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 177.39, "y": 116.98}]}, "bone36": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.65}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.83, "y": 0.08}]}, "bone37": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 3.26}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.82, "y": 0.18}]}, "bone38": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 4.57}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.88, "y": 0.26}]}, "bone43": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 4.84}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.95, "y": 0.29}]}, "bone14": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -1.06}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.36, "y": -0.07}]}, "bone25": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.05}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.99, "y": 0.18}]}, "bone34": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 5.81}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.31, "y": 0.27}]}, "bone13": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -1.38}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11.59, "y": -0.09}]}, "bone24": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 3.02}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.63, "y": 0.12}]}, "bone33": {"rotate": [{"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 6.01}], "translate": [{"x": -18.63, "curve": "stepped"}, {"time": 1.5, "x": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -13.09, "y": 0.29}]}, "bone45": {"scale": [{"x": 0.012, "y": 2.36, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.2, "y": 2.48, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.7333, "x": 0.019, "curve": "stepped"}, {"time": 0.7667, "x": 0.012, "y": 2.36, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.9667, "y": 2.48, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.4667, "x": 0.019, "curve": "stepped"}, {"time": 1.5, "x": 0.012, "y": 2.36, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.7333, "y": 2.48, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2.2333, "x": 0.019, "curve": "stepped"}, {"time": 2.2667, "x": 0.012, "y": 2.36, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 2.5, "y": 2.48, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 3, "x": 0.019}]}}, "path": {"line4": {"position": [{"time": 0.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "position": 0.6756, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 2.1333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8, "position": 0.6756}], "spacing": [{"time": 0.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9667, "spacing": 0.0533, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3, "spacing": 0.0267, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 2.1333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "spacing": 0.0533, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8, "spacing": 0.0267}]}, "bone4": {"position": [{"position": -0.1724, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "position": 0.8773, "curve": "stepped"}, {"time": 0.7667, "position": -0.1724, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "position": 0.8773, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "position": -0.1724, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "position": 0.8773, "curve": "stepped"}, {"time": 2.2667, "position": -0.1724, "curve": 0.25, "c3": 0.75}, {"time": 3, "position": 0.8773}], "spacing": [{"spacing": -54.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.4667, "spacing": -54.5, "curve": "stepped"}, {"time": 1.5, "spacing": -54.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": "stepped"}, {"time": 3, "spacing": -54.5}]}, "line1": {"position": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6333, "position": 0.7634, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "position": 0.6071, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "position": 0.6071, "curve": "stepped"}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "position": 0.6071}], "spacing": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "spacing": 0.067, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "spacing": 0.0268, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "spacing": 0.067, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "spacing": 0.0268, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "spacing": 0.067, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "spacing": 0.0268, "curve": "stepped"}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "spacing": 0.067, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "spacing": 0.0268}]}, "line2": {"position": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "position": 0.8133, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "position": 0.8133, "curve": "stepped"}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "position": 0.8133, "curve": "stepped"}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 3, "position": 0.8133}], "spacing": [{"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "spacing": 0.0444, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "spacing": 0.0178, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.2, "spacing": 0.0444, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "spacing": 0.0356, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "spacing": 0.0444, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "spacing": 0.0178, "curve": "stepped"}, {"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7, "spacing": 0.0444, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "spacing": 0.0178}]}, "line3": {"position": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "position": 0.7067, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "position": 0.7067}], "spacing": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "spacing": 0.0533, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "spacing": 0.0222, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "spacing": 0.0533, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "spacing": 0.0222}]}}, "deform": {"default": {"bone4": {"bone4": [{"vertices": [24.10796, -12.642, 24.10796, -12.642, 24.10796, -12.642, 24.10796, -12.642, 24.10796, -12.642, 24.10796, -12.642, 52.06997, 1.05402, 52.06997, 1.05402, 52.06997, 1.05402], "curve": "stepped"}, {"time": 1.5, "vertices": [24.10796, -12.642, 24.10796, -12.642, 24.10796, -12.642, 1.604, -27.77399, 1.604, -27.77399, 1.604, -27.77399, 29.95398, -28.43394, 29.95398, -28.43394, 29.95398, -28.43394]}]}, "line1": {"line1": [{"offset": 6, "vertices": [15.46595, -19.94296, 15.46595, -19.94296, 15.46595, -19.94296, 43.1419, -66.34091, 28.48992, -54.53793, 3.2058, -42.27251]}]}, "line3": {"line1": [{"time": 0.9, "offset": 12, "vertices": [-11.439, 0.73798, 11.80799, -9.96298, 29.08075, -13.84369]}]}, "line2": {"line1": [{"time": 0.8667, "offset": 6, "vertices": [20.19021, 10.71001, 44.86999, 7.69194, 66.66405, 3.20488, 49.998, 4.48691, 55.767, 3.84595, 64.78388, -0.20482]}]}}}}}}