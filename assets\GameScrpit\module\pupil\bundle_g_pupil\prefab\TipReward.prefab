[{"__type__": "cc.Prefab", "_name": "TipReward", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "TipReward", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 19}, {"__id__": 27}, {"__id__": 489}], "_active": true, "_components": [{"__id__": 497}, {"__id__": 499}, {"__id__": 501}], "_prefab": {"__id__": 503}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afz5FsJUBAYYAdx2tMxlDO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 192}, "_spriteFrame": {"__uuid__": "b730527c-3233-41c2-aaf7-7cdab58f9749@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "205Ak0GUhBlq2T1cBKS6II"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 20, "_originalHeight": 20, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39qscdsLdP/pCP3f1pWfP/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3ffCSvKOVPAp3qYlSsmUS4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "590KCJ1AtK5Zi0yhUUhHtu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 14}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61gC5ZyjVHjYiqqe5AwLxQ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 16}, "clickEvents": [{"__id__": 17}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fxCMDAi5NlYf5RYoxFSlH"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "38dc5x9C+pMA4KrIsB8rc3o", "handler": "onClose", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9ezXd2v0NMpp5yQkb4uC5n", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 250, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 403}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cOZtOinFKZZRL4aM9rHgU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2ada6e64-1496-4163-9c67-8a7e739db12e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83aMm3SaJNrol8jWM2SgSB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 25}, "_opacity": 128, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "306MUhMnJOSItu+tNSPW01"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "21+DLLUG1C0qReYuOR4TDu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 28}, {"__id__": 46}], "_active": true, "_components": [{"__id__": 482}, {"__id__": 484}, {"__id__": 486}], "_prefab": {"__id__": 488}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 250, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1.0000000000000038, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 27}, "_children": [{"__id__": 29}, {"__id__": 35}], "_active": true, "_components": [{"__id__": 41}, {"__id__": 43}], "_prefab": {"__id__": 45}, "_lpos": {"__type__": "cc.Vec3", "x": 1.9757547420340416e-06, "y": -45.2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 2.1855694143368888e-08, "w": 0.9999999999999998}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999999, "y": 1.0000000000000036, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 2.5044780654876575e-06}, "_id": ""}, {"__type__": "cc.Node", "_name": "line_xiaobiaoti", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 28}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 32}], "_prefab": {"__id__": 34}, "_lpos": {"__type__": "cc.Vec3", "x": -231.833, "y": 0.081, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -1.6543612251060553e-24, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -1.895758319773386e-22}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 13}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4d7kevXglJ9IrAHiM7cqdO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 33}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "714655a0-50e7-4cd1-b74d-0d431f9d6a40@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8SqRJ8VBCyKbPEJU8mZci"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eGMj/D81N6Ymo/U563uUs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_xiaobiaoti-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 28}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 227.122, "y": -0.04, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -1.6543612251060553e-24, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -1.895758319773386e-22}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 37}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 13}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccGGuH8IVKzbCW61hPLwV4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 39}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "714655a0-50e7-4cd1-b74d-0d431f9d6a40@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06NEsbOStPfIa5xZflYY2e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "14K7f8mx1AJqoDe1AiTirQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29h6IgRqlDMaJNM/vNsDSv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 44}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 207, "a": 255}, "_string": "修炼额外产出以下道具", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baZja6QjhMI5t5YQaEr18M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37Se62zcNIJIv5ES/qDi9T", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout_grid_item", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 27}, "_children": [{"__id__": 47}, {"__id__": 107}, {"__id__": 167}, {"__id__": 227}, {"__id__": 287}, {"__id__": 347}], "_active": true, "_components": [{"__id__": 477}, {"__id__": 479}], "_prefab": {"__id__": 481}, "_lpos": {"__type__": "cc.Vec3", "x": 6.574192809694068e-06, "y": -215.4, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 2.185569414336889e-08, "w": 0.9999999999999998}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999999, "y": 1.0000000000000036, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 2.5044780654876575e-06}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 48}], "_active": true, "_components": [{"__id__": 104}], "_prefab": {"__id__": 106}, "_lpos": {"__type__": "cc.Vec3", "x": -260, "y": 65, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 47}, "_prefab": {"__id__": 49}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 48}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 50}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "deHSjStMxBfJx+jPfWQp4m", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 51}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}, {"__id__": 58}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 64}, {"__id__": 66}, {"__id__": 68}, {"__id__": 69}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}, {"__id__": 76}, {"__id__": 78}, {"__id__": 80}, {"__id__": 82}, {"__id__": 84}, {"__id__": 86}, {"__id__": 88}, {"__id__": 90}, {"__id__": 92}, {"__id__": 94}, {"__id__": 96}, {"__id__": 98}, {"__id__": 100}, {"__id__": 102}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -8.271806125530275e-24, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -9.478791598866928e-22}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 24, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["ed9XyosgZJUK1U+YQncdTI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["24ZnKGYZVP7abWndgkQ6a+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 54.013, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 75}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["52tBhu9jFD8YLxWZYCWCYE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 85}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["b2orrYmttPQZjn/uCbW6F2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 89}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["96CIjvlQpK1bV6c7+bZaWp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 91}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0cg5N2Pi1IipqXPgu58mv6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["22LivKLyBJnqLaZ/cuuAQq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["4b/dxJwoBBTKbEKF8WXnqj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["75ysoqbbtJ66X+nt4GCiQX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0eCaRzoVpCx4A0jdxwMIY8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 101}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["bdDbxfCo9EzK7XeZ43Y5qx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["48BcVglkFD8aLFQDaGvbmJ"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 105}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4O5wSJMdLcKO40pX26Cek"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a45PaF6ilG16lc1RG9bREX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 108}], "_active": true, "_components": [{"__id__": 164}], "_prefab": {"__id__": 166}, "_lpos": {"__type__": "cc.Vec3", "x": -130, "y": 65, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 107}, "_prefab": {"__id__": 109}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 108}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 110}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "edE1tqq1pA95AAYAtvrKmo", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 111}, {"__id__": 113}, {"__id__": 114}, {"__id__": 115}, {"__id__": 116}, {"__id__": 118}, {"__id__": 120}, {"__id__": 121}, {"__id__": 122}, {"__id__": 124}, {"__id__": 126}, {"__id__": 128}, {"__id__": 129}, {"__id__": 130}, {"__id__": 132}, {"__id__": 134}, {"__id__": 136}, {"__id__": 138}, {"__id__": 140}, {"__id__": 142}, {"__id__": 144}, {"__id__": 146}, {"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 154}, {"__id__": 156}, {"__id__": 158}, {"__id__": 160}, {"__id__": 162}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -8.271806125530275e-24, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -9.478791598866928e-22}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 117}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 24, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["ed9XyosgZJUK1U+YQncdTI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["24ZnKGYZVP7abWndgkQ6a+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 54.013, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["52tBhu9jFD8YLxWZYCWCYE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 137}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 139}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 141}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 143}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 145}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 147}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["b2orrYmttPQZjn/uCbW6F2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["96CIjvlQpK1bV6c7+bZaWp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0cg5N2Pi1IipqXPgu58mv6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 153}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["22LivKLyBJnqLaZ/cuuAQq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["4b/dxJwoBBTKbEKF8WXnqj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 157}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["75ysoqbbtJ66X+nt4GCiQX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0eCaRzoVpCx4A0jdxwMIY8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["bdDbxfCo9EzK7XeZ43Y5qx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 163}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["48BcVglkFD8aLFQDaGvbmJ"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 165}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5Cta3eRpF6o22fmFsPucL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "14AWFYTpFJR73oo96MpTUH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 168}], "_active": true, "_components": [{"__id__": 224}], "_prefab": {"__id__": 226}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 65, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 167}, "_prefab": {"__id__": 169}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 168}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 170}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "4ePuRurPJD9ZmqjT/ClW49", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 171}, {"__id__": 173}, {"__id__": 174}, {"__id__": 175}, {"__id__": 176}, {"__id__": 178}, {"__id__": 180}, {"__id__": 181}, {"__id__": 182}, {"__id__": 184}, {"__id__": 186}, {"__id__": 188}, {"__id__": 189}, {"__id__": 190}, {"__id__": 192}, {"__id__": 194}, {"__id__": 196}, {"__id__": 198}, {"__id__": 200}, {"__id__": 202}, {"__id__": 204}, {"__id__": 206}, {"__id__": 208}, {"__id__": 210}, {"__id__": 212}, {"__id__": 214}, {"__id__": 216}, {"__id__": 218}, {"__id__": 220}, {"__id__": 222}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -8.271806125530275e-24, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -9.478791598866928e-22}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 177}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 24, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 183}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["ed9XyosgZJUK1U+YQncdTI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 185}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["24ZnKGYZVP7abWndgkQ6a+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 187}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 54.013, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 187}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 193}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 195}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["52tBhu9jFD8YLxWZYCWCYE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 199}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 201}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 205}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["b2orrYmttPQZjn/uCbW6F2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 209}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["96CIjvlQpK1bV6c7+bZaWp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 211}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0cg5N2Pi1IipqXPgu58mv6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 213}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["22LivKLyBJnqLaZ/cuuAQq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 215}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["4b/dxJwoBBTKbEKF8WXnqj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 217}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["75ysoqbbtJ66X+nt4GCiQX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 219}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0eCaRzoVpCx4A0jdxwMIY8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 221}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["bdDbxfCo9EzK7XeZ43Y5qx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["48BcVglkFD8aLFQDaGvbmJ"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 225}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "559Zcn9A9J5by30fhxHf0v"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04vGaaq+5E1L2uWu2v7jai", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 228}], "_active": true, "_components": [{"__id__": 284}], "_prefab": {"__id__": 286}, "_lpos": {"__type__": "cc.Vec3", "x": 130, "y": 65, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 227}, "_prefab": {"__id__": 229}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 228}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 230}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8ayPlRahlNJbHYJOkEwuAI", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 231}, {"__id__": 233}, {"__id__": 234}, {"__id__": 235}, {"__id__": 236}, {"__id__": 238}, {"__id__": 240}, {"__id__": 241}, {"__id__": 242}, {"__id__": 244}, {"__id__": 246}, {"__id__": 248}, {"__id__": 249}, {"__id__": 250}, {"__id__": 252}, {"__id__": 254}, {"__id__": 256}, {"__id__": 258}, {"__id__": 260}, {"__id__": 262}, {"__id__": 264}, {"__id__": 266}, {"__id__": 268}, {"__id__": 270}, {"__id__": 272}, {"__id__": 274}, {"__id__": 276}, {"__id__": 278}, {"__id__": 280}, {"__id__": 282}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -8.271806125530275e-24, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -9.478791598866928e-22}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 237}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 24, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 243}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["ed9XyosgZJUK1U+YQncdTI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["24ZnKGYZVP7abWndgkQ6a+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 247}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 54.013, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 247}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 232}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 251}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 253}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["52tBhu9jFD8YLxWZYCWCYE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 257}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 259}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 261}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 263}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 265}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 267}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["b2orrYmttPQZjn/uCbW6F2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 269}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["96CIjvlQpK1bV6c7+bZaWp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0cg5N2Pi1IipqXPgu58mv6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 273}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["22LivKLyBJnqLaZ/cuuAQq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["4b/dxJwoBBTKbEKF8WXnqj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 277}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["75ysoqbbtJ66X+nt4GCiQX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 279}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0eCaRzoVpCx4A0jdxwMIY8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 281}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["bdDbxfCo9EzK7XeZ43Y5qx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 283}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["48BcVglkFD8aLFQDaGvbmJ"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 227}, "_enabled": true, "__prefab": {"__id__": 285}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4EXiCciRDXIsApDyjpHtP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98NVMLDatFoIsIjp6jCffm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 288}], "_active": true, "_components": [{"__id__": 344}], "_prefab": {"__id__": 346}, "_lpos": {"__type__": "cc.Vec3", "x": 260, "y": 65, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 287}, "_prefab": {"__id__": 289}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 288}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 290}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c6Zz6T17pJFb2kb74fbN9t", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 291}, {"__id__": 293}, {"__id__": 294}, {"__id__": 295}, {"__id__": 296}, {"__id__": 298}, {"__id__": 300}, {"__id__": 301}, {"__id__": 302}, {"__id__": 304}, {"__id__": 306}, {"__id__": 308}, {"__id__": 309}, {"__id__": 310}, {"__id__": 312}, {"__id__": 314}, {"__id__": 316}, {"__id__": 318}, {"__id__": 320}, {"__id__": 322}, {"__id__": 324}, {"__id__": 326}, {"__id__": 328}, {"__id__": 330}, {"__id__": 332}, {"__id__": 334}, {"__id__": 336}, {"__id__": 338}, {"__id__": 340}, {"__id__": 342}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -8.271806125530275e-24, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -9.478791598866928e-22}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 297}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 24, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 299}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 303}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["ed9XyosgZJUK1U+YQncdTI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 305}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["24ZnKGYZVP7abWndgkQ6a+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 307}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 54.013, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 307}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 292}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 311}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 313}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 315}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["52tBhu9jFD8YLxWZYCWCYE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 317}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 319}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 321}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 323}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 325}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 327}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["b2orrYmttPQZjn/uCbW6F2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 329}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["96CIjvlQpK1bV6c7+bZaWp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 331}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0cg5N2Pi1IipqXPgu58mv6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 333}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["22LivKLyBJnqLaZ/cuuAQq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 335}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["4b/dxJwoBBTKbEKF8WXnqj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 337}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["75ysoqbbtJ66X+nt4GCiQX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 339}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0eCaRzoVpCx4A0jdxwMIY8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 341}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["bdDbxfCo9EzK7XeZ43Y5qx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 343}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["48BcVglkFD8aLFQDaGvbmJ"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 287}, "_enabled": true, "__prefab": {"__id__": 345}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddlWvWb4xH7JLz7fYUbR9h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edIPliJepAarib5rTqdung", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_remainder", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 348}], "_active": true, "_components": [{"__id__": 474}], "_prefab": {"__id__": 476}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -65, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "layout_remainder", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 347}, "_children": [{"__id__": 349}, {"__id__": 409}], "_active": true, "_components": [{"__id__": 469}, {"__id__": 471}], "_prefab": {"__id__": 473}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -1.6543612251060553e-24, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -1.895758319773386e-22}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_item1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 348}, "_children": [{"__id__": 350}], "_active": true, "_components": [{"__id__": 406}], "_prefab": {"__id__": 408}, "_lpos": {"__type__": "cc.Vec3", "x": -65, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 349}, "_prefab": {"__id__": 351}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 350}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 352}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "07xXDaaRlEeaD80gAIHtG2", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 353}, {"__id__": 355}, {"__id__": 356}, {"__id__": 357}, {"__id__": 358}, {"__id__": 360}, {"__id__": 362}, {"__id__": 363}, {"__id__": 364}, {"__id__": 366}, {"__id__": 368}, {"__id__": 370}, {"__id__": 371}, {"__id__": 372}, {"__id__": 374}, {"__id__": 376}, {"__id__": 378}, {"__id__": 380}, {"__id__": 382}, {"__id__": 384}, {"__id__": 386}, {"__id__": 388}, {"__id__": 390}, {"__id__": 392}, {"__id__": 394}, {"__id__": 396}, {"__id__": 398}, {"__id__": 400}, {"__id__": 402}, {"__id__": 404}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -8.271806125530275e-24, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -9.478791598866928e-22}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 359}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 24, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 361}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 365}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["ed9XyosgZJUK1U+YQncdTI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 367}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["24ZnKGYZVP7abWndgkQ6a+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 369}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 54.013, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 369}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 373}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 375}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 377}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["52tBhu9jFD8YLxWZYCWCYE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 379}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 381}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 383}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 385}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 387}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 389}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["b2orrYmttPQZjn/uCbW6F2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["96CIjvlQpK1bV6c7+bZaWp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 393}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0cg5N2Pi1IipqXPgu58mv6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 395}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["22LivKLyBJnqLaZ/cuuAQq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 397}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["4b/dxJwoBBTKbEKF8WXnqj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 399}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["75ysoqbbtJ66X+nt4GCiQX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 401}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0eCaRzoVpCx4A0jdxwMIY8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 403}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["bdDbxfCo9EzK7XeZ43Y5qx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 405}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["48BcVglkFD8aLFQDaGvbmJ"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 407}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3IZKWPSpCeZ0X7LkFUcVM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44o+cdZspAb66gjNCNp1tv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 348}, "_children": [{"__id__": 410}], "_active": true, "_components": [{"__id__": 466}], "_prefab": {"__id__": 468}, "_lpos": {"__type__": "cc.Vec3", "x": 65, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 409}, "_prefab": {"__id__": 411}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 410}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 412}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "53MJFgoitHlIH9VeVHFfpt", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 413}, {"__id__": 415}, {"__id__": 416}, {"__id__": 417}, {"__id__": 418}, {"__id__": 420}, {"__id__": 422}, {"__id__": 423}, {"__id__": 424}, {"__id__": 426}, {"__id__": 428}, {"__id__": 430}, {"__id__": 431}, {"__id__": 432}, {"__id__": 434}, {"__id__": 436}, {"__id__": 438}, {"__id__": 440}, {"__id__": 442}, {"__id__": 444}, {"__id__": 446}, {"__id__": 448}, {"__id__": 450}, {"__id__": 452}, {"__id__": 454}, {"__id__": 456}, {"__id__": 458}, {"__id__": 460}, {"__id__": 462}, {"__id__": 464}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 414}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 414}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 414}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -8.271806125530275e-24, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 414}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -9.478791598866928e-22}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 419}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 24, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 421}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 414}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 414}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 425}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["ed9XyosgZJUK1U+YQncdTI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 427}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["24ZnKGYZVP7abWndgkQ6a+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 429}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 54.013, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 429}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 414}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 433}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 435}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 437}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["52tBhu9jFD8YLxWZYCWCYE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 439}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 441}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 443}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 445}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 447}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 449}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["b2orrYmttPQZjn/uCbW6F2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 451}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["96CIjvlQpK1bV6c7+bZaWp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 453}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0cg5N2Pi1IipqXPgu58mv6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 455}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["22LivKLyBJnqLaZ/cuuAQq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 457}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["4b/dxJwoBBTKbEKF8WXnqj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 459}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["75ysoqbbtJ66X+nt4GCiQX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 461}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["0eCaRzoVpCx4A0jdxwMIY8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 463}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["bdDbxfCo9EzK7XeZ43Y5qx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 465}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["48BcVglkFD8aLFQDaGvbmJ"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 467}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3bx85zBxHd5J562RfwAtC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b6SWnhxNNMCYbcn5/AryK1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 348}, "_enabled": true, "__prefab": {"__id__": 470}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9cn63dqlNBapghJTFa+Y1"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 348}, "_enabled": true, "__prefab": {"__id__": 472}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8defm3eSdPt5r0SX50dUXt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8fEVmSrPtMxKE3VC8X7kXF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 475}, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57JjMZ2ydCIpm3niv2emtc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8IeifKhFGTLeksa9FrTWX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 478}, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 290}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2lMJQjLpJRKCHNvO4+FAS"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 480}, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 20, "_paddingBottom": 20, "_spacingX": 10, "_spacingY": 10, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60baJJyEROqppdUUyzYRtn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4m9g3KoBN6Zxwut6cbu1q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 483}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 400.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56QeAO4V5EF4yEwEXyEuzc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": false, "__prefab": {"__id__": 485}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2ada6e64-1496-4163-9c67-8a7e739db12e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cvGD24bZH6otFzwqUllVR"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 487}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 20, "_paddingBottom": 40, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6Dc2KuAhJzaA94rSgcBQ4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddiCM6GM1H/pNazzPXHYEk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 490}, {"__id__": 492}, {"__id__": 494}], "_prefab": {"__id__": 496}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -200, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 489}, "_enabled": true, "__prefab": {"__id__": 491}, "_contentSize": {"__type__": "cc.Size", "width": 178.1999969482422, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3UzPuufFA65LUY4kLmeSB"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 489}, "_enabled": true, "__prefab": {"__id__": 493}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "3秒后自动关闭", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19sazTPv5PVY3qssqVgLbv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 489}, "_enabled": true, "__prefab": {"__id__": 495}, "_opacity": 127.5, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62QwUc5B1FRJoHHBnj6KrP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e7F9lbPpJIpKE8m3l6H9kp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 498}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0xXgN5OVLU7yKOneanwNO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 500}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fuyf5nk5HHL38VpH/Imzi"}, {"__type__": "38dc5x9C+pMA4KrIsB8rc3o", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 502}, "uiTransformBg": {"__id__": 20}, "nodeLayoutGridItem": {"__id__": 46}, "nodeLayoutRemainder": {"__id__": 348}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c71bEJp8dM/ICNWFP3lX9d"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 410}, {"__id__": 350}, {"__id__": 288}, {"__id__": 228}, {"__id__": 168}, {"__id__": 108}, {"__id__": 48}]}]