[{"__type__": "cc.Prefab", "_name": "UIClubTips", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIClubTips", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 39}, {"__id__": 53}], "_active": true, "_components": [{"__id__": 59}, {"__id__": 61}, {"__id__": 63}], "_prefab": {"__id__": 65}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "4dpZ8WKpBACYHar+3RCucw", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 5}], "mountedComponents": [], "propertyOverrides": [{"__id__": 13}, {"__id__": 15}, {"__id__": 17}, {"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}, {"__id__": 29}, {"__id__": 31}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 37}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 6}, "nodes": [{"__id__": 7}]}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "cc.Node", "_name": "lbl_name", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 2}}, "_parent": {"__id__": 2}, "_children": [], "_active": false, "_components": [{"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -76.667, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.6666666666666665, "y": 1.6666666666666665, "z": 0.9999999999999998}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": {"__id__": 9}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "757oDBMFBJrZNEydkJXUgA"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": {"__id__": 11}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "道具名", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bAKtKLnZKCYBH2GWUXLMe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dEoCYsDxG041rNb2fiUFU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -969, "y": -50, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 16}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 18}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 22}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 26}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 71, "height": 71}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 30.75, "y": -19.009999999999998, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_fontSize"], "value": 23}, {"__type__": "cc.TargetInfo", "localID": ["8aXJ8fJdZOsa3oa+CmpHN6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 59.199951171875, "height": 32.980000000000004}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_actualFontSize"], "value": 23}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_lineHeight"], "value": 23}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["d1PcUCEbVCQ7t7NK/Q0EQM"]}, {"__type__": "cc.Node", "_name": "tip_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 40}], "_active": true, "_components": [{"__id__": 48}, {"__id__": 50}], "_prefab": {"__id__": 52}, "_lpos": {"__type__": "cc.Vec3", "x": -881, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_tip_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 41}, {"__id__": 43}, {"__id__": 45}], "_prefab": {"__id__": 47}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0bZC0yJxG2pl61qVkcNhg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 44}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aivy9cIFJyItuNFJU+vzk"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 46}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 10, "_paddingBottom": 10, "_spacingX": 10, "_spacingY": 10, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecSrUEsZdFKoCsqLZmSAaB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55JdBM3vFCA5EpmgIEIuB/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 49}, "_contentSize": {"__type__": "cc.Size", "width": 272, "height": 98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a6QGMiBVMhrRJS/9ZLDgc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 51}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e2efc2c1-2d71-491c-8c2e-b7e8309ea73c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7FoDw85lKNLjnnqsm5G9H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2aF+mNuEpO0pNy+KTQUZPD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "indicator", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 54}, {"__id__": 56}], "_prefab": {"__id__": 58}, "_lpos": {"__type__": "cc.Vec3", "x": -880, "y": -2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 55}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dalzqM3NJkoOSeUVHE9DV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 57}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0be1473f-9043-42a6-a77f-8d77a2ca9900@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76plf69hhNO6YYfZrB2Yff"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d3Z6eBZoRMPZk6SOEqpwzN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 60}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82np0Pq5JHso2E+BFt167N"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 62}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22mrFEaUtLsZFJIBbVqE19"}, {"__type__": "eb37dxD+/hLpJe4l/3FOqzj", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 64}, "item": {"__id__": 2}, "bgTipContent": {"__id__": 40}, "indicator": {"__id__": 53}, "tipContent": {"__id__": 39}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07hMjKNDJDNauuK1AIRpEW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [{"__id__": 66}, {"__id__": 69}, {"__id__": 72}, {"__id__": 75}, {"__id__": 78}, {"__id__": 81}, {"__id__": 84}, {"__id__": 87}, {"__id__": 90}, {"__id__": 93}, {"__id__": 96}], "nestedPrefabInstanceRoots": [{"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 67}, "propertyPath": ["bgColor"], "target": {"__id__": 2}, "targetInfo": {"__id__": 68}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["ef0OK1CglK8Y/O9Qla7khp"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 70}, "propertyPath": ["bgIcon"], "target": {"__id__": 2}, "targetInfo": {"__id__": 71}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["d1PcUCEbVCQ7t7NK/Q0EQM"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 73}, "propertyPath": ["lblHas"], "target": {"__id__": 2}, "targetInfo": {"__id__": 74}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["8aXJ8fJdZOsa3oa+CmpHN6"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 76}, "propertyPath": ["lblName"], "target": {"__id__": 2}, "targetInfo": {"__id__": 77}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["2bAKtKLnZKCYBH2GWUXLMe"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 79}, "propertyPath": ["nodeTips"], "target": {"__id__": 2}, "targetInfo": {"__id__": 80}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 82}, "propertyPath": ["bgT<PERSON>s<PERSON><PERSON>nt"], "target": {"__id__": 2}, "targetInfo": {"__id__": 83}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 85}, "propertyPath": ["bgItemColor"], "target": {"__id__": 2}, "targetInfo": {"__id__": 86}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["3eIOGCiW5HbYMZaWwXgGaX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 88}, "propertyPath": ["bgItemIcon"], "target": {"__id__": 2}, "targetInfo": {"__id__": 89}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["35RZGvJ0hBD5629CGQ6j+D"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 91}, "propertyPath": ["lblItemName"], "target": {"__id__": 2}, "targetInfo": {"__id__": 92}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["43L6/IG/9I/pZCDUJhm1Cm"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 94}, "propertyPath": ["lblItemNum"], "target": {"__id__": 2}, "targetInfo": {"__id__": 95}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["c4pyc25U9Dxaj24eKfnB/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 97}, "propertyPath": ["lblItemInfo"], "target": {"__id__": 2}, "targetInfo": {"__id__": 98}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["9aY8Z0t2hGpYCt3Bxc17wV"]}]