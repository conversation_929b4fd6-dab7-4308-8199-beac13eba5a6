import { _decorator, SpriteFrame } from "cc";
import { StartUp } from "../lib/StartUp";
import { SceneMgr } from "../lib/scene/SceneMgr";
import SocketClient from "../lib/socket/SocketClient";
import { MainScene } from "./scene/MainScene";
import BundleUtils from "../lib/utils/BundleUtils";
import { BundleEnum } from "./bundleEnum/BundleEnum";
import { ResHelper } from "../../platform/src/ResHelper";
import { Prefab, instantiate } from "cc";
import AssetUtils from "../lib/utils/AssetUtils";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { ScreenUtil } from "../lib/utils/ScreenUtil";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import GuideMgr from "../ext_guide/GuideMgr";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass } = _decorator;

export const dtTime: number = 0.016;
//npm run buildpb
// npm run gen
@ccclass("BoutStartUp")
export class BoutStartUp extends StartUp {
  private _iscon: boolean = true;

  private static nodeInfo: any;

  onLoad(): void {
    super.onLoad();
    // 是否适配过
    ScreenUtil.adaptScreen();

    this.adaptScreen();
    // 全局错误处理
    window.onerror = function (message, source, lineno, colno, error) {
      log.error("全局异常捕获:", message, source, lineno, colno, error);
      return true;
    };
  }

  protected async startGame(completedCallback) {
    let bundle = await BundleUtils.getBundleN(BundleEnum.BUNDLE_COMMON_ITEM);
    bundle.loadDir("autoItem", SpriteFrame, (err, assetList) => {
      if (err) {
        log.error("err===", err);
        return;
      }
      for (let i = 0; i < assetList.length; i++) {
        let spr = assetList[i];
        spr.addRef();
      }
    });

    if (SocketClient.ins.isOpen) {
      SceneMgr.instance.replaceScene(MainScene);
    }

    // 下载英雄资源
    ResHelper.loadBundle(BundleEnum.BUNDLE_COMMON_HERO_FULL);

    // 战力繁荣度提示层
    const prb: Prefab = (await AssetUtils.loadAsset(
      BundleEnum.BUNDLE_G_COMMON_MAIN,
      "prefab/ui/UIPowerTipsLayer",
      Prefab
    )) as Prefab;

    let tipsLayer = instantiate(prb);
    TipsMgr.getTipsRoot().addChild(tipsLayer);

    await GuideMgr.create();

    completedCallback && completedCallback();
  }
}
