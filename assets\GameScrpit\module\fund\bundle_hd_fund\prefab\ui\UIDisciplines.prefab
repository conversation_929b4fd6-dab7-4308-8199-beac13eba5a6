[{"__type__": "cc.Prefab", "_name": "UIDisciplines", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIDisciplines", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 254}, {"__id__": 256}], "_prefab": {"__id__": 258}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 17}, {"__id__": 29}, {"__id__": 33}, {"__id__": 103}, {"__id__": 181}], "_active": true, "_components": [{"__id__": 251}], "_prefab": {"__id__": 253}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_JJ", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}], "_prefab": {"__id__": 16}, "_lpos": {"__type__": "cc.Vec3", "x": 5.099, "y": -1.879, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 326.763, "y": 572.327, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcIb8aYhVCVZP8RiUfeSHE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8e27d803-97dc-403c-bae5-609f4c45539b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aUNesju1MEah4UCjhlGzs"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 10}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ee+y3uOjxCgbw07tZsLR/o"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37456pZJdJe7h8TQiOEnRR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 13}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 1220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6j7iihVdFsI2H8DsM3Exs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f455f8cd-3502-417f-ac6b-a83c72ca8523@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50DJ7yFf1FIKGqJo0Pyad7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2sRIfwGxKGoC+gqt/Wagh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TY_bg_zhuangshi_02", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 18}], "_active": true, "_components": [{"__id__": 24}, {"__id__": 26}], "_prefab": {"__id__": 28}, "_lpos": {"__type__": "cc.Vec3", "x": -154.456, "y": 538.186, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}], "_prefab": {"__id__": 23}, "_lpos": {"__type__": "cc.Vec3", "x": -193.109, "y": 2.302, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 124.74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fJqb77lBAG5CYxhCO5nmc"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_lineHeight": 99, "_string": "<outline color=#ffa200 width=3><color=#ffffff><size=91>修</size></color></outline><outline color=#ffa200 width=3><color=#ffffff><size=77>行</size></color></outline>", "_horizontalAlign": 1, "_verticalAlign": 2, "_fontSize": 99, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97vhpr8BVJNr3RfS77EZCD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dTK2Njj1H9KM76zrXR/AI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 25}, "_contentSize": {"__type__": "cc.Size", "width": 407, "height": 176}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2agmy3wxBzKggy+2wGS1b"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 27}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "41d4e0c2-61eb-4d08-94fe-e48ab611739d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5q/R+KNlN9ZEDutTpHIId"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cexQj7UpxH4KZLSfJJzH4q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "main_layer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aztjnvd1AH40//xha/zHS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cc6D6yqj1CZaTCPrsCXUPu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lay_bottom_btn_spr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 34}, {"__id__": 50}, {"__id__": 66}, {"__id__": 82}], "_active": true, "_components": [{"__id__": 98}, {"__id__": 100}], "_prefab": {"__id__": 102}, "_lpos": {"__type__": "cc.Vec3", "x": 316, "y": -650, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_type1_spr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 33}, "_children": [{"__id__": 35}, {"__id__": 41}], "_active": true, "_components": [{"__id__": 47}], "_prefab": {"__id__": 49}, "_lpos": {"__type__": "cc.Vec3", "x": -558.25, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "pich", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 37}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eZtZvBvlOVZGg4K3buwfI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 39}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4f6079-50dd-47ea-982e-ad20f3c3c525@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83n0dO+JhDwIC66tOTCCeA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54nF/tqB9PRIuRjjQ4GUmN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_pich", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 42}, {"__id__": 44}], "_prefab": {"__id__": 46}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 43}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71TY4hZwRAAo2tzEMyQ9RT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 45}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1265zJbhN2YjmRQ3lpM6X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91JjDP9l9FCabOXufk4Dyc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 48}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11m4antsFLhaFrADgv3siI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8771H5ys9HrqWrYr3N+tTK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type2_spr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 33}, "_children": [{"__id__": 51}, {"__id__": 57}], "_active": true, "_components": [{"__id__": 63}], "_prefab": {"__id__": 65}, "_lpos": {"__type__": "cc.Vec3", "x": -397.75, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "pich", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 54}], "_prefab": {"__id__": 56}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 53}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0QTewXE9C0buy7k6rMhuR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 55}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4f6079-50dd-47ea-982e-ad20f3c3c525@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5t9o/5kZBMKaLM6NZrGC+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2ruhzphdB/ri31AQ6OOoC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_pich", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 58}, {"__id__": 60}], "_prefab": {"__id__": 62}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 59}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e44JkShwRK8LU6ql8oB8ED"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 61}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26pjgTDJNGoLiNtgF3hkDY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2R1XwBAxP35Ts22xQZWKt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 64}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54GQwexGBICLdhKXJlnGC8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cUvvWLR1GKYbZehrqov4k", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type3_spr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 33}, "_children": [{"__id__": 67}, {"__id__": 73}], "_active": true, "_components": [{"__id__": 79}], "_prefab": {"__id__": 81}, "_lpos": {"__type__": "cc.Vec3", "x": -237.25, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "pich", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 70}], "_prefab": {"__id__": 72}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caqaqDbIBIxaKmLfuBnHhf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 71}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4f6079-50dd-47ea-982e-ad20f3c3c525@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c81gR/g/NL/4zukrsCLzzP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8434HieHpF2LklE9alKEqB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_pich", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [{"__id__": 74}, {"__id__": 76}], "_prefab": {"__id__": 78}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 75}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86tnUlRD1AZa3HDtWUlNMd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 77}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26vlJKUoBO3KtH8rwM33Tx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43LDFCV2hGMaVscHmrAo48", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 80}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acwTpCtIRAWpFxJx6XqEEC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "82kszfMpVDI5TIXfukbTK0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type4_spr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 33}, "_children": [{"__id__": 83}, {"__id__": 89}], "_active": true, "_components": [{"__id__": 95}], "_prefab": {"__id__": 97}, "_lpos": {"__type__": "cc.Vec3", "x": -76.75, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "pich", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 86}], "_prefab": {"__id__": 88}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 85}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bR1kPE0xCErwKpBT8TAOB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 87}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4f6079-50dd-47ea-982e-ad20f3c3c525@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83FFxdVElMULZq4EzkCY3O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6bAtgtSgVGEr7fRlsRpTQ6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_pich", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 92}], "_prefab": {"__id__": 94}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 91}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24DOo0N9JOOYjKwwULeT9p"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 93}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89jeIBf69Lr5NuEW4BH5xu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4bYYWqfKBFA7F+o3N7tHCC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 96}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5e+Gh7AkVH4b3idrfuA4rV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceeJbbboRFWaOdR2IEuMFp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 99}, "_contentSize": {"__type__": "cc.Size", "width": 635, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fVOQkullPvJdhK0+ax2QB"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 101}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 7, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70SpaKuIdFBLujkHzYtHyN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2etrzWExOmanysWk0BlT4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lay_bottom_btn_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 104}, {"__id__": 122}, {"__id__": 140}, {"__id__": 158}], "_active": true, "_components": [{"__id__": 176}, {"__id__": 178}], "_prefab": {"__id__": 180}, "_lpos": {"__type__": "cc.Vec3", "x": 316, "y": -650, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_type1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 103}, "_children": [{"__id__": 105}, {"__id__": 111}], "_active": true, "_components": [{"__id__": 117}, {"__id__": 119}], "_prefab": {"__id__": 121}, "_lpos": {"__type__": "cc.Vec3", "x": -558.25, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "pich_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_components": [{"__id__": 106}, {"__id__": 108}], "_prefab": {"__id__": 110}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": {"__id__": 107}, "_contentSize": {"__type__": "cc.Size", "width": 81.19999694824219, "height": 46.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2irKcb25DxIHZ/q19CTwc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 105}, "_enabled": true, "__prefab": {"__id__": 109}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "修 行", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbfTZ6bjxClrjuoJEosg4n"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11uaZqQoVPjaQUFo+Ebi+W", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_pich_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_components": [{"__id__": 112}, {"__id__": 114}], "_prefab": {"__id__": 116}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 113}, "_contentSize": {"__type__": "cc.Size", "width": 81.19999694824219, "height": 46.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efUVZPUOBPJrDyYXTc2hf4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 115}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "修 行", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 49, "g": 104, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68IvwaRcJMrbApBKAX4vDw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acIZmam2RLQr+0NnCjoJsy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": {"__id__": 118}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76Zinhq7xMmZTwlzAxJzNO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": {"__id__": 120}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 104}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfq23lCz1DWY6YPae+KYBO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31hPjNuFNG+LOdt0beec9/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 103}, "_children": [{"__id__": 123}, {"__id__": 129}], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}], "_prefab": {"__id__": 139}, "_lpos": {"__type__": "cc.Vec3", "x": -397.75, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "pich_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 122}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 126}], "_prefab": {"__id__": 128}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 125}, "_contentSize": {"__type__": "cc.Size", "width": 81.19999694824219, "height": 46.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcP4gPQytD0pzv+Kcln1dv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 127}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "福 泽", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30eeNrCRROw6vc2kFBEC6X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "409RDZcixNxaOIVvqN9nVs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_pich_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 122}, "_children": [], "_active": true, "_components": [{"__id__": 130}, {"__id__": 132}], "_prefab": {"__id__": 134}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 131}, "_contentSize": {"__type__": "cc.Size", "width": 81.19999694824219, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50zzfRfKNCNK+FJNRlqXjR"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 133}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "福 泽", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 49, "g": 104, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81sCfX76FDtrEYVQUx8ccL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "95r0zSL0dJwrcLJvGWJOc1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f66IbVw5VDuZQafH4kkqoo"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": {"__id__": 138}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 122}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77ggY9KPVDYovSOy0yCVeD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d3RfDpM89LErTdxbREWrrK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 103}, "_children": [{"__id__": 141}, {"__id__": 147}], "_active": true, "_components": [{"__id__": 153}, {"__id__": 155}], "_prefab": {"__id__": 157}, "_lpos": {"__type__": "cc.Vec3", "x": -237.25, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "pich_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 140}, "_children": [], "_active": true, "_components": [{"__id__": 142}, {"__id__": 144}], "_prefab": {"__id__": 146}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 143}, "_contentSize": {"__type__": "cc.Size", "width": 81.19999694824219, "height": 46.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5elP3tVlJjKSInmyYm3Jb"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 145}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "任 务", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bemjripc9BB50uSZu5ncRC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7a+80IlapMA6fjdDBLPfy9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_pich_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 140}, "_children": [], "_active": true, "_components": [{"__id__": 148}, {"__id__": 150}], "_prefab": {"__id__": 152}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 149}, "_contentSize": {"__type__": "cc.Size", "width": 81.19999694824219, "height": 46.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8aD1mtJxhKAL5doO3shB0i"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 151}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "任 务", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 49, "g": 104, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35Onb36MtEGpzsiYKSGoWM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5c/Z08655BZaKJmmlNLGiy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 154}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2F7arJcxCL7Vq6DqJJiBQ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 156}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9PfzXAs9F4oAvZoqvxox8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "83qMYwxNxLzKedCmpYTT2H", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 103}, "_children": [{"__id__": 159}, {"__id__": 165}], "_active": true, "_components": [{"__id__": 171}, {"__id__": 173}], "_prefab": {"__id__": 175}, "_lpos": {"__type__": "cc.Vec3", "x": -76.75, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "pich_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 158}, "_children": [], "_active": true, "_components": [{"__id__": 160}, {"__id__": 162}], "_prefab": {"__id__": 164}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 161}, "_contentSize": {"__type__": "cc.Size", "width": 81.19999694824219, "height": 46.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99fUKbFy1KnIKCuN88KHi/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 163}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "礼 包", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55nb7oMQhGmINEAOI9GqVN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6cP7HnEZCVZAvo0dhgmyi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "no_pich_lbl", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 158}, "_children": [], "_active": true, "_components": [{"__id__": 166}, {"__id__": 168}], "_prefab": {"__id__": 170}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": {"__id__": 167}, "_contentSize": {"__type__": "cc.Size", "width": 81.19999694824219, "height": 46.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59PXg1djBMabYiUohvCh4M"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": {"__id__": 169}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "礼 包", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 49, "g": 104, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57aDi8fhxK3LR1r7FKkRD7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccWe7n0aBGg7TSycU1gnS7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 172}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8T9ivyDVKnJcZPARqhDrx"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 174}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55S+C6MqxE+o2EBJGnJLqF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c94q/np21DX5K6ZKff+f9Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": {"__id__": 177}, "_contentSize": {"__type__": "cc.Size", "width": 635, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54Y3X6R2dFT5HCbVhVzb+r"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": {"__id__": 179}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 7, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bh4TfuVZFJ5ALBkPEutXf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "88ZpEkIu5FOq2im94eZioR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lay_bottom_btn_red", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 182}, {"__id__": 198}, {"__id__": 214}, {"__id__": 230}], "_active": true, "_components": [{"__id__": 246}, {"__id__": 248}], "_prefab": {"__id__": 250}, "_lpos": {"__type__": "cc.Vec3", "x": 316, "y": -650, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_type1_red", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [{"__id__": 183}], "_active": true, "_components": [{"__id__": 195}], "_prefab": {"__id__": 197}, "_lpos": {"__type__": "cc.Vec3", "x": -558.25, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 182}, "_prefab": {"__id__": 184}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 183}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 185}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "9ap4eSyBpF+a2b3MtdjRKD", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 186}], "propertyOverrides": [{"__id__": 190}, {"__id__": 192}, {"__id__": 193}, {"__id__": 194}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 187}, "components": [{"__id__": 188}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 183}}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 189}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 11, "_top": 11, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85CovflB5KZ4hGcNouG2Ri"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 65.75, "y": 28, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": {"__id__": 196}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "054JmlHShEbptUHGTAt0iH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdRwHNjHlM9aQ3qX6742Z9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type2_red", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [{"__id__": 199}], "_active": true, "_components": [{"__id__": 211}], "_prefab": {"__id__": 213}, "_lpos": {"__type__": "cc.Vec3", "x": -397.75, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 198}, "_prefab": {"__id__": 200}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 199}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 201}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e5RANiPq5Ew72DN6SmDNWf", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 202}], "propertyOverrides": [{"__id__": 206}, {"__id__": 208}, {"__id__": 209}, {"__id__": 210}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 203}, "components": [{"__id__": 204}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 199}}, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 205}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 11, "_top": 11, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85CovflB5KZ4hGcNouG2Ri"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 65.75, "y": 28, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 212}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4insQQmNFFrQQnu/fCTLn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "406QLQsHBFV4bmoKS7RRm8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type3_red", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [{"__id__": 215}], "_active": true, "_components": [{"__id__": 227}], "_prefab": {"__id__": 229}, "_lpos": {"__type__": "cc.Vec3", "x": -237.25, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 214}, "_prefab": {"__id__": 216}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 215}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 217}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e8lM8TO61K4LPVFlj+4lo6", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 218}], "propertyOverrides": [{"__id__": 222}, {"__id__": 224}, {"__id__": 225}, {"__id__": 226}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 219}, "components": [{"__id__": 220}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 215}}, "node": {"__id__": 215}, "_enabled": true, "__prefab": {"__id__": 221}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 11, "_top": 11, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85CovflB5KZ4hGcNouG2Ri"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 65.75, "y": 28, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 214}, "_enabled": true, "__prefab": {"__id__": 228}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9rH9buE1OXpro2EFqZou2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3VrA6fBtJea5+cWHCPK0Y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_type4_red", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [{"__id__": 231}], "_active": true, "_components": [{"__id__": 243}], "_prefab": {"__id__": 245}, "_lpos": {"__type__": "cc.Vec3", "x": -76.75, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 230}, "_prefab": {"__id__": 232}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 231}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 233}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "18ok1lV7FP5LmhuxhVfpZy", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 234}], "propertyOverrides": [{"__id__": 238}, {"__id__": 240}, {"__id__": 241}, {"__id__": 242}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 235}, "components": [{"__id__": 236}]}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 231}}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 237}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 11, "_top": 11, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85CovflB5KZ4hGcNouG2Ri"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 65.75, "y": 28, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 244}, "_contentSize": {"__type__": "cc.Size", "width": 153.5, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8aUiC1UlJZYMWaPoWymYn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "480sKrynRIxZ79Iir2APMh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": {"__id__": 247}, "_contentSize": {"__type__": "cc.Size", "width": 635, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75jc5dDAZNna9csP35UnfD"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": {"__id__": 249}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 7, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32wbocPMBET6cdq4dF0C83"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dhJEcRD1Nj5iU8EH7ZR09", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 252}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdCrlysVNFa6/2V5e/iL8+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72yBZmxPxLfY/2ZUS5Am/l", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 255}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87esEexbdHCJ+SA6QWD8Nh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 257}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 100, "_bottom": 100, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 750, "_originalHeight": 1400, "_alignMode": 2, "_lockFlags": 45, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47A9WfwkNK/IutSc58UGpl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "03V21guM1IGqEstqo+CVnG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 231}, {"__id__": 215}, {"__id__": 199}, {"__id__": 183}]}]