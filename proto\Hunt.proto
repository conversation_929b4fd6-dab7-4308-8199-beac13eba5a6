syntax = "proto3";
package sim;
import "Player.proto";

// 
message HuntBossLogMessage {
  int64 userId = 1;
  // BOSS打掉的血量
  double deductHp = 2;
  // 是否成功杀死BOSS
  bool win = 3;
  // 打斗的时间戳
  int64 timeStamp = 4;
}

// 
message HuntBossRankMessage {
  double point = 1;
  int32 rank = 2;
  repeated HuntPlayerMessage rankList = 3;
}

// 
message HuntBossResponse {
  // 是否胜利
  bool win = 1;
  // 录像回放
  string replay = 2;
  // 造成的伤害
  double damageHp = 3;
  // 道具
  repeated double resAddList = 4;
  // 
  HuntTrainMessage huntTrainMessage = 5;
}

// 
message HuntBossUpdateMessage {
  // BOSS剩余血量
  double bossRemainHp = 1;
  // BOSS是否死亡
  bool died = 2;
  // 杀死BOSS的人员信息 当died为true时有值否则为null
  sim.PlayerSimpleMessage simpleMessage = 3;
  // 时间戳
  int64 timeStamp = 4;
  // 日志
  HuntBossLogMessage logMessage = 5;
}

// 
message HuntKillMessage {
  // 击杀的玩家信息
  sim.PlayerSimpleMessage simpleMessage = 1;
  // 击杀的时间戳
  int64 timeStamp = 2;
}

// 
message HuntPetResponse {
  // 是否胜利
  bool win = 1;
  // 录像回放
  string replay = 2;
  // 造成的伤害
  double damageHp = 3;
  // 道具
  repeated double resAddList = 4;
  // 进度等信息
  HuntTrainMessage huntTrain = 5;
}

// 
message HuntPlayerMessage {
  // 用户信息
  sim.PlayerSimpleMessage playerSimpleMessage = 1;
  // 分值
  double point = 2;
}

// 
message HuntRankMessage {
  double point = 1;
  int32 rank = 2;
  repeated HuntPlayerMessage rankList = 3;
}

// 
message HuntTrainMessage {
  // 剩余的参数次数
  int32 chance = 1;
  // 目前的战斗进度，打到第几只灵兽(值从0开始)
  int32 progress = 2;
  // 当前狩猎的灵兽剩余的血量
  double petRemainHp = 3;
  // 玩家显示的战力
  double power = 4;
  // 玩家剩余的血量
  double remainHp = 5;
  // 玩家的血槽大小
  double totalHp = 6;
  double bossRemainHp = 7;
  double bossHp = 8;
  // BOSS战力
  double bossPower = 9;
  // 战友信息
  repeated sim.PlayerBaseMessage buddyList = 10;
  // 猎杀BOSS的玩家信息 当BOSS血量为0时，有实际值
  HuntKillMessage killMessage = 11;
  // 剩余的BOSS次数
  int32 bossChance = 12;
  // 当天距离洪荒活动最近开启的时间   curStamp <= 上半场结束时间 ，则值为上半场开始时间，否则为下半场开始时间
  int64 bossStartStamp = 13;
  // 当天距离洪荒活动最晚开启的时间 curStamp <= 上半场结束时间 ，则值为上半场结束时间，否则为下半场结束时间
  int64 bossEndStamp = 14;
}

